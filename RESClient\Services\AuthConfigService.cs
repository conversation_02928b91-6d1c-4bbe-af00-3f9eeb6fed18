using System;
using System.Configuration;
using System.IO;
using System.Security.Cryptography;
using System.Text;
using Newtonsoft.Json;

namespace RESClient.Services
{
    /// <summary>
    /// 认证配置服务
    /// </summary>
    public class AuthConfigService
    {
        private static readonly Lazy<AuthConfigService> _instance = new Lazy<AuthConfigService>(() => new AuthConfigService());

        /// <summary>
        /// 获取配置服务实例
        /// </summary>
        public static AuthConfigService Instance => _instance.Value;

        private const string CONFIG_KEY_ENABLE_AUTO_LOGIN_CHECK = "EnableAutoLoginCheck";
        private const string CONFIG_KEY_REMEMBER_LOGIN_STATE = "RememberLoginState";
        private const string CONFIG_KEY_LOGIN_TIMEOUT_WARNING_MINUTES = "LoginTimeoutWarningMinutes";

        private bool _enableAutoLoginCheck;
        private bool _rememberLoginState;
        private int _loginTimeoutWarningMinutes;
        
        /// <summary>
        /// 私有构造函数
        /// </summary>
        private AuthConfigService()
        {
            LoadConfiguration();
        }
        
        /// <summary>
        /// 是否启用自动登录检查
        /// </summary>
        public bool EnableAutoLoginCheck => _enableAutoLoginCheck;
        
        /// <summary>
        /// 是否记住登录状态
        /// </summary>
        public bool RememberLoginState => _rememberLoginState;
        
        /// <summary>
        /// 登录超时提醒时间（分钟）
        /// </summary>
        public int LoginTimeoutWarningMinutes => _loginTimeoutWarningMinutes;
        
        /// <summary>
        /// 加载配置
        /// </summary>
        private void LoadConfiguration()
        {
            try
            {
                // 读取自动登录检查配置
                var enableAutoLoginCheckValue = ConfigurationManager.AppSettings[CONFIG_KEY_ENABLE_AUTO_LOGIN_CHECK];
                _enableAutoLoginCheck = string.IsNullOrEmpty(enableAutoLoginCheckValue) || 
                                       bool.Parse(enableAutoLoginCheckValue);
                
                // 读取记住登录状态配置
                var rememberLoginStateValue = ConfigurationManager.AppSettings[CONFIG_KEY_REMEMBER_LOGIN_STATE];
                _rememberLoginState = string.IsNullOrEmpty(rememberLoginStateValue) || 
                                     bool.Parse(rememberLoginStateValue);
                
                // 读取登录超时提醒配置
                var loginTimeoutWarningValue = ConfigurationManager.AppSettings[CONFIG_KEY_LOGIN_TIMEOUT_WARNING_MINUTES];
                _loginTimeoutWarningMinutes = string.IsNullOrEmpty(loginTimeoutWarningValue) ? 
                                             30 : int.Parse(loginTimeoutWarningValue);
            }
            catch (Exception ex)
            {
                // 如果配置加载失败，使用默认值
                _enableAutoLoginCheck = true;
                _rememberLoginState = true;
                _loginTimeoutWarningMinutes = 30;
                
                // 可以在这里记录日志
                Console.WriteLine($"加载认证配置失败，使用默认配置: {ex.Message}");
            }
        }
        
        /// <summary>
        /// 获取配置信息摘要
        /// </summary>
        /// <returns>配置信息</returns>
        public string GetConfigurationSummary()
        {
            return $"自动登录检查: {_enableAutoLoginCheck}, 记住登录状态: {_rememberLoginState}, 超时提醒: {_loginTimeoutWarningMinutes}分钟";
        }

        /// <summary>
        /// 保存记住的密码凭据
        /// </summary>
        /// <param name="username">用户名</param>
        /// <param name="password">密码</param>
        /// <param name="rememberPassword">是否记住密码</param>
        public void SaveRememberedCredentials(string username, string password, bool rememberPassword)
        {
            try
            {
                var appDataPath = Environment.GetFolderPath(Environment.SpecialFolder.LocalApplicationData);
                var appFolder = Path.Combine(appDataPath, "RESClient");
                Directory.CreateDirectory(appFolder);

                var credentialsPath = Path.Combine(appFolder, "remembered_credentials.json");

                if (rememberPassword && !string.IsNullOrEmpty(username) && !string.IsNullOrEmpty(password))
                {
                    var credentials = new
                    {
                        Username = username,
                        EncryptedPassword = EncryptPassword(password),
                        RememberPassword = true,
                        SavedAt = DateTime.UtcNow
                    };

                    var json = JsonConvert.SerializeObject(credentials, Formatting.Indented);
                    File.WriteAllText(credentialsPath, json);
                }
                else
                {
                    // 清除已保存的凭据
                    if (File.Exists(credentialsPath))
                    {
                        File.Delete(credentialsPath);
                    }
                }
            }
            catch (Exception ex)
            {
                // 记录错误但不抛出异常
                Console.WriteLine($"保存记住的凭据失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 加载记住的密码凭据
        /// </summary>
        /// <returns>包含用户名和密码的元组，如果没有保存的凭据则返回null</returns>
        public (string Username, string Password)? LoadRememberedCredentials()
        {
            try
            {
                var appDataPath = Environment.GetFolderPath(Environment.SpecialFolder.LocalApplicationData);
                var credentialsPath = Path.Combine(appDataPath, "RESClient", "remembered_credentials.json");

                if (!File.Exists(credentialsPath))
                    return null;

                var json = File.ReadAllText(credentialsPath);
                var credentials = JsonConvert.DeserializeAnonymousType(json, new
                {
                    Username = "",
                    EncryptedPassword = "",
                    RememberPassword = false,
                    SavedAt = default(DateTime)
                });

                if (credentials != null && credentials.RememberPassword && !string.IsNullOrEmpty(credentials.Username))
                {
                    var decryptedPassword = DecryptPassword(credentials.EncryptedPassword);
                    return (credentials.Username, decryptedPassword);
                }
            }
            catch (Exception ex)
            {
                // 记录错误但不抛出异常
                Console.WriteLine($"加载记住的凭据失败: {ex.Message}");
            }

            return null;
        }

        /// <summary>
        /// 清除记住的密码凭据
        /// </summary>
        public void ClearRememberedCredentials()
        {
            try
            {
                var appDataPath = Environment.GetFolderPath(Environment.SpecialFolder.LocalApplicationData);
                var credentialsPath = Path.Combine(appDataPath, "RESClient", "remembered_credentials.json");

                if (File.Exists(credentialsPath))
                {
                    File.Delete(credentialsPath);
                }
            }
            catch (Exception ex)
            {
                // 记录错误但不抛出异常
                Console.WriteLine($"清除记住的凭据失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 加密密码
        /// </summary>
        /// <param name="password">明文密码</param>
        /// <returns>加密后的密码</returns>
        private string EncryptPassword(string password)
        {
            try
            {
                // 使用DPAPI进行加密，只有当前用户可以解密
                var data = Encoding.UTF8.GetBytes(password);
                var encryptedData = ProtectedData.Protect(data, null, DataProtectionScope.CurrentUser);
                return Convert.ToBase64String(encryptedData);
            }
            catch
            {
                // 如果加密失败，返回空字符串
                return string.Empty;
            }
        }

        /// <summary>
        /// 解密密码
        /// </summary>
        /// <param name="encryptedPassword">加密的密码</param>
        /// <returns>明文密码</returns>
        private string DecryptPassword(string encryptedPassword)
        {
            try
            {
                if (string.IsNullOrEmpty(encryptedPassword))
                    return string.Empty;

                var encryptedData = Convert.FromBase64String(encryptedPassword);
                var data = ProtectedData.Unprotect(encryptedData, null, DataProtectionScope.CurrentUser);
                return Encoding.UTF8.GetString(data);
            }
            catch
            {
                // 如果解密失败，返回空字符串
                return string.Empty;
            }
        }
    }
}
