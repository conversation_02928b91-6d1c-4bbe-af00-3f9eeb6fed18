using RESCADServerPlugin.Services;
using System;
using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;
using System.Net;
using System.Text;
using System.Text.Json;

namespace RESCADServerPlugin.Controllers
{
    public class SinglePrintDrawingController
    {
        public async Task ExportSinglePrintDrawing(HttpListenerContext context)
        {
            if (context.Request.HttpMethod != "POST")
            {
                SendErrorResponse(context, 405, "Method Not Allowed");
                return;
            }

            try
            {
                // 读取请求体
                string requestBody;
                using (var reader = new StreamReader(context.Request.InputStream, Encoding.UTF8))
                {
                    requestBody = await reader.ReadToEndAsync();
                }

                // 反序列化请求
                SinglePrintDrawingExportRequest? request = JsonSerializer.Deserialize<SinglePrintDrawingExportRequest>(requestBody, 
                    new JsonSerializerOptions { PropertyNameCaseInsensitive = true });

                if (request == null || string.IsNullOrEmpty(request.FilePath) || !File.Exists(request.FilePath))
                {
                    SendErrorResponse(context, 400, "Invalid request. File path is required and must exist.");
                    return;
                }

                // 获取已处理的图号（如果有提供）
                HashSet<string> processedDrawingNumbers = request.ProcessedDrawingNumbers != null 
                    ? new HashSet<string>(request.ProcessedDrawingNumbers) 
                    : new HashSet<string>();

                // 导出单个打印图
                var result = await SinglePrintDrawingExportService.Instance.ExportSinglePrintDrawing(
                    request.FilePath, 
                    processedDrawingNumbers
                );

                if (result.DrawingFilePath != null)
                {
                    // 构造返回结果
                    var exportResponse = new SinglePrintDrawingExportResponse
                    {
                        DrawingNumber = result.DrawingNumber,
                        CurrentIndex = result.CurrentIndex,
                        TotalCount = result.TotalCount,
                        HasMoreDrawings = result.HasMoreDrawings,
                        AllDrawingNumbers = result.AllDrawingNumbers,
                        FormatInfo = $"{result.CurrentIndex}/{result.TotalCount}"
                    };

                    // 将响应信息序列化为JSON并添加到响应头
                    string responseJson = System.Text.Json.JsonSerializer.Serialize(exportResponse);
                    context.Response.Headers.Add("X-Export-Info", Convert.ToBase64String(Encoding.UTF8.GetBytes(responseJson)));

                    // 准备返回文件
                    FileStream fileStream = new FileStream(result.DrawingFilePath, FileMode.Open, FileAccess.Read);
                    byte[] buffer = new byte[fileStream.Length];
                    await fileStream.ReadAsync(buffer, 0, buffer.Length);
                    fileStream.Close();

                    // 发送文件
                    context.Response.ContentType = "application/acad";
                    context.Response.ContentLength64 = buffer.Length;
                    context.Response.StatusCode = 200;
                    context.Response.Headers.Add("Content-Disposition", $"attachment; filename=\"PrintDrawing_{result.DrawingNumber.Replace('/', '-')}.dwg\"");
                    context.Response.OutputStream.Write(buffer, 0, buffer.Length);
                    context.Response.OutputStream.Close();
                }
                else if (result.HasMoreDrawings == false)
                {
                    var notFoundResponse = new
                    {
                        Message = "No more print drawings to export",
                        AllDrawingNumbers = result.AllDrawingNumbers
                    };
                    
                    string json = JsonSerializer.Serialize(notFoundResponse);
                    byte[] buffer = Encoding.UTF8.GetBytes(json);
                    
                    context.Response.StatusCode = 404;
                    context.Response.ContentType = "application/json";
                    context.Response.ContentLength64 = buffer.Length;
                    context.Response.OutputStream.Write(buffer, 0, buffer.Length);
                    context.Response.OutputStream.Close();
                }
                else
                {
                    SendErrorResponse(context, 500, "Failed to export print drawing");
                }
            }
            catch (Exception ex)
            {
                SendErrorResponse(context, 500, $"Error exporting print drawing: {ex.Message}");
            }
        }

        public void CheckQueue(HttpListenerContext context)
        {
            try
            {
                int queueLength = SinglePrintDrawingExportService.Instance.GetQueueLength();
                
                var response = new
                {
                    QueueLength = queueLength
                };
                
                string json = JsonSerializer.Serialize(response);
                byte[] buffer = Encoding.UTF8.GetBytes(json);
                
                context.Response.StatusCode = 200;
                context.Response.ContentType = "application/json";
                context.Response.ContentLength64 = buffer.Length;
                context.Response.OutputStream.Write(buffer, 0, buffer.Length);
                context.Response.OutputStream.Close();
            }
            catch (Exception ex)
            {
                SendErrorResponse(context, 500, $"Error checking queue: {ex.Message}");
            }
        }

        private void SendErrorResponse(HttpListenerContext context, int statusCode, string message)
        {
            var errorResponse = new
            {
                Error = true,
                Message = message
            };
            
            string json = JsonSerializer.Serialize(errorResponse);
            byte[] buffer = Encoding.UTF8.GetBytes(json);
            
            context.Response.StatusCode = statusCode;
            context.Response.ContentType = "application/json";
            context.Response.ContentLength64 = buffer.Length;
            context.Response.OutputStream.Write(buffer, 0, buffer.Length);
            context.Response.OutputStream.Close();
        }
    }

    // 请求模型
    public class SinglePrintDrawingExportRequest
    {
        public string FilePath { get; set; }
        public List<string> ProcessedDrawingNumbers { get; set; }
    }

    // 响应模型
    public class SinglePrintDrawingExportResponse
    {
        public string DrawingNumber { get; set; }
        public int CurrentIndex { get; set; }
        public int TotalCount { get; set; }
        public bool HasMoreDrawings { get; set; }
        public List<string> AllDrawingNumbers { get; set; }
        public string FormatInfo { get; set; }
    }

    public class PrintDrawingController
    {
        private readonly PrintDrawingApiService _printDrawingService;
        
        public PrintDrawingController()
        {
            // Use the singleton instance instead of creating a new one
            _printDrawingService = PrintDrawingApiService.Instance;
            
            // Start cleaning up old temporary files and progress data
            Task.Run(async () =>
            {
                while (true)
                {
                    _printDrawingService.CleanupTempFiles(TimeSpan.FromHours(24));
                    _printDrawingService.CleanupOldProgressData(TimeSpan.FromHours(24));
                    await Task.Delay(TimeSpan.FromHours(1));
                }
            });
        }
        
        /// <summary>
        /// Handles the HTTP request to process a DWG file and convert print drawings to GIF
        /// </summary>
        public async Task HandleProcessDwgRequest(HttpListenerContext context)
        {
            try
            {
                // Check if the request is a POST with multipart/form-data
                if (!context.Request.HttpMethod.Equals("POST", StringComparison.OrdinalIgnoreCase) || 
                    !context.Request.ContentType.StartsWith("multipart/form-data", StringComparison.OrdinalIgnoreCase))
                {
                    SendErrorResponse(context.Response, 400, "Invalid request. Must be a POST request with multipart/form-data.");
                    return;
                }
                
                // Process the uploaded file
                string tempFilePath = await SaveUploadedFile(context.Request);
                if (string.IsNullOrEmpty(tempFilePath))
                {
                    SendErrorResponse(context.Response, 400, "No file uploaded or invalid file.");
                    return;
                }
                
                try
                {
                    // Get current active processing count
                    int activeCount = _printDrawingService.GetActiveProcessingCount();
                    int maxConcurrency = _printDrawingService.GetMaxConcurrency();

                    // Send an immediate response with processing status if at capacity
                    if (activeCount >= maxConcurrency)
                    {
                        SendJsonResponse(context.Response, 202, new
                        {
                            success = true,
                            message = $"系统正在处理 {activeCount} 个文件（最大并发数: {maxConcurrency}），您的文件将并行处理。",
                            activeProcessing = activeCount,
                            maxConcurrency = maxConcurrency,
                            estimatedWaitTime = "2-5 分钟"
                        });
                        // Continue processing in parallel even at capacity
                    }

                    // Process the DWG file and generate split DWG files (now in parallel)
                    string zipFilePath = await _printDrawingService.ProcessDwgFile(tempFilePath);

                    // Return the ZIP file to the client
                    SendFileResponse(context.Response, zipFilePath, "application/zip", "print_drawings.zip");
                }
                finally
                {
                    // Clean up the temporary uploaded file
                    try
                    {
                        if (File.Exists(tempFilePath))
                        {
                            File.Delete(tempFilePath);
                        }
                    }
                    catch { /* Ignore cleanup errors */ }
                }
            }
            catch (Exception ex)
            {
                SendErrorResponse(context.Response, 500, $"Error processing drawing: {ex.Message}");
            }
        }

        /// <summary>
        /// Handles DWG file processing with progress reporting
        /// </summary>
        public async void HandleProcessWithProgress(HttpListenerContext context)
        {
            try
            {
                // Generate a unique session ID
                string sessionId = Guid.NewGuid().ToString("N");

                // Parse the uploaded file
                string tempFilePath = null;
                try
                {
                    tempFilePath = await SaveUploadedFile(context.Request);

                    // Start processing asynchronously and return session ID immediately
                    _ = Task.Run(async () =>
                    {
                        try
                        {
                            string zipFilePath = await _printDrawingService.ProcessDwgFileWithProgress(tempFilePath, sessionId);
                            // File will be available for download via progress endpoint
                        }
                        catch (Exception ex)
                        {
                            var progressReporter = _printDrawingService.GetProgressReporter();
                            progressReporter.ReportError(sessionId, ex.Message);
                        }
                        finally
                        {
                            // Clean up the temporary uploaded file
                            try
                            {
                                if (File.Exists(tempFilePath))
                                {
                                    File.Delete(tempFilePath);
                                }
                            }
                            catch { /* Ignore cleanup errors */ }
                        }
                    });

                    // Return session ID immediately
                    SendJsonResponse(context.Response, 200, new
                    {
                        success = true,
                        sessionId = sessionId,
                        message = "Processing started. Use the session ID to check progress."
                    });
                }
                catch (Exception ex)
                {
                    // Clean up the temporary uploaded file
                    try
                    {
                        if (File.Exists(tempFilePath))
                        {
                            File.Delete(tempFilePath);
                        }
                    }
                    catch { /* Ignore cleanup errors */ }

                    throw; // Re-throw to outer catch
                }
            }
            catch (Exception ex)
            {
                SendErrorResponse(context.Response, 500, $"Error processing drawing: {ex.Message}");
            }
        }

        /// <summary>
        /// Gets the status of the parallel processing system
        /// </summary>
        public void HandleGetQueueStatus(HttpListenerContext context)
        {
            try
            {
                int activeCount = _printDrawingService.GetActiveProcessingCount();
                int maxConcurrency = _printDrawingService.GetMaxConcurrency();

                SendJsonResponse(context.Response, 200, new
                {
                    success = true,
                    queueLength = activeCount, // For backward compatibility with existing clients
                    activeProcessing = activeCount,
                    maxConcurrency = maxConcurrency,
                    isProcessing = activeCount > 0,
                    estimatedWaitTime = activeCount > 0 ? "2-5 分钟" : "无需等待",
                    processingMode = "parallel"
                });
            }
            catch (Exception ex)
            {
                SendErrorResponse(context.Response, 500, $"Error getting processing status: {ex.Message}");
            }
        }

        /// <summary>
        /// Gets the progress of a specific processing session
        /// </summary>
        public void HandleGetProgress(HttpListenerContext context)
        {
            try
            {
                string sessionId = context.Request.QueryString["sessionId"];
                if (string.IsNullOrEmpty(sessionId))
                {
                    SendErrorResponse(context.Response, 400, "Session ID is required");
                    return;
                }

                var progressReporter = _printDrawingService.GetProgressReporter();
                var progress = progressReporter.GetProgress(sessionId);

                if (progress == null)
                {
                    SendJsonResponse(context.Response, 200, new
                    {
                        success = false,
                        message = "Session not found or expired"
                    });
                    return;
                }

                SendJsonResponse(context.Response, 200, new
                {
                    success = true,
                    sessionId = progress.SessionId,
                    percentComplete = progress.PercentComplete,
                    currentOperation = progress.CurrentOperation,
                    currentFile = progress.CurrentFile,
                    processedCount = progress.ProcessedCount,
                    totalCount = progress.TotalCount,
                    isCompleted = progress.IsCompleted,
                    hasError = progress.HasError,
                    errorMessage = progress.ErrorMessage,
                    lastUpdated = progress.LastUpdated,
                    processedFiles = progress.ProcessedFiles
                });
            }
            catch (Exception ex)
            {
                SendErrorResponse(context.Response, 500, $"Error getting progress: {ex.Message}");
            }
        }

        /// <summary>
        /// Handles download requests for completed processing sessions
        /// </summary>
        public void HandleDownload(HttpListenerContext context)
        {
            try
            {
                string sessionId = context.Request.QueryString["sessionId"];
                if (string.IsNullOrEmpty(sessionId))
                {
                    SendErrorResponse(context.Response, 400, "Session ID is required");
                    return;
                }

                var progressReporter = _printDrawingService.GetProgressReporter();
                var progress = progressReporter.GetProgress(sessionId);

                if (progress == null || !progress.IsCompleted || progress.HasError)
                {
                    SendErrorResponse(context.Response, 404, "Session not found, not completed, or has errors");
                    return;
                }

                // Find the ZIP file in the temp directory
                string tempDir = Path.Combine(Path.GetDirectoryName(System.Reflection.Assembly.GetExecutingAssembly().Location) ?? Path.GetTempPath(), "TempDwgFiles");
                string sessionDir = Path.Combine(tempDir, sessionId);
                string zipFilePath = Path.Combine(sessionDir, "print_drawings.zip");

                if (!File.Exists(zipFilePath))
                {
                    SendErrorResponse(context.Response, 404, "Result file not found");
                    return;
                }

                // Send the ZIP file
                SendFileResponse(context.Response, zipFilePath, "application/zip", "print_drawings.zip");
            }
            catch (Exception ex)
            {
                SendErrorResponse(context.Response, 500, $"Error downloading file: {ex.Message}");
            }
        }

        /// <summary>
        /// Sends a JSON response to the client
        /// </summary>
        private void SendJsonResponse(HttpListenerResponse response, int statusCode, object data)
        {
            response.StatusCode = statusCode;
            response.ContentType = "application/json; charset=utf-8";
            
            byte[] buffer = Encoding.UTF8.GetBytes(JsonSerializer.Serialize(data, new JsonSerializerOptions 
            { 
                WriteIndented = false,
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase
            }));
            
            response.ContentLength64 = buffer.Length;
            response.OutputStream.Write(buffer, 0, buffer.Length);
            response.OutputStream.Close();
        }
        
        /// <summary>
        /// Saves the uploaded file to a temporary location
        /// </summary>
        private async Task<string> SaveUploadedFile(HttpListenerRequest request)
        {
            string tempDir = Path.Combine(Path.GetTempPath(), "RESCADServerPlugin");
            if (!Directory.Exists(tempDir))
            {
                Directory.CreateDirectory(tempDir);
            }
            
            string boundary = GetBoundary(request.ContentType);
            if (string.IsNullOrEmpty(boundary))
            {
                return null;
            }
            
            // Convert boundary to byte array once
            byte[] boundaryBytes = Encoding.ASCII.GetBytes($"--{boundary}\r\n");
            byte[] endBoundaryBytes = Encoding.ASCII.GetBytes($"--{boundary}--\r\n");
            
            using (var ms = new MemoryStream())
            {
                // Copy the request stream to a memory stream
                await request.InputStream.CopyToAsync(ms);
                ms.Position = 0;
                
                // Read the memory stream
                byte[] data = ms.ToArray();
                
                // Find the file content start
                int fileStart = FindBytePattern(data, boundaryBytes);
                if (fileStart < 0)
                {
                    return null;
                }
                
                // Move past boundary
                fileStart += boundaryBytes.Length;
                
                // Find the file data start (after headers)
                int headerEnd = FindBytePattern(data, new byte[] { 13, 10, 13, 10 }, fileStart); // \r\n\r\n
                if (headerEnd < 0)
                {
                    return null;
                }
                
                // Extract headers
                string headers = Encoding.UTF8.GetString(data, fileStart, headerEnd - fileStart);
                
                // Parse Content-Disposition header to get filename
                string fileName = ParseFileName(headers);
                if (string.IsNullOrEmpty(fileName))
                {
                    fileName = $"upload_{Guid.NewGuid()}.dwg";
                }
                else if (!fileName.EndsWith(".dwg", StringComparison.OrdinalIgnoreCase))
                {
                    fileName += ".dwg";
                }
                
                // Move past headers to file content
                int fileContentStart = headerEnd + 4; // +4 for \r\n\r\n
                
                // Find the end boundary
                int fileEnd = FindBytePattern(data, endBoundaryBytes, fileContentStart);
                if (fileEnd < 0)
                {
                    // Try normal boundary
                    fileEnd = FindBytePattern(data, boundaryBytes, fileContentStart);
                    if (fileEnd < 0)
                    {
                        return null;
                    }
                }
                
                // Extract file content
                int fileLength = fileEnd - fileContentStart - 2; // -2 for \r\n before boundary
                
                // Save to temp file
                string tempFilePath = Path.Combine(tempDir, fileName);
                using (FileStream fs = new FileStream(tempFilePath, FileMode.Create))
                {
                    fs.Write(data, fileContentStart, fileLength);
                }
                
                return tempFilePath;
            }
        }
        
        /// <summary>
        /// Gets the multipart boundary from the Content-Type header
        /// </summary>
        private string GetBoundary(string contentType)
        {
            if (string.IsNullOrEmpty(contentType))
            {
                return null;
            }
            
            // Content-Type: multipart/form-data; boundary=----WebKitFormBoundaryXXXXX
            int index = contentType.IndexOf("boundary=");
            if (index < 0)
            {
                return null;
            }
            
            return contentType.Substring(index + 9);
        }
        
        /// <summary>
        /// Parses the filename from the Content-Disposition header
        /// </summary>
        private string ParseFileName(string headers)
        {
            // Content-Disposition: form-data; name="file"; filename="example.dwg"
            int index = headers.IndexOf("filename=\"");
            if (index < 0)
            {
                return null;
            }
            
            int startIndex = index + 10; // +10 for filename="
            int endIndex = headers.IndexOf("\"", startIndex);
            if (endIndex < 0)
            {
                return null;
            }
            
            return headers.Substring(startIndex, endIndex - startIndex);
        }
        
        /// <summary>
        /// Finds a byte pattern in a byte array
        /// </summary>
        private int FindBytePattern(byte[] source, byte[] pattern, int startIndex = 0)
        {
            for (int i = startIndex; i <= source.Length - pattern.Length; i++)
            {
                bool found = true;
                for (int j = 0; j < pattern.Length; j++)
                {
                    if (source[i + j] != pattern[j])
                    {
                        found = false;
                        break;
                    }
                }
                
                if (found)
                {
                    return i;
                }
            }
            
            return -1;
        }
        
        /// <summary>
        /// Sends an error response to the client
        /// </summary>
        private void SendErrorResponse(HttpListenerResponse response, int statusCode, string message)
        {
            response.StatusCode = statusCode;
            response.ContentType = "application/json";
            
            byte[] buffer = Encoding.UTF8.GetBytes(JsonSerializer.Serialize(new
            {
                success = false,
                message = message
            }));
            
            response.ContentLength64 = buffer.Length;
            response.OutputStream.Write(buffer, 0, buffer.Length);
            response.OutputStream.Close();
        }
        
        /// <summary>
        /// Sends a file as the response to the client
        /// </summary>
        private void SendFileResponse(HttpListenerResponse response, string filePath, string contentType, string fileName)
        {
            response.StatusCode = 200;
            response.ContentType = contentType;
            response.AddHeader("Content-Disposition", $"attachment; filename=\"{fileName}\"");
            
            using (FileStream fs = new FileStream(filePath, FileMode.Open, FileAccess.Read))
            {
                response.ContentLength64 = fs.Length;
                byte[] buffer = new byte[64 * 1024]; // 64KB buffer
                int read;
                
                while ((read = fs.Read(buffer, 0, buffer.Length)) > 0)
                {
                    response.OutputStream.Write(buffer, 0, read);
                }
            }
            
            response.OutputStream.Close();
        }
    }
} 