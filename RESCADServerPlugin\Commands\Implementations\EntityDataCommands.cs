using System;
using System.Collections.Generic;
using Autodesk.AutoCAD.ApplicationServices;
using Autodesk.AutoCAD.DatabaseServices;
using Autodesk.AutoCAD.EditorInput;
using Autodesk.AutoCAD.Geometry;

namespace RESCADServerPlugin.Commands.Implementations
{
    /// <summary>
    /// 实体数据命令实现类
    /// </summary>
    public class EntityDataCommands : CommandBase
    {
        /// <summary>
        /// 执行获取实体扩展属性数据命令
        /// </summary>
        public void ExecuteGetEntityExtensionData()
        {
            Document doc = GetActiveDocument();
            if (doc == null) return;

            Editor ed = GetEditor();
            Database db = GetDatabase();

            // 提示用户选择一个实体
            PromptEntityOptions options = new PromptEntityOptions("\n选择一个实体查看其扩展属性数据: ");
            PromptEntityResult result = ed.GetEntity(options);

            if (result.Status != PromptStatus.OK)
            {
                WriteMessage("\n操作已取消。");
                return;
            }

            // 获取选定的实体
            ObjectId entityId = result.ObjectId;

            // 在事务中处理扩展数据
            using (Transaction trans = db.TransactionManager.StartTransaction())
            {
                try
                {
                    Entity entity = trans.GetObject(entityId, OpenMode.ForRead) as Entity;
                    if (entity == null)
                    {
                        WriteMessage("\n无法获取所选实体。");
                        return;
                    }

                    WriteMessage($"\n实体类型: {entity.GetType().Name}, 句柄: {entity.Handle}");

                    bool hasAnyData = false;

                    // 首先检查XData（扩展数据）- 这是最常见的数据存储方式
                    WriteMessage("\n\n=== 检查XData（扩展数据） ===");
                    using (ResultBuffer rb = entity.XData)
                    {
                        if (rb != null)
                        {
                            hasAnyData = true;
                            TypedValue[] values = rb.AsArray();

                            // 当前应用程序名称
                            string currentAppName = "";

                            for (int i = 0; i < values.Length; i++)
                            {
                                TypedValue tv = values[i];

                                if (tv.TypeCode == 1001) // Application name
                                {
                                    currentAppName = tv.Value.ToString();
                                    WriteMessage($"\n应用程序: {currentAppName}");
                                }
                                else
                                {
                                    WriteMessage($"\n  [XData] 类型={tv.TypeCode}, 值={tv.Value}");
                                }
                            }
                        }
                        else
                        {
                            WriteMessage("\n该实体没有XData。");
                        }
                    }

                    // 然后检查扩展字典
                    WriteMessage("\n\n=== 检查扩展字典（Extension Dictionary） ===");
                    if (entity.ExtensionDictionary.IsValid)
                    {
                        hasAnyData = true;
                        // 获取扩展字典
                        DBDictionary extDict = trans.GetObject(entity.ExtensionDictionary, OpenMode.ForRead) as DBDictionary;
                        if (extDict == null)
                        {
                            WriteMessage("\n无法获取扩展字典。");
                        }
                        else
                        {
                            // 遍历扩展字典中的所有条目
                            int entryCount = 0;
                            foreach (DBDictionaryEntry entry in extDict)
                            {
                                entryCount++;
                                WriteMessage($"\n[{entryCount}] 键: {entry.Key}");

                                // 获取条目的值对象
                                DBObject entryObj = trans.GetObject(entry.Value, OpenMode.ForRead);

                                // 处理不同类型的扩展数据
                                if (entryObj is Xrecord xrec)
                                {
                                    // 处理 Xrecord 类型的扩展数据
                                    WriteMessage($" (Xrecord)");
                                    TypedValue[] values = xrec.Data.AsArray();

                                    for (int i = 0; i < values.Length; i++)
                                    {
                                        WriteMessage($"\n    值[{i}]: 类型={values[i].TypeCode}, 值={values[i].Value}");
                                    }
                                }
                                else
                                {
                                    // 其他类型的扩展数据
                                    WriteMessage($" (类型: {entryObj.GetType().Name})");
                                }
                            }

                            if (entryCount == 0)
                            {
                                WriteMessage("\n扩展字典中没有条目。");
                            }
                        }
                    }
                    else
                    {
                        WriteMessage("\n该实体没有扩展字典。");
                    }

                    if (!hasAnyData)
                    {
                        WriteMessage("\n\n该实体没有任何扩展属性数据（既无XData也无扩展字典）。");
                        return;
                    }



                    // 尝试获取所有注册的应用程序XData
                    WriteMessage("\n\n尝试查找已注册应用的XData:");
                    RegAppTable rat = trans.GetObject(db.RegAppTableId, OpenMode.ForRead) as RegAppTable;
                    if (rat != null)
                    {
                        bool foundAny = false;
                        foreach (ObjectId regAppId in rat)
                        {
                            RegAppTableRecord regApp = trans.GetObject(regAppId, OpenMode.ForRead) as RegAppTableRecord;
                            if (regApp != null)
                            {
                                string appName = regApp.Name;
                                using (ResultBuffer appXData = entity.GetXDataForApplication(appName))
                                {
                                    if (appXData != null)
                                    {
                                        foundAny = true;
                                        WriteMessage($"\n应用程序: {appName}");
                                        TypedValue[] appDataArray = appXData.AsArray();
                                        for (int i = 1; i < appDataArray.Length; i++) // Skip the app name (first element)
                                        {
                                            WriteMessage($"\n  [XData] 类型={appDataArray[i].TypeCode}, 值={appDataArray[i].Value}");
                                        }
                                    }
                                }
                            }
                        }
                        if (!foundAny)
                        {
                            WriteMessage("\n没有找到注册应用程序的XData。");
                        }
                    }
                }
                catch (System.Exception ex)
                {
                    WriteMessage($"\n获取扩展属性时出错: {ex.Message}");
                }
                finally
                {
                    trans.Commit();
                }
            }

            WriteMessage("\n扩展属性查询完成。");
        }

        /// <summary>
        /// 执行获取实体Xrecord扩展记录命令
        /// </summary>
        public void ExecuteGetEntityXrecord()
        {
            Document doc = GetActiveDocument();
            if (doc == null) return;

            Editor ed = GetEditor();
            Database db = GetDatabase();

            // 提示用户选择一个实体
            PromptEntityOptions options = new PromptEntityOptions("\n选择一个实体查看其Xrecord扩展记录: ");
            PromptEntityResult result = ed.GetEntity(options);

            if (result.Status != PromptStatus.OK)
            {
                WriteMessage("\n操作已取消。");
                return;
            }

            // 获取选定的实体
            ObjectId entityId = result.ObjectId;

            // 在事务中处理Xrecord扩展记录
            using (Transaction trans = db.TransactionManager.StartTransaction())
            {
                try
                {
                    Entity entity = trans.GetObject(entityId, OpenMode.ForRead) as Entity;
                    if (entity == null)
                    {
                        WriteMessage("\n无法获取所选实体。");
                        return;
                    }

                    WriteMessage($"\n实体类型: {entity.GetType().Name}, 句柄: {entity.Handle}");

                    // 检查实体是否有扩展字典
                    if (!entity.ExtensionDictionary.IsValid)
                    {
                        WriteMessage("\n该实体没有扩展字典，无法获取Xrecord数据。");
                        return;
                    }

                    // 获取扩展字典
                    DBDictionary extDict = trans.GetObject(entity.ExtensionDictionary, OpenMode.ForRead) as DBDictionary;
                    if (extDict == null)
                    {
                        WriteMessage("\n无法获取扩展字典。");
                        return;
                    }

                    // 遍历扩展字典中的所有条目，专注于Xrecord
                    int xrecordCount = 0;
                    foreach (DBDictionaryEntry entry in extDict)
                    {
                        // 获取条目的值对象
                        DBObject entryObj = trans.GetObject(entry.Value, OpenMode.ForRead);
                        
                        // 只处理 Xrecord 类型的扩展数据
                        if (entryObj is Xrecord xrec)
                        {
                            xrecordCount++;
                            WriteMessage($"\n\n[Xrecord {xrecordCount}] 键: {entry.Key}");
                            
                            TypedValue[] values = xrec.Data.AsArray();
                            
                            if (values.Length == 0)
                            {
                                WriteMessage("\n  Xrecord不包含任何数据。");
                                continue;
                            }
                            
                            for (int i = 0; i < values.Length; i++)
                            {
                                string typeName = GetTypeName(values[i].TypeCode);
                                WriteMessage($"\n  项[{i}]: 类型={values[i].TypeCode} ({typeName}), 值={values[i].Value}");
                            }
                        }
                    }

                    if (xrecordCount == 0)
                    {
                        WriteMessage("\n该实体没有Xrecord扩展记录。");
                    }
                    else
                    {
                        WriteMessage($"\n\n共找到 {xrecordCount} 个Xrecord扩展记录。");
                    }
                }
                catch (System.Exception ex)
                {
                    WriteMessage($"\n获取Xrecord数据时出错: {ex.Message}");
                }
                finally
                {
                    trans.Commit();
                }
            }

            WriteMessage("\nXrecord查询完成。");
        }

        /// <summary>
        /// 执行获取块参照内所有实体扩展属性的命令
        /// </summary>
        public void ExecuteGetBlockReferenceEntitiesXData()
        {
            Document doc = GetActiveDocument();
            if (doc == null) return;

            Editor ed = GetEditor();
            Database db = GetDatabase();

            // 提示用户选择一个或多个块参照
            PromptSelectionOptions selOptions = new PromptSelectionOptions();
            selOptions.MessageForAdding = "\n选择一个或多个块参照对象: ";
            selOptions.AllowDuplicates = false;

            // 创建过滤器，只允许选择块参照
            TypedValue[] filterList = new TypedValue[]
            {
                new TypedValue((int)DxfCode.Start, "INSERT")
            };
            SelectionFilter filter = new SelectionFilter(filterList);

            PromptSelectionResult selResult = ed.GetSelection(selOptions, filter);

            if (selResult.Status != PromptStatus.OK)
            {
                WriteMessage("\n操作已取消。");
                return;
            }

            ObjectId[] selectedIds = selResult.Value.GetObjectIds();
            WriteMessage($"\n已选择 {selectedIds.Length} 个块参照对象。");

            // 在事务中处理块参照
            using (Transaction trans = db.TransactionManager.StartTransaction())
            {
                try
                {
                    int processedBlocks = 0;
                    int totalEntitiesProcessed = 0;

                    foreach (ObjectId blockRefId in selectedIds)
                    {
                        BlockReference blockRef = trans.GetObject(blockRefId, OpenMode.ForRead) as BlockReference;
                        if (blockRef == null)
                        {
                            WriteMessage($"\n跳过无效的块参照对象。");
                            continue;
                        }

                        processedBlocks++;
                        WriteMessage($"\n\n=== 块参照 {processedBlocks}: {blockRef.Name} (句柄: {blockRef.Handle}) ===");

                        // 获取块定义
                        BlockTableRecord blockDef = trans.GetObject(blockRef.BlockTableRecord, OpenMode.ForRead) as BlockTableRecord;
                        if (blockDef == null)
                        {
                            WriteMessage("\n无法获取块定义。");
                            continue;
                        }

                        WriteMessage($"\n块定义名称: {blockDef.Name}");
                        WriteMessage($"\n块参照位置: X={blockRef.Position.X:F3}, Y={blockRef.Position.Y:F3}, Z={blockRef.Position.Z:F3}");

                        // 首先检查块参照本身的扩展属性数据
                        WriteMessage($"\n\n--- 检查块参照本身的扩展属性数据 ---");
                        bool blockRefHasXData = ProcessEntityXData(blockRef, 0, trans);
                        if (!blockRefHasXData)
                        {
                            WriteMessage($"\n  块参照本身无扩展属性数据。");
                        }

                        // 然后遍历块定义中的所有实体
                        int entityCount = 0;
                        int entitiesWithXData = 0;

                        foreach (ObjectId entityId in blockDef)
                        {
                            Entity entity = trans.GetObject(entityId, OpenMode.ForRead) as Entity;
                            if (entity == null) continue;

                            entityCount++;
                            bool hasXData = ProcessEntityXData(entity, entityCount, trans);
                            if (hasXData)
                            {
                                entitiesWithXData++;
                            }
                        }

                        totalEntitiesProcessed += entityCount;
                        WriteMessage($"\n块 '{blockRef.Name}' 统计: 共 {entityCount} 个实体，其中 {entitiesWithXData} 个有扩展属性数据。");

                        // 处理嵌套块参照
                        ProcessNestedBlockReferences(blockDef, trans, 1);
                    }

                    WriteMessage($"\n\n=== 处理完成 ===");
                    WriteMessage($"\n共处理 {processedBlocks} 个块参照，总计 {totalEntitiesProcessed} 个实体。");
                }
                catch (System.Exception ex)
                {
                    WriteMessage($"\n处理块参照扩展属性时出错: {ex.Message}");
                }
                finally
                {
                    trans.Commit();
                }
            }

            WriteMessage("\n块参照扩展属性查询完成。");
        }

        /// <summary>
        /// 处理单个实体的扩展属性数据
        /// </summary>
        private bool ProcessEntityXData(Entity entity, int entityIndex, Transaction trans)
        {
            bool hasAnyXData = false;

            WriteMessage($"\n  实体[{entityIndex}]: {entity.GetType().Name} (句柄: {entity.Handle})");

            // 检查扩展字典
            if (entity.ExtensionDictionary.IsValid)
            {
                hasAnyXData = true;
                WriteMessage($"\n    扩展字典:");

                DBDictionary extDict = trans.GetObject(entity.ExtensionDictionary, OpenMode.ForRead) as DBDictionary;
                if (extDict != null)
                {
                    int entryCount = 0;
                    foreach (DBDictionaryEntry entry in extDict)
                    {
                        entryCount++;
                        WriteMessage($"\n      [{entryCount}] 键: {entry.Key}");

                        DBObject entryObj = trans.GetObject(entry.Value, OpenMode.ForRead);
                        if (entryObj is Xrecord xrec)
                        {
                            WriteMessage($" (Xrecord)");
                            TypedValue[] values = xrec.Data.AsArray();
                            for (int i = 0; i < values.Length; i++)
                            {
                                WriteMessage($"\n        值[{i}]: 类型={values[i].TypeCode}, 值={values[i].Value}");
                            }
                        }
                        else
                        {
                            WriteMessage($" (类型: {entryObj.GetType().Name})");
                        }
                    }

                    if (entryCount == 0)
                    {
                        WriteMessage($"\n      扩展字典为空。");
                    }
                }
            }

            // 检查XData
            using (ResultBuffer rb = entity.XData)
            {
                if (rb != null)
                {
                    hasAnyXData = true;
                    WriteMessage($"\n    XData:");

                    TypedValue[] values = rb.AsArray();
                    string currentAppName = "";

                    for (int i = 0; i < values.Length; i++)
                    {
                        TypedValue tv = values[i];

                        if (tv.TypeCode == 1001) // Application name
                        {
                            currentAppName = tv.Value.ToString();
                            WriteMessage($"\n      应用程序: {currentAppName}");
                        }
                        else
                        {
                            WriteMessage($"\n        类型={tv.TypeCode}, 值={tv.Value}");
                        }
                    }
                }
            }

            if (!hasAnyXData)
            {
                WriteMessage($"\n    (无扩展属性数据)");
            }

            return hasAnyXData;
        }

        /// <summary>
        /// 处理嵌套块参照
        /// </summary>
        private void ProcessNestedBlockReferences(BlockTableRecord blockDef, Transaction trans, int nestLevel)
        {
            if (nestLevel > 5) // 防止无限递归
            {
                WriteMessage($"\n    警告: 嵌套层级过深 (>{nestLevel})，停止递归处理。");
                return;
            }

            string indent = new string(' ', nestLevel * 4);

            foreach (ObjectId entityId in blockDef)
            {
                Entity entity = trans.GetObject(entityId, OpenMode.ForRead) as Entity;
                if (entity is BlockReference nestedBlockRef)
                {
                    WriteMessage($"\n{indent}发现嵌套块参照: {nestedBlockRef.Name} (句柄: {nestedBlockRef.Handle})");

                    // 获取嵌套块的定义
                    BlockTableRecord nestedBlockDef = trans.GetObject(nestedBlockRef.BlockTableRecord, OpenMode.ForRead) as BlockTableRecord;
                    if (nestedBlockDef != null)
                    {
                        // 处理嵌套块中的实体
                        int nestedEntityCount = 0;
                        int nestedEntitiesWithXData = 0;

                        foreach (ObjectId nestedEntityId in nestedBlockDef)
                        {
                            Entity nestedEntity = trans.GetObject(nestedEntityId, OpenMode.ForRead) as Entity;
                            if (nestedEntity == null) continue;

                            nestedEntityCount++;
                            WriteMessage($"\n{indent}  嵌套实体[{nestedEntityCount}]: {nestedEntity.GetType().Name} (句柄: {nestedEntity.Handle})");

                            bool hasXData = ProcessNestedEntityXData(nestedEntity, trans, indent + "    ");
                            if (hasXData)
                            {
                                nestedEntitiesWithXData++;
                            }
                        }

                        WriteMessage($"\n{indent}嵌套块 '{nestedBlockRef.Name}' 统计: 共 {nestedEntityCount} 个实体，其中 {nestedEntitiesWithXData} 个有扩展属性数据。");

                        // 递归处理更深层的嵌套
                        ProcessNestedBlockReferences(nestedBlockDef, trans, nestLevel + 1);
                    }
                }
            }
        }

        /// <summary>
        /// 处理嵌套实体的扩展属性数据（简化版本）
        /// </summary>
        private bool ProcessNestedEntityXData(Entity entity, Transaction trans, string indent)
        {
            bool hasAnyXData = false;

            // 检查扩展字典
            if (entity.ExtensionDictionary.IsValid)
            {
                hasAnyXData = true;
                WriteMessage($"\n{indent}扩展字典: 存在");
            }

            // 检查XData
            using (ResultBuffer rb = entity.XData)
            {
                if (rb != null)
                {
                    hasAnyXData = true;
                    TypedValue[] values = rb.AsArray();
                    int appCount = 0;
                    for (int i = 0; i < values.Length; i++)
                    {
                        if (values[i].TypeCode == 1001) // Application name
                        {
                            appCount++;
                        }
                    }
                    WriteMessage($"\n{indent}XData: {appCount} 个应用程序");
                }
            }

            return hasAnyXData;
        }

        /// <summary>
        /// 执行获取块参照内所有实体扩展记录（Xrecord）的命令
        /// </summary>
        public void ExecuteGetBlockReferenceEntitiesXrecord()
        {
            Document doc = GetActiveDocument();
            if (doc == null) return;

            Editor ed = GetEditor();
            Database db = GetDatabase();

            // 提示用户选择一个或多个块参照
            PromptSelectionOptions selOptions = new PromptSelectionOptions();
            selOptions.MessageForAdding = "\n选择一个或多个块参照对象以查看其Xrecord扩展记录: ";
            selOptions.AllowDuplicates = false;

            // 创建过滤器，只允许选择块参照
            TypedValue[] filterList = new TypedValue[]
            {
                new TypedValue((int)DxfCode.Start, "INSERT")
            };
            SelectionFilter filter = new SelectionFilter(filterList);

            PromptSelectionResult selResult = ed.GetSelection(selOptions, filter);

            if (selResult.Status != PromptStatus.OK)
            {
                WriteMessage("\n操作已取消。");
                return;
            }

            ObjectId[] selectedIds = selResult.Value.GetObjectIds();
            WriteMessage($"\n已选择 {selectedIds.Length} 个块参照对象进行Xrecord分析。");

            // 在事务中处理块参照
            using (Transaction trans = db.TransactionManager.StartTransaction())
            {
                try
                {
                    int processedBlocks = 0;
                    int totalEntitiesProcessed = 0;
                    int totalXrecordsFound = 0;

                    foreach (ObjectId blockRefId in selectedIds)
                    {
                        BlockReference blockRef = trans.GetObject(blockRefId, OpenMode.ForRead) as BlockReference;
                        if (blockRef == null)
                        {
                            WriteMessage($"\n跳过无效的块参照对象。");
                            continue;
                        }

                        processedBlocks++;
                        WriteMessage($"\n\n=== 块参照 {processedBlocks}: {blockRef.Name} (句柄: {blockRef.Handle}) ===");

                        // 获取块定义
                        BlockTableRecord blockDef = trans.GetObject(blockRef.BlockTableRecord, OpenMode.ForRead) as BlockTableRecord;
                        if (blockDef == null)
                        {
                            WriteMessage("\n无法获取块定义。");
                            continue;
                        }

                        WriteMessage($"\n块定义名称: {blockDef.Name}");
                        WriteMessage($"\n块参照位置: X={blockRef.Position.X:F3}, Y={blockRef.Position.Y:F3}, Z={blockRef.Position.Z:F3}");

                        // 首先检查块参照本身的Xrecord数据
                        WriteMessage($"\n\n--- 检查块参照本身的Xrecord数据 ---");
                        int blockRefXrecords = ProcessEntityXrecords(blockRef, 0, trans);
                        if (blockRefXrecords > 0)
                        {
                            WriteMessage($"\n  块参照本身有 {blockRefXrecords} 个Xrecord。");
                            totalXrecordsFound += blockRefXrecords;
                        }
                        else
                        {
                            WriteMessage($"\n  块参照本身无Xrecord数据。");
                        }

                        // 遍历块定义中的所有实体
                        int entityCount = 0;
                        int entitiesWithXrecords = 0;
                        int blockXrecordCount = 0;

                        foreach (ObjectId entityId in blockDef)
                        {
                            Entity entity = trans.GetObject(entityId, OpenMode.ForRead) as Entity;
                            if (entity == null) continue;

                            entityCount++;
                            int entityXrecords = ProcessEntityXrecords(entity, entityCount, trans);
                            if (entityXrecords > 0)
                            {
                                entitiesWithXrecords++;
                                blockXrecordCount += entityXrecords;
                            }
                        }

                        totalEntitiesProcessed += entityCount;
                        totalXrecordsFound += blockXrecordCount;
                        WriteMessage($"\n块 '{blockRef.Name}' Xrecord统计: 共 {entityCount} 个实体，其中 {entitiesWithXrecords} 个有Xrecord数据，总计 {blockXrecordCount} 个Xrecord。");

                        // 处理嵌套块参照中的Xrecord
                        int nestedXrecords = ProcessNestedBlockXrecords(blockDef, trans, 1);
                        if (nestedXrecords > 0)
                        {
                            totalXrecordsFound += nestedXrecords;
                            WriteMessage($"\n嵌套块中发现额外 {nestedXrecords} 个Xrecord。");
                        }
                    }

                    WriteMessage($"\n\n=== Xrecord分析完成 ===");
                    WriteMessage($"\n共处理 {processedBlocks} 个块参照，总计 {totalEntitiesProcessed} 个实体。");
                    WriteMessage($"\n总共发现 {totalXrecordsFound} 个Xrecord扩展记录。");
                }
                catch (System.Exception ex)
                {
                    WriteMessage($"\n处理块参照Xrecord时出错: {ex.Message}");
                }
                finally
                {
                    trans.Commit();
                }
            }

            WriteMessage("\n块参照Xrecord扩展记录查询完成。");
        }

        /// <summary>
        /// 处理单个实体的Xrecord扩展记录
        /// </summary>
        private int ProcessEntityXrecords(Entity entity, int entityIndex, Transaction trans)
        {
            int xrecordCount = 0;

            WriteMessage($"\n  实体[{entityIndex}]: {entity.GetType().Name} (句柄: {entity.Handle})");

            // 检查扩展字典
            if (!entity.ExtensionDictionary.IsValid)
            {
                WriteMessage($"\n    (无扩展字典，无Xrecord数据)");
                return 0;
            }

            DBDictionary extDict = trans.GetObject(entity.ExtensionDictionary, OpenMode.ForRead) as DBDictionary;
            if (extDict == null)
            {
                WriteMessage($"\n    (无法访问扩展字典)");
                return 0;
            }

            // 遍历扩展字典，专门查找Xrecord
            bool hasXrecords = false;
            foreach (DBDictionaryEntry entry in extDict)
            {
                DBObject entryObj = trans.GetObject(entry.Value, OpenMode.ForRead);

                // 只处理 Xrecord 类型的扩展数据
                if (entryObj is Xrecord xrec)
                {
                    if (!hasXrecords)
                    {
                        WriteMessage($"\n    Xrecord扩展记录:");
                        hasXrecords = true;
                    }

                    xrecordCount++;
                    WriteMessage($"\n      [{xrecordCount}] 键名: \"{entry.Key}\"");

                    TypedValue[] values = xrec.Data.AsArray();

                    if (values.Length == 0)
                    {
                        WriteMessage($"\n          (空Xrecord，无数据)");
                        continue;
                    }

                    WriteMessage($"\n          数据项数量: {values.Length}");
                    for (int i = 0; i < values.Length; i++)
                    {
                        string typeName = GetTypeName(values[i].TypeCode);
                        string valueStr = FormatXrecordValue(values[i]);
                        WriteMessage($"\n          项[{i}]: 类型={values[i].TypeCode} ({typeName}), 值={valueStr}");
                    }
                }
            }

            if (!hasXrecords)
            {
                WriteMessage($"\n    (扩展字典中无Xrecord数据)");
            }

            return xrecordCount;
        }

        /// <summary>
        /// 处理嵌套块参照中的Xrecord数据
        /// </summary>
        private int ProcessNestedBlockXrecords(BlockTableRecord blockDef, Transaction trans, int nestLevel)
        {
            if (nestLevel > 5) // 防止无限递归
            {
                WriteMessage($"\n    警告: 嵌套层级过深 (>{nestLevel})，停止Xrecord递归处理。");
                return 0;
            }

            string indent = new string(' ', nestLevel * 4);
            int totalNestedXrecords = 0;

            foreach (ObjectId entityId in blockDef)
            {
                Entity entity = trans.GetObject(entityId, OpenMode.ForRead) as Entity;
                if (entity is BlockReference nestedBlockRef)
                {
                    WriteMessage($"\n{indent}发现嵌套块参照: {nestedBlockRef.Name} (句柄: {nestedBlockRef.Handle})");

                    // 获取嵌套块的定义
                    BlockTableRecord nestedBlockDef = trans.GetObject(nestedBlockRef.BlockTableRecord, OpenMode.ForRead) as BlockTableRecord;
                    if (nestedBlockDef != null)
                    {
                        // 处理嵌套块中的实体Xrecord
                        int nestedEntityCount = 0;
                        int nestedXrecordCount = 0;

                        foreach (ObjectId nestedEntityId in nestedBlockDef)
                        {
                            Entity nestedEntity = trans.GetObject(nestedEntityId, OpenMode.ForRead) as Entity;
                            if (nestedEntity == null) continue;

                            nestedEntityCount++;
                            int entityXrecords = ProcessNestedEntityXrecords(nestedEntity, nestedEntityCount, trans, indent + "  ");
                            nestedXrecordCount += entityXrecords;
                        }

                        totalNestedXrecords += nestedXrecordCount;
                        WriteMessage($"\n{indent}嵌套块 '{nestedBlockRef.Name}' Xrecord统计: 共 {nestedEntityCount} 个实体，发现 {nestedXrecordCount} 个Xrecord。");

                        // 递归处理更深层的嵌套
                        int deeperXrecords = ProcessNestedBlockXrecords(nestedBlockDef, trans, nestLevel + 1);
                        totalNestedXrecords += deeperXrecords;
                    }
                }
            }

            return totalNestedXrecords;
        }

        /// <summary>
        /// 处理嵌套实体的Xrecord数据（简化版本）
        /// </summary>
        private int ProcessNestedEntityXrecords(Entity entity, int entityIndex, Transaction trans, string indent)
        {
            int xrecordCount = 0;

            WriteMessage($"\n{indent}嵌套实体[{entityIndex}]: {entity.GetType().Name} (句柄: {entity.Handle})");

            // 检查扩展字典
            if (!entity.ExtensionDictionary.IsValid)
            {
                WriteMessage($"\n{indent}  (无Xrecord数据)");
                return 0;
            }

            DBDictionary extDict = trans.GetObject(entity.ExtensionDictionary, OpenMode.ForRead) as DBDictionary;
            if (extDict == null)
            {
                WriteMessage($"\n{indent}  (无法访问扩展字典)");
                return 0;
            }

            // 统计Xrecord数量和键名
            List<string> xrecordKeys = new List<string>();
            foreach (DBDictionaryEntry entry in extDict)
            {
                DBObject entryObj = trans.GetObject(entry.Value, OpenMode.ForRead);
                if (entryObj is Xrecord)
                {
                    xrecordCount++;
                    xrecordKeys.Add(entry.Key);
                }
            }

            if (xrecordCount > 0)
            {
                WriteMessage($"\n{indent}  Xrecord数量: {xrecordCount}");
                WriteMessage($"\n{indent}  键名: [{string.Join(", ", xrecordKeys)}]");
            }
            else
            {
                WriteMessage($"\n{indent}  (无Xrecord数据)");
            }

            return xrecordCount;
        }

        /// <summary>
        /// 格式化Xrecord值的显示
        /// </summary>
        private string FormatXrecordValue(TypedValue typedValue)
        {
            if (typedValue.Value == null)
                return "(null)";

            try
            {
                switch (typedValue.TypeCode)
                {
                    case 1000: // 字符串
                    case 1001: // 应用程序名称
                    case 1002: // 控制字符串
                    case 1003: // 层名
                        return $"\"{typedValue.Value}\"";

                    case 1004: // 二进制块
                        if (typedValue.Value is byte[] bytes)
                            return $"[二进制数据, {bytes.Length} 字节]";
                        break;

                    case 1005: // 句柄
                        return $"句柄:{typedValue.Value}";

                    case 1010: // XYZ点
                    case 1011: // XYZ位移
                    case 1012: // XYZ方向
                    case 1013: // XYZ中点
                        if (typedValue.Value is Point3d point)
                            return $"({point.X:F3}, {point.Y:F3}, {point.Z:F3})";
                        break;

                    case 1070: // 16位整数
                    case 1071: // 32位整数
                        return typedValue.Value.ToString();

                    case 40: // 实数
                        if (typedValue.Value is double doubleVal)
                            return doubleVal.ToString("F6");
                        break;

                    default:
                        return typedValue.Value.ToString();
                }
            }
            catch (Exception)
            {
                return $"(格式化错误: {typedValue.Value})";
            }

            return typedValue.Value.ToString();
        }

        /// <summary>
        /// 根据TypeCode获取类型名称的辅助方法
        /// </summary>
        private string GetTypeName(int typeCode)
        {
            switch (typeCode)
            {
                case 0: return "无类型";
                case 1: return "字符串";
                case 2: return "名称";
                case 3: return "布尔值";
                case 4: return "整数";
                case 5: return "实数";
                case 6: return "世界坐标";
                case 7: return "世界空间位置";
                case 8: return "世界定向";
                case 9: return "世界位移";
                case 10: return "二维坐标";
                case 11: return "位移";
                case 12: return "眺望";
                case 13: return "向量";
                case 16: return "主色标";
                case 17: return "RGB颜色";
                case 38: return "实体名称";
                case 39: return "绘图名称";
                case 40: return "点";
                case 41: return "位置";
                case 60: return "对象ID";
                case 62: return "整数8位";
                case 63: return "整数16位";
                case 70: return "整数16位";
                case 90: return "整数32位";
                case 160: return "特殊标识码";
                case 170: return "无符号16位";
                case 280: return "无符号8位";
                case 290: return "布尔值";
                case 330: return "对象ID";
                case 350: return "对象ID";
                case 360: return "对象ID";
                case 1000: return "字符串";
                case 1001: return "应用程序名称";
                case 1002: return "控制字符串";
                case 1003: return "层名";
                case 1004: return "二进制块";
                case 1005: return "句柄";
                case 1010: return "XYZ点";
                case 1011: return "XYZ位移";
                case 1012: return "XYZ方向";
                case 1013: return "XYZ中点";
                case 1070: return "16位整数";
                case 1071: return "32位整数";
                default: return "未知类型";
            }
        }

        /// <summary>
        /// 执行实体数据全面分析命令 - 基于DataDumper功能
        /// </summary>
        public void ExecuteDumpEntityData()
        {
            Document doc = GetActiveDocument();
            if (doc == null) return;

            Editor ed = GetEditor();
            Database db = GetDatabase();

            PromptEntityOptions peo = new PromptEntityOptions("\n请选择一个要分析的块参照: ");
            peo.SetRejectMessage("\n选择的不是实体，请重试。");
            PromptEntityResult per = ed.GetEntity(peo);

            if (per.Status != PromptStatus.OK) return;

            using (Transaction tr = db.TransactionManager.StartTransaction())
            {
                try
                {
                    Entity ent = tr.GetObject(per.ObjectId, OpenMode.ForRead) as Entity;
                    if (ent == null)
                    {
                        WriteMessage("\n无法打开实体。");
                        return;
                    }

                    WriteMessage($"\n--- 开始分析实体 (Handle: {ent.Handle}) ---");
                    WriteMessage($"\n类型: {ent.GetType().Name}");

                    // 1. 检查实体本身的 XData 和 扩展字典
                    WriteMessage("\n\n[1] 检查实体本身...");
                    DumpXDataInternal(ent, "  ");
                    DumpExtensionDictionaryInternal(tr, ent, "  ");

                    // 2. 如果是块参照，深入分析
                    if (ent is BlockReference br)
                    {
                        // 2a. 检查块属性 (AttributeReference)
                        WriteMessage("\n\n[2a] 检查块属性 (Attributes)...");
                        if (br.AttributeCollection.Count > 0)
                        {
                            foreach (ObjectId attId in br.AttributeCollection)
                            {
                                AttributeReference attRef = tr.GetObject(attId, OpenMode.ForRead) as AttributeReference;
                                if (attRef != null)
                                {
                                    WriteMessage($"\n  -> 属性: Tag='{attRef.Tag}', Value='{attRef.TextString}'");
                                }
                            }
                        }
                        else
                        {
                            WriteMessage("\n  -> 无块属性。");
                        }

                        // 2b. 检查其引用的块定义 (BlockTableRecord)
                        WriteMessage("\n\n[2b] 检查块定义 (BlockTableRecord)...");
                        ObjectId btrId = br.BlockTableRecord;
                        if (!btrId.IsNull)
                        {
                            BlockTableRecord btr = tr.GetObject(btrId, OpenMode.ForRead) as BlockTableRecord;
                            WriteMessage($"\n  -> 块定义名称: '{btr.Name}'");
                            DumpXDataInternal(btr, "    ");
                            DumpExtensionDictionaryInternal(tr, btr, "    ");

                            // 2c. 检查块定义内部的实体
                            WriteMessage("\n\n[2c] 检查块定义内部的实体...");
                            int subEntCount = 0;
                            bool foundDataEntities = false;
                            foreach (ObjectId subId in btr)
                            {
                                subEntCount++;
                                Entity subEnt = tr.GetObject(subId, OpenMode.ForRead) as Entity;
                                if (subEnt != null)
                                {
                                    bool hasData = subEnt.XData != null || !subEnt.ExtensionDictionary.IsNull;
                                    if (hasData)
                                    {
                                        foundDataEntities = true;
                                        WriteMessage($"\n  -> 发现块内实体 (类型: {subEnt.GetType().Name}, Handle: {subEnt.Handle}) 携带数据:");
                                        DumpXDataInternal(subEnt, "      ");
                                        DumpExtensionDictionaryInternal(tr, subEnt, "      ");
                                    }
                                }
                            }
                            if (subEntCount == 0 || !foundDataEntities)
                            {
                                WriteMessage("\n  -> 块定义内部未发现携带数据的实体。");
                            }
                        }
                    }

                    // 3. 检查命名对象字典 (这个是全局检查，不针对特定实体，但有助于发现插件踪迹)
                    WriteMessage("\n\n[3] 扫描命名对象字典 (Named Objects Dictionary)...");
                    DBDictionary nod = tr.GetObject(db.NamedObjectsDictionaryId, OpenMode.ForRead) as DBDictionary;
                    bool foundCustomDict = false;
                    foreach (var entry in nod)
                    {
                        // 过滤掉AutoCAD自带的字典
                        if (!entry.Key.StartsWith("ACAD"))
                        {
                            foundCustomDict = true;
                            WriteMessage($"\n  -> 发现自定义字典: '{entry.Key}'");
                        }
                    }
                    if (!foundCustomDict)
                    {
                        WriteMessage("\n  -> 未发现自定义字典。");
                    }

                    WriteMessage("\n\n--- 分析结束 ---");
                    tr.Commit();
                }
                catch (System.Exception ex)
                {
                    WriteMessage($"\n分析时发生错误: {ex.Message}");
                    tr.Abort();
                }
            }
        }

        /// <summary>
        /// 内部方法：转储XData信息
        /// </summary>
        private void DumpXDataInternal(DBObject obj, string indent)
        {
            ResultBuffer rb = obj.XData;
            if (rb != null)
            {
                WriteMessage($"\n{indent}发现 XData:");
                foreach (TypedValue tv in rb)
                {
                    WriteMessage($"\n{indent}  Code={tv.TypeCode}, Value='{tv.Value}'");
                }
                rb.Dispose();
            }
        }

        /// <summary>
        /// 内部方法：转储扩展字典信息
        /// </summary>
        private void DumpExtensionDictionaryInternal(Transaction tr, DBObject obj, string indent)
        {
            ObjectId extDictId = obj.ExtensionDictionary;
            if (!extDictId.IsNull)
            {
                DBDictionary extDict = tr.GetObject(extDictId, OpenMode.ForRead) as DBDictionary;
                WriteMessage($"\n{indent}发现扩展字典:");
                foreach (var entry in extDict)
                {
                    WriteMessage($"\n{indent}  Key='{entry.Key}'");
                    // 进一步分析 Xrecord...
                    Xrecord xrec = tr.GetObject(entry.Value, OpenMode.ForRead, false, false) as Xrecord;
                    if (xrec != null && xrec.Data != null)
                    {
                        WriteMessage($"{indent}    Xrecord Data:");
                        foreach (TypedValue tv in xrec.Data)
                        {
                            WriteMessage($"\n{indent}      Code={tv.TypeCode}, Value='{tv.Value}'");
                        }
                    }
                }
            }
        }
    }
}