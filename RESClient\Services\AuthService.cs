using System;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;
using System.IO;
using Newtonsoft.Json;
using RESClient.Models;

namespace RESClient.Services
{
    /// <summary>
    /// 认证服务接口
    /// </summary>
    public interface IAuthService
    {
        Task<AuthenticationStatusInfo> GetServerStatusAsync(bool triggerEvent = true);
        Task<LoginResponse> LoginAsync(LoginRequest request);
        Task<bool> ValidateTokenAsync();
        void Logout();
        bool IsLoggedIn { get; }
        string CurrentToken { get; }
        UserInfo CurrentUser { get; }
        DateTime? TokenExpiresAt { get; }
        event EventHandler<AuthenticationStatusInfo> AuthenticationStatusChanged;
    }

    /// <summary>
    /// 认证服务实现
    /// </summary>
    public class AuthService : IAuthService
    {
        private readonly RESServerConfigService _configService;
        private readonly AuthConfigService _authConfigService;
        private string _currentToken;
        private UserInfo _currentUser;
        private DateTime? _tokenExpiresAt;
        private bool _isAuthenticationEnabled;

        public event EventHandler<AuthenticationStatusInfo> AuthenticationStatusChanged;

        public AuthService(RESServerConfigService configService)
        {
            _configService = configService;
            _authConfigService = AuthConfigService.Instance;

            // 注意：不在构造函数中自动加载凭据进行自动登录
            // 当服务器启用认证时，应该让用户手动登录
            // 记住的密码功能将在LoginForm中处理
        }

        public bool IsLoggedIn => !string.IsNullOrEmpty(_currentToken) && 
                                  _tokenExpiresAt.HasValue && 
                                  _tokenExpiresAt.Value > DateTime.UtcNow;

        public string CurrentToken => _currentToken;
        public UserInfo CurrentUser => _currentUser;
        public DateTime? TokenExpiresAt => _tokenExpiresAt;

        /// <summary>
        /// 获取服务器状态和认证配置
        /// </summary>
        /// <param name="triggerEvent">是否触发状态变更事件，默认为true</param>
        public async Task<AuthenticationStatusInfo> GetServerStatusAsync(bool triggerEvent = true)
        {
            try
            {
                using (var client = new HttpClient())
                {
                    client.Timeout = TimeSpan.FromSeconds(10);
                    
                    var url = _configService.GetApiUrl("/api/auth/status");
                    var response = await client.GetAsync(url);
                    
                    if (response.IsSuccessStatusCode)
                    {
                        var content = await response.Content.ReadAsStringAsync();
                        var serverStatus = JsonConvert.DeserializeObject<ServerStatusResponse>(content);
                        
                        _isAuthenticationEnabled = serverStatus.AuthenticationEnabled;
                        
                        var statusInfo = new AuthenticationStatusInfo
                        {
                            IsAuthenticationEnabled = serverStatus.AuthenticationEnabled,
                            Message = serverStatus.Message
                        };

                        if (!serverStatus.AuthenticationEnabled)
                        {
                            statusInfo.Status = AuthenticationStatus.Disabled;
                            statusInfo.Message = "服务器未启用认证功能";
                        }
                        else if (IsLoggedIn)
                        {
                            statusInfo.Status = AuthenticationStatus.LoggedIn;
                            statusInfo.User = _currentUser;
                            statusInfo.TokenExpiresAt = _tokenExpiresAt;
                            statusInfo.Message = $"已登录用户：{_currentUser?.Username}";
                        }
                        else
                        {
                            statusInfo.Status = AuthenticationStatus.RequiredButNotLoggedIn;
                            statusInfo.Message = "需要登录";
                        }

                        if (triggerEvent)
                        {
                            OnAuthenticationStatusChanged(statusInfo);
                        }
                        return statusInfo;
                    }
                    else
                    {
                        var statusInfo = new AuthenticationStatusInfo
                        {
                            Status = AuthenticationStatus.ServerConnectionFailed,
                            Message = $"无法连接到服务器：{response.StatusCode}"
                        };
                        if (triggerEvent)
                        {
                            OnAuthenticationStatusChanged(statusInfo);
                        }
                        return statusInfo;
                    }
                }
            }
            catch (Exception ex)
            {
                var statusInfo = new AuthenticationStatusInfo
                {
                    Status = AuthenticationStatus.ServerConnectionFailed,
                    Message = $"服务器连接失败：{ex.Message}"
                };
                if (triggerEvent)
                {
                    OnAuthenticationStatusChanged(statusInfo);
                }
                return statusInfo;
            }
        }

        /// <summary>
        /// 用户登录
        /// </summary>
        public async Task<LoginResponse> LoginAsync(LoginRequest request)
        {
            try
            {
                using (var client = new HttpClient())
                {
                    client.Timeout = TimeSpan.FromMinutes(_configService.RequestTimeoutMinutes);
                    
                    var url = _configService.GetApiUrl("/api/auth/login");
                    var json = JsonConvert.SerializeObject(request);
                    var content = new StringContent(json, Encoding.UTF8, "application/json");
                    
                    var response = await client.PostAsync(url, content);
                    var responseContent = await response.Content.ReadAsStringAsync();
                    
                    var loginResponse = JsonConvert.DeserializeObject<LoginResponse>(responseContent);
                    
                    if (loginResponse.Success && !string.IsNullOrEmpty(loginResponse.Token))
                    {
                        _currentToken = loginResponse.Token;
                        _currentUser = loginResponse.User;
                        _tokenExpiresAt = loginResponse.ExpiresAt;
                        
                        SaveCredentials();
                        
                        var statusInfo = new AuthenticationStatusInfo
                        {
                            Status = AuthenticationStatus.LoggedIn,
                            User = _currentUser,
                            TokenExpiresAt = _tokenExpiresAt,
                            IsAuthenticationEnabled = _isAuthenticationEnabled,
                            Message = $"登录成功：{_currentUser?.Username}"
                        };
                        OnAuthenticationStatusChanged(statusInfo);
                    }
                    
                    return loginResponse;
                }
            }
            catch (Exception ex)
            {
                return new LoginResponse
                {
                    Success = false,
                    Message = $"登录请求失败：{ex.Message}"
                };
            }
        }

        /// <summary>
        /// 验证当前 Token
        /// </summary>
        public async Task<bool> ValidateTokenAsync()
        {
            if (string.IsNullOrEmpty(_currentToken))
                return false;

            try
            {
                using (var client = new HttpClient())
                {
                    client.Timeout = TimeSpan.FromSeconds(10);
                    client.DefaultRequestHeaders.Authorization = 
                        new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", _currentToken);
                    
                    var url = _configService.GetApiUrl("/api/auth/validate");
                    var response = await client.GetAsync(url);
                    
                    if (response.IsSuccessStatusCode)
                    {
                        return true;
                    }
                    else
                    {
                        // Token 无效，清除本地存储
                        Logout();
                        return false;
                    }
                }
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 登出
        /// </summary>
        public void Logout()
        {
            _currentToken = null;
            _currentUser = null;
            _tokenExpiresAt = null;
            
            ClearStoredCredentials();
            
            var statusInfo = new AuthenticationStatusInfo
            {
                Status = _isAuthenticationEnabled ? AuthenticationStatus.RequiredButNotLoggedIn : AuthenticationStatus.Disabled,
                Message = "已登出",
                IsAuthenticationEnabled = _isAuthenticationEnabled
            };
            OnAuthenticationStatusChanged(statusInfo);
        }

        /// <summary>
        /// 保存凭据到本地
        /// </summary>
        private void SaveCredentials()
        {
            // 只有在配置允许记住登录状态时才保存
            if (!_authConfigService.RememberLoginState)
                return;

            try
            {
                var appDataPath = Environment.GetFolderPath(Environment.SpecialFolder.LocalApplicationData);
                var appFolder = Path.Combine(appDataPath, "RESClient");
                Directory.CreateDirectory(appFolder);

                var credentialsPath = Path.Combine(appFolder, "auth_credentials.json");

                var credentials = new
                {
                    Token = _currentToken,
                    User = _currentUser,
                    ExpiresAt = _tokenExpiresAt
                };

                var json = JsonConvert.SerializeObject(credentials, Formatting.Indented);
                File.WriteAllText(credentialsPath, json);
            }
            catch
            {
                // 忽略保存错误
            }
        }

        /// <summary>
        /// 加载存储的凭据
        /// </summary>
        private void LoadStoredCredentials()
        {
            try
            {
                var appDataPath = Environment.GetFolderPath(Environment.SpecialFolder.LocalApplicationData);
                var credentialsPath = Path.Combine(appDataPath, "RESClient", "auth_credentials.json");
                
                if (File.Exists(credentialsPath))
                {
                    var json = File.ReadAllText(credentialsPath);
                    var credentials = JsonConvert.DeserializeAnonymousType(json, new
                    {
                        Token = "",
                        User = default(UserInfo),
                        ExpiresAt = default(DateTime?)
                    });
                    
                    if (credentials != null && !string.IsNullOrEmpty(credentials.Token))
                    {
                        _currentToken = credentials.Token;
                        _currentUser = credentials.User;
                        _tokenExpiresAt = credentials.ExpiresAt;
                        
                        // 检查 Token 是否过期
                        if (_tokenExpiresAt.HasValue && _tokenExpiresAt.Value <= DateTime.UtcNow)
                        {
                            ClearStoredCredentials();
                        }
                    }
                }
            }
            catch
            {
                // 忽略加载错误
            }
        }

        /// <summary>
        /// 清除存储的凭据
        /// </summary>
        private void ClearStoredCredentials()
        {
            try
            {
                var appDataPath = Environment.GetFolderPath(Environment.SpecialFolder.LocalApplicationData);
                var credentialsPath = Path.Combine(appDataPath, "RESClient", "auth_credentials.json");
                
                if (File.Exists(credentialsPath))
                {
                    File.Delete(credentialsPath);
                }
            }
            catch
            {
                // 忽略删除错误
            }
        }

        /// <summary>
        /// 触发认证状态变更事件
        /// </summary>
        private void OnAuthenticationStatusChanged(AuthenticationStatusInfo statusInfo)
        {
            AuthenticationStatusChanged?.Invoke(this, statusInfo);
        }
    }
}
