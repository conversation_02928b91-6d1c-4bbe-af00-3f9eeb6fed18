<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>单个打印图导出工具</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background-color: #fff;
            padding: 20px;
            border-radius: 5px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        h1 {
            color: #333;
            margin-top: 0;
            border-bottom: 1px solid #eee;
            padding-bottom: 10px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        .form-control {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        .btn {
            padding: 10px 15px;
            background-color: #337ab7;
            border: none;
            border-radius: 4px;
            color: white;
            cursor: pointer;
            font-weight: bold;
        }
        .btn:hover {
            background-color: #286090;
        }
        .btn:disabled {
            background-color: #cccccc;
            cursor: not-allowed;
        }
        .status {
            margin-top: 20px;
            padding: 10px;
            border-radius: 4px;
        }
        .status.success {
            background-color: #dff0d8;
            color: #3c763d;
        }
        .status.error {
            background-color: #f2dede;
            color: #a94442;
        }
        .status.info {
            background-color: #d9edf7;
            color: #31708f;
        }
        .status.warning {
            background-color: #fcf8e3;
            color: #8a6d3b;
        }
        .panel {
            border: 1px solid #ddd;
            border-radius: 4px;
            margin-bottom: 20px;
            background-color: #f9f9f9;
        }
        .panel-heading {
            padding: 10px 15px;
            background-color: #f5f5f5;
            border-bottom: 1px solid #ddd;
            border-top-left-radius: 3px;
            border-top-right-radius: 3px;
            font-weight: bold;
        }
        .panel-body {
            padding: 15px;
        }
        .processed-list {
            list-style-type: none;
            padding: 0;
            margin: 0;
        }
        .processed-list li {
            padding: 8px 10px;
            border-bottom: 1px solid #eee;
        }
        .processed-list li:last-child {
            border-bottom: none;
        }
        .processed-list li.active {
            background-color: #e8f5e9;
        }
        .tag {
            display: inline-block;
            padding: 3px 8px;
            font-size: 12px;
            border-radius: 10px;
            background-color: #e0e0e0;
            margin-left: 5px;
        }
        .badge {
            display: inline-block;
            min-width: 10px;
            padding: 3px 7px;
            font-size: 12px;
            font-weight: 700;
            line-height: 1;
            color: #fff;
            text-align: center;
            white-space: nowrap;
            vertical-align: middle;
            background-color: #777;
            border-radius: 10px;
        }
        .navbar {
            background-color: #f8f8f8;
            border-radius: 4px;
            margin-bottom: 20px;
            padding: 10px 15px;
        }
        .navbar a {
            margin-right: 15px;
            color: #337ab7;
            text-decoration: none;
        }
        .navbar a:hover {
            text-decoration: underline;
        }
        #progressContainer {
            display: none;
            margin-top: 20px;
        }
        .progress-info {
            margin-bottom: 10px;
        }
        #downloadLink {
            display: none;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="navbar">
            <a href="index.html">首页</a>
            <a href="print-drawings.html">打印图处理（队列）</a>
            <a href="direct-print-drawings.html">打印图直接处理</a>
            <a href="single-print-drawing.html" style="font-weight: bold;">单个打印图导出</a>
        </div>
        
        <h1>单个打印图导出工具</h1>
        <p>本工具可以从DWG文件中逐个导出打印图。每次导出一个未处理过的打印图，直到全部导出完毕。</p>
        
        <div class="panel">
            <div class="panel-heading">第一步：选择DWG文件</div>
            <div class="panel-body">
                <div class="form-group">
                    <label for="fileInput">选择DWG文件：</label>
                    <input type="file" id="fileInput" class="form-control" accept=".dwg">
                </div>
                <p class="help-text">请选择包含打印图的DWG文件。</p>
            </div>
        </div>
        
        <div class="panel">
            <div class="panel-heading">第二步：开始导出</div>
            <div class="panel-body">
                <button id="exportBtn" class="btn" disabled>导出下一个打印图</button>
                <div id="progressContainer">
                    <div class="progress-info">
                        <span>正在导出： </span>
                        <span id="currentProgress">0/0</span>
                    </div>
                </div>
                <div id="statusMessage" class="status info" style="display: none;"></div>
                <div id="downloadLink" class="form-group">
                    <a id="downloadBtn" href="#" class="btn" download>下载打印图文件</a>
                </div>
            </div>
        </div>
        
        <div class="panel">
            <div class="panel-heading">打印图状态 <span id="queueStatus" class="badge">0</span></div>
            <div class="panel-body">
                <ul id="processedList" class="processed-list">
                    <li class="empty-list">暂无处理记录</li>
                </ul>
            </div>
        </div>
    </div>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 获取DOM元素
            const fileInput = document.getElementById('fileInput');
            const exportBtn = document.getElementById('exportBtn');
            const statusMessage = document.getElementById('statusMessage');
            const processedList = document.getElementById('processedList');
            const progressContainer = document.getElementById('progressContainer');
            const currentProgress = document.getElementById('currentProgress');
            const downloadLink = document.getElementById('downloadLink');
            const downloadBtn = document.getElementById('downloadBtn');
            const queueStatus = document.getElementById('queueStatus');
            
            // 文件路径
            let currentFilePath = '';
            
            // 已处理打印图号列表
            const processedDrawingNumbers = [];
            
            // 所有打印图列表
            let allDrawingNumbers = [];
            
            // 启用按钮
            function updateExportButtonState() {
                exportBtn.disabled = !currentFilePath;
            }
            
            // 显示状态信息
            function showStatus(message, type = 'info') {
                statusMessage.textContent = message;
                statusMessage.className = `status ${type}`;
                statusMessage.style.display = 'block';
            }
            
            // 隐藏状态信息
            function hideStatus() {
                statusMessage.style.display = 'none';
            }
            
            // 更新处理列表
            function updateProcessedList() {
                // 清空列表
                processedList.innerHTML = '';
                
                if (processedDrawingNumbers.length === 0 && allDrawingNumbers.length === 0) {
                    const emptyItem = document.createElement('li');
                    emptyItem.textContent = '暂无处理记录';
                    emptyItem.className = 'empty-list';
                    processedList.appendChild(emptyItem);
                    return;
                }
                
                // 添加已处理项
                allDrawingNumbers.forEach((number, index) => {
                    const item = document.createElement('li');
                    item.textContent = `图号: ${number}`;
                    
                    if (processedDrawingNumbers.includes(number)) {
                        const tag = document.createElement('span');
                        tag.className = 'tag';
                        tag.textContent = '已导出';
                        item.appendChild(tag);
                        item.className = 'processed';
                    }
                    
                    processedList.appendChild(item);
                });
                
                // 更新队列状态
                queueStatus.textContent = processedDrawingNumbers.length + '/' + allDrawingNumbers.length;
            }
            
            // 选择文件
            fileInput.addEventListener('change', function(e) {
                if (e.target.files.length > 0) {
                    const file = e.target.files[0];
                    
                    // 创建上传表单数据
                    const formData = new FormData();
                    formData.append('file', file);
                    
                    showStatus(`已选择文件: ${file.name}`, 'info');
                    
                    // 上传文件到服务器临时目录
                    fetch('/api/upload/dwg', {
                        method: 'POST',
                        body: formData
                    })
                    .then(response => {
                        if (!response.ok) {
                            throw new Error('上传文件失败');
                        }
                        return response.json();
                    })
                    .then(data => {
                        currentFilePath = data.path;
                        showStatus(`文件已上传到服务器: ${data.fileName || data.FileName}`, 'success');
                        updateExportButtonState();
                        
                        // 重置处理记录
                        processedDrawingNumbers.length = 0;
                        allDrawingNumbers.length = 0;
                        updateProcessedList();
                    })
                    .catch(error => {
                        showStatus(`错误: ${error.message}`, 'error');
                    });
                }
            });
            
            // 导出打印图
            exportBtn.addEventListener('click', function() {
                if (!currentFilePath) {
                    showStatus('请先选择DWG文件', 'warning');
                    return;
                }
                
                // 禁用导出按钮
                exportBtn.disabled = true;
                
                // 显示进度
                progressContainer.style.display = 'block';
                showStatus('正在处理...', 'info');
                
                // 隐藏之前的下载链接
                downloadLink.style.display = 'none';
                
                // 准备请求数据
                const requestData = {
                    filePath: currentFilePath,
                    processedDrawingNumbers: processedDrawingNumbers
                };
                
                // 发起请求
                fetch('/api/single-print-drawing/export', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(requestData)
                })
                .then(response => {
                    const exportInfo = response.headers.get('X-Export-Info');
                    
                    if (response.ok) {
                        // 解析导出信息
                        if (exportInfo) {
                            const info = JSON.parse(atob(exportInfo));
                            currentProgress.textContent = info.formatInfo;
                            
                            // 添加到已处理列表
                            if (info.drawingNumber && !processedDrawingNumbers.includes(info.drawingNumber)) {
                                processedDrawingNumbers.push(info.drawingNumber);
                            }
                            
                            // 更新所有打印图列表
                            if (info.allDrawingNumbers && info.allDrawingNumbers.length > 0) {
                                allDrawingNumbers = info.allDrawingNumbers;
                            }
                            
                            // 更新列表显示
                            updateProcessedList();
                            
                            showStatus(`成功导出打印图 ${info.drawingNumber}（${info.formatInfo}）`, 'success');
                        }
                        
                        // 获取文件并创建下载链接
                        return response.blob().then(blob => {
                            const url = window.URL.createObjectURL(blob);
                            downloadBtn.href = url;
                            
                            // 设置下载文件名
                            const contentDisposition = response.headers.get('Content-Disposition');
                            if (contentDisposition) {
                                const filenameMatch = contentDisposition.match(/filename="(.+?)"/);
                                if (filenameMatch && filenameMatch[1]) {
                                    downloadBtn.download = filenameMatch[1];
                                } else {
                                    downloadBtn.download = `PrintDrawing_${Date.now()}.dwg`;
                                }
                            } else {
                                downloadBtn.download = `PrintDrawing_${Date.now()}.dwg`;
                            }
                            
                            downloadLink.style.display = 'block';
                        });
                    }
                    // 所有打印图都处理完成
                    else if (response.status === 404) {
                        return response.json().then(data => {
                            showStatus('已导出所有打印图，无更多内容', 'info');
                            
                            // 更新所有打印图列表
                            if (data.allDrawingNumbers && data.allDrawingNumbers.length > 0) {
                                allDrawingNumbers = data.allDrawingNumbers;
                            }
                            
                            updateProcessedList();
                        });
                    }
                    else {
                        throw new Error(`服务器错误 (${response.status})`);
                    }
                })
                .catch(error => {
                    showStatus(`错误: ${error.message}`, 'error');
                })
                .finally(() => {
                    // 重新启用导出按钮
                    exportBtn.disabled = false;
                });
            });
            
            // 检查队列状态
            function checkQueueStatus() {
                fetch('/api/single-print-drawing/check-queue')
                    .then(response => response.json())
                    .then(data => {
                        queueStatus.textContent = data.queueLength;
                    })
                    .catch(() => {
                        // 忽略错误
                    });
            }
            
            // 定期更新队列状态
            setInterval(checkQueueStatus, 5000);
            
            // 初始化页面状态
            updateExportButtonState();
            checkQueueStatus();
        });
    </script>
</body>
</html> 