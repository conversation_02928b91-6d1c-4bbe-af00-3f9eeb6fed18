using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using SevenZip;

namespace RESClient.Services
{
    /// <summary>
    /// 7z压缩包提取服务
    /// 用于处理成果包.7z文件的提取和临时文件管理
    /// </summary>
    public class ArchiveExtractionService : IDisposable
    {
        private readonly List<string> _tempDirectories;
        private readonly string _sevenZipLibraryPath;
        private bool _disposed = false;

        public ArchiveExtractionService()
        {
            _tempDirectories = new List<string>();

            // 设置7z.dll路径 - 使用7z.Libs包提供的路径
            string appDirectory = AppDomain.CurrentDomain.BaseDirectory;

            // 根据当前进程架构选择正确的7z.dll
            bool is64Bit = Environment.Is64BitProcess;
            string architectureFolder = is64Bit ? "x64" : "x86";
            _sevenZipLibraryPath = Path.Combine(appDirectory, architectureFolder, "7z.dll");

            // 如果找不到，尝试旧的路径作为备用
            if (!File.Exists(_sevenZipLibraryPath))
            {
                _sevenZipLibraryPath = Path.Combine(appDirectory, "tool", "7z", "7z.dll");
            }

            // 初始化SevenZipSharp库
            if (File.Exists(_sevenZipLibraryPath))
            {
                SevenZipBase.SetLibraryPath(_sevenZipLibraryPath);
            }
            else
            {
                throw new FileNotFoundException($"7z.dll not found. Searched paths:\n" +
                    $"1. {Path.Combine(appDirectory, architectureFolder, "7z.dll")}\n" +
                    $"2. {Path.Combine(appDirectory, "tool", "7z", "7z.dll")}\n" +
                    $"Current process is {(is64Bit ? "64-bit" : "32-bit")}");
            }
        }

        /// <summary>
        /// 在指定目录中查找成果包.7z文件
        /// </summary>
        /// <param name="dataDirectory">数据目录路径</param>
        /// <returns>找到的7z文件路径列表</returns>
        public List<string> FindArchiveFiles(string dataDirectory)
        {
            var archiveFiles = new List<string>();
            
            if (!Directory.Exists(dataDirectory))
            {
                return archiveFiles;
            }

            try
            {
                // 查找所有成果包.7z文件
                var files = Directory.GetFiles(dataDirectory, "*.7z", SearchOption.AllDirectories)
                    .Where(file => Path.GetFileName(file).Contains("成果包"))
                    .ToList();
                
                archiveFiles.AddRange(files);
            }
            catch (Exception ex)
            {
                throw new Exception($"查找7z文件时出错: {ex.Message}", ex);
            }

            return archiveFiles;
        }

        /// <summary>
        /// 提取7z文件到临时目录
        /// </summary>
        /// <param name="archiveFilePath">7z文件路径</param>
        /// <param name="progressCallback">进度回调</param>
        /// <returns>提取目录路径</returns>
        public string ExtractArchive(string archiveFilePath, Action<int, string> progressCallback = null)
        {
            if (!File.Exists(archiveFilePath))
            {
                throw new FileNotFoundException($"7z文件不存在: {archiveFilePath}");
            }

            // 创建临时目录
            string tempDirectory = Path.Combine(Path.GetTempPath(), "RESClient_Archive_" + Guid.NewGuid().ToString("N").Substring(0, 8));
            Directory.CreateDirectory(tempDirectory);
            _tempDirectories.Add(tempDirectory);

            try
            {
                progressCallback?.Invoke(10, $"[INFO]开始提取7z文件: {Path.GetFileName(archiveFilePath)}");

                using (var extractor = new SevenZipExtractor(archiveFilePath))
                {
                    // 设置进度回调
                    if (progressCallback != null)
                    {
                        extractor.Extracting += (sender, e) =>
                        {
                            int progress = 10 + (int)(e.PercentDone * 0.8); // 10-90%的进度范围
                            progressCallback(progress, $"[INFO]提取进度: {e.PercentDone}%");
                        };
                    }

                    // 提取所有文件
                    extractor.ExtractArchive(tempDirectory);
                }

                progressCallback?.Invoke(95, $"[INFO]7z文件提取完成: {tempDirectory}");
                return tempDirectory;
            }
            catch (Exception ex)
            {
                // 清理失败的临时目录
                try
                {
                    if (Directory.Exists(tempDirectory))
                    {
                        Directory.Delete(tempDirectory, true);
                    }
                    _tempDirectories.Remove(tempDirectory);
                }
                catch
                {
                    // 忽略清理错误
                }

                throw new Exception($"提取7z文件失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 在提取的目录中查找CGB.xls文件
        /// </summary>
        /// <param name="extractedDirectory">提取的目录路径</param>
        /// <returns>CGB.xls文件路径，如果未找到则返回null</returns>
        public string FindCGBFileInExtracted(string extractedDirectory)
        {
            if (!Directory.Exists(extractedDirectory))
            {
                return null;
            }

            try
            {
                // 查找CGB.xls文件
                var files = Directory.GetFiles(extractedDirectory, "*.xls*", SearchOption.AllDirectories)
                    .Where(file => Path.GetFileName(file).ToLower().Contains("cgb"))
                    .ToArray();

                return files.Any() ? files[0] : null;
            }
            catch (Exception ex)
            {
                throw new Exception($"在提取目录中查找CGB文件时出错: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 提取7z文件并查找CGB.xls文件的完整流程
        /// </summary>
        /// <param name="dataDirectory">数据目录路径</param>
        /// <param name="progressCallback">进度回调</param>
        /// <returns>CGB.xls文件路径，如果未找到则返回null</returns>
        public string ExtractAndFindCGBFile(string dataDirectory, Action<int, string> progressCallback = null)
        {
            progressCallback?.Invoke(5, "[INFO]查找成果包.7z文件...");

            // 1. 查找7z文件
            var archiveFiles = FindArchiveFiles(dataDirectory);
            if (archiveFiles.Count == 0)
            {
                progressCallback?.Invoke(5, "[ERROR]未找到成果包.7z文件");
                return null;
            }

            progressCallback?.Invoke(10, $"[INFO]找到 {archiveFiles.Count} 个成果包文件，使用第一个: {Path.GetFileName(archiveFiles[0])}");

            // 2. 提取第一个7z文件
            string extractedDirectory = ExtractArchive(archiveFiles[0], progressCallback);

            // 3. 在提取的目录中查找CGB文件
            progressCallback?.Invoke(95, "[INFO]在提取目录中查找CGB.xls文件...");
            string cgbFilePath = FindCGBFileInExtracted(extractedDirectory);

            if (string.IsNullOrEmpty(cgbFilePath))
            {
                progressCallback?.Invoke(95, "[ERROR]在提取的文件中未找到CGB.xls文件");
                return null;
            }

            progressCallback?.Invoke(100, $"[INFO]找到CGB文件: {Path.GetFileName(cgbFilePath)}");
            return cgbFilePath;
        }

        /// <summary>
        /// 清理所有临时目录
        /// </summary>
        public void CleanupTempDirectories()
        {
            foreach (string tempDir in _tempDirectories.ToList())
            {
                try
                {
                    if (Directory.Exists(tempDir))
                    {
                        Directory.Delete(tempDir, true);
                    }
                }
                catch
                {
                    // 忽略清理错误，可能文件正在使用中
                }
            }
            _tempDirectories.Clear();
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            if (!_disposed)
            {
                CleanupTempDirectories();
                _disposed = true;
            }
        }
    }
}
