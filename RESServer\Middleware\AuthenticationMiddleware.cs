using Microsoft.Extensions.Options;
using RESServer.Models;
using RESServer.Services;
using System.Text.Json;

namespace RESServer.Middleware
{
    /// <summary>
    /// 可配置的认证中间件
    /// 根据配置决定是否启用认证功能
    /// </summary>
    public class AuthenticationMiddleware
    {
        private readonly RequestDelegate _next;
        private readonly AuthConfig _authConfig;
        private readonly ILogger<AuthenticationMiddleware> _logger;

        public AuthenticationMiddleware(
            RequestDelegate next,
            IOptions<AuthConfig> authConfig,
            ILogger<AuthenticationMiddleware> logger)
        {
            _next = next;
            _authConfig = authConfig.Value;
            _logger = logger;
        }

        public async Task InvokeAsync(HttpContext context, IAuthService authService)
        {
            // 如果认证功能未启用，直接跳过认证检查
            if (!_authConfig.EnableAuthentication)
            {
                await _next(context);
                return;
            }

            // 获取请求路径
            var path = context.Request.Path.Value?.ToLower() ?? "";

            // 允许匿名访问的路径
            var anonymousPaths = new[]
            {
                "/api/auth/status",
                "/api/auth/login",
                "/swagger",
                "/",
                "/index.html"
            };

            // 检查是否为允许匿名访问的路径
            if (anonymousPaths.Any(p => path.StartsWith(p)))
            {
                await _next(context);
                return;
            }

            // 检查是否为静态文件
            if (IsStaticFile(path))
            {
                await _next(context);
                return;
            }

            // 获取 Authorization 头
            var authHeader = context.Request.Headers["Authorization"].FirstOrDefault();
            
            if (string.IsNullOrEmpty(authHeader) || !authHeader.StartsWith("Bearer "))
            {
                await WriteUnauthorizedResponse(context, "缺少认证令牌");
                return;
            }

            // 提取 Token
            var token = authHeader.Substring("Bearer ".Length).Trim();
            
            try
            {
                // 验证 Token
                var principal = await authService.GetPrincipalFromTokenAsync(token);
                
                if (principal == null)
                {
                    await WriteUnauthorizedResponse(context, "无效的认证令牌");
                    return;
                }

                // 将用户信息添加到上下文
                context.User = principal;
                
                _logger.LogDebug("用户 {Username} 通过认证访问 {Path}", 
                    principal.Identity?.Name, context.Request.Path);
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Token 验证失败");
                await WriteUnauthorizedResponse(context, "认证令牌验证失败");
                return;
            }

            await _next(context);
        }

        /// <summary>
        /// 检查是否为静态文件
        /// </summary>
        private static bool IsStaticFile(string path)
        {
            var staticExtensions = new[] { ".html", ".css", ".js", ".png", ".jpg", ".jpeg", ".gif", ".ico", ".svg" };
            return staticExtensions.Any(ext => path.EndsWith(ext));
        }

        /// <summary>
        /// 写入未授权响应
        /// </summary>
        private async Task WriteUnauthorizedResponse(HttpContext context, string message)
        {
            context.Response.StatusCode = 401;
            context.Response.ContentType = "application/json";

            var response = new ApiResponse
            {
                Success = false,
                Message = message,
                Error = "Unauthorized"
            };

            var json = JsonSerializer.Serialize(response, new JsonSerializerOptions
            {
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase
            });

            await context.Response.WriteAsync(json);
        }
    }

    /// <summary>
    /// 认证中间件扩展方法
    /// </summary>
    public static class AuthenticationMiddlewareExtensions
    {
        public static IApplicationBuilder UseCustomAuthentication(this IApplicationBuilder builder)
        {
            return builder.UseMiddleware<AuthenticationMiddleware>();
        }
    }
}
