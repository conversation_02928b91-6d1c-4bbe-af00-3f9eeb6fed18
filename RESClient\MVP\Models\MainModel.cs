using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using RESClient.MVP.Interfaces;
using RESClient.Services;

namespace RESClient.MVP.Models
{
    /// <summary>
    /// 主模型实现
    /// </summary>
    public class MainModel : IMainModel
    {
        private readonly Services.ReportGeneratorService _reportGeneratorService;
        private readonly Services.ModuleNumberingService _moduleNumberingService;
        private readonly ErrorCollector _errorCollector;
        private Dictionary<string, string> _directoryInfo;

        /// <summary>
        /// 错误收集器
        /// </summary>
        public ErrorCollector ErrorCollector => _errorCollector;

        /// <summary>
        /// 构造函数
        /// </summary>
        public MainModel()
        {
            _reportGeneratorService = new Services.ReportGeneratorService();
            _moduleNumberingService = new Services.ModuleNumberingService();
            _errorCollector = new ErrorCollector();
            _directoryInfo = new Dictionary<string, string>();
            Initialize();
        }

        /// <summary>
        /// 初始化模型
        /// </summary>
        public void Initialize()
        {
            InitializeDirectoryInfo();
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            // 资源释放代码（如果有）
        }

        /// <summary>
        /// 初始化目录信息
        /// </summary>
        private void InitializeDirectoryInfo()
        {
            // 示例目录结构，后期可以从配置文件读取
            _directoryInfo["TemplatesDir"] = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "报告模板");
            _directoryInfo["OutputDir"] = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "输出");
            _directoryInfo["TestUser"] = "TestUser"; // 测试用户，后期可以从登录信息获取

            // 注意：集成报告功能暂时禁用，相关目录配置已移除
        }

        /// <summary>
        /// 获取报告模板列表
        /// </summary>
        /// <returns>报告模板列表</returns>
        public async Task<List<string>> GetTemplateListAsync()
        {
            return await Task.Run(() =>
            {
                var templatesDir = _directoryInfo["TemplatesDir"];
                if (!Directory.Exists(templatesDir))
                {
                    Directory.CreateDirectory(templatesDir);
                    return new List<string>();
                }

                var templates = new List<string>();
                
                // 获取所有子目录
                var directories = Directory.GetDirectories(templatesDir);
                foreach (var directory in directories)
                {
                    // 在每个子目录中查找.docx文件
                    var files = Directory.GetFiles(directory, "*.docx");
                    foreach (var file in files)
                    {
                        // 添加包含子目录名称的模板名
                        string dirName = Path.GetFileName(directory);
                        string fileName = Path.GetFileName(file);
                        templates.Add($"{dirName}/{fileName}");
                    }
                }

                return templates;
            });
        }

        /// <summary>
        /// 生成报告
        /// </summary>
        /// <param name="parameters">报告参数</param>
        /// <param name="progressCallback">进度回调</param>
        /// <returns>生成结果</returns>
        public async Task<bool> GenerateReportAsync(Dictionary<string, object> parameters, Action<int, string> progressCallback)
        {
            // 清空之前的错误
            _errorCollector.Clear();

            // 创建增强的进度回调，用于收集错误
            Action<int, string> enhancedProgressCallback = (percent, message) =>
            {
                // 解析并收集错误信息
                _errorCollector.ParseAndAddErrorFromProgressMessage(message);

                // 调用原始回调
                progressCallback?.Invoke(percent, message);
            };

            try
            {
                // 检查参数
                if (parameters == null || !parameters.ContainsKey("SelectedModules"))
                {
                    var errorMessage = "缺少必要参数";
                    _errorCollector.AddError(ErrorType.ParameterConfiguration, ErrorSeverity.Error,
                        "系统", errorMessage, suggestedSolution: "请确保传递了正确的参数字典，包含SelectedModules键");
                    enhancedProgressCallback(0, $"[ERROR]{errorMessage}");
                    return false;
                }

                // 获取选中的模块
                var selectedModules = parameters["SelectedModules"] as List<string>;
                if (selectedModules == null || selectedModules.Count == 0)
                {
                    var errorMessage = "未选择要生成的模块";
                    _errorCollector.AddError(ErrorType.ParameterConfiguration, ErrorSeverity.Error,
                        "系统", errorMessage, suggestedSolution: "请至少选择一个要生成的模块");
                    enhancedProgressCallback(0, $"[ERROR]{errorMessage}");
                    return false;
                }

                // 检查是否为完整模块选择
                bool isCompleteSelection = IsCompleteModuleSelection(selectedModules);
                var missingModules = GetMissingModules(selectedModules);

                // 验证资源文件
                enhancedProgressCallback(0, "[INFO]验证资源文件...");
                var resourceValidationResult = ValidateResourceFiles(parameters);
                if (!resourceValidationResult.IsValid)
                {
                    _errorCollector.AddError(ErrorType.MissingFile, ErrorSeverity.Error,
                        "系统", $"资源文件验证失败: {resourceValidationResult.ErrorMessage}",
                        suggestedSolution: "请检查资料文件夹中是否包含所需的文件，确保文件路径正确");
                    enhancedProgressCallback(0, $"[ERROR]资源文件验证失败: {resourceValidationResult.ErrorMessage}");
                    return false;
                }
                enhancedProgressCallback(2, "[INFO]资源文件验证通过");

                // 确保基础输出目录存在
                var baseOutputDir = Path.Combine(_directoryInfo["OutputDir"], _directoryInfo["TestUser"]);
                if (!Directory.Exists(baseOutputDir))
                {
                    Directory.CreateDirectory(baseOutputDir);
                }

                // 由于集成报告功能暂时禁用，所有情况都使用基础输出目录
                string actualOutputDir = baseOutputDir;

                // 添加输出路径到参数
                parameters["OutputDir"] = actualOutputDir;

                // 记录生成类型信息
                if (isCompleteSelection)
                {
                    enhancedProgressCallback(1, "[INFO]检测到完整模块选择");
                    enhancedProgressCallback(1, "[WARNING]集成报告功能暂时禁用，将仅生成单独模块文件");
                }
                else
                {
                    enhancedProgressCallback(1, $"[INFO]检测到部分模块选择，将仅生成单独模块文件");
                    enhancedProgressCallback(1, $"[INFO]未选择的模块: {string.Join(", ", missingModules)}");

                    // 记录部分生成警告
                    _errorCollector.AddError(ErrorType.ParameterConfiguration, ErrorSeverity.Warning,
                        "系统", "部分模块生成",
                        details: $"用户仅选择了 {selectedModules.Count} 个模块，未选择: {string.Join(", ", missingModules)}",
                        suggestedSolution: "如需生成完整集成报告，请选择所有必需的模块");
                }

                // 1. 预生成清理：删除历史结果模块
                enhancedProgressCallback(0, "[INFO]清理历史结果文件...");
                CleanupHistoricalResults(actualOutputDir, enhancedProgressCallback);

                // 2. 生成报告模块
                enhancedProgressCallback(5, "[INFO]开始生成报告模块...");
                bool moduleGenerationResult = await _reportGeneratorService.GenerateReportModulesAsync(selectedModules, parameters, enhancedProgressCallback);

                if (!moduleGenerationResult)
                {
                    _errorCollector.AddError(ErrorType.DocumentGeneration, ErrorSeverity.Error,
                        "系统", "模块生成失败",
                        details: "一个或多个模块在生成过程中失败",
                        suggestedSolution: "请检查具体模块的错误信息，确保模板文件和配置正确");
                    enhancedProgressCallback(100, "[ERROR]模块生成失败");
                    return false;
                }

                // 2.5. 为生成的模块文件添加编号前缀
                enhancedProgressCallback(85, "[INFO]为模块文件添加编号前缀...");
                bool numberingResult = _moduleNumberingService.AddNumberingPrefixes(actualOutputDir, enhancedProgressCallback);

                if (!numberingResult)
                {
                    // 编号失败不应该阻止整个流程，只记录警告
                    _errorCollector.AddError(ErrorType.DocumentGeneration, ErrorSeverity.Warning,
                        "系统", "模块文件编号失败",
                        details: "模块文件生成成功，但添加编号前缀时出现问题",
                        suggestedSolution: "可以手动为文件添加编号前缀，或重新生成报告");
                    enhancedProgressCallback(90, "[WARNING]模块文件编号失败，但模块生成成功");
                }
                else
                {
                    enhancedProgressCallback(90, "[SUCCESS]模块文件编号完成");
                }

                // 3. 后生成处理：根据选择类型决定是否合并
                // 注意：集成报告功能暂时禁用，所有情况都只生成单独模块
                if (isCompleteSelection)
                {
                    // 完整选择：原本会进行文档合并，但现在暂时禁用
                    enhancedProgressCallback(95, "[INFO]检测到完整模块选择");
                    enhancedProgressCallback(100, "[WARNING]集成报告功能暂时禁用，仅生成单独模块文件");
                    enhancedProgressCallback(100, "[SUCCESS]所有模块生成完成");
                    enhancedProgressCallback(100, $"[INFO]已生成 {selectedModules.Count} 个单独模块文件");
                    enhancedProgressCallback(100, "[INFO]集成报告功能将在后续版本中恢复");
                    return true;
                }
                else
                {
                    // 部分选择：不进行合并，仅生成单独模块
                    enhancedProgressCallback(100, "[SUCCESS]部分模块生成完成");
                    enhancedProgressCallback(100, $"[INFO]已生成 {selectedModules.Count} 个单独模块文件");
                    enhancedProgressCallback(100, "[INFO]由于未选择所有必需模块，未生成集成报告");
                    return true;
                }
            }
            catch (Exception ex)
            {
                _errorCollector.AddError(ErrorType.SystemException, ErrorSeverity.Critical,
                    "系统", "报告生成过程中发生未处理的异常",
                    details: ex.Message,
                    suggestedSolution: "请联系技术支持，提供详细的错误信息",
                    exception: ex);
                enhancedProgressCallback(100, $"[ERROR]报告生成过程中发生异常: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 获取目录结构信息
        /// </summary>
        /// <returns>目录结构信息字典</returns>
        public Dictionary<string, string> GetDirectoryInfo()
        {
            return _directoryInfo;
        }

        /// <summary>
        /// 获取所有必需的模块列表
        /// </summary>
        /// <returns>必需模块列表</returns>
        private List<string> GetAllRequiredModules()
        {
            return new List<string>
            {
                "封面",
                "目录",
                "作业声明",
                "作业、质量检查与验收",
                "项目基本信息",
                "楼栋基本信息",
                "经主管部门批准的相关证照",
                "地下室人防区域说明",
                "项目丘地及测绘房屋分布图",
                "建筑物现状影像图",
                "房产面积汇总表",
                "房产分户面积统计表",
                "房产分层测绘图"
            };
        }

        /// <summary>
        /// 检查是否选择了所有必需的模块
        /// </summary>
        /// <param name="selectedModules">用户选择的模块</param>
        /// <returns>是否为完整选择</returns>
        private bool IsCompleteModuleSelection(List<string> selectedModules)
        {
            var allRequiredModules = GetAllRequiredModules();
            return allRequiredModules.All(module => selectedModules.Contains(module));
        }

        /// <summary>
        /// 获取未选择的模块列表
        /// </summary>
        /// <param name="selectedModules">用户选择的模块</param>
        /// <returns>未选择的模块列表</returns>
        private List<string> GetMissingModules(List<string> selectedModules)
        {
            var allRequiredModules = GetAllRequiredModules();
            return allRequiredModules.Where(module => !selectedModules.Contains(module)).ToList();
        }

        /// <summary>
        /// 测试生成房产分层测绘图
        /// </summary>
        /// <param name="progressCallback">进度回调</param>
        /// <returns>生成结果</returns>
        public async Task<bool> TestFloorPlanGenerationAsync(Action<int, string> progressCallback)
        {
            progressCallback?.Invoke(0, "开始测试房产分层测绘图生成...");
            
            // 获取房产分层测绘图模块生成器
            var floorPlanGenerator = _reportGeneratorService.GetModuleGenerator("房产分层测绘图");
            if (floorPlanGenerator == null)
            {
                progressCallback?.Invoke(100, "未找到房产分层测绘图模块生成器");
                return false;
            }
            
            // 准备测试参数
            var parameters = new Dictionary<string, object>
            {
                ["OutputDir"] = Path.Combine(_directoryInfo["OutputDir"], _directoryInfo["TestUser"], "测试")
            };
            
            // 确保输出目录存在
            if (!Directory.Exists(parameters["OutputDir"].ToString()))
            {
                Directory.CreateDirectory(parameters["OutputDir"].ToString());
            }
            
            // 执行测试生成
            return await floorPlanGenerator.GenerateAsync(parameters, progressCallback);
        }

        /// <summary>
        /// 清理历史结果文件
        /// </summary>
        /// <param name="outputDirectory">输出目录</param>
        /// <param name="progressCallback">进度回调</param>
        private void CleanupHistoricalResults(string outputDirectory, Action<int, string> progressCallback)
        {
            try
            {
                if (!Directory.Exists(outputDirectory))
                {
                    return;
                }

                // 定义要清理的文件模式
                var filePatterns = new[]
                {
                    "*.docx", // 所有Word文档
                    "*.pdf",  // 所有PDF文档
                    "*.zip"   // 所有压缩文件
                };

                int deletedCount = 0;
                foreach (string pattern in filePatterns)
                {
                    var files = Directory.GetFiles(outputDirectory, pattern);
                    foreach (string file in files)
                    {
                        try
                        {
                            File.Delete(file);
                            deletedCount++;
                        }
                        catch (Exception ex)
                        {
                            // 记录但不中断清理过程
                            progressCallback?.Invoke(2, $"[WARNING]无法删除文件 {Path.GetFileName(file)}: {ex.Message}");
                        }
                    }
                }

                progressCallback?.Invoke(3, $"[INFO]清理完成，删除了 {deletedCount} 个历史文件");
            }
            catch (Exception ex)
            {
                progressCallback?.Invoke(3, $"[WARNING]清理历史文件时发生错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 设置输出目录结构（简化版本，集成报告功能暂时禁用）
        /// </summary>
        /// <param name="baseOutputDir">基础输出目录</param>
        /// <param name="isCompleteGeneration">是否为完整生成（暂时未使用）</param>
        /// <returns>实际使用的输出目录</returns>
        private string SetupOutputDirectoryStructure(string baseOutputDir, bool isCompleteGeneration)
        {
            try
            {
                // 确保基础输出目录存在
                if (!Directory.Exists(baseOutputDir))
                {
                    Directory.CreateDirectory(baseOutputDir);
                }

                // 由于集成报告功能暂时禁用，所有情况都直接使用基础目录
                return baseOutputDir;
            }
            catch (Exception ex)
            {
                // 如果创建目录失败，返回基础目录
                return baseOutputDir;
            }
        }

        /// <summary>
        /// 清理集成报告目录（已禁用 - 集成报告功能暂时不可用）
        /// </summary>
        /// <param name="baseOutputDir">基础输出目录</param>
        /// <param name="progressCallback">进度回调</param>
        private void CleanupIntegratedReportsDirectory(string baseOutputDir, Action<int, string> progressCallback)
        {
            // 集成报告功能暂时禁用，此方法不再执行任何操作
            progressCallback?.Invoke(3, "[INFO]集成报告功能暂时禁用，跳过目录清理");
        }

        /// <summary>
        /// 验证资源文件
        /// </summary>
        /// <param name="parameters">参数字典</param>
        /// <returns>验证结果</returns>
        private ResourceValidationResult ValidateResourceFiles(Dictionary<string, object> parameters)
        {
            try
            {
                // 检查是否提供了数据文件夹参数
                if (!parameters.TryGetValue("DataFolder", out object dataFolderObj) || dataFolderObj == null)
                {
                    return new ResourceValidationResult
                    {
                        IsValid = false,
                        ErrorMessage = "未提供资料文件夹参数"
                    };
                }

                string dataFolder = dataFolderObj.ToString();
                if (string.IsNullOrWhiteSpace(dataFolder))
                {
                    return new ResourceValidationResult
                    {
                        IsValid = false,
                        ErrorMessage = "资料文件夹路径为空"
                    };
                }

                // 检查数据文件夹是否存在
                if (!Directory.Exists(dataFolder))
                {
                    return new ResourceValidationResult
                    {
                        IsValid = false,
                        ErrorMessage = $"资料文件夹不存在: {dataFolder}"
                    };
                }

                // 检查文件夹是否包含必要的资源文件
                var requiredFiles = new[]
                {
                    "*.xls",  // Excel文件
                    "*.xlsx", // Excel文件
                    "*.dwg",  // CAD图纸文件
                    "*.jpg",  // 图片文件
                    "*.jpeg", // 图片文件
                    "*.png",  // 图片文件
                    "*.bmp"   // 图片文件
                };

                bool hasAnyRequiredFiles = false;
                foreach (string pattern in requiredFiles)
                {
                    var files = Directory.GetFiles(dataFolder, pattern, SearchOption.AllDirectories);
                    if (files.Length > 0)
                    {
                        hasAnyRequiredFiles = true;
                        break;
                    }
                }

                if (!hasAnyRequiredFiles)
                {
                    return new ResourceValidationResult
                    {
                        IsValid = false,
                        ErrorMessage = "资料文件夹中未找到任何支持的资源文件（Excel、CAD图纸、图片等）"
                    };
                }

                return new ResourceValidationResult
                {
                    IsValid = true,
                    ErrorMessage = null
                };
            }
            catch (Exception ex)
            {
                return new ResourceValidationResult
                {
                    IsValid = false,
                    ErrorMessage = $"验证资源文件时发生错误: {ex.Message}"
                };
            }
        }
    }

    /// <summary>
    /// 资源验证结果
    /// </summary>
    public class ResourceValidationResult
    {
        /// <summary>
        /// 是否验证通过
        /// </summary>
        public bool IsValid { get; set; }

        /// <summary>
        /// 错误消息
        /// </summary>
        public string ErrorMessage { get; set; }
    }
}