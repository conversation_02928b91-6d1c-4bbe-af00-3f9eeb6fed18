using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Linq;
using System.Reflection;
using System.Windows.Forms;
using RESClient.MVP.Models;
using RESClient.MVP.Presenters;

namespace RESClient.MVP.Views
{
    /// <summary>
    /// 作业、质量检查与验收参数录入窗体
    /// </summary>
    public partial class WorkQualityParameterInputForm : Form
    {
        private readonly Dictionary<string, Control> _inputControls;
        private readonly WorkQualityParametersModel.WorkQualityParameters _parameters;
        private TableLayoutPanel _mainPanel;
        private Button _okButton;
        private Button _cancelButton;
        private Button _resetButton;

        public WorkQualityParametersModel.WorkQualityParameters Parameters => _parameters;
        public bool IsConfirmed { get; private set; }

        public WorkQualityParameterInputForm(WorkQualityParametersModel.WorkQualityParameters parameters)
        {
            _parameters = parameters ?? throw new ArgumentNullException(nameof(parameters));
            _inputControls = new Dictionary<string, Control>();
            
            InitializeComponent();
            InitializeCustomControls();
            CreateDynamicControls();
            LoadParameterValues();
        }

        private void InitializeCustomControls()
        {
            // 窗体设置 - 与其他参数输入窗体保持一致
            this.Text = "作业、质量检查与验收参数设置";
            this.Size = new Size(620, 800);  // 与封面参数输入窗体保持一致的宽度
            this.MinimumSize = new Size(520, 700);
            this.MaximumSize = new Size(800, 1000);
            this.StartPosition = FormStartPosition.CenterParent;
            this.FormBorderStyle = FormBorderStyle.Sizable;
            this.MaximizeBox = true;
            this.MinimizeBox = false;
            this.ShowInTaskbar = false;

            // 创建主容器面板
            var mainContainer = new Panel
            {
                Dock = DockStyle.Fill,
                Padding = new Padding(20, 15, 5, 60)  // 减小右边距从15到5
            };

            // 创建可滚动的主面板 - 与其他参数输入窗体保持一致
            _mainPanel = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                AutoScroll = true,
                AutoSize = true,
                AutoSizeMode = AutoSizeMode.GrowAndShrink,
                ColumnCount = 2,
                Padding = new Padding(10, 5, 0, 5),  // 减小右内边距从10到0
                CellBorderStyle = TableLayoutPanelCellBorderStyle.None
            };

            // 设置列样式 - 增加标签列宽度以适应较长的中文标签
            _mainPanel.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 200));  // 标签列宽度
            _mainPanel.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 100));   // 输入列

            mainContainer.Controls.Add(_mainPanel);
            this.Controls.Add(mainContainer);
        }

        private void CreateDynamicControls()
        {
            var properties = typeof(WorkQualityParametersModel.WorkQualityParameters).GetProperties()
                .Where(p => p.CanRead && p.CanWrite)
                .ToArray();

            _mainPanel.RowCount = properties.Length + 1;

            int rowIndex = 0;
            foreach (var property in properties)
            {
                CreateControlForProperty(property, rowIndex);
                rowIndex++;
            }

            CreateButtonPanel(rowIndex);
        }

        private void CreateControlForProperty(PropertyInfo property, int rowIndex)
        {
            // 获取显示名称和描述 - 与其他参数输入窗体保持一致
            var displayNameAttr = property.GetCustomAttribute<DisplayNameAttribute>();
            var descriptionAttr = property.GetCustomAttribute<DescriptionAttribute>();

            string displayName = displayNameAttr?.DisplayName ?? property.Name;
            string description = descriptionAttr?.Description ?? "";

            // 创建标签容器 - 调整边距以防止标签截断
            var labelContainer = new Panel
            {
                Dock = DockStyle.Fill,
                Margin = new Padding(5, 8, 3, 8),  // 增加左边距
                Padding = new Padding(10, 0, 15, 0)  // 增加左右内边距以提供更好的标签显示空间
            };

            // 创建标签 - 与其他参数输入窗体保持一致
            var label = new Label
            {
                Text = displayName + ":",
                Dock = DockStyle.Right,
                TextAlign = ContentAlignment.MiddleRight,
                AutoSize = true,
                Font = new Font(this.Font.FontFamily, this.Font.Size, FontStyle.Regular)
            };

            labelContainer.Controls.Add(label);

            // 添加工具提示 - 与其他参数输入窗体保持一致
            if (!string.IsNullOrEmpty(description))
            {
                var toolTip = new ToolTip();
                toolTip.SetToolTip(label, description);
                toolTip.SetToolTip(labelContainer, description);
            }

            // 创建输入控件容器 - 与其他参数输入窗体保持一致
            var inputContainer = new Panel
            {
                Dock = DockStyle.Fill,
                Margin = new Padding(3, 5, 0, 5),  // 减小右边距从3到0
                Padding = new Padding(0, 3, 0, 3)  // 减小右内边距从5到0
            };

            // 创建输入控件
            Control inputControl = CreateInputControl(property);

            // 根据控件类型设置不同的停靠和锚定方式 - 与其他参数输入窗体保持一致
            if (inputControl is DateTimePicker)
            {
                // DateTimePicker使用固定宽度，左对齐
                inputControl.Dock = DockStyle.None;
                inputControl.Anchor = AnchorStyles.Left | AnchorStyles.Top;
            }
            else
            {
                // 其他控件填充整个容器
                inputControl.Dock = DockStyle.Fill;
                inputControl.Anchor = AnchorStyles.Left | AnchorStyles.Right | AnchorStyles.Top;
            }

            inputContainer.Controls.Add(inputControl);

            // 添加到面板
            _mainPanel.Controls.Add(labelContainer, 0, rowIndex);
            _mainPanel.Controls.Add(inputContainer, 1, rowIndex);

            // 设置行样式
            int rowHeight = GetRowHeight(property);
            _mainPanel.RowStyles.Add(new RowStyle(SizeType.Absolute, rowHeight));

            // 保存控件引用
            _inputControls[property.Name] = inputControl;
        }

        private int GetRowHeight(PropertyInfo property)
        {
            // 所有字段都使用标准行高
            return 40;
        }

        private Control CreateInputControl(PropertyInfo property)
        {
            if (property.PropertyType == typeof(string))
            {
                var textBox = new TextBox
                {
                    Font = new Font(this.Font.FontFamily, this.Font.Size, FontStyle.Regular),
                    BorderStyle = BorderStyle.FixedSingle,
                    Multiline = false,
                    Height = 25,
                    TextAlign = HorizontalAlignment.Left,  // 确保文本左对齐，与其他参数输入窗体保持一致
                    Anchor = AnchorStyles.Left | AnchorStyles.Right | AnchorStyles.Top
                };

                return textBox;
            }

            // 默认返回文本框 - 与其他参数输入窗体保持一致
            return new TextBox
            {
                Font = new Font(this.Font.FontFamily, this.Font.Size, FontStyle.Regular),
                BorderStyle = BorderStyle.FixedSingle,
                Height = 25,
                TextAlign = HorizontalAlignment.Left,  // 确保文本左对齐，与其他参数输入窗体保持一致
                Anchor = AnchorStyles.Left | AnchorStyles.Right | AnchorStyles.Top
            };
        }

        private void CreateButtonPanel(int rowIndex)
        {
            // 创建按钮容器 - 与其他参数输入窗体保持一致
            var buttonContainer = new Panel
            {
                Height = 50,
                Dock = DockStyle.Bottom,
                Padding = new Padding(15, 10, 15, 10)
            };

            var buttonPanel = new FlowLayoutPanel
            {
                FlowDirection = FlowDirection.RightToLeft,
                Dock = DockStyle.Fill,
                WrapContents = false,
                AutoSize = true
            };

            // 创建按钮 - 与其他参数输入窗体保持一致的样式和大小
            _cancelButton = new Button
            {
                Text = "取消",
                Size = new Size(80, 32),
                UseVisualStyleBackColor = true,
                DialogResult = DialogResult.Cancel,
                Margin = new Padding(5, 0, 0, 0),
                Font = new Font(this.Font.FontFamily, this.Font.Size, FontStyle.Regular)
            };

            _okButton = new Button
            {
                Text = "确定",
                Size = new Size(80, 32),
                UseVisualStyleBackColor = true,
                Margin = new Padding(5, 0, 0, 0),
                Font = new Font(this.Font.FontFamily, this.Font.Size, FontStyle.Bold)
            };

            _resetButton = new Button
            {
                Text = "重置",
                Size = new Size(80, 32),
                UseVisualStyleBackColor = true,
                Margin = new Padding(5, 0, 0, 0),
                Font = new Font(this.Font.FontFamily, this.Font.Size, FontStyle.Regular)
            };

            // 绑定事件
            _okButton.Click += OkButton_Click;
            _cancelButton.Click += CancelButton_Click;
            _resetButton.Click += ResetButton_Click;

            // 按钮添加顺序：取消、确定、重置（从右到左）
            buttonPanel.Controls.Add(_cancelButton);
            buttonPanel.Controls.Add(_okButton);
            buttonPanel.Controls.Add(_resetButton);

            buttonContainer.Controls.Add(buttonPanel);

            // 将按钮容器添加到窗体而不是TableLayoutPanel
            this.Controls.Add(buttonContainer);

            // 设置默认按钮
            this.CancelButton = _cancelButton;
        }

        private void LoadParameterValues()
        {
            var properties = typeof(WorkQualityParametersModel.WorkQualityParameters).GetProperties()
                .Where(p => p.CanRead && p.CanWrite);

            foreach (var property in properties)
            {
                if (_inputControls.TryGetValue(property.Name, out Control control))
                {
                    var value = property.GetValue(_parameters);
                    SetControlValue(control, value);
                }
            }
        }

        private void SetControlValue(Control control, object value)
        {
            switch (control)
            {
                case TextBox textBox:
                    textBox.Text = value?.ToString() ?? "";
                    break;
            }
        }

        private void SaveParameterValues()
        {
            var properties = typeof(WorkQualityParametersModel.WorkQualityParameters).GetProperties()
                .Where(p => p.CanRead && p.CanWrite);

            foreach (var property in properties)
            {
                if (_inputControls.TryGetValue(property.Name, out Control control))
                {
                    var value = GetControlValue(control, property.PropertyType);
                    property.SetValue(_parameters, value);
                }
            }
        }

        private object GetControlValue(Control control, Type targetType)
        {
            switch (control)
            {
                case TextBox textBox:
                    // 如果文本框为空，自动填充反斜杠占位符并记录警告
                    string textValue = textBox.Text?.Trim() ?? "";
                    if (string.IsNullOrEmpty(textValue))
                    {
                        textValue = "\\";
                        // 记录参数为空的警告到错误收集器
                        LogEmptyParameterWarning(control.Name, "作业、质量检查与验收");
                    }
                    return textValue;
                default:
                    return null;
            }
        }

        /// <summary>
        /// 记录空参数警告
        /// </summary>
        /// <param name="parameterName">参数名称</param>
        /// <param name="moduleName">模块名称</param>
        private void LogEmptyParameterWarning(string parameterName, string moduleName)
        {
            try
            {
                // 获取主窗体的错误收集器
                var mainForm = Application.OpenForms.OfType<Form>()
                    .FirstOrDefault(f => f.GetType().Name == "MainForm");

                if (mainForm != null)
                {
                    // 通过反射获取MainPresenter和ErrorCollector
                    var presenterField = mainForm.GetType().GetField("_presenter",
                        System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);

                    if (presenterField?.GetValue(mainForm) is MainPresenter presenter)
                    {
                        var modelProperty = presenter.GetType().GetProperty("Model",
                            System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);

                        if (modelProperty?.GetValue(presenter) is MainModel model)
                        {
                            model.ErrorCollector.AddError(
                                Services.ErrorType.ParameterConfiguration,
                                Services.ErrorSeverity.Warning,
                                moduleName,
                                $"参数 '{GetParameterDisplayName(parameterName)}' 未填写",
                                details: $"用户未填写参数 '{parameterName}'，已自动使用占位符 '\\'",
                                suggestedSolution: "建议在参数设置中填写完整的参数信息以获得更好的报告质量");
                        }
                    }
                }
            }
            catch (Exception)
            {
                // 忽略警告记录失败，不影响主要功能
            }
        }

        /// <summary>
        /// 获取参数显示名称
        /// </summary>
        /// <param name="parameterName">参数名称</param>
        /// <returns>显示名称</returns>
        private string GetParameterDisplayName(string parameterName)
        {
            // 通过反射获取DisplayName特性
            try
            {
                var property = typeof(WorkQualityParametersModel.WorkQualityParameters).GetProperty(parameterName);
                var displayNameAttr = property?.GetCustomAttribute<System.ComponentModel.DisplayNameAttribute>();
                return displayNameAttr?.DisplayName ?? parameterName;
            }
            catch
            {
                return parameterName;
            }
        }

        private void OkButton_Click(object sender, EventArgs e)
        {
            try
            {
                // 保存当前输入的参数值
                SaveParameterValues();

                // 验证参数
                var validationResult = _parameters.Validate();
                if (!validationResult.IsValid)
                {
                    // 构建详细的错误信息
                    string errorMessage = "参数验证失败，请检查以下问题：\n\n";
                    for (int i = 0; i < validationResult.Errors.Count; i++)
                    {
                        errorMessage += $"{i + 1}. {validationResult.Errors[i]}\n";
                    }
                    errorMessage += "\n请修正上述问题后重试。";

                    // 显示错误提示，用户点击确定后返回到输入窗体继续编辑
                    MessageBox.Show(errorMessage, "参数验证失败",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);

                    // 验证失败时不关闭窗体，让用户继续编辑
                    return;
                }

                // 验证成功，设置确认标志并关闭窗体
                IsConfirmed = true;
                this.DialogResult = DialogResult.OK;
                this.Close();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"保存参数时发生错误: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void CancelButton_Click(object sender, EventArgs e)
        {
            IsConfirmed = false;
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }

        private void ResetButton_Click(object sender, EventArgs e)
        {
            try
            {
                // 重置为默认值
                var defaultParameters = new WorkQualityParametersModel().GetCurrentParameters();
                
                // 复制默认值到当前参数
                var properties = typeof(WorkQualityParametersModel.WorkQualityParameters).GetProperties()
                    .Where(p => p.CanRead && p.CanWrite);

                foreach (var property in properties)
                {
                    var defaultValue = property.GetValue(defaultParameters);
                    property.SetValue(_parameters, defaultValue);
                }

                // 重新加载界面值
                LoadParameterValues();

                MessageBox.Show("参数已重置为默认值", "重置完成",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"重置参数时发生错误: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }
}


