using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.IO;
using Newtonsoft.Json;
using RESClient.MVP.Base;

namespace RESClient.MVP.Models
{
    /// <summary>
    /// 楼栋基本信息参数数据模型
    /// </summary>
    public class BuildingInfoParametersModel : BaseModel
    {
        /// <summary>
        /// 楼栋基本信息参数数据结构
        /// </summary>
        public class BuildingInfoParameters
        {
            [DisplayName("建成年代")]
            [Description("建筑物建成的年代或时期")]
            public string BuildingAge { get; set; } = "";

            [DisplayName("勘验日期")]
            [Description("建筑物勘验的具体日期")]
            public string InspectionDate { get; set; } = "";

            /// <summary>
            /// 验证参数完整性
            /// </summary>
            /// <returns>验证结果</returns>
            public (bool IsValid, List<string> Errors) Validate()
            {
                var errors = new List<string>();

                // 移除所有验证逻辑，允许自由文本输入
                // 不再要求必填或格式验证

                return (errors.Count == 0, errors);
            }

            /// <summary>
            /// 获取变量映射字典
            /// </summary>
            /// <returns>变量映射</returns>
            public Dictionary<string, string> GetVariableMapping()
            {
                return new Dictionary<string, string>
                {
                    { "${建成年代}", string.IsNullOrWhiteSpace(BuildingAge) ? "\\" : BuildingAge },
                    { "${勘验日期}", string.IsNullOrWhiteSpace(InspectionDate) ? "\\" : InspectionDate }
                };
            }
        }

        private readonly string _configFilePath;
        private BuildingInfoParameters _currentParameters;

        public BuildingInfoParametersModel()
        {
            // 配置文件保存在应用程序数据目录
            string appDataPath = Path.Combine(
                Environment.GetFolderPath(Environment.SpecialFolder.LocalApplicationData),
                "RESClient");
            
            if (!Directory.Exists(appDataPath))
            {
                Directory.CreateDirectory(appDataPath);
            }

            _configFilePath = Path.Combine(appDataPath, "building_info_parameters.json");
            _currentParameters = LoadParameters();
        }

        /// <summary>
        /// 获取当前参数
        /// </summary>
        /// <returns>当前参数</returns>
        public BuildingInfoParameters GetCurrentParameters()
        {
            return _currentParameters ?? CreateDefaultParameters();
        }

        /// <summary>
        /// 保存参数
        /// </summary>
        /// <param name="parameters">要保存的参数</param>
        /// <returns>是否保存成功</returns>
        public bool SaveParameters(BuildingInfoParameters parameters)
        {
            try
            {
                if (parameters == null)
                {
                    return false;
                }

                // 验证参数
                var validationResult = parameters.Validate();
                if (!validationResult.IsValid)
                {
                    return false;
                }

                // 序列化为JSON
                string json = JsonConvert.SerializeObject(parameters, Formatting.Indented);

                // 写入文件
                File.WriteAllText(_configFilePath, json, System.Text.Encoding.UTF8);

                // 更新当前参数
                _currentParameters = parameters;

                return true;
            }
            catch (Exception ex)
            {
                HandleException(ex);
                return false;
            }
        }

        /// <summary>
        /// 加载参数
        /// </summary>
        /// <returns>加载的参数</returns>
        private BuildingInfoParameters LoadParameters()
        {
            try
            {
                if (!File.Exists(_configFilePath))
                {
                    return CreateDefaultParameters();
                }

                string json = File.ReadAllText(_configFilePath, System.Text.Encoding.UTF8);
                var parameters = JsonConvert.DeserializeObject<BuildingInfoParameters>(json);

                return parameters ?? CreateDefaultParameters();
            }
            catch (Exception ex)
            {
                HandleException(ex);
                return CreateDefaultParameters();
            }
        }

        /// <summary>
        /// 创建默认参数
        /// </summary>
        /// <returns>默认参数</returns>
        private BuildingInfoParameters CreateDefaultParameters()
        {
            return new BuildingInfoParameters
            {
                BuildingAge = "",
                InspectionDate = ""
            };
        }

        /// <summary>
        /// 重置为默认参数
        /// </summary>
        /// <returns>是否重置成功</returns>
        public bool ResetToDefault()
        {
            try
            {
                _currentParameters = CreateDefaultParameters();
                return SaveParameters(_currentParameters);
            }
            catch (Exception ex)
            {
                HandleException(ex);
                return false;
            }
        }

        /// <summary>
        /// 检查参数文件是否存在
        /// </summary>
        /// <returns>是否存在</returns>
        public bool ConfigFileExists()
        {
            return File.Exists(_configFilePath);
        }

        /// <summary>
        /// 获取配置文件路径
        /// </summary>
        /// <returns>配置文件路径</returns>
        public string GetConfigFilePath()
        {
            return _configFilePath;
        }
    }
}
