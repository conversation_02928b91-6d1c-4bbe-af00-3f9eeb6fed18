using System;
using System.IO;
using System.Threading.Tasks;
using RESClient.MVP.Models;
using RESClient.Services.Implementations;
using System.Collections.Generic;

namespace RESClient
{
    /// <summary>
    /// 测试地下室人防区域说明模块的样式保留修复
    /// </summary>
    public class TestBasementDefenseStyleFix
    {
        public static async Task TestBasementDefenseStylePreservation()
        {
            Console.WriteLine("=== 地下室人防区域说明模块样式保留测试 ===");
            Console.WriteLine();

            try
            {
                // 使用模块生成器进行测试
                var generator = new BasementDefenseModuleGenerator();

                // 设置输出路径
                string outputDir = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "测试输出");
                if (!Directory.Exists(outputDir))
                {
                    Directory.CreateDirectory(outputDir);
                }

                // 创建测试参数
                var parameters = new Dictionary<string, object>
                {
                    { "OutputDirectory", outputDir },
                    { "DataProvider", "四川省川建勘察设计院有限公司" },
                    { "DrawingName", "地下室人防区域平面图 DRG-001" },
                    { "Description", "本项目地下室设有人防区域，位于地下一层东侧，总面积约500平方米，按照人防工程设计规范进行设计和施工。" }
                };

                Console.WriteLine("开始生成地下室人防区域说明文档...");
                Console.WriteLine($"输出目录: {outputDir}");
                Console.WriteLine();

                // 生成文档
                bool result = await generator.GenerateAsync(parameters, (progress, message) =>
                {
                    Console.WriteLine($"[{progress}%] {message}");
                });

                if (result)
                {
                    string outputPath = Path.Combine(outputDir, "地下室人防区域说明.docx");
                    Console.WriteLine("✓ 文档生成成功！");
                    Console.WriteLine();
                    Console.WriteLine("请检查生成的文档：");
                    Console.WriteLine($"  文件位置: {outputPath}");
                    Console.WriteLine();
                    Console.WriteLine("验证要点：");
                    Console.WriteLine("1. 检查占位符是否被正确替换：");
                    Console.WriteLine($"   - ${{依据提供方}} → {parameters["DataProvider"]}");
                    Console.WriteLine($"   - ${{图纸名称}} → {parameters["DrawingName"]}");
                    Console.WriteLine($"   - ${{说明}} → {parameters["Description"]}");
                    Console.WriteLine();
                    Console.WriteLine("2. 检查文档样式是否保持一致：");
                    Console.WriteLine("   - 字体、字号是否与模板一致");
                    Console.WriteLine("   - 文字颜色是否保持原样");
                    Console.WriteLine("   - 段落对齐方式是否正确");
                    Console.WriteLine("   - 行间距、段间距是否保持");
                    Console.WriteLine("   - 表格格式是否完整");
                    Console.WriteLine();
                    Console.WriteLine("3. 对比原始模板文件：");
                    Console.WriteLine("   - 除了占位符内容外，其他格式应完全一致");
                    Console.WriteLine("   - 不应出现格式丢失或样式变化");

                    // 尝试打开文件所在目录
                    try
                    {
                        System.Diagnostics.Process.Start("explorer.exe", outputDir);
                        Console.WriteLine();
                        Console.WriteLine("已自动打开输出目录");
                    }
                    catch
                    {
                        // 忽略打开目录失败的错误
                    }
                }
                else
                {
                    Console.WriteLine("✗ 文档生成失败！");
                    Console.WriteLine("请检查：");
                    Console.WriteLine("1. 模板文件是否存在");
                    Console.WriteLine("2. 输出目录是否可写");
                    Console.WriteLine("3. 是否有其他程序占用文件");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"✗ 测试过程中发生异常: {ex.Message}");
                Console.WriteLine($"详细信息: {ex}");
            }

        }
    }
}
