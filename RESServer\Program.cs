using RESServer.Models;
using RESServer.Services;
using RESServer.Configuration;
using RESServer.Data;
using RESServer.Middleware;
using Microsoft.AspNetCore.Server.Kestrel.Core;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.EntityFrameworkCore;
using Microsoft.IdentityModel.Tokens;
using Microsoft.OpenApi.Models;
using System.Reflection;
using System.Text;

var builder = WebApplication.CreateBuilder(args);

// Configure Kestrel to handle large responses
builder.Services.Configure<KestrelServerOptions>(options =>
{
    options.Limits.MaxRequestBodySize = 100_000_000; // ~100MB
    options.Limits.MaxResponseBufferSize = 100_000_000; // ~100MB
});

builder.Services.Configure<IISServerOptions>(options =>
{
    options.MaxRequestBodySize = 100_000_000; // ~100MB
});

// Add services to the container.
builder.Services.AddControllers(options => 
{
    options.OutputFormatters.RemoveType<Microsoft.AspNetCore.Mvc.Formatters.StreamOutputFormatter>();
}).AddJsonOptions(options =>
{
    // 禁用响应的JSON转义，确保大型响应正确返回
    options.JsonSerializerOptions.WriteIndented = false;
    options.JsonSerializerOptions.PropertyNamingPolicy = System.Text.Json.JsonNamingPolicy.CamelCase;
});

// Configure Gemini API settings
builder.Services.Configure<GeminiConfig>(builder.Configuration.GetSection("Gemini"));
builder.Services.AddHttpClient();
builder.Services.AddSingleton<GeminiService>();
builder.Services.AddSingleton<FileConversionService>();

// 注册 AccoreConsoleService 用于处理 DWG 文件
builder.Services.AddSingleton<AccoreConsoleService>();

// 添加后台服务，定期清理过期的临时文件
builder.Services.AddHostedService<TempFileCleanupService>();

// 配置认证相关服务
builder.Services.Configure<AuthConfig>(builder.Configuration.GetSection("Auth"));

// 配置数据库
builder.Services.AddDbContext<AuthDbContext>(options =>
    options.UseSqlite(builder.Configuration.GetConnectionString("AuthDatabase")));

// 注册认证服务
builder.Services.AddScoped<IAuthService, AuthService>();

// 配置 JWT 认证
var authConfig = builder.Configuration.GetSection("Auth").Get<AuthConfig>();
if (authConfig?.EnableAuthentication == true)
{
    builder.Services.AddAuthentication(JwtBearerDefaults.AuthenticationScheme)
        .AddJwtBearer(options =>
        {
            options.TokenValidationParameters = new TokenValidationParameters
            {
                ValidateIssuerSigningKey = true,
                IssuerSigningKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(authConfig.Jwt.SecretKey)),
                ValidateIssuer = true,
                ValidIssuer = authConfig.Jwt.Issuer,
                ValidateAudience = true,
                ValidAudience = authConfig.Jwt.Audience,
                ValidateLifetime = true,
                ClockSkew = TimeSpan.Zero
            };
        });

    builder.Services.AddAuthorization();
}

// 配置 Swagger/OpenAPI
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen(c =>
{
    c.SwaggerDoc("v1", new OpenApiInfo
    {
        Title = "RESServer API",
        Version = "v1",
        Description = "RESServer API，提供文件处理和分析功能",
        Contact = new OpenApiContact
        {
            Name = "RESServer 团队"
        }
    });

    // 启用 XML 文档
    var xmlFile = $"{Assembly.GetExecutingAssembly().GetName().Name}.xml";
    var xmlPath = Path.Combine(AppContext.BaseDirectory, xmlFile);
    if (File.Exists(xmlPath))
    {
        c.IncludeXmlComments(xmlPath);
    }

    // 配置文件上传
    c.OperationFilter<FileUploadOperationFilter>();

    // 配置 JWT 认证
    if (authConfig?.EnableAuthentication == true)
    {
        c.AddSecurityDefinition("Bearer", new OpenApiSecurityScheme
        {
            Description = "JWT Authorization header using the Bearer scheme. Example: \"Authorization: Bearer {token}\"",
            Name = "Authorization",
            In = ParameterLocation.Header,
            Type = SecuritySchemeType.ApiKey,
            Scheme = "Bearer"
        });

        c.AddSecurityRequirement(new OpenApiSecurityRequirement
        {
            {
                new OpenApiSecurityScheme
                {
                    Reference = new OpenApiReference
                    {
                        Type = ReferenceType.SecurityScheme,
                        Id = "Bearer"
                    }
                },
                new string[] {}
            }
        });
    }
});

var app = builder.Build();

// 确保数据库已创建
using (var scope = app.Services.CreateScope())
{
    var context = scope.ServiceProvider.GetRequiredService<AuthDbContext>();
    context.Database.EnsureCreated();
}

// Configure the HTTP request pipeline.
if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI(c =>
    {
        c.SwaggerEndpoint("/swagger/v1/swagger.json", "RESServer API v1");
        c.RoutePrefix = "swagger"; // 设置 Swagger UI 在根路径
        c.DocExpansion(Swashbuckle.AspNetCore.SwaggerUI.DocExpansion.None);
        c.DefaultModelsExpandDepth(-1); // 隐藏模型部分
    });
}

app.UseHttpsRedirection();
app.UseStaticFiles();

// 使用自定义认证中间件（根据配置决定是否启用）
app.UseCustomAuthentication();

// 如果启用了认证，使用标准的认证和授权中间件
if (authConfig?.EnableAuthentication == true)
{
    app.UseAuthentication();
    app.UseAuthorization();
}

app.MapControllers();

app.Run();
