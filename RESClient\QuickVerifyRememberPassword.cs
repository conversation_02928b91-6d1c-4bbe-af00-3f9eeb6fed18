using System;
using System.Windows.Forms;
using RESClient.Services;

namespace RESClient
{
    /// <summary>
    /// 快速验证记住密码功能
    /// </summary>
    public static class QuickVerifyRememberPassword
    {
        /// <summary>
        /// 快速验证功能是否正常工作
        /// </summary>
        public static void QuickVerify()
        {
            Console.WriteLine("=== 快速验证记住密码功能 ===");
            Console.WriteLine();
            
            bool allTestsPassed = true;
            
            try
            {
                // 测试1: AuthConfigService基本功能
                Console.WriteLine("测试1: AuthConfigService基本功能");
                if (TestAuthConfigService())
                {
                    Console.WriteLine("   ✅ 通过");
                }
                else
                {
                    Console.WriteLine("   ❌ 失败");
                    allTestsPassed = false;
                }
                
                // 测试2: 密码加密解密
                Console.WriteLine("测试2: 密码加密解密");
                if (TestPasswordEncryption())
                {
                    Console.WriteLine("   ✅ 通过");
                }
                else
                {
                    Console.WriteLine("   ❌ 失败");
                    allTestsPassed = false;
                }
                
                // 测试3: 凭据清除功能
                Console.WriteLine("测试3: 凭据清除功能");
                if (TestCredentialClearing())
                {
                    Console.WriteLine("   ✅ 通过");
                }
                else
                {
                    Console.WriteLine("   ❌ 失败");
                    allTestsPassed = false;
                }
                
                // 测试4: 边界条件
                Console.WriteLine("测试4: 边界条件");
                if (TestEdgeCases())
                {
                    Console.WriteLine("   ✅ 通过");
                }
                else
                {
                    Console.WriteLine("   ❌ 失败");
                    allTestsPassed = false;
                }
                
                Console.WriteLine();
                if (allTestsPassed)
                {
                    Console.WriteLine("🎉 所有测试通过！记住密码功能工作正常。");
                }
                else
                {
                    Console.WriteLine("⚠️ 部分测试失败，请检查实现。");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ 验证过程中发生错误: {ex.Message}");
                allTestsPassed = false;
            }
            
            Console.WriteLine();
        }
        
        /// <summary>
        /// 测试AuthConfigService基本功能
        /// </summary>
        private static bool TestAuthConfigService()
        {
            try
            {
                var authConfig = AuthConfigService.Instance;
                
                // 确保开始时没有凭据
                authConfig.ClearRememberedCredentials();
                
                // 测试保存凭据
                authConfig.SaveRememberedCredentials("testuser", "testpass", true);
                
                // 测试加载凭据
                var credentials = authConfig.LoadRememberedCredentials();
                
                if (!credentials.HasValue)
                {
                    Console.WriteLine("     错误: 无法加载保存的凭据");
                    return false;
                }
                
                if (credentials.Value.Username != "testuser")
                {
                    Console.WriteLine("     错误: 用户名不匹配");
                    return false;
                }
                
                if (credentials.Value.Password != "testpass")
                {
                    Console.WriteLine("     错误: 密码不匹配");
                    return false;
                }
                
                // 清理
                authConfig.ClearRememberedCredentials();
                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"     异常: {ex.Message}");
                return false;
            }
        }
        
        /// <summary>
        /// 测试密码加密解密
        /// </summary>
        private static bool TestPasswordEncryption()
        {
            try
            {
                var authConfig = AuthConfigService.Instance;
                
                // 测试各种密码
                string[] testPasswords = {
                    "simple",
                    "Complex@123!",
                    "中文密码",
                    "!@#$%^&*()",
                    "a",
                    new string('x', 100) // 长密码
                };
                
                foreach (var password in testPasswords)
                {
                    authConfig.SaveRememberedCredentials("user", password, true);
                    var credentials = authConfig.LoadRememberedCredentials();
                    
                    if (!credentials.HasValue || credentials.Value.Password != password)
                    {
                        Console.WriteLine($"     错误: 密码 '{password}' 加密解密失败");
                        return false;
                    }
                }
                
                // 清理
                authConfig.ClearRememberedCredentials();
                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"     异常: {ex.Message}");
                return false;
            }
        }
        
        /// <summary>
        /// 测试凭据清除功能
        /// </summary>
        private static bool TestCredentialClearing()
        {
            try
            {
                var authConfig = AuthConfigService.Instance;
                
                // 保存凭据
                authConfig.SaveRememberedCredentials("user", "pass", true);
                
                // 验证保存成功
                var credentials = authConfig.LoadRememberedCredentials();
                if (!credentials.HasValue)
                {
                    Console.WriteLine("     错误: 凭据保存失败");
                    return false;
                }
                
                // 清除凭据
                authConfig.ClearRememberedCredentials();
                
                // 验证清除成功
                var clearedCredentials = authConfig.LoadRememberedCredentials();
                if (clearedCredentials.HasValue)
                {
                    Console.WriteLine("     错误: 凭据清除失败");
                    return false;
                }
                
                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"     异常: {ex.Message}");
                return false;
            }
        }
        
        /// <summary>
        /// 测试边界条件
        /// </summary>
        private static bool TestEdgeCases()
        {
            try
            {
                var authConfig = AuthConfigService.Instance;
                
                // 测试空密码
                authConfig.SaveRememberedCredentials("user", "", true);
                var credentials = authConfig.LoadRememberedCredentials();
                if (!credentials.HasValue || credentials.Value.Password != "")
                {
                    Console.WriteLine("     错误: 空密码处理失败");
                    return false;
                }
                
                // 测试不记住密码
                authConfig.SaveRememberedCredentials("user", "pass", false);
                var noRememberCredentials = authConfig.LoadRememberedCredentials();
                if (noRememberCredentials.HasValue)
                {
                    Console.WriteLine("     错误: 不记住密码时仍然保存了凭据");
                    return false;
                }
                
                // 测试空用户名
                authConfig.SaveRememberedCredentials("", "pass", true);
                var emptyUserCredentials = authConfig.LoadRememberedCredentials();
                if (emptyUserCredentials.HasValue)
                {
                    Console.WriteLine("     错误: 空用户名时仍然保存了凭据");
                    return false;
                }
                
                // 清理
                authConfig.ClearRememberedCredentials();
                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"     异常: {ex.Message}");
                return false;
            }
        }
        
        /// <summary>
        /// 显示功能状态
        /// </summary>
        public static void ShowFeatureStatus()
        {
            Console.WriteLine("=== 记住密码功能状态 ===");
            Console.WriteLine();
            
            try
            {
                var authConfig = AuthConfigService.Instance;
                
                // 检查配置
                Console.WriteLine("📋 配置状态:");
                Console.WriteLine($"   自动登录检查: {authConfig.EnableAutoLoginCheck}");
                Console.WriteLine($"   记住登录状态: {authConfig.RememberLoginState}");
                Console.WriteLine($"   超时提醒时间: {authConfig.LoginTimeoutWarningMinutes} 分钟");
                
                // 检查当前凭据
                Console.WriteLine();
                Console.WriteLine("🔐 当前凭据状态:");
                var credentials = authConfig.LoadRememberedCredentials();
                if (credentials.HasValue)
                {
                    Console.WriteLine($"   用户名: {credentials.Value.Username}");
                    Console.WriteLine($"   密码: {new string('*', credentials.Value.Password.Length)}");
                    Console.WriteLine("   状态: 已保存");
                }
                else
                {
                    Console.WriteLine("   状态: 无保存的凭据");
                }
                
                // 检查存储位置
                Console.WriteLine();
                Console.WriteLine("📁 存储信息:");
                var appDataPath = Environment.GetFolderPath(Environment.SpecialFolder.LocalApplicationData);
                var credentialsPath = System.IO.Path.Combine(appDataPath, "RESClient", "remembered_credentials.json");
                Console.WriteLine($"   存储位置: {credentialsPath}");
                Console.WriteLine($"   文件存在: {System.IO.File.Exists(credentialsPath)}");
                
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ 获取状态失败: {ex.Message}");
            }
            
            Console.WriteLine();
        }
    }
}
