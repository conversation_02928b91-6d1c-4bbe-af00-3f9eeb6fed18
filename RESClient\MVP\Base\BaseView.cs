using System;
using System.Windows.Forms;
using RESClient.MVP.Interfaces;

namespace RESClient.MVP.Base
{
    /// <summary>
    /// 视图基类
    /// </summary>
    public class BaseView : Form, IView
    {
        /// <summary>
        /// 显示消息
        /// </summary>
        /// <param name="message">消息内容</param>
        /// <param name="title">消息标题</param>
        public void ShowMessage(string message, string title)
        {
            MessageBox.Show(this, message, title, MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        /// <summary>
        /// 显示错误消息
        /// </summary>
        /// <param name="message">错误消息内容</param>
        /// <param name="title">错误消息标题</param>
        public void ShowError(string message, string title)
        {
            MessageBox.Show(this, message, title, MessageBoxButtons.OK, MessageBoxIcon.Error);
        }

        /// <summary>
        /// 显示确认对话框
        /// </summary>
        /// <param name="message">确认消息</param>
        /// <param name="title">确认标题</param>
        /// <returns>用户选择结果</returns>
        protected bool ShowConfirmation(string message, string title)
        {
            return MessageBox.Show(this, message, title, MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.Yes;
        }

        private void InitializeComponent()
        {
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(BaseView));
            this.SuspendLayout();
            // 
            // BaseView
            // 
            this.ClientSize = new System.Drawing.Size(894, 557);
            this.Icon = ((System.Drawing.Icon)(resources.GetObject("$this.Icon")));
            this.Name = "BaseView";
            this.Text = "XSENTRY_RES";
            this.ResumeLayout(false);

        }
    }
} 