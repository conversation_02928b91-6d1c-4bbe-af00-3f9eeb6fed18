using System;
using System.Drawing;
using System.Threading.Tasks;
using System.Windows.Forms;
using RESClient.Models;
using RESClient.Services;

namespace RESClient.Views
{
    /// <summary>
    /// 登录窗体
    /// </summary>
    public partial class LoginForm : Form
    {
        private readonly IAuthService _authService;
        private bool _isLoggingIn = false;
        private bool _hasCheckedServerStatus = false;

        public LoginForm(IAuthService authService)
        {
            _authService = authService;
            InitializeComponent();
            InitializeUI();
        }

        private void InitializeUI()
        {
            // 绑定事件
            btnLogin.Click += BtnLogin_Click;
            btnCancel.Click += BtnCancel_Click;
            btnClearCredentials.Click += BtnClearCredentials_Click;
            txtPassword.KeyPress += TxtPassword_KeyPress;
            txtUsername.KeyPress += TxtUsername_KeyPress;
            chkRememberPassword.CheckedChanged += ChkRememberPassword_CheckedChanged;
            Load += LoginForm_Load;

            // 设置默认用户名
            txtUsername.Text = "admin";

            // 加载记住的密码凭据
            LoadRememberedCredentials();
        }

        private void LoginForm_Load(object sender, EventArgs e)
        {
            // 窗体加载完成后设置焦点
            txtPassword.Focus();

            // 注意：不在Load事件中检查服务器状态，避免与OnShown重复
            // 服务器状态检查将在OnShown事件中进行
        }

        private void TxtUsername_KeyPress(object sender, KeyPressEventArgs e)
        {
            if (e.KeyChar == (char)Keys.Enter)
            {
                txtPassword.Focus();
                e.Handled = true;
            }
        }

        private void TxtPassword_KeyPress(object sender, KeyPressEventArgs e)
        {
            if (e.KeyChar == (char)Keys.Enter)
            {
                BtnLogin_Click(sender, e);
                e.Handled = true;
            }
        }

        private async void BtnLogin_Click(object sender, EventArgs e)
        {
            if (_isLoggingIn)
                return;

            if (string.IsNullOrWhiteSpace(txtUsername.Text))
            {
                ShowStatus("请输入用户名", Color.Red);
                txtUsername.Focus();
                return;
            }

            if (string.IsNullOrWhiteSpace(txtPassword.Text))
            {
                ShowStatus("请输入密码", Color.Red);
                txtPassword.Focus();
                return;
            }

            await PerformLogin();
        }

        private void BtnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }

        private void BtnClearCredentials_Click(object sender, EventArgs e)
        {
            var result = MessageBox.Show(
                "确定要清除已保存的登录凭据吗？\n\n清除后将不再自动填充用户名和密码。",
                "确认清除",
                MessageBoxButtons.YesNo,
                MessageBoxIcon.Question,
                MessageBoxDefaultButton.Button2
            );

            if (result == DialogResult.Yes)
            {
                try
                {
                    var authConfig = Services.AuthConfigService.Instance;
                    authConfig.ClearRememberedCredentials();

                    // 清空当前输入框
                    txtUsername.Text = "admin"; // 恢复默认用户名
                    txtPassword.Clear();
                    chkRememberPassword.Checked = false;

                    // 隐藏清除按钮
                    btnClearCredentials.Visible = false;

                    ShowStatus("已清除保存的登录凭据", Color.Green);
                }
                catch (Exception ex)
                {
                    ShowStatus($"清除凭据失败：{ex.Message}", Color.Red);
                }
            }
        }

        private void ChkRememberPassword_CheckedChanged(object sender, EventArgs e)
        {
            // 当取消勾选记住密码时，显示清除按钮（如果有已保存的凭据）
            if (!chkRememberPassword.Checked)
            {
                var authConfig = Services.AuthConfigService.Instance;
                var credentials = authConfig.LoadRememberedCredentials();
                btnClearCredentials.Visible = credentials.HasValue;
            }
            else
            {
                btnClearCredentials.Visible = false;
            }
        }

        private async Task PerformLogin()
        {
            _isLoggingIn = true;
            SetControlsEnabled(false);
            ShowProgress(true);
            ShowStatus("正在登录...", Color.Blue);

            try
            {
                var request = new LoginRequest
                {
                    Username = txtUsername.Text.Trim(),
                    Password = txtPassword.Text
                };

                var response = await _authService.LoginAsync(request);

                if (response.Success)
                {
                    // 保存记住的密码凭据
                    SaveRememberedCredentials();

                    ShowStatus("登录成功", Color.Green);
                    await Task.Delay(500); // 短暂显示成功消息
                    this.DialogResult = DialogResult.OK;
                    this.Close();
                }
                else
                {
                    ShowStatus(response.Message, Color.Red);
                    txtPassword.Clear();
                    txtPassword.Focus();
                }
            }
            catch (Exception ex)
            {
                ShowStatus($"登录失败：{ex.Message}", Color.Red);
            }
            finally
            {
                _isLoggingIn = false;
                SetControlsEnabled(true);
                ShowProgress(false);
            }
        }

        private void SetControlsEnabled(bool enabled)
        {
            txtUsername.Enabled = enabled;
            txtPassword.Enabled = enabled;
            btnLogin.Enabled = enabled;
            btnCancel.Enabled = enabled;
        }

        private void ShowProgress(bool show)
        {
            progressBar.Visible = show;
        }

        private void ShowStatus(string message, Color color)
        {
            lblStatus.Text = message;
            lblStatus.ForeColor = color;
        }

        protected override void OnShown(EventArgs e)
        {
            base.OnShown(e);

            // 只检查一次服务器状态，避免重复调用
            if (!_hasCheckedServerStatus)
            {
                _hasCheckedServerStatus = true;
                // 窗体显示时检查服务器状态 - 不等待，避免阻塞UI
                _ = CheckServerStatusAsync();
            }
        }

        private async Task CheckServerStatusAsync()
        {
            try
            {
                // 确保在UI线程上更新状态
                if (InvokeRequired)
                {
                    Invoke(new Action(() => ShowStatus("正在检查服务器状态...", Color.Blue)));
                }
                else
                {
                    ShowStatus("正在检查服务器状态...", Color.Blue);
                }

                // 使用不触发事件的版本，避免循环调用
                var status = await _authService.GetServerStatusAsync(triggerEvent: false);

                // 确保在UI线程上处理结果
                if (InvokeRequired)
                {
                    Invoke(new Action(() => HandleServerStatus(status)));
                }
                else
                {
                    HandleServerStatus(status);
                }
            }
            catch (Exception ex)
            {
                // 确保在UI线程上显示错误
                if (InvokeRequired)
                {
                    Invoke(new Action(() => ShowStatus($"检查服务器状态失败：{ex.Message}", Color.Red)));
                }
                else
                {
                    ShowStatus($"检查服务器状态失败：{ex.Message}", Color.Red);
                }
            }
        }

        private void HandleServerStatus(AuthenticationStatusInfo status)
        {
            // 确保在UI线程上执行
            if (InvokeRequired)
            {
                Invoke(new Action(() => HandleServerStatus(status)));
                return;
            }

            if (status.Status == AuthenticationStatus.ServerConnectionFailed)
            {
                ShowStatus("无法连接到服务器", Color.Red);
                SetControlsEnabled(false);
            }
            else if (status.Status == AuthenticationStatus.Disabled)
            {
                ShowStatus("服务器未启用认证功能", Color.Orange);
                DialogResult = DialogResult.Ignore; // 表示不需要登录
                Close();
            }
            else if (status.Status == AuthenticationStatus.LoggedIn)
            {
                ShowStatus("用户已登录", Color.Green);
                DialogResult = DialogResult.OK; // 已经登录，直接成功
                Close();
            }
            else
            {
                ShowStatus("请输入用户名和密码", Color.Black);
            }
        }

        /// <summary>
        /// 加载记住的密码凭据
        /// </summary>
        private void LoadRememberedCredentials()
        {
            try
            {
                var authConfig = Services.AuthConfigService.Instance;
                var credentials = authConfig.LoadRememberedCredentials();

                if (credentials.HasValue)
                {
                    txtUsername.Text = credentials.Value.Username;
                    txtPassword.Text = credentials.Value.Password;
                    chkRememberPassword.Checked = true;

                    // 显示清除按钮，但只在未勾选记住密码时显示
                    btnClearCredentials.Visible = false;
                }
                else
                {
                    // 没有保存的凭据，隐藏清除按钮
                    btnClearCredentials.Visible = false;
                }
            }
            catch (Exception ex)
            {
                // 记录错误但不影响登录界面显示
                System.Diagnostics.Debug.WriteLine($"加载记住的凭据失败: {ex.Message}");
                btnClearCredentials.Visible = false;
            }
        }

        /// <summary>
        /// 保存记住的密码凭据
        /// </summary>
        private void SaveRememberedCredentials()
        {
            try
            {
                var authConfig = Services.AuthConfigService.Instance;
                authConfig.SaveRememberedCredentials(
                    txtUsername.Text.Trim(),
                    txtPassword.Text,
                    chkRememberPassword.Checked
                );
            }
            catch (Exception ex)
            {
                // 记录错误但不影响登录流程
                System.Diagnostics.Debug.WriteLine($"保存记住的凭据失败: {ex.Message}");
            }
        }
    }
}
