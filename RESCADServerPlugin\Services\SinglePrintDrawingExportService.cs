using Autodesk.AutoCAD.ApplicationServices;
using Autodesk.AutoCAD.DatabaseServices;
using Autodesk.AutoCAD.EditorInput;
using Autodesk.AutoCAD.Geometry;
using RESCADServerPlugin.Configuration;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading;
using System.Threading.Tasks;

namespace RESCADServerPlugin.Services
{
    public class SinglePrintDrawingExportService
    {
        private readonly string _tempDirectory;
        private readonly ConcurrentQueue<ProcessingRequest> _processingQueue = new ConcurrentQueue<ProcessingRequest>();
        private readonly SemaphoreSlim _processingLock = new SemaphoreSlim(1, 1);
        private bool _isProcessing = false;
        private readonly string _logFilePath;
        
        // Singleton instance
        private static SinglePrintDrawingExportService _instance;
        private static readonly object _lock = new object();
        
        public static SinglePrintDrawingExportService Instance
        {
            get
            {
                if (_instance == null)
                {
                    lock (_lock)
                    {
                        if (_instance == null)
                        {
                            _instance = new SinglePrintDrawingExportService();
                        }
                    }
                }
                return _instance;
            }
        }
        
        // Private constructor to enforce singleton pattern
        private SinglePrintDrawingExportService()
        {
            // Create a temporary directory for processing files
            string pluginDir = Path.GetDirectoryName(System.Reflection.Assembly.GetExecutingAssembly().Location);
            _tempDirectory = Path.Combine(pluginDir, "TempSinglePrintDrawings");
            
            if (!Directory.Exists(_tempDirectory))
            {
                Directory.CreateDirectory(_tempDirectory);
            }
            
            // 创建日志文件路径
            _logFilePath = Path.Combine(pluginDir, "SinglePrintDrawingExportService.log");
            LogToFile("SinglePrintDrawingExportService 初始化", true);
            
            // Start the processing thread
            Task.Run(() => ProcessQueueAsync());
        }
        
        /// <summary>
        /// 记录日志到文件
        /// </summary>
        private void LogToFile(string message, bool clearLog = false)
        {
            try
            {
                string logMessage = $"[{DateTime.Now:yyyy-MM-dd HH:mm:ss.fff}] {message}";
                
                // 创建包含调用栈信息的更详细日志
                var stackTrace = new StackTrace(true);
                string callerInfo = "";
                
                if (stackTrace.FrameCount > 1)
                {
                    var frame = stackTrace.GetFrame(1);
                    var method = frame.GetMethod();
                    callerInfo = $" [{method.DeclaringType}.{method.Name}]";
                }
                
                logMessage += callerInfo;
                
                // 将日志写入文件
                using (StreamWriter writer = new StreamWriter(_logFilePath, !clearLog, Encoding.UTF8))
                {
                    writer.WriteLine(logMessage);
                }
                
                // 同时输出到调试窗口
                System.Diagnostics.Debug.WriteLine(logMessage);
                
                // 如果有活动文档，也输出到命令行
                Document doc = Application.DocumentManager.MdiActiveDocument;
                if (doc != null)
                {
                    doc.Editor.WriteMessage($"\n{logMessage}");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"记录日志时出错: {ex.Message}");
            }
        }
        
        // Request processing class to hold request information
        private class ProcessingRequest
        {
            public string FilePath { get; set; }
            public HashSet<string> ProcessedDrawingNumbers { get; set; }
            public TaskCompletionSource<ExportResult> CompletionSource { get; set; }
        }
        
        // Result class for returning export information
        public class ExportResult
        {
            public string DrawingFilePath { get; set; }
            public string DrawingNumber { get; set; }
            public int CurrentIndex { get; set; }
            public int TotalCount { get; set; }
            public bool HasMoreDrawings { get; set; }
            public List<string> AllDrawingNumbers { get; set; }
        }

        /// <summary>
        /// 导出单个打印图纸（每次调用仅导出一个未处理过的图纸）
        /// </summary>
        /// <param name="dwgFilePath">源DWG文件路径</param>
        /// <param name="processedDrawingNumbers">已处理过的图纸编号集合（可为null）</param>
        /// <returns>导出结果</returns>
        public Task<ExportResult> ExportSinglePrintDrawing(string dwgFilePath, HashSet<string> processedDrawingNumbers = null)
        {
            Document doc = Application.DocumentManager.MdiActiveDocument;
            if (doc != null)
            {
                doc.Editor.WriteMessage($"\n添加单个打印图导出请求到队列: {Path.GetFileName(dwgFilePath)}");
            }
            
            // 如果未提供已处理集合，创建一个空集合
            if (processedDrawingNumbers == null)
            {
                processedDrawingNumbers = new HashSet<string>();
            }
            
            // Create a completion source for this request
            var completionSource = new TaskCompletionSource<ExportResult>();
            
            // Add to the processing queue
            _processingQueue.Enqueue(new ProcessingRequest
            {
                FilePath = dwgFilePath,
                ProcessedDrawingNumbers = processedDrawingNumbers,
                CompletionSource = completionSource
            });
            
            // Trigger processing if not already running
            TriggerProcessing();
            
            return completionSource.Task;
        }
        
        /// <summary>
        /// Triggers the queue processing if it's not already running
        /// </summary>
        private void TriggerProcessing()
        {
            if (!_isProcessing)
            {
                lock (_lock)
                {
                    if (!_isProcessing)
                    {
                        _isProcessing = true;
                        Task.Run(() => ProcessQueueAsync());
                    }
                }
            }
        }
        
        /// <summary>
        /// Processes the queue of requests
        /// </summary>
        private async Task ProcessQueueAsync()
        {
            while (_processingQueue.TryDequeue(out ProcessingRequest request))
            {
                Document doc = Application.DocumentManager.MdiActiveDocument;
                if (doc != null)
                {
                    doc.Editor.WriteMessage($"\n开始处理文件: {Path.GetFileName(request.FilePath)}");
                    doc.Editor.WriteMessage($"\n当前队列中剩余任务: {_processingQueue.Count}");
                }
                
                try
                {
                    // Ensure only one file is processed at a time
                    await _processingLock.WaitAsync();
                    
                    try
                    {
                        // Process the file
                        ExportResult result = await ProcessDwgFileInternal(request.FilePath, request.ProcessedDrawingNumbers);
                        request.CompletionSource.SetResult(result);
                    }
                    catch (Exception ex)
                    {
                        request.CompletionSource.SetException(ex);
                    }
                    finally
                    {
                        _processingLock.Release();
                    }
                }
                catch (Exception ex)
                {
                    request.CompletionSource.SetException(ex);
                    
                    if (doc != null)
                    {
                        doc.Editor.WriteMessage($"\n处理队列任务时出错: {ex.Message}");
                    }
                }
            }
            
            // No more items in the queue
            _isProcessing = false;
        }
        
        /// <summary>
        /// Internal method to process a DWG file - exports a single print drawing not previously processed
        /// </summary>
        private Task<ExportResult> ProcessDwgFileInternal(string dwgFilePath, HashSet<string> processedDrawingNumbers)
        {
            var tcs = new TaskCompletionSource<ExportResult>();
            LogToFile($"准备在AutoCAD主线程上下文中处理单个打印图: {dwgFilePath}");

            try
            {
                Application.DocumentManager.ExecuteInApplicationContext(callback => 
                {
                    LogToFile($"进入AutoCAD主线程上下文执行 ProcessSinglePrintDrawingCore: {dwgFilePath}");
                    ExportResult result = ProcessSinglePrintDrawingCore(dwgFilePath, processedDrawingNumbers);
                    tcs.SetResult(result);
                    LogToFile("ProcessSinglePrintDrawingCore 执行完毕并设置结果");
                }, null);
            }
            catch (Exception ex)
            {
                LogToFile($"调度到主线程时出错 (ProcessDwgFileInternal): {ex.Message}\n{ex.StackTrace}");
                tcs.SetException(ex);
            }
            return tcs.Task;
        }
        
        /// <summary>
        /// 核心处理逻辑：处理单个未导出的打印图
        /// </summary>
        private ExportResult ProcessSinglePrintDrawingCore(string dwgFilePath, HashSet<string> processedDrawingNumbers)
        {
            LogToFile($"ProcessSinglePrintDrawingCore 开始执行: {dwgFilePath}");
            Database sourceDb = null;
            string sessionId = Guid.NewGuid().ToString("N");
            string sessionDir = Path.Combine(_tempDirectory, sessionId);
            string drawingFilePath = null;
            string exportedDrawingNumber = null;
            List<string> allDrawingNumbers = new List<string>();
            ExportResult result = new ExportResult();
            
            try
            {
                Directory.CreateDirectory(sessionDir);
                
                // 读取源DWG文件
                LogToFile("尝试读取源DWG文件...");
                sourceDb = new Database(false, true); 
                sourceDb.ReadDwgFile(dwgFilePath, FileShare.Read, true, ""); 
                LogToFile("源DWG文件读取成功");
                
                // 创建一个副本数据库用于处理
                LogToFile("创建副本数据库...");
                Database copyDb = sourceDb.Wblock();
                LogToFile("副本数据库创建成功");
                
                using (Transaction tr = copyDb.TransactionManager.StartTransaction())
                {
                    // 获取模型空间
                    BlockTable bt = tr.GetObject(copyDb.BlockTableId, OpenMode.ForRead) as BlockTable;
                    BlockTableRecord btr = tr.GetObject(bt[BlockTableRecord.ModelSpace], OpenMode.ForRead) as BlockTableRecord;
                    
                    // 存储所有可能的图框和文本
                    List<Tuple<ObjectId, Polyline>> potentialFrames = new List<Tuple<ObjectId, Polyline>>();
                    List<DBText> allDbTexts = new List<DBText>(); 
                    List<MText> allMTexts = new List<MText>();   
                    
                    LogToFile("开始遍历模型空间实体...");
                    // 收集所有实体
                    foreach (ObjectId objId in btr)
                    {
                        Entity ent = tr.GetObject(objId, OpenMode.ForRead) as Entity;
                        if (ent == null) continue;
                        
                        if (ent is Polyline pl && pl.Closed)
                        {
                            potentialFrames.Add(new Tuple<ObjectId, Polyline>(pl.ObjectId, pl.Clone() as Polyline));
                        }
                        else if (ent is DBText dbText)
                        {
                            allDbTexts.Add(dbText.Clone() as DBText);
                        }
                        else if (ent is MText mText)
                        {
                            allMTexts.Add(mText.Clone() as MText);
                        }
                    }
                    
                    LogToFile($"找到 {potentialFrames.Count} 个潜在图框");
                    
                    if (!potentialFrames.Any())
                    {
                        LogToFile("未找到任何闭合多段线框架");
                        tr.Abort();
                        return new ExportResult 
                        { 
                            HasMoreDrawings = false,
                            AllDrawingNumbers = new List<string>()
                        };
                    }
                    
                    // 按面积从大到小排序图框
                    List<PrintFrame> printFrames = new List<PrintFrame>();
                    int totalPrintDrawings = 0;
                    
                    // 分析所有可能的图框，找出符合条件的打印图
                    foreach (var frameTuple in potentialFrames.OrderByDescending(t => t.Item2.Area))
                    {
                        ObjectId originalFrameId = frameTuple.Item1;
                        Polyline framePolyline = frameTuple.Item2;
                        
                        List<DBText> dbTextsInFrame = allDbTexts.Where(t => IsPointInsidePolyline(t.Position, framePolyline)).ToList();
                        List<MText> mTextsInFrame = allMTexts.Where(mt => IsPointInsidePolyline(mt.Location, framePolyline)).ToList();
                        
                        // 1. 检查"比例"文本（必须正好有一个）
                        int countScaleText = dbTextsInFrame.Count(t => t.TextString.Trim().Replace(" ", "").Contains("比例")) +
                                            mTextsInFrame.Count(mt => mt.Text.Trim().Replace(" ", "").Contains("比例"));
                        
                        // 2. 检查"图号"标签（必须正好有一个）
                        int countDrawingNumLabel = dbTextsInFrame.Count(t => t.TextString.Trim().Replace(" ", "").Contains("图号")) +
                                                  mTextsInFrame.Count(mt => mt.Text.Trim().Replace(" ", "").Contains("图号"));
                        
                        // 3. 寻找图号数值文本（如"15/83"或"201"）
                        List<string> drawingNumberValuesFound = new List<string>();
                        drawingNumberValuesFound.AddRange(dbTextsInFrame
                            .Select(t => t.TextString.Trim().Replace(" ", ""))
                            .Where(text => Regex.IsMatch(text, @"^\d+([/\\]\d+)?$|^[\d]+$")));
                        drawingNumberValuesFound.AddRange(mTextsInFrame
                            .Select(mt => mt.Text.Trim().Replace(" ", ""))
                            .Where(text => Regex.IsMatch(text, @"^\d+([/\\]\d+)?$|^[\d]+$")));
                        
                        List<string> distinctDrawingNumberValues = drawingNumberValuesFound.Distinct().ToList();
                        
                        // 判断条件：正好一个"比例"，正好一个"图号"，至少一个数值
                        if (countScaleText == 1 && countDrawingNumLabel == 1 && distinctDrawingNumberValues.Count > 0)
                        {
                            string drawingNumber = distinctDrawingNumberValues.First();
                            LogToFile($"找到打印图 #{totalPrintDrawings + 1}, 图号: {drawingNumber}");
                            allDrawingNumbers.Add(drawingNumber);
                            
                            // 记录找到的有效打印图框
                            printFrames.Add(new PrintFrame
                            {
                                FrameId = originalFrameId,
                                FramePolyline = framePolyline,
                                DrawingNumber = drawingNumber,
                                DbTextsInFrame = dbTextsInFrame,
                                MTextsInFrame = mTextsInFrame
                            });
                            
                            totalPrintDrawings++;
                        }
                    }
                    
                    LogToFile($"共找到 {totalPrintDrawings} 个打印图");
                    
                    // 如果没有找到打印图，返回空结果
                    if (totalPrintDrawings == 0)
                    {
                        tr.Abort();
                        return new ExportResult 
                        { 
                            HasMoreDrawings = false,
                            AllDrawingNumbers = new List<string>()
                        };
                    }
                    
                    // 找出一个未处理过的打印图
                    PrintFrame frameToPrint = null;
                    foreach (var frame in printFrames)
                    {
                        if (!processedDrawingNumbers.Contains(frame.DrawingNumber))
                        {
                            frameToPrint = frame;
                            LogToFile($"选择导出未处理的打印图: {frame.DrawingNumber}");
                            break;
                        }
                    }
                    
                    // 如果所有图都已处理，返回结果
                    if (frameToPrint == null)
                    {
                        LogToFile("所有打印图都已处理，无需导出");
                        tr.Abort();
                        return new ExportResult 
                        { 
                            HasMoreDrawings = false,
                            AllDrawingNumbers = allDrawingNumbers
                        };
                    }
                    
                    // 找到要保留的实体（在图框内或与图框相交的）
                    ObjectIdCollection entitiesToKeep = new ObjectIdCollection();
                    entitiesToKeep.Add(frameToPrint.FrameId); // 先保留图框本身
                    
                    // 准备删除图框外的实体
                    btr.UpgradeOpen();
                    
                    // 使用边界框检查来识别与图框相交的实体
                    Extents3d frameExtents;
                    try 
                    {
                        frameExtents = frameToPrint.FramePolyline.GeometricExtents;
                    }
                    catch(System.Exception)
                    {
                        // 如果无法获取几何边界，使用顶点构建边界
                        frameExtents = new Extents3d();
                        for(int i = 0; i < frameToPrint.FramePolyline.NumberOfVertices; i++)
                        {
                            frameExtents.AddPoint(frameToPrint.FramePolyline.GetPoint3dAt(i));
                        }
                    }
                    
                    // 检查所有实体，收集需要保留的
                    foreach (ObjectId objId in btr)
                    {
                        if (objId == frameToPrint.FrameId) continue; // 已添加图框本身
                        
                        Entity ent = tr.GetObject(objId, OpenMode.ForRead) as Entity;
                        if (ent == null) continue;
                        
                        bool shouldKeep = false;
                        
                        try
                        {
                            // 尝试使用边界框快速检查
                            Extents3d entExtents = ent.GeometricExtents;
                            
                            // 使用边界框相交作为快速过滤
                            if (entExtents.MinPoint.X <= frameExtents.MaxPoint.X &&
                                entExtents.MaxPoint.X >= frameExtents.MinPoint.X &&
                                entExtents.MinPoint.Y <= frameExtents.MaxPoint.Y &&
                                entExtents.MaxPoint.Y >= frameExtents.MinPoint.Y)
                            {
                                shouldKeep = true;
                            }
                        }
                        catch (System.Exception)
                        {
                            // 特殊处理某些类型的实体
                            if (ent is BlockReference blockRef)
                            {
                                // 对于块参照，检查插入点
                                if (IsPointInsidePolyline(blockRef.Position, frameToPrint.FramePolyline) || 
                                    IsPointNearPolyline(blockRef.Position, frameToPrint.FramePolyline, 10.0))
                                {
                                    shouldKeep = true;
                                }
                            }
                            else if (ent is DBText dbText)
                            {
                                if (IsPointInsidePolyline(dbText.Position, frameToPrint.FramePolyline) ||
                                    IsPointNearPolyline(dbText.Position, frameToPrint.FramePolyline, 5.0))
                                {
                                    shouldKeep = true;
                                }
                            }
                            else if (ent is MText mText)
                            {
                                if (IsPointInsidePolyline(mText.Location, frameToPrint.FramePolyline) ||
                                    IsPointNearPolyline(mText.Location, frameToPrint.FramePolyline, 5.0))
                                {
                                    shouldKeep = true;
                                }
                            }
                        }
                        
                        if (shouldKeep)
                        {
                            entitiesToKeep.Add(objId);
                        }
                    }
                    
                    // 删除所有不需要保留的实体
                    LogToFile($"从模型空间删除图框外的实体...");
                    int removedCount = 0;
                    foreach (ObjectId objId in btr)
                    {
                        bool shouldKeep = false;
                        
                        for (int i = 0; i < entitiesToKeep.Count; i++)
                        {
                            if (objId == entitiesToKeep[i])
                            {
                                shouldKeep = true;
                                break;
                            }
                        }
                        
                        if (!shouldKeep)
                        {
                            Entity ent = tr.GetObject(objId, OpenMode.ForWrite) as Entity;
                            if (ent != null)
                            {
                                ent.Erase();
                                removedCount++;
                            }
                        }
                    }
                    
                    LogToFile($"已删除 {removedCount} 个图框外实体");
                    
                    // 检查是否需要旋转（横向图框）
                    bool needRotation = IsHorizontalFrame(copyDb, frameToPrint.FramePolyline, frameToPrint.DbTextsInFrame, frameToPrint.MTextsInFrame, tr);
                    
                    if (needRotation)
                    {
                        LogToFile("检测到横向图框，准备旋转...");
                        // 计算旋转中心点
                        Point3d rotMinPoint = new Point3d(double.MaxValue, double.MaxValue, 0);
                        Point3d rotMaxPoint = new Point3d(double.MinValue, double.MinValue, 0);
                        
                        for (int i = 0; i < frameToPrint.FramePolyline.NumberOfVertices; i++)
                        {
                            Point3d pt = frameToPrint.FramePolyline.GetPoint3dAt(i);
                            rotMinPoint = new Point3d(
                                Math.Min(rotMinPoint.X, pt.X),
                                Math.Min(rotMinPoint.Y, pt.Y),
                                0
                            );
                            rotMaxPoint = new Point3d(
                                Math.Max(rotMaxPoint.X, pt.X),
                                Math.Max(rotMaxPoint.Y, pt.Y),
                                0
                            );
                        }
                        
                        // 计算中心点
                        Point3d centerPoint = new Point3d(
                            (rotMinPoint.X + rotMaxPoint.X) / 2,
                            (rotMinPoint.Y + rotMaxPoint.Y) / 2,
                            0
                        );
                        
                        // 创建旋转矩阵 - 逆时针旋转90度
                        Matrix3d rotMatrix = Matrix3d.Rotation(-Math.PI / 2, Vector3d.ZAxis, centerPoint);
                        
                        // 旋转所有实体
                        foreach (ObjectId id in btr)
                        {
                            Entity ent = tr.GetObject(id, OpenMode.ForWrite) as Entity;
                            if (ent != null)
                            {
                                ent.TransformBy(rotMatrix);
                            }
                        }
                        
                        LogToFile("所有实体旋转完成");
                    }
                    
                    // 将所有文本设置为宋体
                    LogToFile("设置所有文字样式为宋体...");
                    ObjectIdCollection allEntitiesInModelSpace = new ObjectIdCollection();
                    foreach (ObjectId id in btr)
                    {
                        allEntitiesInModelSpace.Add(id);
                    }
                    SetAllTextStyleToSimSun(copyDb, allEntitiesInModelSpace, tr);
                    
                    // 提交事务
                    tr.Commit();
                    
                    // 清理文件名
                    string fileName = frameToPrint.DrawingNumber.Replace('/', '-').Replace('\\', '-');
                    fileName = string.Join("_", fileName.Split(Path.GetInvalidFileNameChars()));
                    fileName = Regex.Replace(fileName, @"[^\w\.-]", "_");
                    drawingFilePath = Path.Combine(sessionDir, $"{fileName}.dwg");
                    
                    // 将修改后的数据库保存为新文件
                    LogToFile($"保存单个打印图文件: {drawingFilePath}");
                    copyDb.SaveAs(drawingFilePath, DwgVersion.Current);
                    
                    // 记录导出信息
                    exportedDrawingNumber = frameToPrint.DrawingNumber;
                    
                    // 返回结果
                    result.DrawingFilePath = drawingFilePath;
                    result.DrawingNumber = exportedDrawingNumber;
                    result.CurrentIndex = printFrames.IndexOf(frameToPrint) + 1;
                    result.TotalCount = printFrames.Count;
                    result.HasMoreDrawings = printFrames.Count > processedDrawingNumbers.Count + 1;
                    result.AllDrawingNumbers = allDrawingNumbers;
                }
                
                return result;
            }
            catch (Exception ex)
            {
                LogToFile($"处理单个打印图时出错: {ex.Message}\n{ex.StackTrace}");
                throw;
            }
            finally
            {
                if (sourceDb != null && !sourceDb.IsDisposed)
                {
                    sourceDb.Dispose();
                }
            }
        }
        
        #region Helper Methods
        
        // 用于记录打印图框的内部类
        private class PrintFrame
        {
            public ObjectId FrameId { get; set; }
            public Polyline FramePolyline { get; set; }
            public string DrawingNumber { get; set; }
            public List<DBText> DbTextsInFrame { get; set; }
            public List<MText> MTextsInFrame { get; set; }
        }
        
        // 判断点是否在多段线内部
        private bool IsPointInsidePolyline(Point3d testPoint, Polyline poly)
        {
            if (!poly.Closed) return false;
            if (poly.NumberOfVertices < 3) return false;

            int crossings = 0;
            for (int i = 0; i < poly.NumberOfVertices; i++)
            {
                Point2d p1 = poly.GetPoint2dAt(i);
                Point2d p2 = poly.GetPoint2dAt((i + 1) % poly.NumberOfVertices);

                if (((p1.Y <= testPoint.Y && testPoint.Y < p2.Y) ||
                     (p2.Y <= testPoint.Y && testPoint.Y < p1.Y)) &&
                    (testPoint.X < (p2.X - p1.X) * (testPoint.Y - p1.Y) / (p2.Y - p1.Y) + p1.X))
                {
                    crossings++;
                }
            }
            return (crossings % 2) == 1;
        }
        
        // 判断点是否靠近多段线
        private bool IsPointNearPolyline(Point3d testPoint, Polyline poly, double tolerance)
        {
            if (poly.NumberOfVertices < 2) return false;

            for (int i = 0; i < poly.NumberOfVertices; i++)
            {
                Point3d p1 = poly.GetPoint3dAt(i);
                Point3d p2 = poly.GetPoint3dAt((i + 1) % poly.NumberOfVertices);
                
                // 计算点到线段的距离
                double dist = PointToLineSegmentDistance(testPoint, p1, p2);
                if (dist <= tolerance)
                {
                    return true;
                }
            }
            return false;
        }

        // 计算点到线段的距离
        private double PointToLineSegmentDistance(Point3d point, Point3d lineStart, Point3d lineEnd)
        {
            Vector3d v = lineEnd - lineStart;
            Vector3d w = point - lineStart;

            double c1 = w.DotProduct(v);
            if (c1 <= 0)
                return point.DistanceTo(lineStart);

            double c2 = v.DotProduct(v);
            if (c2 <= c1)
                return point.DistanceTo(lineEnd);

            double b = c1 / c2;
            Point3d pb = lineStart + b * v;
            return point.DistanceTo(pb);
        }
        
        // 判断是否为横向图框(检查文本旋转角度)
        private bool IsHorizontalFrame(Database db, Polyline framePolyline, List<DBText> dbTextsInFrame, List<MText> mTextsInFrame, Transaction tr)
        {
            try
            {
                // 查找"比例"和"图号"文本
                var scaleTexts = dbTextsInFrame.FindAll(t => t.TextString.Trim().Replace(" ", "").Contains("比例"));
                var drawingNumTexts = dbTextsInFrame.FindAll(t => t.TextString.Trim().Replace(" ", "").Contains("图号"));
                
                // 如果找到了这些文本，检查它们的旋转角度
                foreach (var text in scaleTexts)
                {
                    // 检查旋转值是否接近90度（以角度为单位）
                    if (Math.Abs(text.Rotation * 180 / Math.PI - 90) < 5)
                    {
                        return true;
                    }
                }
                
                foreach (var text in drawingNumTexts)
                {
                    // 检查旋转值是否接近90度（以角度为单位）
                    if (Math.Abs(text.Rotation * 180 / Math.PI - 90) < 5)
                    {
                        return true;
                    }
                }
                
                // 也检查多行文本
                foreach (var mtext in mTextsInFrame)
                {
                    string content = mtext.Text.Trim().Replace(" ", "");
                    if (content.Contains("比例") || content.Contains("图号"))
                    {
                        // 检查旋转值是否接近90度（以角度为单位）
                        if (Math.Abs(mtext.Rotation * 180 / Math.PI - 90) < 5)
                        {
                            return true;
                        }
                    }
                }
                
                // 没有找到横向的文本
                return false;
            }
            catch (System.Exception)
            {
                return false; // 出错时默认为非横向
            }
        }
        
        // 将所有文本和块参照属性的文字样式设置为宋体
        private void SetAllTextStyleToSimSun(Database db, ObjectIdCollection entityIds, Transaction tr)
        {
            try
            {
                // 首先确保宋体文字样式存在
                string simSunStyleName = "宋体";
                TextStyleTable textStyleTable = tr.GetObject(db.TextStyleTableId, OpenMode.ForRead) as TextStyleTable;
                
                ObjectId simSunStyleId;
                if (!textStyleTable.Has(simSunStyleName))
                {
                    // 如果宋体样式不存在，尝试创建它
                    textStyleTable.UpgradeOpen();
                    TextStyleTableRecord simSunStyle = new TextStyleTableRecord();
                    simSunStyle.Name = simSunStyleName;
                    simSunStyle.FileName = "simsun.ttf"; // 宋体字体文件
                    textStyleTable.Add(simSunStyle);
                    tr.AddNewlyCreatedDBObject(simSunStyle, true);
                    simSunStyleId = simSunStyle.ObjectId;
                }
                else
                {
                    simSunStyleId = textStyleTable[simSunStyleName];
                }
                
                // 遍历所有选中的实体
                foreach (ObjectId id in entityIds)
                {
                    Entity ent = tr.GetObject(id, OpenMode.ForRead) as Entity;
                    if (ent == null) continue;
                    
                    // 处理文本对象
                    if (ent is DBText)
                    {
                        DBText text = ent as DBText;
                        if (text.TextStyleId != simSunStyleId)
                        {
                            text.UpgradeOpen();
                            text.TextStyleId = simSunStyleId;
                        }
                    }
                    else if (ent is MText)
                    {
                        MText mtext = ent as MText;
                        if (mtext.TextStyleId != simSunStyleId)
                        {
                            mtext.UpgradeOpen();
                            mtext.TextStyleId = simSunStyleId;
                        }
                    }
                    // 处理块参照
                    else if (ent is BlockReference)
                    {
                        BlockReference blockRef = ent as BlockReference;
                        
                        // 检查块参照是否有属性
                        AttributeCollection attColl = blockRef.AttributeCollection;
                        if (attColl.Count > 0)
                        {
                            // 遍历所有属性
                            foreach (ObjectId attId in attColl)
                            {
                                AttributeReference attRef = tr.GetObject(attId, OpenMode.ForWrite) as AttributeReference;
                                if (attRef != null && attRef.TextStyleId != simSunStyleId)
                                {
                                    attRef.TextStyleId = simSunStyleId;
                                }
                            }
                        }
                    }
                    // 处理所有类型的标注
                    else if (ent is Dimension)
                    {
                        Dimension dim = ent as Dimension;
                        if (dim.TextStyleId != simSunStyleId)
                        {
                            dim.UpgradeOpen();
                            dim.TextStyleId = simSunStyleId;
                        }
                    }
                }
            }
            catch (System.Exception ex)
            {
                LogToFile($"设置文字样式出错: {ex.Message}");
            }
        }
        
        #endregion
        
        /// <summary>
        /// Cleans up temporary files older than a specified time
        /// </summary>
        public void CleanupTempFiles(TimeSpan maxAge)
        {
            try
            {
                if (!Directory.Exists(_tempDirectory))
                {
                    return;
                }
                
                foreach (string dir in Directory.GetDirectories(_tempDirectory))
                {
                    DirectoryInfo dirInfo = new DirectoryInfo(dir);
                    if (DateTime.Now - dirInfo.CreationTime > maxAge)
                    {
                        try
                        {
                            Directory.Delete(dir, true);
                        }
                        catch (Exception)
                        {
                            // Ignore errors when cleaning up
                        }
                    }
                }
            }
            catch (Exception)
            {
                // Ignore errors when cleaning up
            }
        }
        
        /// <summary>
        /// Gets the current queue status
        /// </summary>
        public int GetQueueLength()
        {
            return _processingQueue.Count;
        }
    }
} 