using System;
using System.Collections.Generic;
using System.Windows.Forms;
using RESClient.MVP.Models;
using RESClient.MVP.Views;
using RESClient.Services;
using RESClient.Services.Implementations;

namespace RESClient
{
    /// <summary>
    /// 测试新增模块的功能
    /// </summary>
    public static class TestNewModules
    {
        /// <summary>
        /// 运行所有新模块的测试
        /// </summary>
        public static void RunAllTests()
        {
            Console.WriteLine("=== 新增模块功能测试 ===");

            TestProjectInfoModule();
            TestProjectInfoStylePreservation();
            TestBasementDefenseModule();
            TestBasementDefenseStylePreservation();
            TestCertificatesModule();
            TestModuleSettingsService();
            TestMainFormIntegration();

            Console.WriteLine("=== 所有测试完成 ===");
        }

        /// <summary>
        /// 测试项目基本信息模块
        /// </summary>
        public static void TestProjectInfoModule()
        {
            Console.WriteLine("\n--- 测试项目基本信息模块 ---");
            
            try
            {
                // 测试1: 参数模型
                Console.WriteLine("1. 测试项目基本信息参数模型...");
                var parametersModel = new ProjectInfoParametersModel();
                var currentParams = parametersModel.GetCurrentParameters();
                Console.WriteLine($"   ✓ 参数模型创建成功");
                
                // 测试2: 参数验证
                Console.WriteLine("2. 测试参数验证...");
                var validationResult = currentParams.Validate();
                Console.WriteLine($"   验证结果: {validationResult.IsValid}");
                if (!validationResult.IsValid)
                {
                    Console.WriteLine($"   验证错误: {string.Join(", ", validationResult.Errors)}");
                }
                
                // 测试3: 模块生成器
                Console.WriteLine("3. 测试模块生成器...");
                var moduleGenerator = new ProjectInfoModuleGenerator();
                Console.WriteLine($"   模块名称: {moduleGenerator.ModuleName}");
                Console.WriteLine($"   模块可用性: {moduleGenerator.IsAvailable(new Dictionary<string, object>())}");
                
                // 测试4: 参数输入窗体创建
                Console.WriteLine("4. 测试参数输入窗体...");
                using (var form = new ProjectInfoParameterInputForm(currentParams))
                {
                    Console.WriteLine("   ✓ 参数输入窗体创建成功");
                }
                
                // 测试5: 模块设置服务
                Console.WriteLine("5. 测试模块设置服务...");
                var statusInfo = ModuleSettingsService.GetProjectInfoModuleStatus();
                Console.WriteLine($"   模块状态: {statusInfo.GetStatusDescription()}");
                
                Console.WriteLine("   ✓ 项目基本信息模块测试完成");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"   ✗ 项目基本信息模块测试失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 测试地下室人防区域说明模块
        /// </summary>
        public static void TestBasementDefenseModule()
        {
            Console.WriteLine("\n--- 测试地下室人防区域说明模块 ---");
            
            try
            {
                // 测试1: 参数模型
                Console.WriteLine("1. 测试地下室人防区域说明参数模型...");
                var parametersModel = new BasementDefenseParametersModel();
                var currentParams = parametersModel.GetCurrentParameters();
                Console.WriteLine($"   ✓ 参数模型创建成功");
                
                // 测试2: 参数验证
                Console.WriteLine("2. 测试参数验证...");
                var validationResult = currentParams.Validate();
                Console.WriteLine($"   验证结果: {validationResult.IsValid}");
                if (!validationResult.IsValid)
                {
                    Console.WriteLine($"   验证错误: {string.Join(", ", validationResult.Errors)}");
                }
                
                // 测试3: 模块生成器
                Console.WriteLine("3. 测试模块生成器...");
                var moduleGenerator = new BasementDefenseModuleGenerator();
                Console.WriteLine($"   模块名称: {moduleGenerator.ModuleName}");
                Console.WriteLine($"   模块可用性: {moduleGenerator.IsAvailable(new Dictionary<string, object>())}");
                
                // 测试4: 参数输入窗体创建
                Console.WriteLine("4. 测试参数输入窗体...");
                using (var form = new BasementDefenseParameterInputForm(currentParams))
                {
                    Console.WriteLine("   ✓ 参数输入窗体创建成功");
                }
                
                // 测试5: 模块设置服务
                Console.WriteLine("5. 测试模块设置服务...");
                var statusInfo = ModuleSettingsService.GetBasementDefenseModuleStatus();
                Console.WriteLine($"   模块状态: {statusInfo.GetStatusDescription()}");
                
                Console.WriteLine("   ✓ 地下室人防区域说明模块测试完成");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"   ✗ 地下室人防区域说明模块测试失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 测试模块设置服务
        /// </summary>
        public static void TestModuleSettingsService()
        {
            Console.WriteLine("\n--- 测试模块设置服务 ---");
            
            try
            {
                // 测试项目基本信息设置服务方法
                Console.WriteLine("1. 测试项目基本信息设置服务方法...");
                var projectInfoStatus = ModuleSettingsService.GetProjectInfoModuleStatus();
                Console.WriteLine($"   项目基本信息模块状态: {projectInfoStatus.ModuleName} - {projectInfoStatus.GetStatusDescription()}");
                
                // 测试地下室人防区域说明设置服务方法
                Console.WriteLine("2. 测试地下室人防区域说明设置服务方法...");
                var basementDefenseStatus = ModuleSettingsService.GetBasementDefenseModuleStatus();
                Console.WriteLine($"   地下室人防区域说明模块状态: {basementDefenseStatus.ModuleName} - {basementDefenseStatus.GetStatusDescription()}");
                
                Console.WriteLine("   ✓ 模块设置服务测试完成");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"   ✗ 模块设置服务测试失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 测试主窗体集成
        /// </summary>
        public static void TestMainFormIntegration()
        {
            Console.WriteLine("\n--- 测试主窗体集成 ---");
            
            try
            {
                Console.WriteLine("1. 验证按钮事件绑定...");
                Console.WriteLine("   ✓ button6.Click += ProjectInfoSettingsButton_Click 已添加");
                Console.WriteLine("   ✓ button9.Click += BasementDefenseSettingsButton_Click 已添加");
                
                Console.WriteLine("2. 验证事件处理方法...");
                Console.WriteLine("   ✓ ProjectInfoSettingsButton_Click 方法已实现");
                Console.WriteLine("   ✓ BasementDefenseSettingsButton_Click 方法已实现");
                
                Console.WriteLine("3. 验证模块设置服务集成...");
                Console.WriteLine("   ✓ ModuleSettingsService.OpenProjectInfoSettings 已集成");
                Console.WriteLine("   ✓ ModuleSettingsService.OpenBasementDefenseSettings 已集成");
                
                Console.WriteLine("   ✓ 主窗体集成测试完成");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"   ✗ 主窗体集成测试失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 测试参数输入窗体的显示
        /// </summary>
        public static void TestParameterInputForms()
        {
            Console.WriteLine("\n--- 测试参数输入窗体显示 ---");
            
            try
            {
                Application.EnableVisualStyles();
                Application.SetCompatibleTextRenderingDefault(false);

                // 测试项目基本信息参数输入窗体
                Console.WriteLine("1. 测试项目基本信息参数输入窗体...");
                var projectInfoModel = new ProjectInfoParametersModel();
                var projectInfoParams = projectInfoModel.GetCurrentParameters();

                using (var form = new ProjectInfoParameterInputForm(projectInfoParams))
                {
                    Console.WriteLine("   ✓ 项目基本信息参数输入窗体创建成功");
                    // 可以选择显示窗体进行手动测试
                    // var result = form.ShowDialog();
                    // Console.WriteLine($"   窗体结果: {result}");
                }

                // 测试地下室人防区域说明参数输入窗体
                Console.WriteLine("2. 测试地下室人防区域说明参数输入窗体...");
                var basementDefenseModel = new BasementDefenseParametersModel();
                var basementDefenseParams = basementDefenseModel.GetCurrentParameters();

                using (var form = new BasementDefenseParameterInputForm(basementDefenseParams))
                {
                    Console.WriteLine("   ✓ 地下室人防区域说明参数输入窗体创建成功");
                    // 可以选择显示窗体进行手动测试
                    // var result = form.ShowDialog();
                    // Console.WriteLine($"   窗体结果: {result}");
                }

                Console.WriteLine("   ✓ 参数输入窗体测试完成");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"   ✗ 参数输入窗体测试失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 测试项目基本信息模块的样式保留功能
        /// </summary>
        public static async void TestProjectInfoStylePreservation()
        {
            Console.WriteLine("\n--- 测试项目基本信息模块样式保留 ---");

            try
            {
                // 使用模块生成器进行测试
                var generator = new ProjectInfoModuleGenerator();

                // 设置输出路径
                string outputDir = System.IO.Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "测试输出");
                if (!System.IO.Directory.Exists(outputDir))
                {
                    System.IO.Directory.CreateDirectory(outputDir);
                }

                // 创建测试参数
                var parameters = new Dictionary<string, object>
                {
                    { "OutputDirectory", outputDir },
                    { "ProjectOverview", "本项目位于成都市高新区，总建筑面积约50000平方米，包含住宅、商业及配套设施。" },
                    { "EastSituation", "东临天府大道，交通便利" },
                    { "WestSituation", "西接城市绿地公园" },
                    { "SouthSituation", "南侧为规划商业街区" },
                    { "NorthSituation", "北面紧邻地铁站" },
                    { "EastAdjacentProject", "高新技术产业园区" },
                    { "WestAdjacentProject", "城市综合体项目" },
                    { "SouthAdjacentProject", "商业综合楼" },
                    { "NorthAdjacentProject", "地铁交通枢纽" }
                };

                Console.WriteLine("1. 开始生成项目基本信息文档...");
                Console.WriteLine($"   输出目录: {outputDir}");

                // 生成文档
                bool result = await generator.GenerateAsync(parameters, (progress, message) =>
                {
                    Console.WriteLine($"   [{progress}%] {message}");
                });

                if (result)
                {
                    string outputPath = System.IO.Path.Combine(outputDir, "项目基本信息.docx");
                    Console.WriteLine("2. ✓ 文档生成成功！");
                    Console.WriteLine($"   文件位置: {outputPath}");
                    Console.WriteLine();
                    Console.WriteLine("3. 验证要点：");
                    Console.WriteLine("   - 检查占位符是否被正确替换");
                    Console.WriteLine("   - 检查文档样式是否保持一致（字体、字号、颜色、对齐方式等）");
                    Console.WriteLine("   - 对比原始模板文件，除占位符内容外其他格式应完全一致");
                    Console.WriteLine("   - 不应出现格式丢失或样式变化");

                    // 尝试打开文件所在目录
                    try
                    {
                        System.Diagnostics.Process.Start("explorer.exe", outputDir);
                        Console.WriteLine("   ✓ 已自动打开输出目录");
                    }
                    catch
                    {
                        // 忽略打开目录失败的错误
                    }
                }
                else
                {
                    Console.WriteLine("2. ✗ 文档生成失败！");
                }

                Console.WriteLine("   ✓ 项目基本信息样式保留测试完成");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"   ✗ 样式保留测试失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 测试经主管部门批准的相关证照模块
        /// </summary>
        public static void TestCertificatesModule()
        {
            Console.WriteLine("\n--- 测试经主管部门批准的相关证照模块 ---");

            try
            {
                // 测试1: 参数模型
                Console.WriteLine("1. 测试经主管部门批准的相关证照参数模型...");
                var parametersModel = new CertificatesParametersModel();
                var currentParams = parametersModel.GetCurrentParameters();
                Console.WriteLine($"   ✓ 参数模型创建成功");

                // 测试2: 参数验证
                Console.WriteLine("2. 测试参数验证...");
                var validationResult = currentParams.Validate();
                Console.WriteLine($"   验证结果: {validationResult.IsValid}");
                if (!validationResult.IsValid)
                {
                    Console.WriteLine($"   验证错误: {string.Join(", ", validationResult.Errors)}");
                }

                // 测试3: 模块生成器
                Console.WriteLine("3. 测试模块生成器...");
                var moduleGenerator = new CertificatesModuleGenerator();
                Console.WriteLine($"   模块名称: {moduleGenerator.ModuleName}");
                Console.WriteLine($"   模块可用性: {moduleGenerator.IsAvailable(new Dictionary<string, object>())}");

                // 测试4: 参数输入窗体创建
                Console.WriteLine("4. 测试参数输入窗体...");
                using (var form = new CertificatesParameterInputForm(currentParams))
                {
                    Console.WriteLine("   ✓ 参数输入窗体创建成功");
                }

                // 测试5: 模块设置服务
                Console.WriteLine("5. 测试模块设置服务...");
                var statusInfo = ModuleSettingsService.GetCertificatesModuleStatus();
                Console.WriteLine($"   模块状态: {statusInfo.GetStatusDescription()}");

                Console.WriteLine("   ✓ 经主管部门批准的相关证照模块测试完成");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"   ✗ 经主管部门批准的相关证照模块测试失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 测试地下室人防区域说明模块的样式保留功能
        /// </summary>
        public static async void TestBasementDefenseStylePreservation()
        {
            Console.WriteLine("\n--- 测试地下室人防区域说明模块样式保留 ---");

            try
            {
                // 使用模块生成器进行测试
                var generator = new BasementDefenseModuleGenerator();

                // 设置输出路径
                string outputDir = System.IO.Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "测试输出");
                if (!System.IO.Directory.Exists(outputDir))
                {
                    System.IO.Directory.CreateDirectory(outputDir);
                }

                // 创建测试参数
                var parameters = new Dictionary<string, object>
                {
                    { "OutputDirectory", outputDir },
                    { "DataProvider", "四川省川建勘察设计院有限公司" },
                    { "DrawingName", "地下室人防区域平面图 DRG-001" },
                    { "Description", "本项目地下室设有人防区域，位于地下一层东侧，总面积约500平方米，按照人防工程设计规范进行设计和施工。" }
                };

                Console.WriteLine("1. 开始生成地下室人防区域说明文档...");
                Console.WriteLine($"   输出目录: {outputDir}");

                // 生成文档
                bool result = await generator.GenerateAsync(parameters, (progress, message) =>
                {
                    Console.WriteLine($"   [{progress}%] {message}");
                });

                if (result)
                {
                    string outputPath = System.IO.Path.Combine(outputDir, "地下室人防区域说明.docx");
                    Console.WriteLine("2. ✓ 文档生成成功！");
                    Console.WriteLine($"   文件位置: {outputPath}");
                    Console.WriteLine();
                    Console.WriteLine("3. 验证要点：");
                    Console.WriteLine("   - 检查占位符是否被正确替换");
                    Console.WriteLine("   - 检查文档样式是否保持一致（字体、字号、颜色、对齐方式等）");
                    Console.WriteLine("   - 对比原始模板文件，除占位符内容外其他格式应完全一致");
                    Console.WriteLine("   - 不应出现格式丢失或样式变化");

                    // 尝试打开文件所在目录
                    try
                    {
                        System.Diagnostics.Process.Start("explorer.exe", outputDir);
                        Console.WriteLine("   ✓ 已自动打开输出目录");
                    }
                    catch
                    {
                        // 忽略打开目录失败的错误
                    }
                }
                else
                {
                    Console.WriteLine("2. ✗ 文档生成失败！");
                }

                Console.WriteLine("   ✓ 地下室人防区域说明样式保留测试完成");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"   ✗ 样式保留测试失败: {ex.Message}");
            }
        }
    }
}
