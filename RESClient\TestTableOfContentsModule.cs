using System;
using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;
using RESClient.Services.Implementations;

namespace RESClient
{
    /// <summary>
    /// 目录模块测试类
    /// </summary>
    public static class TestTableOfContentsModule
    {
        /// <summary>
        /// 运行所有测试
        /// </summary>
        public static async Task RunAllTests()
        {
            Console.WriteLine("=== 目录模块测试开始 ===");
            
            TestModuleAvailability();
            await TestModuleGeneration();
            
            Console.WriteLine("=== 目录模块测试完成 ===");
        }

        /// <summary>
        /// 测试模块可用性检查
        /// </summary>
        public static void TestModuleAvailability()
        {
            Console.WriteLine("\n--- 测试模块可用性检查 ---");
            
            try
            {
                var generator = new TableOfContentsModuleGenerator();
                var parameters = new Dictionary<string, object>();
                
                bool isAvailable = generator.IsAvailable(parameters);
                Console.WriteLine($"模块可用性: {(isAvailable ? "✓ 可用" : "✗ 不可用")}");
                
                // 检查模板文件是否存在
                string templateDirectory = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "报告模板");
                string templatePath = Path.Combine(templateDirectory, "02_目录", "目录.docx");
                Console.WriteLine($"模板文件路径: {templatePath}");
                Console.WriteLine($"模板文件存在: {(File.Exists(templatePath) ? "✓ 是" : "✗ 否")}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"✗ 可用性检查测试失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 测试模块生成功能
        /// </summary>
        public static async Task TestModuleGeneration()
        {
            Console.WriteLine("\n--- 测试模块生成功能 ---");
            
            try
            {
                var generator = new TableOfContentsModuleGenerator();
                
                // 创建测试输出目录
                string testOutputDir = Path.Combine(Path.GetTempPath(), "RESClient_Test_TableOfContents");
                if (Directory.Exists(testOutputDir))
                {
                    Directory.Delete(testOutputDir, true);
                }
                Directory.CreateDirectory(testOutputDir);
                
                Console.WriteLine($"测试输出目录: {testOutputDir}");
                
                // 准备参数
                var parameters = new Dictionary<string, object>
                {
                    ["OutputDir"] = testOutputDir
                };
                
                // 进度回调
                void ProgressCallback(int progress, string message)
                {
                    Console.WriteLine($"[{progress}%] {message}");
                }
                
                // 执行生成
                Console.WriteLine("开始生成目录模块...");
                bool result = await generator.GenerateAsync(parameters, ProgressCallback);
                
                Console.WriteLine($"生成结果: {(result ? "✓ 成功" : "✗ 失败")}");
                
                // 检查输出文件
                string outputFile = Path.Combine(testOutputDir, "目录.docx");
                bool fileExists = File.Exists(outputFile);
                Console.WriteLine($"输出文件存在: {(fileExists ? "✓ 是" : "✗ 否")}");
                
                if (fileExists)
                {
                    FileInfo fileInfo = new FileInfo(outputFile);
                    Console.WriteLine($"输出文件大小: {fileInfo.Length} 字节");
                    Console.WriteLine($"输出文件路径: {outputFile}");
                }
                
                // 清理测试目录
                try
                {
                    if (Directory.Exists(testOutputDir))
                    {
                        Directory.Delete(testOutputDir, true);
                        Console.WriteLine("✓ 清理测试目录完成");
                    }
                }
                catch (Exception cleanupEx)
                {
                    Console.WriteLine($"⚠ 清理测试目录失败: {cleanupEx.Message}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"✗ 模块生成测试失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 测试错误处理
        /// </summary>
        public static async Task TestErrorHandling()
        {
            Console.WriteLine("\n--- 测试错误处理 ---");
            
            try
            {
                var generator = new TableOfContentsModuleGenerator();
                
                // 测试缺少输出目录参数
                var emptyParameters = new Dictionary<string, object>();
                
                void ProgressCallback(int progress, string message)
                {
                    Console.WriteLine($"[{progress}%] {message}");
                }
                
                Console.WriteLine("测试缺少输出目录参数...");
                bool result = await generator.GenerateAsync(emptyParameters, ProgressCallback);
                Console.WriteLine($"预期失败结果: {(result ? "✗ 意外成功" : "✓ 正确失败")}");
                
                // 测试无效输出目录
                var invalidParameters = new Dictionary<string, object>
                {
                    ["OutputDir"] = "C:\\InvalidPath\\That\\Does\\Not\\Exist\\And\\Cannot\\Be\\Created\\Because\\It\\Is\\Too\\Long\\Or\\Invalid"
                };
                
                Console.WriteLine("测试无效输出目录...");
                result = await generator.GenerateAsync(invalidParameters, ProgressCallback);
                Console.WriteLine($"无效目录处理结果: {(result ? "意外成功" : "✓ 正确处理")}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"✗ 错误处理测试失败: {ex.Message}");
            }
        }
    }
}
