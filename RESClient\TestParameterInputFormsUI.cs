using System;
using System.Drawing;
using System.Windows.Forms;
using RESClient.MVP.Models;
using RESClient.MVP.Views;

namespace RESClient
{
    /// <summary>
    /// 参数输入对话框UI一致性测试程序
    /// </summary>
    public static class TestParameterInputFormsUI
    {
        /// <summary>
        /// 测试所有参数输入对话框的UI一致性和优化效果
        /// </summary>
        public static void TestAllParameterInputForms()
        {
            Console.WriteLine("=== 参数输入对话框UI一致性和优化效果测试 ===\n");

            try
            {
                // 测试封面参数输入窗体（标准样式）
                TestCoverParameterInputForm();

                // 测试作业声明参数输入窗体（标准样式）
                TestWorkStatementParameterInputForm();

                // 测试地下室人防区域说明参数输入窗体
                TestBasementDefenseParameterInputForm();

                // 测试项目基本信息参数输入窗体
                TestProjectInfoParameterInputForm();

                // 测试作业、质量检查与验收参数输入窗体
                TestWorkQualityParameterInputForm();

                // 测试楼栋基本信息参数输入窗体（已优化高度）
                TestBuildingInfoParameterInputForm();

                // 测试经主管部门批准的相关证照参数输入窗体（已修复文本框布局）
                TestCertificatesParameterInputForm();

                // 测试房产面积汇总表参数输入窗体（已优化高度）
                TestEstateAreaSummaryParameterInputForm();

                Console.WriteLine("\n=== UI优化效果验证 ===");
                TestUIOptimizationEffects();

                Console.WriteLine("\n=== 所有参数输入对话框UI测试完成 ===");
                Console.WriteLine("✅ 所有对话框都已统一为标准UI样式并完成优化");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ 测试过程中发生错误: {ex.Message}");
                Console.WriteLine($"详细信息: {ex}");
            }
        }

        private static void TestCoverParameterInputForm()
        {
            Console.WriteLine("1. 测试封面参数输入窗体（标准样式）...");
            try
            {
                var model = new CoverParametersModel();
                var parameters = model.GetCurrentParameters();
                
                using (var form = new ParameterInputForm(parameters))
                {
                    ValidateFormUI(form, "封面参数设置");
                    Console.WriteLine("   ✅ 封面参数输入窗体UI验证通过");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"   ❌ 封面参数输入窗体测试失败: {ex.Message}");
            }
        }

        private static void TestWorkStatementParameterInputForm()
        {
            Console.WriteLine("2. 测试作业声明参数输入窗体（标准样式）...");
            try
            {
                var model = new WorkStatementParametersModel();
                var parameters = model.GetCurrentParameters();
                
                using (var form = new WorkStatementParameterInputForm(parameters))
                {
                    ValidateFormUI(form, "作业声明参数设置");
                    Console.WriteLine("   ✅ 作业声明参数输入窗体UI验证通过");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"   ❌ 作业声明参数输入窗体测试失败: {ex.Message}");
            }
        }

        private static void TestBasementDefenseParameterInputForm()
        {
            Console.WriteLine("3. 测试地下室人防区域说明参数输入窗体...");
            try
            {
                var model = new BasementDefenseParametersModel();
                var parameters = model.GetCurrentParameters();
                
                using (var form = new BasementDefenseParameterInputForm(parameters))
                {
                    ValidateFormUI(form, "地下室人防区域说明参数设置");
                    Console.WriteLine("   ✅ 地下室人防区域说明参数输入窗体UI验证通过");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"   ❌ 地下室人防区域说明参数输入窗体测试失败: {ex.Message}");
            }
        }

        private static void TestProjectInfoParameterInputForm()
        {
            Console.WriteLine("4. 测试项目基本信息参数输入窗体...");
            try
            {
                var model = new ProjectInfoParametersModel();
                var parameters = model.GetCurrentParameters();
                
                using (var form = new ProjectInfoParameterInputForm(parameters))
                {
                    ValidateFormUI(form, "项目基本信息参数设置");
                    Console.WriteLine("   ✅ 项目基本信息参数输入窗体UI验证通过");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"   ❌ 项目基本信息参数输入窗体测试失败: {ex.Message}");
            }
        }

        private static void TestWorkQualityParameterInputForm()
        {
            Console.WriteLine("5. 测试作业、质量检查与验收参数输入窗体...");
            try
            {
                var model = new WorkQualityParametersModel();
                var parameters = model.GetCurrentParameters();
                
                using (var form = new WorkQualityParameterInputForm(parameters))
                {
                    ValidateFormUI(form, "作业、质量检查与验收参数设置");
                    Console.WriteLine("   ✅ 作业、质量检查与验收参数输入窗体UI验证通过");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"   ❌ 作业、质量检查与验收参数输入窗体测试失败: {ex.Message}");
            }
        }

        private static void TestBuildingInfoParameterInputForm()
        {
            Console.WriteLine("6. 测试楼栋基本信息参数输入窗体（已优化高度）...");
            try
            {
                var model = new BuildingInfoParametersModel();
                var parameters = model.GetCurrentParameters();

                using (var form = new BuildingInfoParameterInputForm(parameters))
                {
                    ValidateFormUI(form, "楼栋基本信息参数设置");

                    // 验证高度优化效果（只有2个字段，应该使用较小高度）
                    if (form.Size.Height <= 350)
                    {
                        Console.WriteLine("   ✅ 楼栋基本信息参数输入窗体UI验证通过（已优化高度适应2个字段）");
                    }
                    else
                    {
                        Console.WriteLine($"   ⚠️ 楼栋基本信息参数输入窗体高度可能过高: {form.Size.Height}px");
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"   ❌ 楼栋基本信息参数输入窗体测试失败: {ex.Message}");
            }
        }

        private static void TestCertificatesParameterInputForm()
        {
            Console.WriteLine("7. 测试经主管部门批准的相关证照参数输入窗体（已修复文本框布局）...");
            try
            {
                var model = new CertificatesParametersModel();
                var parameters = model.GetCurrentParameters();

                using (var form = new CertificatesParameterInputForm(parameters))
                {
                    ValidateFormUI(form, "经主管部门批准的相关证照参数设置");
                    Console.WriteLine("   ✅ 经主管部门批准的相关证照参数输入窗体UI验证通过（已统一文本框宽度布局）");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"   ❌ 经主管部门批准的相关证照参数输入窗体测试失败: {ex.Message}");
            }
        }

        private static void TestEstateAreaSummaryParameterInputForm()
        {
            Console.WriteLine("8. 测试房产面积汇总表参数输入窗体（已优化高度）...");
            try
            {
                var model = new EstateAreaSummaryParametersModel();
                var parameters = model.GetCurrentParameters();

                using (var form = new EstateAreaSummaryParameterInputForm(parameters))
                {
                    ValidateFormUI(form, "房产面积汇总表参数设置");

                    // 验证高度优化效果（只有1个多行文本框字段，应该使用较小高度）
                    if (form.Size.Height <= 320)
                    {
                        Console.WriteLine("   ✅ 房产面积汇总表参数输入窗体UI验证通过（已优化高度适应1个字段）");
                    }
                    else
                    {
                        Console.WriteLine($"   ⚠️ 房产面积汇总表参数输入窗体高度可能过高: {form.Size.Height}px");
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"   ❌ 房产面积汇总表参数输入窗体测试失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 验证窗体UI是否符合标准样式
        /// </summary>
        /// <param name="form">要验证的窗体</param>
        /// <param name="expectedTitle">期望的窗体标题</param>
        private static void ValidateFormUI(Form form, string expectedTitle)
        {
            // 验证窗体基本设置
            if (form.Text != expectedTitle)
                throw new Exception($"窗体标题不匹配，期望: {expectedTitle}，实际: {form.Text}");

            if (form.FormBorderStyle != FormBorderStyle.Sizable)
                throw new Exception($"窗体边框样式不正确，期望: Sizable，实际: {form.FormBorderStyle}");

            if (form.StartPosition != FormStartPosition.CenterParent)
                throw new Exception($"窗体启动位置不正确，期望: CenterParent，实际: {form.StartPosition}");

            if (!form.MaximizeBox)
                throw new Exception("窗体应该允许最大化");

            if (form.MinimizeBox)
                throw new Exception("窗体不应该允许最小化");

            if (form.ShowInTaskbar)
                throw new Exception("窗体不应该显示在任务栏");

            // 验证窗体大小范围
            if (form.Size.Width < 500 || form.Size.Height < 300)
                throw new Exception($"窗体大小过小，当前大小: {form.Size}");

            // 验证是否有按钮容器（底部固定的按钮面板）
            bool hasButtonContainer = false;
            foreach (Control control in form.Controls)
            {
                if (control is Panel panel && panel.Dock == DockStyle.Bottom && panel.Height == 50)
                {
                    hasButtonContainer = true;
                    break;
                }
            }

            if (!hasButtonContainer)
                throw new Exception("窗体缺少标准的底部按钮容器");

            Console.WriteLine($"     - 窗体标题: {form.Text}");
            Console.WriteLine($"     - 窗体大小: {form.Size}");
            Console.WriteLine($"     - 边框样式: {form.FormBorderStyle}");
            Console.WriteLine($"     - 启动位置: {form.StartPosition}");
            Console.WriteLine($"     - 最大化按钮: {form.MaximizeBox}");
            Console.WriteLine($"     - 最小化按钮: {form.MinimizeBox}");
            Console.WriteLine($"     - 任务栏显示: {form.ShowInTaskbar}");
        }

        /// <summary>
        /// 测试UI优化效果
        /// </summary>
        private static void TestUIOptimizationEffects()
        {
            Console.WriteLine("验证UI优化效果...");

            try
            {
                // 测试楼栋基本信息对话框高度优化
                Console.WriteLine("1. 验证楼栋基本信息对话框高度优化...");
                var buildingModel = new BuildingInfoParametersModel();
                var buildingParams = buildingModel.GetCurrentParameters();
                using (var buildingForm = new BuildingInfoParameterInputForm(buildingParams))
                {
                    if (buildingForm.Size.Height <= 350)
                    {
                        Console.WriteLine($"   ✅ 楼栋基本信息对话框高度已优化: {buildingForm.Size.Height}px (适合2个字段)");
                    }
                    else
                    {
                        Console.WriteLine($"   ❌ 楼栋基本信息对话框高度未优化: {buildingForm.Size.Height}px");
                    }
                }

                // 测试房产面积汇总表对话框高度优化
                Console.WriteLine("2. 验证房产面积汇总表对话框高度优化...");
                var estateModel = new EstateAreaSummaryParametersModel();
                var estateParams = estateModel.GetCurrentParameters();
                using (var estateForm = new EstateAreaSummaryParameterInputForm(estateParams))
                {
                    if (estateForm.Size.Height <= 320)
                    {
                        Console.WriteLine($"   ✅ 房产面积汇总表对话框高度已优化: {estateForm.Size.Height}px (适合1个多行字段)");
                    }
                    else
                    {
                        Console.WriteLine($"   ❌ 房产面积汇总表对话框高度未优化: {estateForm.Size.Height}px");
                    }
                }

                // 测试内容居中布局优化
                Console.WriteLine("3. 验证内容居中布局优化...");
                var coverModel = new CoverParametersModel();
                var coverParams = coverModel.GetCurrentParameters();
                using (var coverForm = new ParameterInputForm(coverParams))
                {
                    // 检查主容器的边距设置
                    bool hasProperPadding = false;
                    foreach (Control control in coverForm.Controls)
                    {
                        if (control is Panel panel && panel.Dock == DockStyle.Fill)
                        {
                            if (panel.Padding.Left >= 20 && panel.Padding.Top >= 20)
                            {
                                hasProperPadding = true;
                                break;
                            }
                        }
                    }

                    if (hasProperPadding)
                    {
                        Console.WriteLine("   ✅ 内容居中布局已优化: 主容器边距已增加");
                    }
                    else
                    {
                        Console.WriteLine("   ❌ 内容居中布局未优化: 主容器边距不足");
                    }
                }

                Console.WriteLine("✅ UI优化效果验证完成");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ UI优化效果验证失败: {ex.Message}");
            }
        }
    }
}
