using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using Microsoft.IdentityModel.Tokens;
using RESServer.Data;
using RESServer.Models;
using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using System.Text;

namespace RESServer.Services
{
    /// <summary>
    /// 认证服务接口
    /// </summary>
    public interface IAuthService
    {
        Task<LoginResponse> LoginAsync(LoginRequest request);
        Task<ApiResponse<UserInfo>> CreateUserAsync(CreateUserRequest request);
        Task<ApiResponse<UserInfo>> UpdateUserAsync(int userId, UpdateUserRequest request);
        Task<ApiResponse> ChangePasswordAsync(int userId, ChangePasswordRequest request);
        Task<ApiResponse> DeleteUserAsync(int userId);
        Task<ApiResponse<List<UserInfo>>> GetUsersAsync();
        Task<ApiResponse<UserInfo>> GetUserByIdAsync(int userId);
        Task<bool> ValidateTokenAsync(string token);
        Task<ClaimsPrincipal?> GetPrincipalFromTokenAsync(string token);
    }

    /// <summary>
    /// 认证服务实现
    /// </summary>
    public class AuthService : IAuthService
    {
        private readonly AuthDbContext _context;
        private readonly AuthConfig _authConfig;
        private readonly ILogger<AuthService> _logger;

        public AuthService(
            AuthDbContext context,
            IOptions<AuthConfig> authConfig,
            ILogger<AuthService> logger)
        {
            _context = context;
            _authConfig = authConfig.Value;
            _logger = logger;
        }

        /// <summary>
        /// 用户登录
        /// </summary>
        public async Task<LoginResponse> LoginAsync(LoginRequest request)
        {
            try
            {
                // 查找用户
                var user = await _context.Users
                    .FirstOrDefaultAsync(u => u.Username == request.Username && u.IsActive);

                if (user == null)
                {
                    _logger.LogWarning("登录失败：用户名 {Username} 不存在或已禁用", request.Username);
                    return new LoginResponse
                    {
                        Success = false,
                        Message = "用户名或密码错误"
                    };
                }

                // 验证密码
                if (!BCrypt.Net.BCrypt.Verify(request.Password, user.PasswordHash))
                {
                    _logger.LogWarning("登录失败：用户 {Username} 密码错误", request.Username);
                    return new LoginResponse
                    {
                        Success = false,
                        Message = "用户名或密码错误"
                    };
                }

                // 更新最后登录时间
                user.LastLoginAt = DateTime.UtcNow;
                await _context.SaveChangesAsync();

                // 生成 JWT Token
                var token = GenerateJwtToken(user);
                var expiresAt = DateTime.UtcNow.AddMinutes(_authConfig.Jwt.ExpirationMinutes);

                _logger.LogInformation("用户 {Username} 登录成功", request.Username);

                return new LoginResponse
                {
                    Success = true,
                    Message = "登录成功",
                    Token = token,
                    ExpiresAt = expiresAt,
                    User = MapToUserInfo(user)
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "登录过程中发生错误");
                return new LoginResponse
                {
                    Success = false,
                    Message = "登录过程中发生错误"
                };
            }
        }

        /// <summary>
        /// 创建用户
        /// </summary>
        public async Task<ApiResponse<UserInfo>> CreateUserAsync(CreateUserRequest request)
        {
            try
            {
                // 检查用户名是否已存在
                var existingUser = await _context.Users
                    .FirstOrDefaultAsync(u => u.Username == request.Username);

                if (existingUser != null)
                {
                    return new ApiResponse<UserInfo>
                    {
                        Success = false,
                        Message = "用户名已存在"
                    };
                }

                // 检查邮箱是否已存在
                if (!string.IsNullOrEmpty(request.Email))
                {
                    var existingEmail = await _context.Users
                        .FirstOrDefaultAsync(u => u.Email == request.Email);

                    if (existingEmail != null)
                    {
                        return new ApiResponse<UserInfo>
                        {
                            Success = false,
                            Message = "邮箱已存在"
                        };
                    }
                }

                // 创建新用户
                var user = new User
                {
                    Username = request.Username,
                    PasswordHash = BCrypt.Net.BCrypt.HashPassword(request.Password),
                    Email = request.Email,
                    FullName = request.FullName,
                    Role = request.Role,
                    Description = request.Description,
                    IsActive = true,
                    CreatedAt = DateTime.UtcNow
                };

                _context.Users.Add(user);
                await _context.SaveChangesAsync();

                _logger.LogInformation("创建用户成功：{Username}", request.Username);

                return new ApiResponse<UserInfo>
                {
                    Success = true,
                    Message = "用户创建成功",
                    Data = MapToUserInfo(user)
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "创建用户时发生错误");
                return new ApiResponse<UserInfo>
                {
                    Success = false,
                    Message = "创建用户时发生错误"
                };
            }
        }

        /// <summary>
        /// 更新用户信息
        /// </summary>
        public async Task<ApiResponse<UserInfo>> UpdateUserAsync(int userId, UpdateUserRequest request)
        {
            try
            {
                var user = await _context.Users.FindAsync(userId);
                if (user == null)
                {
                    return new ApiResponse<UserInfo>
                    {
                        Success = false,
                        Message = "用户不存在"
                    };
                }

                // 检查邮箱是否已被其他用户使用
                if (!string.IsNullOrEmpty(request.Email) && request.Email != user.Email)
                {
                    var existingEmail = await _context.Users
                        .FirstOrDefaultAsync(u => u.Email == request.Email && u.Id != userId);

                    if (existingEmail != null)
                    {
                        return new ApiResponse<UserInfo>
                        {
                            Success = false,
                            Message = "邮箱已被其他用户使用"
                        };
                    }
                }

                // 更新用户信息
                if (request.Email != null) user.Email = request.Email;
                if (request.FullName != null) user.FullName = request.FullName;
                if (request.Role != null) user.Role = request.Role;
                if (request.IsActive.HasValue) user.IsActive = request.IsActive.Value;
                if (request.Description != null) user.Description = request.Description;

                await _context.SaveChangesAsync();

                _logger.LogInformation("更新用户信息成功：{Username}", user.Username);

                return new ApiResponse<UserInfo>
                {
                    Success = true,
                    Message = "用户信息更新成功",
                    Data = MapToUserInfo(user)
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新用户信息时发生错误");
                return new ApiResponse<UserInfo>
                {
                    Success = false,
                    Message = "更新用户信息时发生错误"
                };
            }
        }

        /// <summary>
        /// 修改密码
        /// </summary>
        public async Task<ApiResponse> ChangePasswordAsync(int userId, ChangePasswordRequest request)
        {
            try
            {
                var user = await _context.Users.FindAsync(userId);
                if (user == null)
                {
                    return new ApiResponse
                    {
                        Success = false,
                        Message = "用户不存在"
                    };
                }

                // 验证当前密码
                if (!BCrypt.Net.BCrypt.Verify(request.CurrentPassword, user.PasswordHash))
                {
                    return new ApiResponse
                    {
                        Success = false,
                        Message = "当前密码错误"
                    };
                }

                // 更新密码
                user.PasswordHash = BCrypt.Net.BCrypt.HashPassword(request.NewPassword);
                await _context.SaveChangesAsync();

                _logger.LogInformation("用户 {Username} 修改密码成功", user.Username);

                return new ApiResponse
                {
                    Success = true,
                    Message = "密码修改成功"
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "修改密码时发生错误");
                return new ApiResponse
                {
                    Success = false,
                    Message = "修改密码时发生错误"
                };
            }
        }

        /// <summary>
        /// 删除用户
        /// </summary>
        public async Task<ApiResponse> DeleteUserAsync(int userId)
        {
            try
            {
                var user = await _context.Users.FindAsync(userId);
                if (user == null)
                {
                    return new ApiResponse
                    {
                        Success = false,
                        Message = "用户不存在"
                    };
                }

                // 不允许删除管理员用户
                if (user.Role == "Admin")
                {
                    return new ApiResponse
                    {
                        Success = false,
                        Message = "不允许删除管理员用户"
                    };
                }

                _context.Users.Remove(user);
                await _context.SaveChangesAsync();

                _logger.LogInformation("删除用户成功：{Username}", user.Username);

                return new ApiResponse
                {
                    Success = true,
                    Message = "用户删除成功"
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "删除用户时发生错误");
                return new ApiResponse
                {
                    Success = false,
                    Message = "删除用户时发生错误"
                };
            }
        }

        /// <summary>
        /// 获取所有用户
        /// </summary>
        public async Task<ApiResponse<List<UserInfo>>> GetUsersAsync()
        {
            try
            {
                var users = await _context.Users
                    .OrderBy(u => u.Username)
                    .ToListAsync();

                var userInfos = users.Select(MapToUserInfo).ToList();

                return new ApiResponse<List<UserInfo>>
                {
                    Success = true,
                    Message = "获取用户列表成功",
                    Data = userInfos
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取用户列表时发生错误");
                return new ApiResponse<List<UserInfo>>
                {
                    Success = false,
                    Message = "获取用户列表时发生错误"
                };
            }
        }

        /// <summary>
        /// 根据ID获取用户
        /// </summary>
        public async Task<ApiResponse<UserInfo>> GetUserByIdAsync(int userId)
        {
            try
            {
                var user = await _context.Users.FindAsync(userId);
                if (user == null)
                {
                    return new ApiResponse<UserInfo>
                    {
                        Success = false,
                        Message = "用户不存在"
                    };
                }

                return new ApiResponse<UserInfo>
                {
                    Success = true,
                    Message = "获取用户信息成功",
                    Data = MapToUserInfo(user)
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取用户信息时发生错误");
                return new ApiResponse<UserInfo>
                {
                    Success = false,
                    Message = "获取用户信息时发生错误"
                };
            }
        }

        /// <summary>
        /// 验证 JWT Token
        /// </summary>
        public async Task<bool> ValidateTokenAsync(string token)
        {
            try
            {
                var principal = await GetPrincipalFromTokenAsync(token);
                return principal != null;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 从 Token 获取用户信息
        /// </summary>
        public async Task<ClaimsPrincipal?> GetPrincipalFromTokenAsync(string token)
        {
            try
            {
                var tokenHandler = new JwtSecurityTokenHandler();
                var key = Encoding.UTF8.GetBytes(_authConfig.Jwt.SecretKey);

                var validationParameters = new TokenValidationParameters
                {
                    ValidateIssuerSigningKey = true,
                    IssuerSigningKey = new SymmetricSecurityKey(key),
                    ValidateIssuer = true,
                    ValidIssuer = _authConfig.Jwt.Issuer,
                    ValidateAudience = true,
                    ValidAudience = _authConfig.Jwt.Audience,
                    ValidateLifetime = true,
                    ClockSkew = TimeSpan.Zero
                };

                var principal = tokenHandler.ValidateToken(token, validationParameters, out SecurityToken validatedToken);
                return principal;
            }
            catch
            {
                return null;
            }
        }

        /// <summary>
        /// 生成 JWT Token
        /// </summary>
        private string GenerateJwtToken(User user)
        {
            var tokenHandler = new JwtSecurityTokenHandler();
            var key = Encoding.UTF8.GetBytes(_authConfig.Jwt.SecretKey);

            var claims = new[]
            {
                new Claim(ClaimTypes.NameIdentifier, user.Id.ToString()),
                new Claim(ClaimTypes.Name, user.Username),
                new Claim(ClaimTypes.Role, user.Role),
                new Claim("email", user.Email ?? ""),
                new Claim("fullName", user.FullName ?? "")
            };

            var tokenDescriptor = new SecurityTokenDescriptor
            {
                Subject = new ClaimsIdentity(claims),
                Expires = DateTime.UtcNow.AddMinutes(_authConfig.Jwt.ExpirationMinutes),
                Issuer = _authConfig.Jwt.Issuer,
                Audience = _authConfig.Jwt.Audience,
                SigningCredentials = new SigningCredentials(new SymmetricSecurityKey(key), SecurityAlgorithms.HmacSha256Signature)
            };

            var token = tokenHandler.CreateToken(tokenDescriptor);
            return tokenHandler.WriteToken(token);
        }

        /// <summary>
        /// 将 User 实体映射为 UserInfo
        /// </summary>
        private static UserInfo MapToUserInfo(User user)
        {
            return new UserInfo
            {
                Id = user.Id,
                Username = user.Username,
                Email = user.Email,
                FullName = user.FullName,
                Role = user.Role,
                IsActive = user.IsActive,
                CreatedAt = user.CreatedAt,
                LastLoginAt = user.LastLoginAt
            };
        }
    }
}
