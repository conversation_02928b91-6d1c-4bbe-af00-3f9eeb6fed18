using System;
using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;
using RESClient.Services;
using NPOI.XWPF.UserModel;

namespace RESClient
{
    /// <summary>
    /// 文档合并功能综合测试 - 验证格式保持和完整性
    /// </summary>
    public class TestDocumentMergingComprehensive
    {
        /// <summary>
        /// 运行所有综合测试
        /// </summary>
        public static async Task RunAllTests()
        {
            Console.WriteLine("=== 文档合并功能综合测试 ===");
            
            try
            {
                // 1. 测试模块顺序验证
                Console.WriteLine("\n1. 测试模块顺序验证...");
                await TestModuleOrderValidation();
                
                // 2. 测试格式保持
                Console.WriteLine("\n2. 测试格式保持...");
                await TestFormatPreservation();
                
                // 3. 测试诊断信息收集
                Console.WriteLine("\n3. 测试诊断信息收集...");
                await TestDiagnosticsCollection();
                
                // 4. 测试错误恢复
                Console.WriteLine("\n4. 测试错误恢复...");
                await TestErrorRecovery();
                
                // 5. 测试完整性验证
                Console.WriteLine("\n5. 测试完整性验证...");
                await TestCompletenessValidation();
                
                Console.WriteLine("\n✓ 所有文档合并综合测试完成");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"\n✗ 测试过程中发生错误: {ex.Message}");
                Console.WriteLine($"详细信息: {ex.StackTrace}");
            }
        }

        /// <summary>
        /// 测试模块顺序验证
        /// </summary>
        private static async Task TestModuleOrderValidation()
        {
            try
            {
                Console.WriteLine("   测试标准模块顺序验证...");
                
                var documentMergingService = new DocumentMergingService();
                string testOutputDir = Path.Combine(Path.GetTempPath(), "RESClient_OrderTest_" + Guid.NewGuid().ToString("N").Substring(0, 8));
                Directory.CreateDirectory(testOutputDir);
                
                // 创建测试模块文件（顺序错误）
                var testModules = new List<string> { "目录", "封面", "作业声明" }; // 故意错误的顺序
                
                foreach (var moduleName in testModules)
                {
                    string moduleFile = Path.Combine(testOutputDir, $"{moduleName}.docx");
                    CreateTestDocument(moduleFile, moduleName);
                }
                
                var progressMessages = new List<string>();
                Action<int, string> progressCallback = (progress, message) =>
                {
                    progressMessages.Add(message);
                    Console.WriteLine($"     {message}");
                };
                
                bool result = await documentMergingService.MergeDocumentsAsync(testOutputDir, "测试报告.docx", progressCallback);
                
                // 验证是否检测到顺序问题
                bool hasOrderWarning = progressMessages.Exists(msg => msg.Contains("模块顺序不匹配"));
                Console.WriteLine($"   顺序验证结果: {(hasOrderWarning ? "检测到顺序问题" : "未检测到顺序问题")}");
                
                // 清理测试目录
                Directory.Delete(testOutputDir, true);
                Console.WriteLine("   ✓ 模块顺序验证测试通过");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"   ✗ 模块顺序验证测试失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 测试格式保持
        /// </summary>
        private static async Task TestFormatPreservation()
        {
            try
            {
                Console.WriteLine("   测试格式保持功能...");
                
                var documentMergingService = new DocumentMergingService();
                string testOutputDir = Path.Combine(Path.GetTempPath(), "RESClient_FormatTest_" + Guid.NewGuid().ToString("N").Substring(0, 8));
                Directory.CreateDirectory(testOutputDir);
                
                // 创建包含复杂格式的测试文档
                string moduleFile = Path.Combine(testOutputDir, "封面.docx");
                CreateComplexFormattedDocument(moduleFile);
                
                var progressMessages = new List<string>();
                Action<int, string> progressCallback = (progress, message) =>
                {
                    progressMessages.Add(message);
                    Console.WriteLine($"     {message}");
                };
                
                bool result = await documentMergingService.MergeDocumentsAsync(testOutputDir, "格式测试报告.docx", progressCallback);
                
                // 验证格式相关的消息
                bool hasFormatInfo = progressMessages.Exists(msg => msg.Contains("格式") || msg.Contains("样式"));
                Console.WriteLine($"   格式处理结果: {(hasFormatInfo ? "有格式处理信息" : "无格式处理信息")}");
                
                // 清理测试目录
                Directory.Delete(testOutputDir, true);
                Console.WriteLine("   ✓ 格式保持测试通过");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"   ✗ 格式保持测试失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 测试诊断信息收集
        /// </summary>
        private static async Task TestDiagnosticsCollection()
        {
            try
            {
                Console.WriteLine("   测试诊断信息收集...");
                
                var documentMergingService = new DocumentMergingService();
                string testOutputDir = Path.Combine(Path.GetTempPath(), "RESClient_DiagTest_" + Guid.NewGuid().ToString("N").Substring(0, 8));
                Directory.CreateDirectory(testOutputDir);
                
                // 创建多个测试模块
                var testModules = new List<string> { "封面", "目录", "作业声明" };
                
                foreach (var moduleName in testModules)
                {
                    string moduleFile = Path.Combine(testOutputDir, $"{moduleName}.docx");
                    CreateTestDocument(moduleFile, moduleName);
                }
                
                var progressMessages = new List<string>();
                Action<int, string> progressCallback = (progress, message) =>
                {
                    progressMessages.Add(message);
                    Console.WriteLine($"     {message}");
                };
                
                bool result = await documentMergingService.MergeDocumentsAsync(testOutputDir, "诊断测试报告.docx", progressCallback);
                
                // 验证诊断信息
                bool hasDetailedInfo = progressMessages.Exists(msg => msg.Contains("内容分析") || msg.Contains("统计"));
                Console.WriteLine($"   诊断信息收集结果: {(hasDetailedInfo ? "收集到详细信息" : "信息收集不足")}");
                Console.WriteLine($"   总进度消息数: {progressMessages.Count}");
                
                // 清理测试目录
                Directory.Delete(testOutputDir, true);
                Console.WriteLine("   ✓ 诊断信息收集测试通过");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"   ✗ 诊断信息收集测试失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 测试错误恢复
        /// </summary>
        private static async Task TestErrorRecovery()
        {
            try
            {
                Console.WriteLine("   测试错误恢复机制...");
                
                var documentMergingService = new DocumentMergingService();
                string testOutputDir = Path.Combine(Path.GetTempPath(), "RESClient_RecoveryTest_" + Guid.NewGuid().ToString("N").Substring(0, 8));
                Directory.CreateDirectory(testOutputDir);
                
                // 创建正常文档和损坏文档
                string validFile = Path.Combine(testOutputDir, "封面.docx");
                string corruptFile = Path.Combine(testOutputDir, "目录.docx");
                
                CreateTestDocument(validFile, "封面");
                File.WriteAllText(corruptFile, "这不是有效的Word文档内容"); // 故意创建损坏文件
                
                var progressMessages = new List<string>();
                Action<int, string> progressCallback = (progress, message) =>
                {
                    progressMessages.Add(message);
                    Console.WriteLine($"     {message}");
                };
                
                bool result = await documentMergingService.MergeDocumentsAsync(testOutputDir, "恢复测试报告.docx", progressCallback);
                
                // 验证错误恢复
                bool hasWarnings = progressMessages.Exists(msg => msg.Contains("[WARNING]"));
                bool hasPartialSuccess = progressMessages.Exists(msg => msg.Contains("成功") && msg.Contains("1"));
                
                Console.WriteLine($"   错误恢复结果: 有警告={hasWarnings}, 部分成功={hasPartialSuccess}");
                
                // 清理测试目录
                Directory.Delete(testOutputDir, true);
                Console.WriteLine("   ✓ 错误恢复测试通过");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"   ✗ 错误恢复测试失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 测试完整性验证
        /// </summary>
        private static async Task TestCompletenessValidation()
        {
            try
            {
                Console.WriteLine("   测试完整性验证...");
                
                var documentMergingService = new DocumentMergingService();
                string testOutputDir = Path.Combine(Path.GetTempPath(), "RESClient_CompletenessTest_" + Guid.NewGuid().ToString("N").Substring(0, 8));
                Directory.CreateDirectory(testOutputDir);
                
                // 创建完整的标准模块集
                var standardModules = new List<string>
                {
                    "封面", "目录", "作业声明", "作业、质量检查与验收", "项目基本信息"
                };
                
                foreach (var moduleName in standardModules)
                {
                    string moduleFile = Path.Combine(testOutputDir, $"{moduleName}.docx");
                    CreateTestDocument(moduleFile, moduleName);
                }
                
                var progressMessages = new List<string>();
                Action<int, string> progressCallback = (progress, message) =>
                {
                    progressMessages.Add(message);
                    Console.WriteLine($"     {message}");
                };
                
                bool result = await documentMergingService.MergeDocumentsAsync(testOutputDir, "完整性测试报告.docx", progressCallback);
                
                // 验证完整性
                bool hasStatistics = progressMessages.Exists(msg => msg.Contains("合并统计"));
                bool allProcessed = progressMessages.Exists(msg => msg.Contains($"成功 {standardModules.Count}"));
                
                Console.WriteLine($"   完整性验证结果: 有统计={hasStatistics}, 全部处理={allProcessed}");
                
                // 检查最终文件是否存在
                string finalReport = Path.Combine(testOutputDir, "完整性测试报告.docx");
                bool fileExists = File.Exists(finalReport);
                Console.WriteLine($"   最终报告文件存在: {fileExists}");
                
                // 清理测试目录
                Directory.Delete(testOutputDir, true);
                Console.WriteLine("   ✓ 完整性验证测试通过");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"   ✗ 完整性验证测试失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 创建测试文档
        /// </summary>
        private static void CreateTestDocument(string filePath, string moduleName)
        {
            var document = new XWPFDocument();
            
            // 添加标题段落
            var titleParagraph = document.CreateParagraph();
            titleParagraph.Alignment = ParagraphAlignment.CENTER;
            var titleRun = titleParagraph.CreateRun();
            titleRun.SetText($"{moduleName} - 测试模块");
            titleRun.IsBold = true;
            titleRun.FontSize = 16;
            
            // 添加内容段落
            var contentParagraph = document.CreateParagraph();
            var contentRun = contentParagraph.CreateRun();
            contentRun.SetText($"这是 {moduleName} 模块的测试内容。包含一些基本的文本和格式。");
            
            // 添加测试表格
            var table = document.CreateTable(2, 2);
            table.GetRow(0).GetCell(0).SetText("项目");
            table.GetRow(0).GetCell(1).SetText("值");
            table.GetRow(1).GetCell(0).SetText("模块名称");
            table.GetRow(1).GetCell(1).SetText(moduleName);
            
            // 保存文档
            using (var stream = new FileStream(filePath, FileMode.Create, FileAccess.Write))
            {
                document.Write(stream);
            }
            
            document.Close();
        }

        /// <summary>
        /// 创建复杂格式的测试文档
        /// </summary>
        private static void CreateComplexFormattedDocument(string filePath)
        {
            var document = new XWPFDocument();
            
            // 添加多种格式的段落
            var paragraph1 = document.CreateParagraph();
            paragraph1.Alignment = ParagraphAlignment.CENTER;
            paragraph1.SpacingAfter = 200;
            var run1 = paragraph1.CreateRun();
            run1.SetText("复杂格式测试文档");
            run1.IsBold = true;
            run1.FontSize = 18;
            run1.FontFamily = "宋体";
            
            var paragraph2 = document.CreateParagraph();
            paragraph2.IndentationLeft = 400;
            var run2 = paragraph2.CreateRun();
            run2.SetText("这是一个包含缩进的段落。");
            run2.IsItalic = true;
            
            // 添加复杂表格
            var table = document.CreateTable(3, 3);
            for (int i = 0; i < 3; i++)
            {
                for (int j = 0; j < 3; j++)
                {
                    table.GetRow(i).GetCell(j).SetText($"单元格 ({i+1},{j+1})");
                }
            }
            
            // 保存文档
            using (var stream = new FileStream(filePath, FileMode.Create, FileAccess.Write))
            {
                document.Write(stream);
            }
            
            document.Close();
        }

        /// <summary>
        /// 演示改进功能
        /// </summary>
        public static void DemonstrateImprovements()
        {
            Console.WriteLine("=== 文档合并功能改进演示 ===");
            Console.WriteLine();
            Console.WriteLine("主要改进:");
            Console.WriteLine("1. 格式保持增强");
            Console.WriteLine("   - 完整的段落格式复制 (对齐、间距、缩进、边框)");
            Console.WriteLine("   - 详细的运行格式复制 (字体、大小、样式)");
            Console.WriteLine("   - 表格样式和单元格格式保持");
            Console.WriteLine();
            Console.WriteLine("2. 诊断和监控");
            Console.WriteLine("   - 详细的合并过程日志");
            Console.WriteLine("   - 模块顺序验证");
            Console.WriteLine("   - 内容分析和统计");
            Console.WriteLine("   - 格式问题跟踪");
            Console.WriteLine();
            Console.WriteLine("3. 错误处理和恢复");
            Console.WriteLine("   - 单个模块失败不影响整体合并");
            Console.WriteLine("   - 详细的错误定位信息");
            Console.WriteLine("   - 优雅的格式降级处理");
            Console.WriteLine();
            Console.WriteLine("4. 完整性保证");
            Console.WriteLine("   - 确保所有选中模块都被处理");
            Console.WriteLine("   - 清晰的成功/失败统计");
            Console.WriteLine("   - 部分成功的明确反馈");
            Console.WriteLine();
        }
    }
}
