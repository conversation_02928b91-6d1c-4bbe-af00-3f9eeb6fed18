using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.IO;
using Newtonsoft.Json;
using RESClient.MVP.Base;

namespace RESClient.MVP.Models
{
    /// <summary>
    /// 作业、质量检查与验收参数数据模型
    /// </summary>
    public class WorkQualityParametersModel : BaseModel
    {
        /// <summary>
        /// 作业、质量检查与验收参数数据结构
        /// </summary>
        public class WorkQualityParameters
        {
            // 内业作业人信息
            [DisplayName("内业作业人姓名")]
            [Description("内业作业人员姓名")]
            public string InternalWorkerName { get; set; } = "";

            [DisplayName("内业作业人职业资格证书号")]
            [Description("内业作业人员职业资格证书号")]
            public string InternalWorkerCertificateNumber { get; set; } = "";

            [DisplayName("内业作业人作业证号")]
            [Description("内业作业人员测绘作业证书号")]
            public string InternalWorkerWorkCertificate { get; set; } = "";

            [DisplayName("内业作业人证书有效期")]
            [Description("内业作业人员作业证书有效期")]
            public string InternalWorkerCertificateValidity { get; set; } = "";

            // 外业作业人1信息
            [DisplayName("外业作业人1姓名")]
            [Description("外业作业人员1姓名")]
            public string ExternalWorker1Name { get; set; } = "";

            [DisplayName("外业作业人1职业资格证书号")]
            [Description("外业作业人员1职业资格证书号")]
            public string ExternalWorker1CertificateNumber { get; set; } = "";

            [DisplayName("外业作业人1作业证号")]
            [Description("外业作业人员1测绘作业证书号")]
            public string ExternalWorker1WorkCertificate { get; set; } = "";

            [DisplayName("外业作业人1证书有效期")]
            [Description("外业作业人员1作业证书有效期")]
            public string ExternalWorker1CertificateValidity { get; set; } = "";

            // 外业作业人2信息
            [DisplayName("外业作业人2姓名")]
            [Description("外业作业人员2姓名")]
            public string ExternalWorker2Name { get; set; } = "";

            [DisplayName("外业作业人2职业资格证书号")]
            [Description("外业作业人员2职业资格证书号")]
            public string ExternalWorker2CertificateNumber { get; set; } = "";

            [DisplayName("外业作业人2作业证号")]
            [Description("外业作业人员2测绘作业证书号")]
            public string ExternalWorker2WorkCertificate { get; set; } = "";

            [DisplayName("外业作业人2证书有效期")]
            [Description("外业作业人员2作业证书有效期")]
            public string ExternalWorker2CertificateValidity { get; set; } = "";

            // 一级检查人信息
            [DisplayName("一级检查人姓名")]
            [Description("一级检查人员姓名")]
            public string FirstLevelInspectorName { get; set; } = "";

            [DisplayName("一级检查人职业资格证书号")]
            [Description("一级检查人员职业资格证书号")]
            public string FirstLevelInspectorCertificateNumber { get; set; } = "";

            [DisplayName("一级检查人作业证号")]
            [Description("一级检查人员测绘作业证书号")]
            public string FirstLevelInspectorWorkCertificate { get; set; } = "";

            [DisplayName("一级检查人证书有效期")]
            [Description("一级检查人员作业证书有效期")]
            public string FirstLevelInspectorCertificateValidity { get; set; } = "";

            // 二级检查人信息
            [DisplayName("二级检查人姓名")]
            [Description("二级检查人员姓名")]
            public string SecondLevelInspectorName { get; set; } = "";

            [DisplayName("二级检查人职业资格证书号")]
            [Description("二级检查人员职业资格证书号")]
            public string SecondLevelInspectorCertificateNumber { get; set; } = "";

            [DisplayName("二级检查人作业证号")]
            [Description("二级检查人员测绘作业证书号")]
            public string SecondLevelInspectorWorkCertificate { get; set; } = "";

            [DisplayName("二级检查人证书有效期")]
            [Description("二级检查人员作业证书有效期")]
            public string SecondLevelInspectorCertificateValidity { get; set; } = "";

            // 签发人信息
            [DisplayName("签发人姓名")]
            [Description("签发人员姓名")]
            public string SignerName { get; set; } = "";

            [DisplayName("签发人职业资格证书号")]
            [Description("签发人员职业资格证书号")]
            public string SignerCertificateNumber { get; set; } = "";

            [DisplayName("签发人作业证号")]
            [Description("签发人员测绘作业证书号")]
            public string SignerWorkCertificate { get; set; } = "";

            [DisplayName("签发人证书有效期")]
            [Description("签发人员作业证书有效期")]
            public string SignerCertificateValidity { get; set; } = "";

            // 作业相关信息
            [DisplayName("计算方法")]
            [Description("本次作业面积计算方法")]
            public string CalculationMethod { get; set; } = "";

            [DisplayName("比较器")]
            [Description("实测边长与批准的图纸设计尺寸较差绝对值比较器")]
            public string Comparator { get; set; } = "";

            [DisplayName("边长采用")]
            [Description("本次作业采用的边长计算方法")]
            public string EdgeLengthMethod { get; set; } = "";

            /// <summary>
            /// 获取所有变量的映射字典
            /// </summary>
            /// <returns>变量名到值的映射</returns>
            public Dictionary<string, string> GetVariableMapping()
            {
                return new Dictionary<string, string>
                {
                    // 内业作业人
                    { "${内业姓名}", InternalWorkerName },
                    { "${内业作业人职业资格证书号}", InternalWorkerCertificateNumber },
                    { "${内业作业证号}", InternalWorkerWorkCertificate },
                    { "${内业作业证有效期}", InternalWorkerCertificateValidity },
                    
                    // 外业作业人1
                    { "${外业1姓名}", ExternalWorker1Name },
                    { "${外业作业人1职业资格证书号}", ExternalWorker1CertificateNumber },
                    { "${外业1作业证号}", ExternalWorker1WorkCertificate },
                    { "${外业1作业证有效期}", ExternalWorker1CertificateValidity },
                    
                    // 外业作业人2
                    { "${外业2姓名}", ExternalWorker2Name },
                    { "${外业作业人2职业资格证书号}", ExternalWorker2CertificateNumber },
                    { "${外业2作业证号}", ExternalWorker2WorkCertificate },
                    { "${外业2作业证有效期}", ExternalWorker2CertificateValidity },
                    
                    // 一级检查人
                    { "${一级检查人姓名}", FirstLevelInspectorName },
                    { "${一级检查人职业资格证书号}", FirstLevelInspectorCertificateNumber },
                    { "${一级检查人作业证号}", FirstLevelInspectorWorkCertificate },
                    { "${一级检查人作业证有效期}", FirstLevelInspectorCertificateValidity },
                    
                    // 二级检查人
                    { "${二级检查人姓名}", SecondLevelInspectorName },
                    { "${二级检查人职业资格证书号}", SecondLevelInspectorCertificateNumber },
                    { "${二级检查人作业证号}", SecondLevelInspectorWorkCertificate },
                    { "${二级检查人作业证有效期}", SecondLevelInspectorCertificateValidity },
                    
                    // 签发人
                    { "${签发人姓名}", SignerName },
                    { "${签发人职业资格证书号}", SignerCertificateNumber },
                    { "${签发人作业证号}", SignerWorkCertificate },
                    { "${签发人作业证有效期}", SignerCertificateValidity },
                    
                    // 作业信息
                    { "${算法}", CalculationMethod },
                    { "${比较器}", Comparator },
                    { "${边长采用}", EdgeLengthMethod }
                };
            }

            /// <summary>
            /// 验证参数
            /// </summary>
            /// <returns>验证结果和错误信息</returns>
            public (bool IsValid, List<string> Errors) Validate()
            {
                var errors = new List<string>();

                // 验证内业作业人信息
                if (string.IsNullOrWhiteSpace(InternalWorkerName))
                    errors.Add("内业作业人姓名不能为空");
                if (string.IsNullOrWhiteSpace(InternalWorkerCertificateNumber))
                    errors.Add("内业作业人职业资格证书号不能为空");
                if (string.IsNullOrWhiteSpace(InternalWorkerWorkCertificate))
                    errors.Add("内业作业人作业证号不能为空");
                if (string.IsNullOrWhiteSpace(InternalWorkerCertificateValidity))
                    errors.Add("内业作业人证书有效期不能为空");

                // 验证外业作业人1信息
                if (string.IsNullOrWhiteSpace(ExternalWorker1Name))
                    errors.Add("外业作业人1姓名不能为空");
                if (string.IsNullOrWhiteSpace(ExternalWorker1CertificateNumber))
                    errors.Add("外业作业人1职业资格证书号不能为空");
                if (string.IsNullOrWhiteSpace(ExternalWorker1WorkCertificate))
                    errors.Add("外业作业人1作业证号不能为空");
                if (string.IsNullOrWhiteSpace(ExternalWorker1CertificateValidity))
                    errors.Add("外业作业人1证书有效期不能为空");

                // 验证外业作业人2信息
                if (string.IsNullOrWhiteSpace(ExternalWorker2Name))
                    errors.Add("外业作业人2姓名不能为空");
                if (string.IsNullOrWhiteSpace(ExternalWorker2CertificateNumber))
                    errors.Add("外业作业人2职业资格证书号不能为空");
                if (string.IsNullOrWhiteSpace(ExternalWorker2WorkCertificate))
                    errors.Add("外业作业人2作业证号不能为空");
                if (string.IsNullOrWhiteSpace(ExternalWorker2CertificateValidity))
                    errors.Add("外业作业人2证书有效期不能为空");

                // 验证一级检查人信息
                if (string.IsNullOrWhiteSpace(FirstLevelInspectorName))
                    errors.Add("一级检查人姓名不能为空");
                if (string.IsNullOrWhiteSpace(FirstLevelInspectorCertificateNumber))
                    errors.Add("一级检查人职业资格证书号不能为空");
                if (string.IsNullOrWhiteSpace(FirstLevelInspectorWorkCertificate))
                    errors.Add("一级检查人作业证号不能为空");
                if (string.IsNullOrWhiteSpace(FirstLevelInspectorCertificateValidity))
                    errors.Add("一级检查人证书有效期不能为空");

                // 验证二级检查人信息
                if (string.IsNullOrWhiteSpace(SecondLevelInspectorName))
                    errors.Add("二级检查人姓名不能为空");
                if (string.IsNullOrWhiteSpace(SecondLevelInspectorCertificateNumber))
                    errors.Add("二级检查人职业资格证书号不能为空");
                if (string.IsNullOrWhiteSpace(SecondLevelInspectorWorkCertificate))
                    errors.Add("二级检查人作业证号不能为空");
                if (string.IsNullOrWhiteSpace(SecondLevelInspectorCertificateValidity))
                    errors.Add("二级检查人证书有效期不能为空");

                // 验证签发人信息
                if (string.IsNullOrWhiteSpace(SignerName))
                    errors.Add("签发人姓名不能为空");
                if (string.IsNullOrWhiteSpace(SignerCertificateNumber))
                    errors.Add("签发人职业资格证书号不能为空");
                if (string.IsNullOrWhiteSpace(SignerWorkCertificate))
                    errors.Add("签发人作业证号不能为空");
                if (string.IsNullOrWhiteSpace(SignerCertificateValidity))
                    errors.Add("签发人证书有效期不能为空");

                // 验证作业信息
                if (string.IsNullOrWhiteSpace(CalculationMethod))
                    errors.Add("计算方法不能为空");
                if (string.IsNullOrWhiteSpace(Comparator))
                    errors.Add("比较器不能为空");
                if (string.IsNullOrWhiteSpace(EdgeLengthMethod))
                    errors.Add("边长采用不能为空");

                return (errors.Count == 0, errors);
            }
        }

        private readonly string _configFilePath;
        private WorkQualityParameters _currentParameters;

        public WorkQualityParametersModel()
        {
            // 配置文件保存在应用程序数据目录
            string appDataPath = Path.Combine(
                Environment.GetFolderPath(Environment.SpecialFolder.LocalApplicationData),
                "RESClient");

            if (!Directory.Exists(appDataPath))
            {
                Directory.CreateDirectory(appDataPath);
            }

            _configFilePath = Path.Combine(appDataPath, "work_quality_parameters.json");
            _currentParameters = LoadParameters();
        }

        /// <summary>
        /// 获取当前参数
        /// </summary>
        /// <returns>当前参数</returns>
        public WorkQualityParameters GetCurrentParameters()
        {
            return _currentParameters;
        }

        /// <summary>
        /// 更新参数
        /// </summary>
        /// <param name="parameters">新参数</param>
        /// <returns>是否成功</returns>
        public bool UpdateParameters(WorkQualityParameters parameters)
        {
            try
            {
                _currentParameters = parameters ?? throw new ArgumentNullException(nameof(parameters));
                return SaveParameters();
            }
            catch (Exception ex)
            {
                HandleException(ex);
                return false;
            }
        }

        /// <summary>
        /// 重置为默认参数
        /// </summary>
        /// <returns>是否成功</returns>
        public bool ResetToDefaults()
        {
            try
            {
                _currentParameters = CreateDefaultParameters();
                return SaveParameters();
            }
            catch (Exception ex)
            {
                HandleException(ex);
                return false;
            }
        }

        /// <summary>
        /// 保存参数
        /// </summary>
        /// <returns>是否成功</returns>
        private bool SaveParameters()
        {
            try
            {
                string json = JsonConvert.SerializeObject(_currentParameters, Formatting.Indented);
                File.WriteAllText(_configFilePath, json, System.Text.Encoding.UTF8);
                return true;
            }
            catch (Exception ex)
            {
                HandleException(ex);
                return false;
            }
        }

        /// <summary>
        /// 加载参数
        /// </summary>
        /// <returns>加载的参数，如果失败则返回默认参数</returns>
        private WorkQualityParameters LoadParameters()
        {
            try
            {
                if (!File.Exists(_configFilePath))
                {
                    return CreateDefaultParameters();
                }

                string json = File.ReadAllText(_configFilePath, System.Text.Encoding.UTF8);
                var parameters = JsonConvert.DeserializeObject<WorkQualityParameters>(json);

                return parameters ?? CreateDefaultParameters();
            }
            catch (Exception ex)
            {
                HandleException(ex);
                return CreateDefaultParameters();
            }
        }

        /// <summary>
        /// 创建默认参数
        /// </summary>
        /// <returns>默认参数</returns>
        private WorkQualityParameters CreateDefaultParameters()
        {
            return new WorkQualityParameters
            {
                // 使用示例数据作为默认值
                InternalWorkerName = "贾羽",
                InternalWorkerCertificateNumber = "176800300350263Z",
                InternalWorkerWorkCertificate = "5100014821",
                InternalWorkerCertificateValidity = "2022年5月20日至2025年5月19日",

                ExternalWorker1Name = "贾羽",
                ExternalWorker1CertificateNumber = "176800300350263Z",
                ExternalWorker1WorkCertificate = "5100014821",
                ExternalWorker1CertificateValidity = "2022年5月20日至2025年5月19日",

                ExternalWorker2Name = "杨林",
                ExternalWorker2CertificateNumber = "176800300350263S",
                ExternalWorker2WorkCertificate = "5100014822",
                ExternalWorker2CertificateValidity = "2022年5月20日至2025年5月19日",

                FirstLevelInspectorName = "陈小波",
                FirstLevelInspectorCertificateNumber = "186800300350045G",
                FirstLevelInspectorWorkCertificate = "5100014823",
                FirstLevelInspectorCertificateValidity = "2022年5月20日至2025年5月19日",

                SecondLevelInspectorName = "熊春蓉",
                SecondLevelInspectorCertificateNumber = "146800300350122Z",
                SecondLevelInspectorWorkCertificate = "5100102910",
                SecondLevelInspectorCertificateValidity = "2022年5月20日至2025年5月19日",

                SignerName = "陈鸿阳",
                SignerCertificateNumber = "066800300350042Z",
                SignerWorkCertificate = "5100016815",
                SignerCertificateValidity = "2022年12月14日至2025年12月13日",

                CalculationMethod = "几何图形解析法",
                Comparator = "小于",
                EdgeLengthMethod = "设计边长"
            };
        }
    }
}
