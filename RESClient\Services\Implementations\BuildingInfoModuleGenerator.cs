using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.IO;
using System.Linq;
using NPOI.SS.UserModel;
using NPOI.XWPF.UserModel;
using NPOI.Util;
using RESClient.Services;


namespace RESClient.Services.Implementations
{
    /// <summary>
    /// 楼栋基本信息模块生成器
    /// </summary>
    public class BuildingInfoModuleGenerator : IReportModuleGenerator
    {
        private const string TEMPLATE_FILE_NAME = "楼栋基本信息.docx";
        private const string EXCEL_SHEET_NAME = "楼盘表信息";
        private ArchiveExtractionService _archiveExtractionService;

        /// <summary>
        /// 模块名称
        /// </summary>
        public string ModuleName => "楼栋基本信息";

        /// <summary>
        /// 生成报告模块
        /// </summary>
        /// <param name="parameters">生成参数</param>
        /// <param name="progressCallback">进度回调</param>
        /// <returns>生成结果</returns>
        public async Task<bool> GenerateAsync(Dictionary<string, object> parameters, Action<int, string> progressCallback)
        {
            _archiveExtractionService = new ArchiveExtractionService();

            try
            {
                // 记录开始进度
                progressCallback?.Invoke(0, $"[INFO]开始生成{ModuleName}...");

                // 1. 获取必要参数
                if (!parameters.TryGetValue("DataFolder", out object dataFolderObj) ||
                    !(dataFolderObj is string dataDirectory))
                {
                    progressCallback?.Invoke(0, $"[ERROR]未提供数据目录路径");
                    return false;
                }

                if (!parameters.TryGetValue("OutputDir", out object outputDirObj) ||
                    !(outputDirObj is string outputDirectory))
                {
                    progressCallback?.Invoke(0, $"[ERROR]未提供输出目录路径");
                    return false;
                }

                progressCallback?.Invoke(5, $"[INFO]数据目录: {dataDirectory}");
                progressCallback?.Invoke(5, $"[INFO]输出目录: {outputDirectory}");

                // 2. 查找CGB.xls文件（从7z压缩包中提取）
                progressCallback?.Invoke(10, "[INFO]查找CGB.xls文件...");
                string excelFile = FindCGBFile(dataDirectory, progressCallback);
                if (string.IsNullOrEmpty(excelFile))
                {
                    progressCallback?.Invoke(25, "[ERROR]未找到CGB.xls文件");
                    return false;
                }

                progressCallback?.Invoke(30, $"[INFO]找到CGB文件: {Path.GetFileName(excelFile)}");

                // 3. 读取Excel数据
                progressCallback?.Invoke(25, "[INFO]读取楼盘表信息数据...");
                var buildingData = ReadBuildingData(excelFile, progressCallback);
                if (buildingData == null || buildingData.Count == 0)
                {
                    progressCallback?.Invoke(25, "[ERROR]未能读取到有效的楼盘表信息数据");
                    return false;
                }

                progressCallback?.Invoke(40, $"[SUCCESS]成功读取{buildingData.Count}栋楼的基本信息数据");

                // 4. 获取模板路径
                string templateDirectory = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "报告模板");
                string templatePath = Path.Combine(templateDirectory, "06_楼栋基本信息", TEMPLATE_FILE_NAME);
                if (!File.Exists(templatePath))
                {
                    progressCallback?.Invoke(40, $"[ERROR]模板文件不存在: {templatePath}");
                    return false;
                }

                progressCallback?.Invoke(45, $"[INFO]找到模板文件: {templatePath}");

                // 5. 确保输出目录存在
                Directory.CreateDirectory(outputDirectory);
                string outputPath = Path.Combine(outputDirectory, TEMPLATE_FILE_NAME);

                // 6. 获取用户参数
                progressCallback?.Invoke(45, "[INFO]获取用户参数...");
                string userBuildingAge = "";
                string userInspectionDate = "";

                try
                {
                    // 尝试加载用户参数，如果失败则使用默认值
                    var parametersModel = new RESClient.MVP.Models.BuildingInfoParametersModel();
                    var userParameters = parametersModel.GetCurrentParameters();
                    userBuildingAge = userParameters.BuildingAge ?? "";
                    userInspectionDate = userParameters.InspectionDate ?? "";
                    progressCallback?.Invoke(48, "[SUCCESS]用户参数加载成功");
                }
                catch (Exception ex)
                {
                    progressCallback?.Invoke(48, $"[WARNING]用户参数加载失败，使用默认值: {ex.Message}");
                    userBuildingAge = "";
                    userInspectionDate = "";
                }

                // 7. 生成Word文档
                progressCallback?.Invoke(50, "[INFO]开始生成Word文档...");
                bool result = await Task.Run(() => GenerateWordDocument(templatePath, outputPath, buildingData, userBuildingAge, userInspectionDate, progressCallback));

                progressCallback?.Invoke(100, result ? $"[SUCCESS]{ModuleName}生成完成" : $"[ERROR]{ModuleName}生成失败");
                return result;
            }
            catch (Exception ex)
            {
                progressCallback?.Invoke(100, $"[ERROR]{ModuleName}生成过程中发生异常: {ex.Message}");
                return false;
            }
            finally
            {
                // 清理临时文件
                _archiveExtractionService?.Dispose();
            }
        }

        /// <summary>
        /// 查找CGB.xls文件（支持从7z压缩包中提取）
        /// </summary>
        /// <param name="directoryPath">目录路径</param>
        /// <param name="progressCallback">进度回调</param>
        /// <returns>文件路径</returns>
        private string FindCGBFile(string directoryPath, Action<int, string> progressCallback = null)
        {
            // 第一步：尝试查找包含4个指定工作表的Excel文件（最高优先级）
            progressCallback?.Invoke(5, "[INFO]查找包含标准工作表的Excel文件...");
            string standardExcelFile = FindStandardExcelFile(directoryPath, progressCallback);
            if (!string.IsNullOrEmpty(standardExcelFile))
            {
                progressCallback?.Invoke(25, "[INFO]找到包含标准工作表的Excel文件");
                return standardExcelFile;
            }

            // 第二步：尝试直接查找CGB.xls文件（向后兼容）
            progressCallback?.Invoke(10, "[INFO]查找CGB.xls文件...");
            var files = Directory.GetFiles(directoryPath, "*.xls*", SearchOption.AllDirectories)
                .Where(file => Path.GetFileName(file).ToLower().Contains("cgb"))
                .ToArray();

            if (files.Any())
            {
                progressCallback?.Invoke(25, "[INFO]找到直接的CGB.xls文件");
                return files[0];
            }

            // 第三步：如果没有找到直接的CGB文件，尝试从7z压缩包中提取
            progressCallback?.Invoke(15, "[INFO]未找到直接的CGB文件，尝试从成果包.7z中提取...");
            return _archiveExtractionService?.ExtractAndFindCGBFile(directoryPath, progressCallback);
        }

        /// <summary>
        /// 查找包含标准工作表的Excel文件
        /// </summary>
        /// <param name="directoryPath">目录路径</param>
        /// <param name="progressCallback">进度回调</param>
        /// <returns>符合条件的Excel文件路径，如果未找到则返回null</returns>
        private string FindStandardExcelFile(string directoryPath, Action<int, string> progressCallback = null)
        {
            try
            {
                // 查找所有Excel文件
                var excelFiles = Directory.GetFiles(directoryPath, "*.xls*", SearchOption.AllDirectories);

                progressCallback?.Invoke(7, $"[INFO]找到 {excelFiles.Length} 个Excel文件，开始验证工作表...");

                foreach (string filePath in excelFiles)
                {
                    try
                    {
                        if (ValidateExcelFileStructure(filePath))
                        {
                            progressCallback?.Invoke(20, $"[INFO]验证通过: {Path.GetFileName(filePath)}");
                            return filePath;
                        }
                    }
                    catch (Exception ex)
                    {
                        progressCallback?.Invoke(8, $"[WARNING]验证文件失败 {Path.GetFileName(filePath)}: {ex.Message}");
                        continue;
                    }
                }

                progressCallback?.Invoke(10, "[INFO]未找到包含标准工作表的Excel文件");
                return null;
            }
            catch (Exception ex)
            {
                progressCallback?.Invoke(10, $"[WARNING]查找标准Excel文件时出错: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 验证Excel文件是否包含4个指定的工作表
        /// </summary>
        /// <param name="filePath">Excel文件路径</param>
        /// <returns>是否符合条件</returns>
        private bool ValidateExcelFileStructure(string filePath)
        {
            // 定义必需的工作表名称
            var requiredSheets = new HashSet<string>
            {
                "房屋建筑面积总表",
                "共有建筑面积的分摊",
                "房产分户面积统计表",
                "楼盘表信息"
            };

            try
            {
                using (var stream = new FileStream(filePath, FileMode.Open, FileAccess.Read, FileShare.ReadWrite))
                {
                    IWorkbook workbook = WorkbookFactory.Create(stream);

                    // 检查工作表数量是否为4
                    if (workbook.NumberOfSheets != 4)
                    {
                        return false;
                    }

                    // 检查工作表名称是否完全匹配
                    var actualSheets = new HashSet<string>();
                    for (int i = 0; i < workbook.NumberOfSheets; i++)
                    {
                        actualSheets.Add(workbook.GetSheetName(i));
                    }

                    // 必须完全匹配所有4个工作表名称
                    return requiredSheets.SetEquals(actualSheets);
                }
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 读取楼盘表信息数据
        /// </summary>
        /// <param name="filePath">Excel文件路径</param>
        /// <param name="progressCallback">进度回调</param>
        /// <returns>按栋号分组的楼栋基本信息</returns>
        private Dictionary<string, BuildingInfo> ReadBuildingData(string filePath, Action<int, string> progressCallback)
        {
            var buildingData = new Dictionary<string, BuildingInfo>();

            try
            {
                if (string.IsNullOrWhiteSpace(filePath) || !File.Exists(filePath))
                {
                    progressCallback?.Invoke(25, $"[ERROR]Excel文件不存在：{filePath}");
                    return buildingData;
                }

                using (var stream = new FileStream(filePath, FileMode.Open, FileAccess.Read, FileShare.ReadWrite))
                {
                    progressCallback?.Invoke(25, "[INFO]正在打开Excel文件...");
                    IWorkbook workbook = WorkbookFactory.Create(stream);

                    ISheet worksheet = FindSheet(workbook, EXCEL_SHEET_NAME);

                    if (worksheet == null)
                    {
                        progressCallback?.Invoke(30, $"[ERROR]未找到{EXCEL_SHEET_NAME}工作表");
                        return buildingData;
                    }

                progressCallback?.Invoke(30, $"[INFO]找到{EXCEL_SHEET_NAME}，开始解析数据...");

                // 查找表头行
                int headerRowIndex = FindHeaderRow(worksheet);
                if (headerRowIndex == -1)
                {
                    progressCallback?.Invoke(32, "[ERROR]未找到有效的表头行");
                    return buildingData;
                }

                // 获取列映射
                var columnMapping = GetColumnMapping(worksheet.GetRow(headerRowIndex));
                progressCallback?.Invoke(35, $"[INFO]列映射完成，共{columnMapping.Count}列");

                // 读取数据行
                int dataStartRow = headerRowIndex + 1;
                for (int rowIndex = dataStartRow; rowIndex <= worksheet.LastRowNum; rowIndex++)
                {
                    IRow row = worksheet.GetRow(rowIndex);
                    if (row == null || IsEmptyRow(row)) continue;

                    ProcessDataRow(row, columnMapping, buildingData);
                }

                progressCallback?.Invoke(38, $"[SUCCESS]数据读取完成，共{buildingData.Count}栋楼");
                }
            }
            catch (Exception ex)
            {
                progressCallback?.Invoke(25, $"[ERROR]读取Excel出错: {ex.Message}");
            }

            return buildingData;
        }

        /// <summary>
        /// 楼栋基本信息数据类
        /// </summary>
        private class BuildingInfo
        {
            public string BuildingNumber { get; set; } = "";
            public int UnitCount { get; set; } = 0;
            public int MaxFloor { get; set; } = 0;
            public int MinFloor { get; set; } = 0; // 最小楼层（地下室）
            public HashSet<string> Structures { get; set; } = new HashSet<string>();
            public Dictionary<int, HashSet<string>> FloorPurposes { get; set; } = new Dictionary<int, HashSet<string>>();
            public HashSet<string> Units { get; set; } = new HashSet<string>(); // 存储不重复的单元号
            public string BuildingAge { get; set; } = ""; // 建成年代 - 用户输入
            public string InspectionDate { get; set; } = ""; // 勘验日期 - 用户输入

            /// <summary>
            /// 获取单元数显示文本
            /// </summary>
            public string GetUnitCountText()
            {
                // 使用不重复单元号的数量，而不是简单的计数
                int distinctUnitCount = Units.Count;
                return distinctUnitCount == 0 ? "\\" : distinctUnitCount.ToString();
            }

            /// <summary>
            /// 获取楼层信息显示文本
            /// </summary>
            public string GetFloorInfoText()
            {
                // 如果没有楼层数据
                if (MaxFloor == 0 && MinFloor == 0) return "\\";

                // 计算地上和地下楼层数
                int aboveGroundFloors = MaxFloor > 0 ? MaxFloor : 0;
                int belowGroundFloors = MinFloor < 0 ? Math.Abs(MinFloor) : 0;
                int totalFloors = aboveGroundFloors + belowGroundFloors;

                // 根据不同情况格式化显示文本
                if (aboveGroundFloors > 0 && belowGroundFloors > 0)
                {
                    return $"总楼层为{totalFloors}层, 地下{belowGroundFloors}层，地上{aboveGroundFloors}层";
                }
                else if (aboveGroundFloors > 0)
                {
                    return $"总楼层为{aboveGroundFloors}层, 地上{aboveGroundFloors}层";
                }
                else if (belowGroundFloors > 0)
                {
                    return $"总楼层为{belowGroundFloors}层, 地下{belowGroundFloors}层";
                }
                else
                {
                    return "\\";
                }
            }

            /// <summary>
            /// 获取建筑结构显示文本
            /// </summary>
            public string GetStructureText()
            {
                if (Structures.Count == 0) return "\\";
                return string.Join("、", Structures.Where(s => !string.IsNullOrWhiteSpace(s)));
            }

            /// <summary>
            /// 获取设计用途显示文本
            /// </summary>
            public string GetDesignPurposeText()
            {
                if (FloorPurposes.Count == 0) return "\\";

                var result = new List<string>();

                // 按楼层排序处理
                var sortedFloors = FloorPurposes.OrderBy(kvp => kvp.Key).ToList();

                // 新的逻辑：按楼层分组，每个楼层列出所有用途
                // 首先创建楼层到用途组合的映射
                var floorToPurposeGroups = new Dictionary<int, string>();

                foreach (var floorData in sortedFloors)
                {
                    int floor = floorData.Key;
                    var purposes = floorData.Value.OrderBy(p => p).ToList(); // 按用途排序以保持一致性

                    // 将该楼层的所有用途用逗号连接
                    string purposeText = string.Join("、", purposes);
                    floorToPurposeGroups[floor] = purposeText;
                }

                // 现在尝试合并具有相同用途组合的连续楼层
                var mergedRanges = MergeConsecutiveFloorsWithSamePurposes(floorToPurposeGroups);

                // 生成最终的文本
                foreach (var range in mergedRanges)
                {
                    string floorText;
                    if (range.StartFloor == range.EndFloor)
                    {
                        floorText = $"{range.StartFloor}层为{range.Purposes}";
                    }
                    else
                    {
                        floorText = $"{range.StartFloor}-{range.EndFloor}层为{range.Purposes}";
                    }
                    result.Add(floorText);
                }

                return string.Join("；\n", result) + "；";
            }

            /// <summary>
            /// 楼层范围数据类
            /// </summary>
            private class FloorRange
            {
                public int StartFloor { get; set; }
                public int EndFloor { get; set; }
                public string Purposes { get; set; }
            }

            /// <summary>
            /// 合并具有相同用途组合的连续楼层
            /// </summary>
            private List<FloorRange> MergeConsecutiveFloorsWithSamePurposes(Dictionary<int, string> floorToPurposeGroups)
            {
                var result = new List<FloorRange>();
                if (floorToPurposeGroups.Count == 0) return result;

                var sortedFloors = floorToPurposeGroups.OrderBy(kvp => kvp.Key).ToList();

                int currentStart = sortedFloors[0].Key;
                int currentEnd = sortedFloors[0].Key;
                string currentPurposes = sortedFloors[0].Value;

                for (int i = 1; i < sortedFloors.Count; i++)
                {
                    int floor = sortedFloors[i].Key;
                    string purposes = sortedFloors[i].Value;

                    // 检查是否可以合并：楼层连续且用途相同
                    if (floor == currentEnd + 1 && purposes == currentPurposes)
                    {
                        // 扩展当前范围
                        currentEnd = floor;
                    }
                    else
                    {
                        // 保存当前范围并开始新范围
                        result.Add(new FloorRange
                        {
                            StartFloor = currentStart,
                            EndFloor = currentEnd,
                            Purposes = currentPurposes
                        });

                        currentStart = floor;
                        currentEnd = floor;
                        currentPurposes = purposes;
                    }
                }

                // 添加最后一个范围
                result.Add(new FloorRange
                {
                    StartFloor = currentStart,
                    EndFloor = currentEnd,
                    Purposes = currentPurposes
                });

                return result;
            }


        }

        /// <summary>
        /// 查找工作表
        /// </summary>
        /// <param name="workbook">Excel工作簿</param>
        /// <param name="sheetName">工作表名</param>
        /// <returns>工作表</returns>
        private ISheet FindSheet(IWorkbook workbook, string sheetName)
        {
            // 首先尝试通过名称直接查找工作表
            ISheet sheet = workbook.GetSheet(sheetName);
            if (sheet != null) return sheet;

            // 其次尝试通过包含名称查找
            for (int i = 0; i < workbook.NumberOfSheets; i++)
            {
                if (workbook.GetSheetName(i).Contains(sheetName))
                {
                    return workbook.GetSheetAt(i);
                }
            }

            return null;
        }

        /// <summary>
        /// 查找表头行
        /// </summary>
        /// <param name="worksheet">工作表</param>
        /// <returns>表头行索引，-1表示未找到</returns>
        private int FindHeaderRow(ISheet worksheet)
        {
            // 在前20行中查找包含"栋号"的行作为表头行
            for (int i = 0; i <= Math.Min(worksheet.LastRowNum, 19); i++)
            {
                IRow row = worksheet.GetRow(i);
                if (row == null) continue;

                for (int j = 0; j < row.LastCellNum; j++)
                {
                    NPOI.SS.UserModel.ICell cell = row.GetCell(j);
                    string cellValue = GetCellValueAsString(cell);
                    if (cellValue.Contains("栋号"))
                    {
                        return i;
                    }
                }
            }

            return -1;
        }

        /// <summary>
        /// 获取列映射
        /// </summary>
        /// <param name="headerRow">表头行</param>
        /// <returns>列索引到列名的映射</returns>
        private Dictionary<int, string> GetColumnMapping(IRow headerRow)
        {
            var mapping = new Dictionary<int, string>();

            if (headerRow == null) return mapping;

            for (int i = 0; i < headerRow.LastCellNum; i++)
            {
                NPOI.SS.UserModel.ICell cell = headerRow.GetCell(i);
                string cellValue = GetCellValueAsString(cell).Trim();

                if (!string.IsNullOrEmpty(cellValue))
                {
                    mapping[i] = cellValue;
                }
            }

            return mapping;
        }

        /// <summary>
        /// 处理数据行
        /// </summary>
        /// <param name="row">数据行</param>
        /// <param name="columnMapping">列映射</param>
        /// <param name="buildingData">楼栋数据集合</param>
        private void ProcessDataRow(IRow row, Dictionary<int, string> columnMapping, Dictionary<string, BuildingInfo> buildingData)
        {
            var rowData = new Dictionary<string, string>();

            // 读取行数据
            foreach (var mapping in columnMapping)
            {
                int colIndex = mapping.Key;
                string fieldName = mapping.Value;
                string cellValue = GetCellValueAsString(row.GetCell(colIndex));
                rowData[fieldName] = cellValue;
            }

            // 获取栋号
            string buildingNumber = GetFieldValue(rowData, "栋号");
            if (string.IsNullOrWhiteSpace(buildingNumber)) return;

            // 确保楼栋信息对象存在
            if (!buildingData.ContainsKey(buildingNumber))
            {
                buildingData[buildingNumber] = new BuildingInfo { BuildingNumber = buildingNumber };
            }

            var building = buildingData[buildingNumber];

            // 处理单元数 - 收集不重复的单元号
            string unit = GetFieldValue(rowData, "单元");
            if (!string.IsNullOrWhiteSpace(unit))
            {
                // 将单元号添加到HashSet中，自动去重
                building.Units.Add(unit.Trim());
                // 更新UnitCount为当前不重复单元的数量（保持向后兼容）
                building.UnitCount = building.Units.Count;
            }

            // 处理楼层
            string floorStr = GetFieldValue(rowData, "楼层");
            if (int.TryParse(floorStr, out int floor))
            {
                // 更新最大和最小楼层
                if (floor > 0)
                {
                    building.MaxFloor = Math.Max(building.MaxFloor, floor);
                }
                else if (floor < 0)
                {
                    building.MinFloor = Math.Min(building.MinFloor, floor);
                }

                // 处理设计用途（包括地下室）
                string designPurpose = GetFieldValue(rowData, "设计用途");
                if (!string.IsNullOrWhiteSpace(designPurpose))
                {
                    // 如果该楼层还没有用途记录，创建新的HashSet
                    if (!building.FloorPurposes.ContainsKey(floor))
                    {
                        building.FloorPurposes[floor] = new HashSet<string>();
                    }
                    // 添加用途到该楼层的用途集合中（自动去重）
                    building.FloorPurposes[floor].Add(designPurpose);
                }
            }

            // 处理建筑结构
            string structure = GetFieldValue(rowData, "结构");
            if (!string.IsNullOrWhiteSpace(structure))
            {
                building.Structures.Add(structure);
            }
        }

        /// <summary>
        /// 获取单元格值作为字符串
        /// </summary>
        /// <param name="cell">单元格</param>
        /// <returns>字符串值</returns>
        private string GetCellValueAsString(NPOI.SS.UserModel.ICell cell)
        {
            if (cell == null) return string.Empty;

            switch (cell.CellType)
            {
                case CellType.String:
                    return cell.StringCellValue ?? string.Empty;
                case CellType.Numeric:
                    if (DateUtil.IsCellDateFormatted(cell))
                    {
                        return cell.DateCellValue.ToString();
                    }
                    return cell.NumericCellValue.ToString();
                case CellType.Boolean:
                    return cell.BooleanCellValue.ToString();
                case CellType.Formula:
                    try
                    {
                        return cell.StringCellValue ?? string.Empty;
                    }
                    catch
                    {
                        try
                        {
                            return cell.NumericCellValue.ToString();
                        }
                        catch
                        {
                            return string.Empty;
                        }
                    }
                default:
                    return string.Empty;
            }
        }

        /// <summary>
        /// 检查是否为空行
        /// </summary>
        /// <param name="row">行</param>
        /// <returns>是否为空行</returns>
        private bool IsEmptyRow(IRow row)
        {
            if (row == null) return true;

            for (int i = 0; i < row.LastCellNum; i++)
            {
                NPOI.SS.UserModel.ICell cell = row.GetCell(i);
                if (!string.IsNullOrWhiteSpace(GetCellValueAsString(cell)))
                {
                    return false;
                }
            }

            return true;
        }

        /// <summary>
        /// 从行数据中获取字段值
        /// </summary>
        /// <param name="rowData">行数据</param>
        /// <param name="fieldName">字段名</param>
        /// <returns>字段值</returns>
        private string GetFieldValue(Dictionary<string, string> rowData, string fieldName)
        {
            return rowData.TryGetValue(fieldName, out string value) ? value?.Trim() ?? string.Empty : string.Empty;
        }

        /// <summary>
        /// 生成Word文档
        /// </summary>
        /// <param name="templatePath">模板路径</param>
        /// <param name="outputPath">输出路径</param>
        /// <param name="buildingData">楼栋数据</param>
        /// <param name="userBuildingAge">用户输入的建成年代</param>
        /// <param name="userInspectionDate">用户输入的勘验日期</param>
        /// <param name="progressCallback">进度回调</param>
        /// <returns>是否成功</returns>
        private bool GenerateWordDocument(string templatePath, string outputPath, Dictionary<string, BuildingInfo> buildingData, string userBuildingAge, string userInspectionDate, Action<int, string> progressCallback)
        {
            try
            {
                // 复制模板文件到输出路径
                File.Copy(templatePath, outputPath, true);

                XWPFDocument doc;
                XWPFTable originalTemplate;

                // 首先读取模板文档
                using (FileStream fs = new FileStream(outputPath, FileMode.Open, FileAccess.Read))
                {
                    doc = new XWPFDocument(fs);
                }

                if (doc.Tables.Count == 0)
                {
                    progressCallback?.Invoke(50, "[ERROR]模板文件中没有找到表格");
                    return false;
                }

                // 获取第一个表格作为模板
                originalTemplate = doc.Tables[0];

                // 创建模板表格的深层副本以备后用
                XWPFDocument tempDocForCopy = new XWPFDocument();
                XWPFTable deepCopiedTemplateTable = tempDocForCopy.CreateTable();
                CopyTableStructure(originalTemplate, deepCopiedTemplateTable);

                // 处理楼栋数据 - 使用智能排序
                var sortedBuildings = buildingData.Values
                    .OrderBy(b =>
                    {
                        // 纯数字栋号排在前面，非数字栋号排在后面
                        if (int.TryParse(b.BuildingNumber, out _))
                            return 0; // 数字栋号
                        else
                            return 1; // 非数字栋号
                    })
                    .ThenBy(b =>
                    {
                        // 对于数字栋号，按数值排序
                        if (int.TryParse(b.BuildingNumber, out int num))
                            return num;
                        else
                            return 0; // 非数字栋号在这里不影响排序
                    })
                    .ThenBy(b => b.BuildingNumber) // 对于非数字栋号，按字符串排序
                    .ToList();
                int totalBuildings = sortedBuildings.Count;

                progressCallback?.Invoke(52, $"[INFO]开始处理{totalBuildings}栋楼的数据");

                // 为所有楼栋设置用户参数
                foreach (var building in sortedBuildings)
                {
                    building.BuildingAge = userBuildingAge;
                    building.InspectionDate = userInspectionDate;
                }

                for (int i = 0; i < totalBuildings; i++)
                {
                    var building = sortedBuildings[i];
                    XWPFTable tableToUse;

                    progressCallback?.Invoke(53 + i, $"[DEBUG]处理楼栋: {building.BuildingNumber}, 单元数: {building.GetUnitCountText()}, 楼层: {building.GetFloorInfoText()}");

                    if (i == 0)
                    {
                        // 第一个楼栋直接使用原始模板表格
                        tableToUse = originalTemplate;
                        progressCallback?.Invoke(54 + i, $"[DEBUG]使用原始模板表格处理第一栋楼: {building.BuildingNumber}");
                    }
                    else
                    {
                        // 后续楼栋需要复制表格
                        // 使用深层副本创建新表格
                        tableToUse = doc.CreateTable();
                        CopyTableStructure(deepCopiedTemplateTable, tableToUse);

                        progressCallback?.Invoke(54 + i, $"[DEBUG]复制深层副本模板表格处理楼栋: {building.BuildingNumber}");
                    }

                    // 填充表格数据
                    FillTableWithBuildingData(tableToUse, building);

                    // 在表格后面添加空白段落作为间隔（从第二个表格开始，除了最后一个表格）
                    if (i > 0 && i < totalBuildings - 1)
                    {
                        XWPFParagraph spacingPara = doc.CreateParagraph();
                        // 可以设置段落间距来控制表格之间的距离
                    }

                    // 更新进度
                    int progress = 55 + (i + 1) * 40 / totalBuildings;
                    progressCallback?.Invoke(progress, $"[INFO]已完成楼栋 {i + 1}/{totalBuildings} - {building.BuildingNumber}");
                }

                // 保存文档
                using (FileStream outStream = new FileStream(outputPath, FileMode.Create))
                {
                    doc.Write(outStream);
                }

                return true;
            }
            catch (Exception ex)
            {
                progressCallback?.Invoke(50, $"[ERROR]生成Word文档时发生错误: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 复制表格结构，保持原始模板的占位符和格式
        /// </summary>
        /// <param name="source">源表格</param>
        /// <param name="target">目标表格</param>
        private void CopyTableStructure(XWPFTable source, XWPFTable target)
        {
            // 复制表格级属性
            if (source.GetCTTbl().tblPr != null)
            {
                if (target.GetCTTbl().tblPr == null) target.GetCTTbl().AddNewTblPr();
                target.GetCTTbl().tblPr = source.GetCTTbl().tblPr.Copy();
            }

            // 复制表格网格
            if (source.GetCTTbl().tblGrid != null)
            {
                if (target.GetCTTbl().tblGrid == null) target.GetCTTbl().AddNewTblGrid();
                target.GetCTTbl().tblGrid = source.GetCTTbl().tblGrid.Copy();
            }

            // 复制行
            for (int i = 0; i < source.Rows.Count; i++)
            {
                XWPFTableRow sourceRow = source.Rows[i];
                XWPFTableRow targetRow = target.CreateRow();

                // 复制行属性
                if (sourceRow.GetCTRow().trPr != null)
                {
                    targetRow.GetCTRow().AddNewTrPr();
                    targetRow.GetCTRow().trPr = sourceRow.GetCTRow().trPr.Copy();
                }

                // 复制单元格
                for (int j = 0; j < sourceRow.GetTableCells().Count; j++)
                {
                    XWPFTableCell sourceCell = sourceRow.GetTableCells()[j];
                    XWPFTableCell targetCell = (j == 0) ? targetRow.GetCell(0) : targetRow.AddNewTableCell();

                    // 复制单元格属性
                    if (sourceCell.GetCTTc().tcPr != null)
                    {
                        if (targetCell.GetCTTc().tcPr == null)
                        {
                             targetCell.GetCTTc().AddNewTcPr();
                        }
                        targetCell.GetCTTc().tcPr = sourceCell.GetCTTc().tcPr.Copy();
                    }

                    // 新单元格有一个默认的空段落，在复制前应清除它
                    for (int p = targetCell.Paragraphs.Count - 1; p >= 0; p--)
                    {
                        targetCell.RemoveParagraph(p);
                    }

                    // 从源单元格复制段落
                    foreach (var paragraph in sourceCell.Paragraphs)
                    {
                        XWPFParagraph newPara = targetCell.AddParagraph();
                        CopyParagraph(paragraph, newPara);
                    }
                }
            }

            // 目标表格创建时带有一个空的默认行，将其删除
            if (target.Rows.Count > source.Rows.Count)
            {
                target.RemoveRow(0);
            }
        }

        /// <summary>
        /// 复制段落
        /// </summary>
        /// <param name="source">源段落</param>
        /// <param name="target">目标段落</param>
        private void CopyParagraph(XWPFParagraph source, XWPFParagraph target)
        {
            // 复制段落属性
            if (source.GetCTP().pPr != null)
            {
                if (target.GetCTP().pPr == null) target.GetCTP().AddNewPPr();
                target.GetCTP().pPr = source.GetCTP().pPr.Copy();
            }

            // 复制运行块
            foreach (var sourceRun in source.Runs)
            {
                XWPFRun targetRun = target.CreateRun();
                CopyRun(sourceRun, targetRun);
            }
        }

        /// <summary>
        /// 复制运行
        /// </summary>
        /// <param name="source">源运行</param>
        /// <param name="target">目标运行</param>
        private void CopyRun(XWPFRun source, XWPFRun target)
        {
            // 复制运行块属性
            if (source.GetCTR().rPr != null)
            {
                if (target.GetCTR().rPr == null) target.GetCTR().AddNewRPr();
                target.GetCTR().rPr = source.GetCTR().rPr.Copy();
            }

            // 复制文本
            target.SetText(source.Text, 0);
        }



        /// <summary>
        /// 填充表格数据
        /// </summary>
        /// <param name="table">表格</param>
        /// <param name="building">楼栋信息</param>
        private void FillTableWithBuildingData(XWPFTable table, BuildingInfo building)
        {
            // 创建占位符映射
            var placeholders = new Dictionary<string, string>
            {
                { "${栋号}", building.BuildingNumber },
                { "${建成年代}", building.BuildingAge },
                { "${勘验日期}", building.InspectionDate },
                { "${单元数}", building.GetUnitCountText() },
                { "${楼层}", building.GetFloorInfoText() },
                { "${建筑结构}", building.GetStructureText() },
                { "${设计用途}", building.GetDesignPurposeText() }
            };

            // Debug: 输出占位符映射
            System.Diagnostics.Debug.WriteLine($"=== 填充楼栋 {building.BuildingNumber} 的数据 ===");
            foreach (var placeholder in placeholders)
            {
                System.Diagnostics.Debug.WriteLine($"{placeholder.Key} -> '{placeholder.Value}'");
            }

            int replacementCount = 0;
            var foundPlaceholders = new List<string>();

            // 遍历表格的所有单元格，替换占位符
            foreach (XWPFTableRow row in table.Rows)
            {
                foreach (XWPFTableCell cell in row.GetTableCells())
                {
                    foreach (XWPFParagraph paragraph in cell.Paragraphs)
                    {
                        string originalText = paragraph.Text;
                        if (originalText.Contains("${"))
                        {
                            System.Diagnostics.Debug.WriteLine($"找到包含占位符的段落: '{originalText}'");

                            // 记录找到的占位符
                            foreach (var placeholder in placeholders.Keys)
                            {
                                if (originalText.Contains(placeholder) && !foundPlaceholders.Contains(placeholder))
                                {
                                    foundPlaceholders.Add(placeholder);
                                    System.Diagnostics.Debug.WriteLine($"找到占位符: {placeholder}");
                                }
                            }

                            ReplaceVariablesInParagraph(paragraph, placeholders);
                            replacementCount++;

                            // 检查替换后的文本
                            System.Diagnostics.Debug.WriteLine($"替换后的段落: '{paragraph.Text}'");
                        }
                    }
                }
            }

            // Debug: 记录详细的替换信息
            System.Diagnostics.Debug.WriteLine($"楼栋 {building.BuildingNumber} - 替换了 {replacementCount} 个段落，找到占位符: {string.Join(", ", foundPlaceholders)}");
            System.Diagnostics.Debug.WriteLine($"楼栋 {building.BuildingNumber} - 数据: 栋号={building.BuildingNumber}, 单元数={building.GetUnitCountText()}, 楼层={building.GetFloorInfoText()}");
        }

        /// <summary>
        /// 在段落中替换变量，保持原有格式
        /// </summary>
        /// <param name="paragraph">段落</param>
        /// <param name="variables">变量映射</param>
        private void ReplaceVariablesInParagraph(XWPFParagraph paragraph, Dictionary<string, string> variables)
        {
            // 获取段落的完整文本
            string fullText = paragraph.Text;
            System.Diagnostics.Debug.WriteLine($"检查段落: '{fullText}', 运行数量: {paragraph.Runs.Count}");

            if (string.IsNullOrEmpty(fullText))
            {
                return;
            }

            // 检查是否包含任何占位符
            bool hasPlaceholder = false;
            foreach (var variable in variables.Keys)
            {
                if (fullText.Contains(variable))
                {
                    hasPlaceholder = true;
                    System.Diagnostics.Debug.WriteLine($"找到占位符: {variable}");
                    break;
                }
            }

            if (!hasPlaceholder)
            {
                return;
            }

            // 方法1：尝试简单的运行级替换
            bool simpleReplacement = TrySimpleRunReplacement(paragraph, variables);

            if (!simpleReplacement)
            {
                // 方法2：如果简单替换失败，使用完整重建方法
                System.Diagnostics.Debug.WriteLine("简单替换失败，使用完整重建方法");
                RebuildParagraphWithReplacements(paragraph, variables);
            }
        }

        private bool TrySimpleRunReplacement(XWPFParagraph paragraph, Dictionary<string, string> variables)
        {
            bool hasReplacement = false;

            foreach (XWPFRun run in paragraph.Runs)
            {
                string runText = run.Text;
                if (!string.IsNullOrEmpty(runText))
                {
                    string newRunText = runText;
                    foreach (var variable in variables)
                    {
                        if (runText.Contains(variable.Key))
                        {
                            newRunText = newRunText.Replace(variable.Key, variable.Value);
                            hasReplacement = true;
                            System.Diagnostics.Debug.WriteLine($"在运行中替换 {variable.Key} -> {variable.Value}");
                        }
                    }

                    if (newRunText != runText)
                    {
                        run.SetText(newRunText, 0);
                        System.Diagnostics.Debug.WriteLine($"运行文本从 '{runText}' 替换为 '{newRunText}'");
                    }
                }
            }

            // 验证替换是否成功
            string newFullText = paragraph.Text;
            foreach (var variable in variables.Keys)
            {
                if (newFullText.Contains(variable))
                {
                    System.Diagnostics.Debug.WriteLine($"简单替换后仍包含占位符: {variable}");
                    return false; // 还有占位符未替换
                }
            }

            if (hasReplacement)
            {
                System.Diagnostics.Debug.WriteLine($"简单替换成功，最终文本: '{newFullText}'");
            }

            return hasReplacement;
        }

        private void RebuildParagraphWithReplacements(XWPFParagraph paragraph, Dictionary<string, string> variables)
        {
            // 获取完整文本
            string fullText = paragraph.Text;

            // 替换所有占位符
            string newText = fullText;
            foreach (var variable in variables)
            {
                newText = newText.Replace(variable.Key, variable.Value);
                System.Diagnostics.Debug.WriteLine($"完整替换 {variable.Key} -> {variable.Value}");
            }

            if (newText == fullText)
            {
                return; // 没有变化
            }

            // 保存第一个运行的格式
            XWPFRun templateRun = paragraph.Runs.Count > 0 ? paragraph.Runs[0] : null;

            // 清除所有运行
            for (int i = paragraph.Runs.Count - 1; i >= 0; i--)
            {
                paragraph.RemoveRun(i);
            }

            // 创建新运行
            XWPFRun newRun = paragraph.CreateRun();
            newRun.SetText(newText);

            // 复制格式
            if (templateRun != null)
            {
                try
                {
                    newRun.IsBold = templateRun.IsBold;
                    newRun.IsItalic = templateRun.IsItalic;
                    if (templateRun.FontSize > 0)
                        newRun.FontSize = templateRun.FontSize;
                    if (!string.IsNullOrEmpty(templateRun.FontFamily))
                        newRun.FontFamily = templateRun.FontFamily;
                    newRun.Underline = templateRun.Underline;
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"复制格式时发生错误: {ex.Message}");
                }
            }

            System.Diagnostics.Debug.WriteLine($"完整重建完成，最终文本: '{paragraph.Text}'");
        }



        /// <summary>
        /// 是否可用
        /// </summary>
        /// <param name="parameters">检查参数</param>
        /// <returns>是否可用</returns>
        public bool IsAvailable(Dictionary<string, object> parameters)
        {
            try
            {
                if (parameters == null)
                    return false;

                // 检查必要参数
                if (!parameters.TryGetValue("DataFolder", out object dataFolderObj) ||
                    !(dataFolderObj is string dataDirectory))
                    return false;

                if (!parameters.TryGetValue("OutputDir", out object outputDirObj) ||
                    !(outputDirObj is string outputDirectory))
                    return false;

                // 检查数据目录
                if (string.IsNullOrEmpty(dataDirectory) || !Directory.Exists(dataDirectory))
                    return false;

                // 检查是否存在可用的Excel文件（标准Excel文件、CGB.xls文件或成果包.7z文件）
                // 首先检查标准Excel文件（包含4个指定工作表）
                var excelFiles = Directory.GetFiles(dataDirectory, "*.xls*", SearchOption.AllDirectories);
                foreach (string filePath in excelFiles)
                {
                    try
                    {
                        if (ValidateExcelFileStructure(filePath))
                        {
                            return true; // 找到标准Excel文件
                        }
                    }
                    catch
                    {
                        // 忽略验证失败的文件，继续检查其他文件
                        continue;
                    }
                }

                // 检查直接的CGB文件
                var cgbFiles = Directory.GetFiles(dataDirectory, "*.xls*", SearchOption.AllDirectories)
                    .Where(file => Path.GetFileName(file).ToLower().Contains("cgb"))
                    .ToArray();

                if (!cgbFiles.Any())
                {
                    // 检查是否存在成果包.7z文件
                    var archiveFiles = Directory.GetFiles(dataDirectory, "*.7z", SearchOption.AllDirectories)
                        .Where(file => Path.GetFileName(file).Contains("成果包"))
                        .ToArray();

                    if (!archiveFiles.Any())
                        return false;
                }

                // 检查模板文件
                string templatePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "报告模板", "06_楼栋基本信息", TEMPLATE_FILE_NAME);
                if (!File.Exists(templatePath))
                    return false;

                return true;
            }
            catch (Exception)
            {
                return false;
            }
        }
    }
}