using System;
using Autodesk.AutoCAD.ApplicationServices;
using Autodesk.AutoCAD.EditorInput;
using RESCADServerPlugin.Services;

namespace RESCADServerPlugin.Commands.Implementations
{
    /// <summary>
    /// 绘图命令实现类
    /// </summary>
    public class DrawingCommands : CommandBase
    {
        /// <summary>
        /// 执行绘图演示命令 - 创建一个简单房间布局示例
        /// </summary>
        public void ExecuteDrawDemo()
        {
            Document doc = GetActiveDocument();
            if (doc == null) return;

            Editor ed = GetEditor();
            WriteMessage("\n执行绘图演示命令...");
            
            // 获取绘图服务
            var drawingService = CADServiceManager.GetDrawingService();
            
            // 创建一个简单的房间布局（矩形+门+窗户+标签）
            try
            {
                // 绘制矩形房间轮廓（使用四条直线）
                double x1 = 0, y1 = 0;      // 左下
                double x2 = 100, y2 = 0;    // 右下
                double x3 = 100, y3 = 80;   // 右上
                double x4 = 0, y4 = 80;     // 左上
                
                if (!drawingService.DrawLine(x1, y1, x2, y2, out string msg1))
                {
                    WriteMessage($"\n绘制失败: {msg1}");
                    return;
                }
                
                if (!drawingService.DrawLine(x2, y2, x3, y3, out string msg2))
                {
                    WriteMessage($"\n绘制失败: {msg2}");
                    return;
                }
                
                if (!drawingService.DrawLine(x3, y3, x4, y4, out string msg3))
                {
                    WriteMessage($"\n绘制失败: {msg3}");
                    return;
                }
                
                if (!drawingService.DrawLine(x4, y4, x1, y1, out string msg4))
                {
                    WriteMessage($"\n绘制失败: {msg4}");
                    return;
                }
                
                // 绘制门 (在下方墙壁中间)
                double doorX1 = 40, doorY1 = 0;
                double doorX2 = 60, doorY2 = 0;
                double doorArcX = 50, doorArcY = 10;
                
                if (!drawingService.DrawLine(doorX1, doorY1, doorX2, doorY2, out string msg5))
                {
                    WriteMessage($"\n绘制失败: {msg5}");
                    return;
                }
                
                // 绘制门弧线
                if (!drawingService.DrawCircle(doorArcX, doorArcY, 10, out string msg6))
                {
                    WriteMessage($"\n绘制失败: {msg6}");
                    return;
                }
                
                // 绘制窗户 (在右侧墙壁中间)
                double windowX = 100, windowY = 40;
                
                if (!drawingService.AddText(windowX - 10, windowY, "窗", 5, out string msg7))
                {
                    WriteMessage($"\n绘制失败: {msg7}");
                    return;
                }
                
                // 添加房间标签
                if (!drawingService.AddText(50, 40, "客厅", 8, out string msg8))
                {
                    WriteMessage($"\n绘制失败: {msg8}");
                    return;
                }
                
                // 添加面积标签
                if (!drawingService.AddText(40, 30, "面积: 8000", 5, out string msg9))
                {
                    WriteMessage($"\n绘制失败: {msg9}");
                    return;
                }
                
                WriteMessage("\n绘图演示完成: 成功创建一个简单的房间布局");
            }
            catch (System.Exception ex)
            {
                WriteMessage($"\n绘图演示出错: {ex.Message}");
            }
        }
    }
} 