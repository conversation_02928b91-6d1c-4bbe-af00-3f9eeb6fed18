<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RESCADServerPlugin - 服务</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background-color: #fff;
            padding: 20px;
            border-radius: 5px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        h1 {
            color: #333;
            margin-top: 0;
            border-bottom: 1px solid #eee;
            padding-bottom: 10px;
        }
        .menu {
            list-style: none;
            padding: 0;
        }
        .menu li {
            margin-bottom: 10px;
        }
        .menu a {
            display: block;
            padding: 12px 15px;
            background-color: #f9f9f9;
            border-radius: 4px;
            color: #333;
            text-decoration: none;
            font-weight: bold;
            transition: background-color 0.2s;
        }
        .menu a:hover {
            background-color: #e9e9e9;
        }
        .status {
            padding: 10px;
            background-color: #dff0d8;
            border-radius: 4px;
            color: #3c763d;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>RESCADServerPlugin 服务</h1>
        <p>欢迎使用 RESCADServerPlugin 服务。请选择以下功能：</p>
        
        <ul class="menu">
            <li><a href="print-drawings.html">CAD 打印图处理（后台队列）</a></li>
            <li><a href="direct-print-drawings.html">CAD 打印图直接处理（无队列）</a></li>
            <li><a href="single-print-drawing.html">CAD 单个打印图导出工具</a></li>
            <li><a href="frame-isolation.html">CAD 图框隔离工具</a></li>
        </ul>
        
        <div class="status">
            <strong>服务状态：</strong> 运行中
        </div>
    </div>
</body>
</html> 