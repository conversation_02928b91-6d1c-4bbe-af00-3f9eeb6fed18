using Autodesk.AutoCAD.ApplicationServices;
using Autodesk.AutoCAD.DatabaseServices;
using Autodesk.AutoCAD.EditorInput;
using Autodesk.AutoCAD.Geometry;
using RESCADServerPlugin.Configuration;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Text.RegularExpressions;

namespace RESCADServerPlugin.Commands.Implementations
{
    public class PrintDrawingCommands
    {
        public void ExecuteSavePrintDrawings()
        {
            Document doc = Application.DocumentManager.MdiActiveDocument;
            if (doc == null)
            {
                //Application.ShowAlertDialog("No active document.");
                return;
            }

            Editor ed = doc.Editor;
            Database db = doc.Database;
            PluginSettings settings = ConfigurationService.GetSettings();
            string outputBaseDir;
            string docName = Path.GetFileNameWithoutExtension(doc.Name);

            if (settings != null && !string.IsNullOrWhiteSpace(settings.PrintDrawingOutputPath))
            {
                if (Directory.Exists(settings.PrintDrawingOutputPath))
                {
                    outputBaseDir = settings.PrintDrawingOutputPath;
                    // ed.WriteMessage($"\nUsing configured output directory: {outputBaseDir}");
                }
                else
                {
                    // ed.WriteMessage($"\nConfigured output directory does not exist: {settings.PrintDrawingOutputPath}. Attempting to create it.");
                    try
                    {
                        Directory.CreateDirectory(settings.PrintDrawingOutputPath);
                        outputBaseDir = settings.PrintDrawingOutputPath;
                        // ed.WriteMessage($"\nSuccessfully created output directory: {outputBaseDir}");
                    }
                    catch (System.Exception ex)
                    {
                        ed.WriteMessage($"\nError creating configured output directory: {ex.Message}. Using default output directory instead.");
                        // 使用插件DLL所在目录而不是当前CAD文档所在目录
                        string dllPath = System.Reflection.Assembly.GetExecutingAssembly().Location;
                        string dllDir = Path.GetDirectoryName(dllPath);
                        outputBaseDir = Path.Combine(dllDir, "PrintDrawingsOutput");
                        Directory.CreateDirectory(outputBaseDir); // Ensure default fallback exists
                    }
                }
            }
            else
            {
                // ed.WriteMessage("\nPrintDrawingOutputPath not configured or empty. Using default output directory.");
                // 使用插件DLL所在目录而不是当前CAD文档所在目录
                string dllPath = System.Reflection.Assembly.GetExecutingAssembly().Location;
                string dllDir = Path.GetDirectoryName(dllPath);
                outputBaseDir = Path.Combine(dllDir, "PrintDrawingsOutput");
                Directory.CreateDirectory(outputBaseDir); // Ensure default exists
            }

            // 在基本输出目录下根据当前文件名创建子目录
            outputBaseDir = Path.Combine(outputBaseDir, docName);
            Directory.CreateDirectory(outputBaseDir); // 确保子目录存在

            // 清空 PrintDrawingsOutput 文件夹下的所有数据
            try
            {
                // ed.WriteMessage("\nClearing PrintDrawingsOutput directory...");
                foreach (string file in Directory.GetFiles(outputBaseDir))
                {
                    try
                    {
                        File.Delete(file);
                    }
                    catch (Exception ex)
                    {
                        ed.WriteMessage($"\n清除文件失败 {file}: {ex.Message}");
                    }
                }
                // ed.WriteMessage("\nPrintDrawingsOutput directory cleared successfully.");
            }
            catch (Exception ex)
            {
                ed.WriteMessage($"\n清除输出目录出错: {ex.Message}");
            }

            ed.WriteMessage("\n开始查找和保存打印图...");

            List<ObjectId> allFramesProcessed = new List<ObjectId>(); 
            // 保存已生成的DWG文件路径，用于后续转换为WMF
            List<string> dwgFilesToConvert = new List<string>();

            using (Transaction tr = db.TransactionManager.StartTransaction())
            {
                BlockTable bt = (BlockTable)tr.GetObject(db.BlockTableId, OpenMode.ForRead);
                BlockTableRecord btr = (BlockTableRecord)tr.GetObject(bt[BlockTableRecord.ModelSpace], OpenMode.ForRead);

                // Store Tuples of <OriginalObjectId, ClonedPolyline>
                List<Tuple<ObjectId, Polyline>> potentialFrames = new List<Tuple<ObjectId, Polyline>>();
                List<DBText> allDbTexts = new List<DBText>(); // Clones
                List<MText> allMTexts = new List<MText>();   // Clones

                foreach (ObjectId objId in btr)
                {
                    Entity ent = tr.GetObject(objId, OpenMode.ForRead) as Entity;
                    if (ent == null) continue;

                    if (ent is Polyline pl)
                    {
                        if (pl.Closed)
                        {
                            potentialFrames.Add(new Tuple<ObjectId, Polyline>(pl.ObjectId, pl.Clone() as Polyline));
                        }
                    }
                    else if (ent is DBText dbText)
                    {
                        allDbTexts.Add(dbText.Clone() as DBText);
                    }
                    else if (ent is MText mText)
                    {
                        allMTexts.Add(mText.Clone() as MText);
                    }
                }
                
                tr.Commit(); 

                if (!potentialFrames.Any())
                {
                    ed.WriteMessage("\nNo closed polyline frames found.");
                    return;
                }

                ed.WriteMessage($"\nFound {potentialFrames.Count} potential closed polyline frames.");
                int savedCount = 0;

                foreach (var frameTuple in potentialFrames.OrderByDescending(t => t.Item2.Area)) 
                {
                    ObjectId originalFrameId = frameTuple.Item1;
                    Polyline framePolyline = frameTuple.Item2; // This is the clone

                    if (allFramesProcessed.Contains(originalFrameId)) continue;

                    // Extents3d frameExtents = framePolyline.GeometricExtents; // Clone may not have valid extents until added to a DB. Not strictly needed here.

                    List<DBText> dbTextsInFrame = allDbTexts.Where(t => IsPointInsidePolyline(t.Position, framePolyline)).ToList();
                    List<MText> mTextsInFrame = allMTexts.Where(mt => IsPointInsidePolyline(mt.Location, framePolyline)).ToList();
                    
                    // 1. Check for "比例" text (must be exactly one)
                    int countScaleText = dbTextsInFrame.Count(t => t.TextString.Trim().Replace(" ", "").Contains("比例")) +
                                         mTextsInFrame.Count(mt => mt.Text.Trim().Replace(" ", "").Contains("比例"));

                    // 2. Check for "图号" label text (must be exactly one)
                    int countDrawingNumLabel = dbTextsInFrame.Count(t => t.TextString.Trim().Replace(" ", "").Contains("图号")) +
                                               mTextsInFrame.Count(mt => mt.Text.Trim().Replace(" ", "").Contains("图号"));

                    // 3. Find drawing number value text (e.g., "15/83" or "201")
                    List<string> drawingNumberValuesFound = new List<string>();
                    drawingNumberValuesFound.AddRange(dbTextsInFrame
                        .Select(t => t.TextString.Trim().Replace(" ", ""))
                        .Where(text => Regex.IsMatch(text, @"^\d+([/\\]\d+)?$|^[\d]+$")));
                    drawingNumberValuesFound.AddRange(mTextsInFrame
                        .Select(mt => mt.Text.Trim().Replace(" ", ""))
                        .Where(text => Regex.IsMatch(text, @"^\d+([/\\]\d+)?$|^[\d]+$")));
                    
                    List<string> distinctDrawingNumberValues = drawingNumberValuesFound.Distinct().ToList();

                    // Add detailed debug output
                    ed.WriteMessage($"\nFrame ID: {originalFrameId}, Texts in frame: {dbTextsInFrame.Count + mTextsInFrame.Count}");
                    ed.WriteMessage($"\n  Scale texts found: {countScaleText} ('比例')");
                    ed.WriteMessage($"\n  Drawing number labels found: {countDrawingNumLabel} ('图号')");
                    ed.WriteMessage($"\n  Drawing number values found: {distinctDrawingNumberValues.Count} ({string.Join(", ", distinctDrawingNumberValues)})");
                    
                    // List all text content for debugging
                    ed.WriteMessage("\n  All DBText content in frame:");
                    foreach (var txt in dbTextsInFrame)
                        ed.WriteMessage($"\n    '{txt.TextString.Trim()}'");
                    ed.WriteMessage("\n  All MText content in frame:");
                    foreach (var txt in mTextsInFrame)
                        ed.WriteMessage($"\n    '{txt.Text.Trim()}'");

                    // Modified criteria - EXACTLY one "比例" AND EXACTLY one "图号" AND at least one numeric value
                    // 确保每个有效的打印图框内只包含一个比例和一个图号文本
                    if (countScaleText == 1 && countDrawingNumLabel == 1 && distinctDrawingNumberValues.Count > 0)
                    {
                        // If multiple drawing numbers found, use the first one
                        string rawDrawingNumber = distinctDrawingNumberValues.First();
                        
                        ed.WriteMessage($"\nFound print drawing. Frame ID: {originalFrameId}, Scale: 1, Label: 1, Number: '{rawDrawingNumber}'");

                        string fileName = rawDrawingNumber.Replace('/', '-').Replace('\\', '-');
                        fileName = string.Join("_", fileName.Split(Path.GetInvalidFileNameChars()));
                        fileName = Regex.Replace(fileName, @"[^\w\.-]", "_"); // Further sanitize
                        string outputFilePath = Path.Combine(outputBaseDir, $"{fileName}.dwg");

                        
                        using (Transaction selectionTr = db.TransactionManager.StartTransaction())
                        {
                            // 使用 SelectCrossingPolygon 方法更精确地选择图框内的实体
                            Point3dCollection polygonVertices = new Point3dCollection();
                            for (int i = 0; i < framePolyline.NumberOfVertices; i++)
                            {
                                polygonVertices.Add(framePolyline.GetPoint3dAt(i));
                            }
                            PromptSelectionResult selRes = doc.Editor.SelectCrossingPolygon(polygonVertices);
                            
                            if (selRes.Status == PromptStatus.OK)
                            {
                                // 收集所选实体IDs
                                ObjectIdCollection idsToWblock = new ObjectIdCollection();
                                SelectionSet ss = selRes.Value;
                                
                                foreach (ObjectId id in ss.GetObjectIds())
                                {
                                    idsToWblock.Add(id);
                                }
                                
                                if (idsToWblock.Count > 0)
                                {
                                    Database newDb = new Database(true, false);
                                    try
                                    {
                                        // 检查是否为横向图框并需要旋转
                                        bool needRotation = IsHorizontalFrame(db, framePolyline, dbTextsInFrame, mTextsInFrame, tr);
                                        
                                        // 如果需要旋转，创建临时数据库并旋转实体
                                        if (needRotation)
                                        {
                                            ed.WriteMessage("\n检测到横向图框，正在旋转实体...");
                                            
                                            // 创建临时数据库复制选中的实体
                                            Database tempDb = new Database(true, false);
                                            try
                                            {
                                                // 复制实体到临时数据库
                                                db.Wblock(tempDb, idsToWblock, Point3d.Origin, DuplicateRecordCloning.Replace);
                                                
                                                // 计算旋转中心点
                                                Point3d rotMinPoint = new Point3d(double.MaxValue, double.MaxValue, 0);
                                                Point3d rotMaxPoint = new Point3d(double.MinValue, double.MinValue, 0);
                                                
                                                for (int i = 0; i < framePolyline.NumberOfVertices; i++)
                                                {
                                                    Point3d pt = framePolyline.GetPoint3dAt(i);
                                                    rotMinPoint = new Point3d(
                                                        Math.Min(rotMinPoint.X, pt.X),
                                                        Math.Min(rotMinPoint.Y, pt.Y),
                                                        0
                                                    );
                                                    rotMaxPoint = new Point3d(
                                                        Math.Max(rotMaxPoint.X, pt.X),
                                                        Math.Max(rotMaxPoint.Y, pt.Y),
                                                        0
                                                    );
                                                }
                                                
                                                // 计算中心点
                                                Point3d centerPoint = new Point3d(
                                                    (rotMinPoint.X + rotMaxPoint.X) / 2,
                                                    (rotMinPoint.Y + rotMaxPoint.Y) / 2,
                                                    0
                                                );
                                                
                                                // 在临时数据库中旋转所有实体
                                                using (Transaction tempTr = tempDb.TransactionManager.StartTransaction())
                                                {
                                                    BlockTable tempBt = tempTr.GetObject(tempDb.BlockTableId, OpenMode.ForRead) as BlockTable;
                                                    BlockTableRecord tempBtr = tempTr.GetObject(tempBt[BlockTableRecord.ModelSpace], OpenMode.ForWrite) as BlockTableRecord;
                                                    
                                                    // 创建旋转矩阵 - 逆时针旋转90度（将横向转为纵向）
                                                    Matrix3d rotMatrix = Matrix3d.Rotation(-Math.PI / 2, Vector3d.ZAxis, centerPoint);
                                                    
                                                    // 旋转所有实体
                                                    foreach (ObjectId id in tempBtr)
                                                    {
                                                        Entity ent = tempTr.GetObject(id, OpenMode.ForWrite) as Entity;
                                                        if (ent != null)
                                                        {
                                                            ent.TransformBy(rotMatrix);
                                                        }
                                                    }
                                                    
                                                    tempTr.Commit();
                                                }
                                                
                                                // 在导出前设置文字样式为宋体
                                                using (Transaction tempTr2 = tempDb.TransactionManager.StartTransaction())
                                                {
                                                    // 从临时数据库获取要导出的实体ID
                                                    BlockTable tempBt2 = tempTr2.GetObject(tempDb.BlockTableId, OpenMode.ForRead) as BlockTable;
                                                    BlockTableRecord tempBtr2 = tempTr2.GetObject(tempBt2[BlockTableRecord.ModelSpace], OpenMode.ForRead) as BlockTableRecord;
                                                    
                                                    ObjectIdCollection rotatedIds = new ObjectIdCollection();
                                                    foreach (ObjectId id in tempBtr2)
                                                    {
                                                        rotatedIds.Add(id);
                                                    }
                                                    
                                                    // 设置文字样式
                                                    SetAllTextStyleToSimSun(tempDb, rotatedIds, tempTr2);
                                                    
                                                    // 导出旋转和设置字体后的结果
                                                    tempDb.Wblock(newDb, rotatedIds, Point3d.Origin, DuplicateRecordCloning.Replace);
                                                    tempTr2.Commit();
                                                }
                                            }
                                            finally
                                            {
                                                tempDb.Dispose();
                                            }
                                        }
                                        else
                                        {
                                            // 不需要旋转的正常流程
                                            // 在导出前设置文字样式为宋体
                                            SetAllTextStyleToSimSun(db, idsToWblock, selectionTr);
                                            
                                            // 使用Replace模式确保所有数据被复制
                                            db.Wblock(newDb, idsToWblock, Point3d.Origin, DuplicateRecordCloning.Replace);
                                            
                                            // 确保字体和文本样式正确复制
                                            CopyDrawingSettings(db, newDb);
                                        }
                                        
                                        // 保存文件
                                        newDb.SaveAs(outputFilePath, DwgVersion.Current);
                                        ed.WriteMessage($"\nSaved: {outputFilePath}");
                                        ed.WriteMessage($"\nTotal entities saved: {idsToWblock.Count}");
                                        savedCount++;
                                        allFramesProcessed.Add(originalFrameId); 
                                        
                                        // 将生成的DWG文件路径添加到列表中，用于后续转换为WMF
                                        dwgFilesToConvert.Add(outputFilePath);

                                        foreach (var otherFrameTuple in potentialFrames)
                                        {
                                            if (otherFrameTuple.Item1 != originalFrameId && !allFramesProcessed.Contains(otherFrameTuple.Item1))
                                            {
                                                if (AreAllVerticesInsidePolyline(otherFrameTuple.Item2, framePolyline))
                                                {
                                                    allFramesProcessed.Add(otherFrameTuple.Item1);
                                                }
                                            }
                                        }
                                    }
                                    catch (System.Exception ex)
                                    {
                                        ed.WriteMessage($"\nError saving {outputFilePath}: {ex.Message}");
                                    }
                                    finally
                                    {
                                        newDb.Dispose();
                                    }
                                }
                                else
                                {
                                    ed.WriteMessage($"\nNo entities selected for drawing {rawDrawingNumber} (Frame ID: {originalFrameId}).");
                                }
                            }
                            else
                            {
                                ed.WriteMessage($"\nSelection failed for drawing {rawDrawingNumber} (Frame ID: {originalFrameId}): {selRes.Status}");
                            }
                            
                            selectionTr.Commit();
                        }
                    }
                    else
                    {
                        // Optional: Add verbose logging for skipped frames if criteria not met
                        // ed.WriteMessage($"\nSkipping frame {originalFrameId}. ScaleTxt: {countScaleText}, DwgNumLabel: {countDrawingNumLabel}, DwgNumValues: {distinctDrawingNumberValues.Count} (Values: {string.Join(",", distinctDrawingNumberValues)})");
                    }
                }

                if (savedCount > 0)
                {
                    //Application.ShowAlertDialog($"成功保存 {savedCount} 个打印图到 {outputBaseDir}");
                    ed.WriteMessage($"\n成功保存 {savedCount} 个打印图到 {outputBaseDir}");
                }
                else
                {
                    ed.WriteMessage("\n未找到符合条件的打印图。");
                }
            }
            
            // 转换DWG文件为WMF格式
            if (dwgFilesToConvert.Count > 0)
            {
                ConvertDwgToWmf(dwgFilesToConvert, outputBaseDir, ed);
            }
            
            ed.WriteMessage("\nPrint drawing processing complete.");
        }

        // 将DWG文件转换为WMF格式的方法
        private void ConvertDwgToWmf(List<string> dwgFiles, string outputDir, Editor ed)
        {
            ed.WriteMessage($"\n开始转换 {dwgFiles.Count} 个DWG文件到WMF格式...");
            
            try
            {
                string toolPath = Path.Combine(Path.GetDirectoryName(System.Reflection.Assembly.GetExecutingAssembly().Location), "tool", "Acme CAD Converter", "AcmeCADConverter.exe");
                
                if (!File.Exists(toolPath))
                {
                    ed.WriteMessage($"\n错误: 未找到AcmeCADConverter.exe: {toolPath}");
                    return;
                }
                
                if (dwgFiles.Count == 0)
                {
                    // ed.WriteMessage("\nNo DWG files to convert.");
                    return;
                }
                
                // 创建命令行参数，使用固定尺寸
                int dpi = 300;
                string baseArguments = $"/r /e /p 1 /l convert.log /f 3 /d \"{outputDir}\" /w {210}mm /h {297}mm /res {dpi} /key 15KJNKUPIY5D7WL9NSLSYZHCG3SGWMBOX2JYDWBEFPX8A9ZC7GJXMW09MOMCS";
                
                // 添加所有DWG文件到一个命令中
                string arguments = baseArguments;
                foreach (string dwgFile in dwgFiles)
                {
                    arguments += $" \"{dwgFile}\"";
                }
                
                // 显示命令行
                // ed.WriteMessage($"\nConverting {dwgFiles.Count} DWG files to WMF...");
                // ed.WriteMessage($"\nCommand: {toolPath} {arguments}");
                
                // 启动转换进程
                ProcessStartInfo psi = new ProcessStartInfo(toolPath, arguments);
                psi.CreateNoWindow = true;
                psi.UseShellExecute = false;
                psi.RedirectStandardOutput = true;
                psi.RedirectStandardError = true;
                
                using (Process process = Process.Start(psi))
                {
                    string output = process.StandardOutput.ReadToEnd();
                    string error = process.StandardError.ReadToEnd();
                    process.WaitForExit();
                    
                    if (process.ExitCode == 0)
                    {
                        ed.WriteMessage($"\n成功转换 {dwgFiles.Count} 个DWG文件到WMF");
                    }
                    else
                    {
                        ed.WriteMessage($"\n转换DWG文件到WMF失败。错误代码: {process.ExitCode}");
                        if (!string.IsNullOrEmpty(error))
                            ed.WriteMessage($"\n错误: {error}");
                    }
                }
            }
            catch (Exception ex)
            {
                ed.WriteMessage($"\nWMF转换过程错误: {ex.Message}");
            }
            
            // ed.WriteMessage("\nDWG to WMF conversion complete.");
        }

        // Helper to check if a point is horizontal/vertical relative to another (with tolerance)
        private bool IsHorizontal(Point3d p1, Point3d p2, double tolerance = 1e-6)
        {
            return Math.Abs(p1.Y - p2.Y) < tolerance;
        }

        private bool IsVertical(Point3d p1, Point3d p2, double tolerance = 1e-6)
        {
            return Math.Abs(p1.X - p2.X) < tolerance;
        }
        
        // Robust Point in Polyline Check (Crossing Number Algorithm)
        private bool IsPointInsidePolyline(Point3d testPoint, Polyline poly)
        {
            if (!poly.Closed) return false; // Or handle open polylines if necessary
            if (poly.NumberOfVertices < 3) return false;

            int crossings = 0;
            for (int i = 0; i < poly.NumberOfVertices; i++)
            {
                Point2d p1 = poly.GetPoint2dAt(i);
                Point2d p2 = poly.GetPoint2dAt((i + 1) % poly.NumberOfVertices);

                if (((p1.Y <= testPoint.Y && testPoint.Y < p2.Y) ||
                     (p2.Y <= testPoint.Y && testPoint.Y < p1.Y)) &&
                    (testPoint.X < (p2.X - p1.X) * (testPoint.Y - p1.Y) / (p2.Y - p1.Y) + p1.X))
                {
                    crossings++;
                }
            }
            return (crossings % 2) == 1;
        }

        private bool IsEntityInsidePolyline(Entity ent, Polyline framePolyline, Transaction tr)
        {
            if (ent == null || framePolyline == null) return false;

            // Handle specific entity types for more accurate containment check
            if (ent is DBPoint dbPoint)
            {
                return IsPointInsidePolyline(dbPoint.Position, framePolyline);
            }
            if (ent is DBText dbText) // Also MText uses Location
            {
                return IsPointInsidePolyline(dbText.Position, framePolyline);
            }
            if (ent is MText mText)
            {
                return IsPointInsidePolyline(mText.Location, framePolyline);
            }
            if (ent is Line line)
            {
                return IsPointInsidePolyline(line.StartPoint, framePolyline) && 
                       IsPointInsidePolyline(line.EndPoint, framePolyline);
            }
            if (ent is Polyline pl) // For polylines, check if all vertices are inside
            {
                if (pl.ObjectId == framePolyline.ObjectId) return true; // The frame itself
                for (int i = 0; i < pl.NumberOfVertices; i++)
                {
                    if (!IsPointInsidePolyline(pl.GetPoint3dAt(i), framePolyline))
                    {
                        return false;
                    }
                }
                return true;
            }
             if (ent is Circle circle)
            {
                // Check if center is inside and circle is fully contained (approximate)
                if (!IsPointInsidePolyline(circle.Center, framePolyline)) return false;
                // A more robust check would ensure the circle's radius doesn't extend beyond the polyline.
                // For now, center point check might suffice for typical drawing elements.
                // Or check bounding box of circle against polyline extents.
                Extents3d circleExtents = circle.GeometricExtents;
                return AreExtentsInsidePolyline(circleExtents, framePolyline);
            }
            if (ent is Arc arc)
            {
                 return IsPointInsidePolyline(arc.StartPoint, framePolyline) &&
                        IsPointInsidePolyline(arc.EndPoint, framePolyline) &&
                        IsPointInsidePolyline(arc.Center, framePolyline); // Center might be outside for small arcs
            }


            // Default: check if the entity's bounding box is completely within the frame's bounding box
            // This is a less precise but general check.
            try
            {
                Extents3d entExtents = ent.GeometricExtents; // This can throw for some entities if not properly handled
                 return AreExtentsInsidePolyline(entExtents, framePolyline);
            }
            catch (Autodesk.AutoCAD.Runtime.Exception ace) 
            {
                // Some entities like Hatches might not have simple geometric extents or can cause issues.
                // Or if GeometricExtents is called outside a transaction for a non-database-resident entity.
                Application.DocumentManager.MdiActiveDocument.Editor.WriteMessage($"\nWarning: Could not get GeometricExtents for entity {ent.GetType().Name} ({ent.ObjectId}): {ace.Message}");
                return false; 
            }
             catch (System.Exception ex)
            {
                Application.DocumentManager.MdiActiveDocument.Editor.WriteMessage($"\nError getting extents for {ent.GetType().Name} ({ent.ObjectId}): {ex.Message}");
                return false;
            }
        }
        
        private bool AreExtentsInsidePolyline(Extents3d innerExtents, Polyline outerPolyline)
        {
            // Check if all 4 corners of the innerExtents are inside the outerPolyline
            Point3d min = innerExtents.MinPoint;
            Point3d max = innerExtents.MaxPoint;

            return IsPointInsidePolyline(min, outerPolyline) &&
                   IsPointInsidePolyline(max, outerPolyline) &&
                   IsPointInsidePolyline(new Point3d(min.X, max.Y, (min.Z + max.Z) / 2.0), outerPolyline) &&
                   IsPointInsidePolyline(new Point3d(max.X, min.Y, (min.Z + max.Z) / 2.0), outerPolyline);
        }
        
        private bool AreAllVerticesInsidePolyline(Polyline innerPoly, Polyline outerPoly)
        {
            for (int i = 0; i < innerPoly.NumberOfVertices; i++)
            {
                if (!IsPointInsidePolyline(innerPoly.GetPoint3dAt(i), outerPoly))
                {
                    return false;
                }
            }
            return true;
        }

        // 添加新方法处理字体和文本样式复制
        private void CopyDrawingSettings(Database sourceDb, Database targetDb)
        {
            using (Transaction sourceTr = sourceDb.TransactionManager.StartTransaction())
            using (Transaction targetTr = targetDb.TransactionManager.StartTransaction())
            {
                try
                {
                    // 处理文本样式表以确保中文字体正确复制
                    TextStyleTable sourceTextStyleTable = sourceTr.GetObject(sourceDb.TextStyleTableId, OpenMode.ForRead) as TextStyleTable;
                    TextStyleTable targetTextStyleTable = targetTr.GetObject(targetDb.TextStyleTableId, OpenMode.ForWrite) as TextStyleTable;

                    foreach (ObjectId styleId in sourceTextStyleTable)
                    {
                        TextStyleTableRecord sourceStyle = sourceTr.GetObject(styleId, OpenMode.ForRead) as TextStyleTableRecord;
                        if (!targetTextStyleTable.Has(sourceStyle.Name))
                        {
                            continue; // 如果目标没有这个样式，跳过(Wblock应该已经复制)
                        }

                        // 获取目标中同名样式进行更新
                        if (targetTextStyleTable.Has(sourceStyle.Name))
                        {
                            ObjectId targetStyleId = targetTextStyleTable[sourceStyle.Name];
                            TextStyleTableRecord targetStyle = targetTr.GetObject(targetStyleId, OpenMode.ForWrite) as TextStyleTableRecord;
                            
                            // 确保字体名称正确复制
                            if (string.IsNullOrEmpty(targetStyle.FileName) && !string.IsNullOrEmpty(sourceStyle.FileName))
                            {
                                targetStyle.FileName = sourceStyle.FileName;
                            }

                            // 复制字体相关设置
                            targetStyle.Font = sourceStyle.Font;
                            
                            // 特别处理中文大字体文件 (SHX字体常用于中文)
                            if (string.IsNullOrEmpty(targetStyle.BigFontFileName) && !string.IsNullOrEmpty(sourceStyle.BigFontFileName))
                            {
                                targetStyle.BigFontFileName = sourceStyle.BigFontFileName;
                            }
                            
                            // 复制其他重要的文字样式属性
                            targetStyle.XScale = sourceStyle.XScale;
                            targetStyle.TextSize = sourceStyle.TextSize;
                            targetStyle.ObliquingAngle = sourceStyle.ObliquingAngle;
                            targetStyle.IsVertical = sourceStyle.IsVertical;
                        }
                    }
                    
                    // 复制常用对象的可视性设置和图层信息
                    CopySymbolTables(sourceDb, targetDb, sourceTr, targetTr);
                    
                    // 提交修改
                    targetTr.Commit();
                }
                catch (System.Exception ex)
                {
                    Application.DocumentManager.MdiActiveDocument.Editor.WriteMessage($"\nError copying drawing settings: {ex.Message}");
                    targetTr.Abort();
                }
            }
        }
        
        // 辅助方法：复制图层和其他符号表对象
        private void CopySymbolTables(Database sourceDb, Database targetDb, Transaction sourceTr, Transaction targetTr)
        {
            try
            {
                // 复制图层表
                LayerTable sourceLayerTable = sourceTr.GetObject(sourceDb.LayerTableId, OpenMode.ForRead) as LayerTable;
                LayerTable targetLayerTable = targetTr.GetObject(targetDb.LayerTableId, OpenMode.ForWrite) as LayerTable;
                
                foreach (ObjectId layerId in sourceLayerTable)
                {
                    LayerTableRecord sourceLayer = sourceTr.GetObject(layerId, OpenMode.ForRead) as LayerTableRecord;
                    if (targetLayerTable.Has(sourceLayer.Name))
                    {
                        ObjectId targetLayerId = targetLayerTable[sourceLayer.Name];
                        LayerTableRecord targetLayer = targetTr.GetObject(targetLayerId, OpenMode.ForWrite) as LayerTableRecord;
                        
                        // 确保图层颜色和可见性等属性正确复制
                        targetLayer.Color = sourceLayer.Color;
                        targetLayer.IsOff = sourceLayer.IsOff;
                        targetLayer.IsFrozen = sourceLayer.IsFrozen;
                        targetLayer.IsLocked = sourceLayer.IsLocked;
                        targetLayer.LineWeight = sourceLayer.LineWeight;
                        // Linetype不是直接属性，需要设置ObjectId
                        if (!sourceLayer.LinetypeObjectId.IsNull)
                        {
                            targetLayer.LinetypeObjectId = sourceLayer.LinetypeObjectId;
                        }
                    }
                }
            }
            catch (System.Exception ex)
            {
                Application.DocumentManager.MdiActiveDocument.Editor.WriteMessage($"\nError copying symbol tables: {ex.Message}");
            }
        }

        // 将所有文本和块参照属性的文字样式设置为宋体
        private void SetAllTextStyleToSimSun(Database db, ObjectIdCollection entityIds, Transaction tr)
        {
            try
            {
                // 首先确保宋体文字样式存在
                string simSunStyleName = "宋体";
                TextStyleTable textStyleTable = tr.GetObject(db.TextStyleTableId, OpenMode.ForRead) as TextStyleTable;
                
                ObjectId simSunStyleId;
                if (!textStyleTable.Has(simSunStyleName))
                {
                    // 如果宋体样式不存在，尝试创建它
                    textStyleTable.UpgradeOpen();
                    TextStyleTableRecord simSunStyle = new TextStyleTableRecord();
                    simSunStyle.Name = simSunStyleName;
                    simSunStyle.FileName = "simsun.ttf"; // 宋体字体文件
                    textStyleTable.Add(simSunStyle);
                    tr.AddNewlyCreatedDBObject(simSunStyle, true);
                    simSunStyleId = simSunStyle.ObjectId;
                }
                else
                {
                    simSunStyleId = textStyleTable[simSunStyleName];
                }
                
                Application.DocumentManager.MdiActiveDocument.Editor.WriteMessage("\n设置文字样式为宋体...");
                
                // 遍历所有选中的实体
                foreach (ObjectId id in entityIds)
                {
                    Entity ent = tr.GetObject(id, OpenMode.ForRead) as Entity;
                    if (ent == null) continue;
                    
                    // 处理文本对象
                    if (ent is DBText)
                    {
                        DBText text = ent as DBText;
                        if (text.TextStyleId != simSunStyleId)
                        {
                            text.UpgradeOpen();
                            text.TextStyleId = simSunStyleId;
                        }
                    }
                    else if (ent is MText)
                    {
                        MText mtext = ent as MText;
                        if (mtext.TextStyleId != simSunStyleId)
                        {
                            mtext.UpgradeOpen();
                            mtext.TextStyleId = simSunStyleId;
                        }
                    }
                    // 处理块参照
                    else if (ent is BlockReference)
                    {
                        BlockReference blockRef = ent as BlockReference;
                        
                        // 检查块参照是否有属性
                        AttributeCollection attColl = blockRef.AttributeCollection;
                        if (attColl.Count > 0)
                        {
                            // 遍历所有属性
                            foreach (ObjectId attId in attColl)
                            {
                                AttributeReference attRef = tr.GetObject(attId, OpenMode.ForWrite) as AttributeReference;
                                if (attRef != null && attRef.TextStyleId != simSunStyleId)
                                {
                                    attRef.TextStyleId = simSunStyleId;
                                }
                            }
                        }
                    }
                    // 处理所有类型的标注(包括对齐标注)
                    else if (ent is Dimension)
                    {
                        Dimension dim = ent as Dimension;
                        if (dim.TextStyleId != simSunStyleId)
                        {
                            dim.UpgradeOpen();
                            dim.TextStyleId = simSunStyleId;
                            
                            // 设置DIMTXSTY系统变量，确保标注文字使用宋体
                            try
                            {
                                // 尝试直接修改标注样式
                                string dimStyleName = dim.DimensionStyleName;
                                if (!string.IsNullOrEmpty(dimStyleName))
                                {
                                    // 获取标注样式表
                                    DimStyleTable dimStyleTable = tr.GetObject(db.DimStyleTableId, OpenMode.ForRead) as DimStyleTable;
                                    if (dimStyleTable != null && dimStyleTable.Has(dimStyleName))
                                    {
                                        ObjectId dimStyleId = dimStyleTable[dimStyleName];
                                        DimStyleTableRecord dimStyle = tr.GetObject(dimStyleId, OpenMode.ForWrite) as DimStyleTableRecord;
                                        if (dimStyle != null)
                                        {
                                            // 设置标注样式的文字样式为宋体
                                            dimStyle.Dimtxsty = simSunStyleId;
                                        }
                                    }
                                }
                            }
                            catch (System.Exception ex)
                            {
                                Application.DocumentManager.MdiActiveDocument.Editor.WriteMessage($"\n更新标注样式时出错: {ex.Message}");
                            }
                        }
                    }
                }
                
                Application.DocumentManager.MdiActiveDocument.Editor.WriteMessage("\n文字样式设置完成。");
            }
            catch (System.Exception ex)
            {
                Application.DocumentManager.MdiActiveDocument.Editor.WriteMessage($"\n设置文字样式出错: {ex.Message}");
            }
        }

        // 判断是否为横向图框(检查文本旋转角度)
        private bool IsHorizontalFrame(Database db, Polyline framePolyline, List<DBText> dbTextsInFrame, List<MText> mTextsInFrame, Transaction tr)
        {
            try
            {
                // 查找"比例"和"图号"文本
                var scaleTexts = dbTextsInFrame.FindAll(t => t.TextString.Trim().Replace(" ", "").Contains("比例"));
                var drawingNumTexts = dbTextsInFrame.FindAll(t => t.TextString.Trim().Replace(" ", "").Contains("图号"));
                
                // 如果找到了这些文本，检查它们的旋转角度
                foreach (var text in scaleTexts)
                {
                    // 检查旋转值是否接近90度（以角度为单位）
                    if (Math.Abs(text.Rotation * 180 / Math.PI - 90) < 5)
                    {
                        return true;
                    }
                }
                
                foreach (var text in drawingNumTexts)
                {
                    // 检查旋转值是否接近90度（以角度为单位）
                    if (Math.Abs(text.Rotation * 180 / Math.PI - 90) < 5)
                    {
                        return true;
                    }
                }
                
                // 也检查多行文本
                foreach (var mtext in mTextsInFrame)
                {
                    string content = mtext.Text.Trim().Replace(" ", "");
                    if (content.Contains("比例") || content.Contains("图号"))
                    {
                        // 检查旋转值是否接近90度（以角度为单位）
                        if (Math.Abs(mtext.Rotation * 180 / Math.PI - 90) < 5)
                        {
                            return true;
                        }
                    }
                }
                
                // 没有找到横向的文本
                return false;
            }
            catch (System.Exception ex)
            {
                Application.DocumentManager.MdiActiveDocument.Editor.WriteMessage($"\n检测图框方向时出错: {ex.Message}");
                return false; // 出错时默认为非横向
            }
        }
    }
} 