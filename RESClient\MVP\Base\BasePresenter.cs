using System;
using RESClient.MVP.Interfaces;

namespace RESClient.MVP.Base
{
    /// <summary>
    /// 控制器基类
    /// </summary>
    /// <typeparam name="TView">视图接口类型</typeparam>
    public abstract class BasePresenter<TView> : IPresenter<TView> where TView : IView
    {
        /// <summary>
        /// 获取关联的视图
        /// </summary>
        public TView View { get; private set; }

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="view">视图接口</param>
        protected BasePresenter(TView view)
        {
            if (view == null)
            {
                throw new ArgumentNullException(nameof(view));
            }
            
            View = view;
            Initialize();
        }

        /// <summary>
        /// 初始化控制器
        /// </summary>
        public virtual void Initialize()
        {
            // 子类可重写此方法实现特定的初始化逻辑
        }

        /// <summary>
        /// 释放控制器资源
        /// </summary>
        public virtual void Dispose()
        {
            // 子类可重写此方法实现特定的资源释放逻辑
        }

        /// <summary>
        /// 显示错误消息
        /// </summary>
        /// <param name="message">错误消息</param>
        protected void ShowError(string message)
        {
            View.ShowError(message, "错误");
        }

        /// <summary>
        /// 显示消息
        /// </summary>
        /// <param name="message">消息内容</param>
        protected void ShowMessage(string message)
        {
            View.ShowMessage(message, "提示");
        }
    }
} 