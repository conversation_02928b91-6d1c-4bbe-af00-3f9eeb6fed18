using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.IO;
using Newtonsoft.Json;
using RESClient.Utils;
using RESClient.MVP.Base;

namespace RESClient.MVP.Models
{
    /// <summary>
    /// 项目基本信息参数数据模型
    /// </summary>
    public class ProjectInfoParametersModel : BaseModel
    {
        /// <summary>
        /// 项目基本信息参数数据结构
        /// </summary>
        public class ProjectInfoParameters
        {
            [DisplayName("项目概况")]
            [Description("项目的基本概况描述")]
            public string ProjectOverview { get; set; } = "";

            [DisplayName("东侧情况")]
            [Description("项目东侧的道路或环境情况")]
            public string EastSituation { get; set; } = "";

            [DisplayName("西侧情况")]
            [Description("项目西侧的道路或环境情况")]
            public string WestSituation { get; set; } = "";

            [DisplayName("南侧情况")]
            [Description("项目南侧的道路或环境情况")]
            public string SouthSituation { get; set; } = "";

            [DisplayName("北侧情况")]
            [Description("项目北侧的道路或环境情况")]
            public string NorthSituation { get; set; } = "";

            [DisplayName("东邻近项目")]
            [Description("项目东侧邻近的项目或建筑")]
            public string EastAdjacentProject { get; set; } = "";

            [DisplayName("西邻近项目")]
            [Description("项目西侧邻近的项目或建筑")]
            public string WestAdjacentProject { get; set; } = "";

            [DisplayName("南邻近项目")]
            [Description("项目南侧邻近的项目或建筑")]
            public string SouthAdjacentProject { get; set; } = "";

            [DisplayName("北邻近项目")]
            [Description("项目北侧邻近的项目或建筑")]
            public string NorthAdjacentProject { get; set; } = "";

            /// <summary>
            /// 获取所有变量的映射字典
            /// </summary>
            /// <returns>变量名到值的映射</returns>
            public Dictionary<string, string> GetVariableMapping()
            {
                return new Dictionary<string, string>
                {
                    { "${项目概况}", ProjectOverview },
                    { "${东}", EastSituation },
                    { "${西}", WestSituation },
                    { "${南}", SouthSituation },
                    { "${北}", NorthSituation },
                    { "${东邻近项目}", EastAdjacentProject },
                    { "${西项目邻近目}", WestAdjacentProject },  // Note: This matches the template typo
                    { "${南邻近项目}", SouthAdjacentProject },
                    { "${北邻近项目}", NorthAdjacentProject }
                };
            }

            /// <summary>
            /// 验证参数完整性
            /// </summary>
            /// <returns>验证结果和错误信息</returns>
            public (bool IsValid, List<string> Errors) Validate()
            {
                var errors = new List<string>();

                if (string.IsNullOrWhiteSpace(ProjectOverview))
                    errors.Add("项目概况不能为空");

                // 至少需要填写一个方向的情况
                if (string.IsNullOrWhiteSpace(EastSituation) &&
                    string.IsNullOrWhiteSpace(WestSituation) &&
                    string.IsNullOrWhiteSpace(SouthSituation) &&
                    string.IsNullOrWhiteSpace(NorthSituation))
                {
                    errors.Add("至少需要填写一个方向的周边情况");
                }

                return (errors.Count == 0, errors);
            }
        }

        private readonly string _configFilePath;
        private ProjectInfoParameters _currentParameters;

        /// <summary>
        /// 构造函数
        /// </summary>
        public ProjectInfoParametersModel()
        {
            // 设置配置文件路径
            string appDataPath = Environment.GetFolderPath(Environment.SpecialFolder.LocalApplicationData);
            string appFolder = Path.Combine(appDataPath, "RESClient");
            
            if (!Directory.Exists(appFolder))
            {
                Directory.CreateDirectory(appFolder);
            }
            
            _configFilePath = Path.Combine(appFolder, "project_info_parameters.json");
            
            // 加载参数
            _currentParameters = LoadParameters();
        }

        /// <summary>
        /// 获取当前参数
        /// </summary>
        /// <returns>当前参数</returns>
        public ProjectInfoParameters GetCurrentParameters()
        {
            return _currentParameters;
        }

        /// <summary>
        /// 保存参数
        /// </summary>
        /// <param name="parameters">要保存的参数</param>
        /// <returns>保存是否成功</returns>
        public bool SaveParameters(ProjectInfoParameters parameters)
        {
            try
            {
                _currentParameters = parameters;
                string json = JsonConvert.SerializeObject(parameters, Formatting.Indented);
                File.WriteAllText(_configFilePath, json, System.Text.Encoding.UTF8);
                return true;
            }
            catch (Exception ex)
            {
                HandleException(ex);
                return false;
            }
        }

        /// <summary>
        /// 加载参数
        /// </summary>
        /// <returns>加载的参数，如果失败则返回默认参数</returns>
        private ProjectInfoParameters LoadParameters()
        {
            try
            {
                if (!File.Exists(_configFilePath))
                {
                    return CreateDefaultParameters();
                }

                string json = File.ReadAllText(_configFilePath, System.Text.Encoding.UTF8);
                var parameters = JsonConvert.DeserializeObject<ProjectInfoParameters>(json);
                
                return parameters ?? CreateDefaultParameters();
            }
            catch (Exception ex)
            {
                HandleException(ex);
                return CreateDefaultParameters();
            }
        }

        /// <summary>
        /// 创建默认参数
        /// </summary>
        /// <returns>默认参数</returns>
        private ProjectInfoParameters CreateDefaultParameters()
        {
            return new ProjectInfoParameters
            {
                ProjectOverview = "/",
                EastSituation = "/",
                WestSituation = "/",
                SouthSituation = "/",
                NorthSituation = "/",
                EastAdjacentProject = "/",
                WestAdjacentProject = "/",
                SouthAdjacentProject = "/",
                NorthAdjacentProject = "/"
            };
        }
    }
}
