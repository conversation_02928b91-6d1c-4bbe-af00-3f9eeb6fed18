using System;
using System.Threading.Tasks;
using RESClient.Services;
using RESClient.Models;

namespace RESClient
{
    /// <summary>
    /// 连接服务测试程序
    /// </summary>
    public class TestConnectionService
    {
        /// <summary>
        /// 测试连接服务的基本功能
        /// </summary>
        public static async Task TestBasicFunctionality()
        {
            Console.WriteLine("=== 测试连接服务基本功能 ===");
            
            try
            {
                // 创建连接服务
                var configService = RESServerConfigService.Instance;
                var connectionService = new ServerConnectionService(configService);
                
                Console.WriteLine($"服务器地址: {configService.ServerAddress}");
                Console.WriteLine("开始连接测试...");
                
                // 测试连接
                var connectionInfo = await connectionService.CheckConnectionAsync(10);
                
                // 显示结果
                Console.WriteLine($"连接状态: {ServerConnectionService.GetStatusDescription(connectionInfo.Status)}");
                Console.WriteLine($"响应时间: {connectionInfo.ResponseTime.TotalMilliseconds:F0}ms");
                Console.WriteLine($"消息: {connectionInfo.Message}");
                
                if (!string.IsNullOrEmpty(connectionInfo.ErrorDetails))
                {
                    Console.WriteLine($"错误详情: {connectionInfo.ErrorDetails}");
                }
                
                if (!string.IsNullOrEmpty(connectionInfo.ServerVersion))
                {
                    Console.WriteLine($"服务器版本: {connectionInfo.ServerVersion}");
                }
                
                // 测试状态描述方法
                Console.WriteLine("\n=== 测试状态描述方法 ===");
                foreach (ServerConnectionStatus status in Enum.GetValues(typeof(ServerConnectionStatus)))
                {
                    var description = ServerConnectionService.GetStatusDescription(status);
                    var color = ServerConnectionService.GetStatusColor(status);
                    var showDetails = ServerConnectionService.ShouldShowErrorDetails(status);
                    
                    Console.WriteLine($"{status}: {description} (颜色: {color.Name}, 显示详情: {showDetails})");
                }
                
                Console.WriteLine("\n=== 测试完成 ===");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"测试过程中发生错误: {ex.Message}");
                Console.WriteLine($"堆栈跟踪: {ex.StackTrace}");
            }
        }
        
        /// <summary>
        /// 运行连接服务测试
        /// </summary>
        public static async Task RunConnectionTests()
        {
            await TestBasicFunctionality();
        }
    }
}
