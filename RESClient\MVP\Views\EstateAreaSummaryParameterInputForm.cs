using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Linq;
using System.Reflection;
using System.Windows.Forms;
using RESClient.MVP.Models;

namespace RESClient.MVP.Views
{
    /// <summary>
    /// 房产面积汇总表参数输入窗体
    /// </summary>
    public partial class EstateAreaSummaryParameterInputForm : Form
    {
        private EstateAreaSummaryParametersModel.EstateAreaSummaryParameters _parameters;
        private Dictionary<string, Control> _inputControls;
        private TableLayoutPanel _mainPanel;
        private Button _okButton;
        private Button _cancelButton;
        private Button _resetButton;

        /// <summary>
        /// 是否已确认
        /// </summary>
        public bool IsConfirmed { get; private set; }

        /// <summary>
        /// 参数
        /// </summary>
        public EstateAreaSummaryParametersModel.EstateAreaSummaryParameters Parameters => _parameters;

        public EstateAreaSummaryParameterInputForm(EstateAreaSummaryParametersModel.EstateAreaSummaryParameters parameters)
        {
            _parameters = parameters ?? new EstateAreaSummaryParametersModel.EstateAreaSummaryParameters();
            _inputControls = new Dictionary<string, Control>();
            InitializeComponent();
            InitializeCustomControls();
            CreateDynamicControls();
            LoadParameterValues();
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();
            this.ResumeLayout(false);
        }

        private void InitializeCustomControls()
        {
            // 应用标准窗体设置，但使用适合内容的较小高度（只有1个多行文本框字段）
            var customSize = new Size(620, 280); // 减少高度以适应只有1个多行文本框字段的内容
            ParameterInputFormStyleConfig.ApplyStandardFormSettings(this, "房产面积汇总表参数设置", customSize);

            // 设置更合适的最小和最大尺寸
            this.MinimumSize = new Size(520, 230);
            this.MaximumSize = new Size(800, 350);

            // 创建主容器面板
            var mainContainer = ParameterInputFormStyleConfig.CreateMainContainer();

            // 创建可滚动的主面板
            _mainPanel = ParameterInputFormStyleConfig.CreateMainPanel();

            mainContainer.Controls.Add(_mainPanel);
            this.Controls.Add(mainContainer);
        }

        private void CreateDynamicControls()
        {
            var properties = typeof(EstateAreaSummaryParametersModel.EstateAreaSummaryParameters).GetProperties()
                .Where(p => p.CanRead && p.CanWrite)
                .ToArray();

            _mainPanel.RowCount = properties.Length + 1; // +1 for buttons

            int rowIndex = 0;
            foreach (var property in properties)
            {
                CreateControlForProperty(property, rowIndex);
                rowIndex++;
            }

            // 创建按钮面板
            CreateButtonPanel(rowIndex);
        }

        private void CreateControlForProperty(PropertyInfo property, int rowIndex)
        {
            // 获取显示名称和描述
            string displayName = property.GetCustomAttribute<DisplayNameAttribute>()?.DisplayName ?? property.Name;
            string description = property.GetCustomAttribute<DescriptionAttribute>()?.Description ?? "";

            // 判断是否为多行文本框
            bool isMultiline = property.Name == "Remarks";
            int rowHeight = isMultiline ? ParameterInputFormStyleConfig.MultilineRowHeight : ParameterInputFormStyleConfig.StandardRowHeight;

            // 设置行样式
            _mainPanel.RowStyles.Add(new RowStyle(SizeType.Absolute, rowHeight));

            // 创建标签容器
            var labelContainer = ParameterInputFormStyleConfig.CreateLabelContainer();
            var label = ParameterInputFormStyleConfig.CreateStandardLabel(displayName, this.Font);
            labelContainer.Controls.Add(label);

            // 创建输入控件容器
            var inputContainer = ParameterInputFormStyleConfig.CreateInputContainer();
            var inputControl = CreateInputControl(property);
            inputContainer.Controls.Add(inputControl);

            // 设置工具提示
            if (!string.IsNullOrEmpty(description))
            {
                var toolTip = new ToolTip();
                toolTip.SetToolTip(label, description);
                toolTip.SetToolTip(inputControl, description);
            }

            // 添加到面板
            _mainPanel.Controls.Add(labelContainer, 0, rowIndex);
            _mainPanel.Controls.Add(inputContainer, 1, rowIndex);

            // 保存控件引用
            _inputControls[property.Name] = inputControl;
        }

        private Control CreateInputControl(PropertyInfo property)
        {
            if (property.PropertyType == typeof(string))
            {
                // 对于备注字段，使用多行文本框
                bool isMultiline = property.Name == "Remarks";
                return ParameterInputFormStyleConfig.CreateStandardTextBox(this.Font, isMultiline);
            }
            else
            {
                // 默认使用文本框
                return ParameterInputFormStyleConfig.CreateStandardTextBox(this.Font);
            }
        }

        private void LoadParameterValues()
        {
            var properties = typeof(EstateAreaSummaryParametersModel.EstateAreaSummaryParameters).GetProperties()
                .Where(p => p.CanRead && p.CanWrite);

            foreach (var property in properties)
            {
                if (_inputControls.TryGetValue(property.Name, out Control control))
                {
                    var value = property.GetValue(_parameters);
                    SetControlValue(control, value);
                }
            }
        }

        private void SetControlValue(Control control, object value)
        {
            switch (control)
            {
                case TextBox textBox:
                    textBox.Text = value?.ToString() ?? "";
                    break;
            }
        }

        private void SaveParameterValues()
        {
            var properties = typeof(EstateAreaSummaryParametersModel.EstateAreaSummaryParameters).GetProperties()
                .Where(p => p.CanRead && p.CanWrite);

            foreach (var property in properties)
            {
                if (_inputControls.TryGetValue(property.Name, out Control control))
                {
                    var value = GetControlValue(control, property.PropertyType);
                    property.SetValue(_parameters, value);
                }
            }
        }

        private object GetControlValue(Control control, Type targetType)
        {
            switch (control)
            {
                case TextBox textBox:
                    return textBox.Text;
                default:
                    return null;
            }
        }

        private void CreateButtonPanel(int rowIndex)
        {
            // 创建固定在底部的按钮面板
            var buttonContainer = ParameterInputFormStyleConfig.CreateButtonContainer();
            var buttonPanel = ParameterInputFormStyleConfig.CreateButtonPanel();

            // 取消按钮
            _cancelButton = ParameterInputFormStyleConfig.CreateStandardButton("取消", this.Font, false, DialogResult.Cancel);
            _cancelButton.Click += CancelButton_Click;

            // 确定按钮
            _okButton = ParameterInputFormStyleConfig.CreateStandardButton("确定", this.Font, true);
            _okButton.Click += OkButton_Click;

            // 重置按钮
            _resetButton = ParameterInputFormStyleConfig.CreateStandardButton("重置", this.Font);
            _resetButton.Click += ResetButton_Click;

            // 按钮添加顺序：取消、确定、重置（从右到左）
            buttonPanel.Controls.Add(_cancelButton);
            buttonPanel.Controls.Add(_okButton);
            buttonPanel.Controls.Add(_resetButton);

            buttonContainer.Controls.Add(buttonPanel);

            // 将按钮容器添加到窗体而不是TableLayoutPanel
            this.Controls.Add(buttonContainer);

            // 设置默认按钮
            this.CancelButton = _cancelButton;
        }

        private void OkButton_Click(object sender, EventArgs e)
        {
            try
            {
                SaveParameterValues();
                IsConfirmed = true;
                this.DialogResult = DialogResult.OK;
                this.Close();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"保存参数时发生错误: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void CancelButton_Click(object sender, EventArgs e)
        {
            IsConfirmed = false;
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }

        private void ResetButton_Click(object sender, EventArgs e)
        {
            try
            {
                var result = MessageBox.Show(
                    "确定要重置所有参数为默认值吗？\n\n此操作将清除当前所有输入的内容。",
                    "确认重置",
                    MessageBoxButtons.YesNo,
                    MessageBoxIcon.Question);

                if (result == DialogResult.Yes)
                {
                    // 创建默认参数
                    _parameters = new EstateAreaSummaryParametersModel.EstateAreaSummaryParameters();

                    // 重新加载参数值到控件
                    LoadParameterValues();

                    MessageBox.Show("参数已重置为默认值", "重置完成",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"重置参数时发生错误: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }
}
