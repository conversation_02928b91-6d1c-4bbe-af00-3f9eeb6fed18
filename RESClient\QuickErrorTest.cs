using System;
using System.Collections.Generic;
using System.Linq;
using RESClient.Services;
using RESClient.MVP.Models;

namespace RESClient
{
    /// <summary>
    /// 快速错误报告系统测试
    /// </summary>
    public class QuickErrorTest
    {
        /// <summary>
        /// 运行快速测试
        /// </summary>
        public static void RunQuickTest()
        {
            Console.WriteLine("=== 快速错误报告系统测试 ===");
            
            try
            {
                // 1. 测试错误收集器
                Console.WriteLine("1. 测试错误收集器...");
                TestErrorCollector();
                
                // 2. 测试MainModel错误收集
                Console.WriteLine("2. 测试MainModel错误收集...");
                TestMainModelErrorCollection();
                
                Console.WriteLine("✅ 快速测试完成！错误报告系统工作正常。");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ 测试失败: {ex.Message}");
                Console.WriteLine($"详细信息: {ex.StackTrace}");
            }
        }

        /// <summary>
        /// 测试错误收集器基本功能
        /// </summary>
        private static void TestErrorCollector()
        {
            var errorCollector = new ErrorCollector();
            
            // 添加测试错误
            errorCollector.AddError(ErrorType.MissingFile, ErrorSeverity.Error, 
                "封面", "模板文件不存在");
            
            errorCollector.AddError(ErrorType.DataValidation, ErrorSeverity.Warning, 
                "项目信息", "数据验证警告");
            
            // 测试解析进度消息
            errorCollector.ParseAndAddErrorFromProgressMessage("[MODULE_FAILED]楼栋信息:生成失败");
            errorCollector.ParseAndAddErrorFromProgressMessage("[ERROR]资源文件验证失败");
            
            // 验证结果
            var errors = errorCollector.GetAllErrors();
            Console.WriteLine($"   收集到 {errors.Count} 个错误");
            Console.WriteLine($"   是否有严重错误: {errorCollector.HasCriticalErrors()}");
            
            // 测试统计
            var typeStats = errorCollector.GetErrorStatistics();
            Console.WriteLine($"   错误类型数: {typeStats.Count}");
            
            Console.WriteLine("   ✅ 错误收集器测试通过");
        }

        /// <summary>
        /// 测试MainModel错误收集
        /// </summary>
        private static void TestMainModelErrorCollection()
        {
            var mainModel = new MainModel();
            
            // 验证ErrorCollector属性存在
            var errorCollector = mainModel.ErrorCollector;
            Console.WriteLine($"   MainModel.ErrorCollector 可访问: {errorCollector != null}");
            
            // 添加测试错误
            errorCollector.AddError(ErrorType.ParameterConfiguration, ErrorSeverity.Error, 
                "系统", "测试错误");
            
            Console.WriteLine($"   添加错误后计数: {errorCollector.GetErrorCount()}");
            Console.WriteLine("   ✅ MainModel错误收集测试通过");
        }
    }
}
