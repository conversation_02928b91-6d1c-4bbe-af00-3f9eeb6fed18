using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using RESClient.Services.Implementations;

namespace RESClient
{
    /// <summary>
    /// 测试样式保留修复的效果
    /// </summary>
    public static class TestStylePreservationFix
    {
        /// <summary>
        /// 运行样式保留测试
        /// </summary>
        public static async Task RunStylePreservationTests()
        {
            Console.WriteLine("=== 样式保留修复测试 ===");
            Console.WriteLine();

            await TestProjectInfoStylePreservation();
            Console.WriteLine();
            await TestBasementDefenseStylePreservation();

            Console.WriteLine();
            Console.WriteLine("=== 样式保留测试完成 ===");
            Console.WriteLine();
            Console.WriteLine("请检查生成的文档：");
            Console.WriteLine("1. 对比原始模板文件，确认样式是否完全保留");
            Console.WriteLine("2. 验证占位符是否被正确替换");
            Console.WriteLine("3. 确认字体、字号、颜色、对齐方式等格式属性未被修改");
        }

        /// <summary>
        /// 测试项目基本信息模块的样式保留
        /// </summary>
        private static async Task TestProjectInfoStylePreservation()
        {
            Console.WriteLine("--- 测试项目基本信息模块样式保留 ---");
            
            try
            {
                var generator = new ProjectInfoModuleGenerator();
                
                string outputDir = System.IO.Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "样式测试输出");
                if (!System.IO.Directory.Exists(outputDir))
                {
                    System.IO.Directory.CreateDirectory(outputDir);
                }

                var parameters = new Dictionary<string, object>
                {
                    { "OutputDirectory", outputDir },
                    { "ProjectOverview", "本项目位于成都市高新区，总建筑面积约50000平方米，包含住宅、商业及配套设施。项目采用现代化设计理念，注重绿色环保和智能化建设。" },
                    { "EastSituation", "东临天府大道，交通便利，距离地铁站约500米" },
                    { "WestSituation", "西接城市绿地公园，环境优美，空气清新" },
                    { "SouthSituation", "南侧为规划商业街区，生活配套完善" },
                    { "NorthSituation", "北面紧邻地铁站，出行便捷" },
                    { "EastAdjacentProject", "高新技术产业园区，科技氛围浓厚" },
                    { "WestAdjacentProject", "城市综合体项目，商业繁华" },
                    { "SouthAdjacentProject", "商业综合楼，购物娱乐便利" },
                    { "NorthAdjacentProject", "地铁交通枢纽，交通四通八达" }
                };

                Console.WriteLine("正在生成项目基本信息文档...");
                
                bool result = await generator.GenerateAsync(parameters, (progress, message) =>
                {
                    if (progress % 25 == 0 || progress == 100)
                    {
                        Console.WriteLine($"  [{progress}%] {message}");
                    }
                });

                if (result)
                {
                    Console.WriteLine("✓ 项目基本信息文档生成成功");
                    Console.WriteLine($"  文件位置: {System.IO.Path.Combine(outputDir, "项目基本信息.docx")}");
                }
                else
                {
                    Console.WriteLine("✗ 项目基本信息文档生成失败");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"✗ 项目基本信息测试异常: {ex.Message}");
            }
        }

        /// <summary>
        /// 测试地下室人防区域说明模块的样式保留
        /// </summary>
        private static async Task TestBasementDefenseStylePreservation()
        {
            Console.WriteLine("--- 测试地下室人防区域说明模块样式保留 ---");
            
            try
            {
                var generator = new BasementDefenseModuleGenerator();
                
                string outputDir = System.IO.Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "样式测试输出");
                if (!System.IO.Directory.Exists(outputDir))
                {
                    System.IO.Directory.CreateDirectory(outputDir);
                }

                var parameters = new Dictionary<string, object>
                {
                    { "OutputDirectory", outputDir },
                    { "DataProvider", "四川省川建勘察设计院有限公司" },
                    { "DrawingName", "地下室人防区域平面图 DRG-001（1:100）" },
                    { "Description", "本项目地下室设有人防区域，位于地下一层东侧，总面积约500平方米，按照《人民防空工程设计规范》GB50225-2005进行设计和施工。人防区域包括人员掩蔽部、物资库房、设备用房等功能区域，满足战时防护和平时使用的双重要求。" }
                };

                Console.WriteLine("正在生成地下室人防区域说明文档...");
                
                bool result = await generator.GenerateAsync(parameters, (progress, message) =>
                {
                    if (progress % 25 == 0 || progress == 100)
                    {
                        Console.WriteLine($"  [{progress}%] {message}");
                    }
                });

                if (result)
                {
                    Console.WriteLine("✓ 地下室人防区域说明文档生成成功");
                    Console.WriteLine($"  文件位置: {System.IO.Path.Combine(outputDir, "地下室人防区域说明.docx")}");
                }
                else
                {
                    Console.WriteLine("✗ 地下室人防区域说明文档生成失败");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"✗ 地下室人防区域说明测试异常: {ex.Message}");
            }
        }
    }
}
