using System;
using System.Configuration;
using System.IO;
using System.Net.Http;

namespace RESClient.Services
{
    /// <summary>
    /// RESServer配置服务
    /// </summary>
    public class RESServerConfigService
    {
        private static readonly Lazy<RESServerConfigService> _instance = new Lazy<RESServerConfigService>(() => new RESServerConfigService());
        
        /// <summary>
        /// 获取配置服务实例
        /// </summary>
        public static RESServerConfigService Instance => _instance.Value;
        
        private const string DEFAULT_SERVER_ADDRESS = "http://localhost:5104";
        private const string CONFIG_KEY_SERVER_ADDRESS = "RESServerAddress";
        private const string CONFIG_KEY_CONNECTION_TIMEOUT = "RESServerConnectionTimeout";
        private const string CONFIG_KEY_REQUEST_TIMEOUT = "RESServerRequestTimeout";
        
        private string _serverAddress;
        private int _connectionTimeoutMinutes;
        private int _requestTimeoutMinutes;
        
        /// <summary>
        /// 私有构造函数
        /// </summary>
        private RESServerConfigService()
        {
            LoadConfiguration();
        }
        
        /// <summary>
        /// RESServer服务器地址
        /// </summary>
        public string ServerAddress => _serverAddress;
        
        /// <summary>
        /// 连接超时时间（分钟）
        /// </summary>
        public int ConnectionTimeoutMinutes => _connectionTimeoutMinutes;
        
        /// <summary>
        /// 请求超时时间（分钟）
        /// </summary>
        public int RequestTimeoutMinutes => _requestTimeoutMinutes;
        
        /// <summary>
        /// 获取完整的API URL
        /// </summary>
        /// <param name="endpoint">API端点</param>
        /// <returns>完整的API URL</returns>
        public string GetApiUrl(string endpoint)
        {
            if (string.IsNullOrEmpty(endpoint))
                throw new ArgumentException("API端点不能为空", nameof(endpoint));
            
            // 确保endpoint以/开头
            if (!endpoint.StartsWith("/"))
                endpoint = "/" + endpoint;
            
            // 移除ServerAddress末尾的/
            string baseUrl = _serverAddress.TrimEnd('/');
            
            return baseUrl + endpoint;
        }
        
        /// <summary>
        /// 获取DWG处理相关的API URL
        /// </summary>
        /// <param name="action">操作类型 (process, status, download, files)</param>
        /// <param name="sessionId">会话ID（可选）</param>
        /// <returns>API URL</returns>
        public string GetDrawingProcessingApiUrl(string action, string sessionId = null)
        {
            string endpoint = $"/api/DrawingProcessing/{action}";
            
            if (!string.IsNullOrEmpty(sessionId))
            {
                endpoint += $"/{sessionId}";
            }
            
            return GetApiUrl(endpoint);
        }
        
        /// <summary>
        /// 加载配置
        /// </summary>
        private void LoadConfiguration()
        {
            try
            {
                // 从App.config读取配置
                _serverAddress = ConfigurationManager.AppSettings[CONFIG_KEY_SERVER_ADDRESS] ?? DEFAULT_SERVER_ADDRESS;
                
                // 读取超时配置
                if (!int.TryParse(ConfigurationManager.AppSettings[CONFIG_KEY_CONNECTION_TIMEOUT], out _connectionTimeoutMinutes))
                {
                    _connectionTimeoutMinutes = 15; // 默认15分钟
                }
                
                if (!int.TryParse(ConfigurationManager.AppSettings[CONFIG_KEY_REQUEST_TIMEOUT], out _requestTimeoutMinutes))
                {
                    _requestTimeoutMinutes = 10; // 默认10分钟
                }
                
                // 验证服务器地址格式
                if (!Uri.TryCreate(_serverAddress, UriKind.Absolute, out Uri serverUri))
                {
                    throw new ConfigurationErrorsException($"无效的服务器地址配置: {_serverAddress}");
                }
                
                // 确保地址不以/结尾
                _serverAddress = _serverAddress.TrimEnd('/');
            }
            catch (Exception ex)
            {
                // 如果配置加载失败，使用默认值
                _serverAddress = DEFAULT_SERVER_ADDRESS;
                _connectionTimeoutMinutes = 15;
                _requestTimeoutMinutes = 10;
                
                // 可以在这里记录日志
                Console.WriteLine($"加载RESServer配置失败，使用默认配置: {ex.Message}");
            }
        }
        
        /// <summary>
        /// 更新服务器地址配置
        /// </summary>
        /// <param name="serverAddress">新的服务器地址</param>
        public void UpdateServerAddress(string serverAddress)
        {
            if (string.IsNullOrWhiteSpace(serverAddress))
                throw new ArgumentException("服务器地址不能为空", nameof(serverAddress));
            
            if (!Uri.TryCreate(serverAddress, UriKind.Absolute, out Uri serverUri))
                throw new ArgumentException($"无效的服务器地址: {serverAddress}", nameof(serverAddress));
            
            _serverAddress = serverAddress.TrimEnd('/');
            
            // 可以在这里保存到配置文件
            SaveConfiguration();
        }
        
        /// <summary>
        /// 保存配置到文件
        /// </summary>
        private void SaveConfiguration()
        {
            try
            {
                // 这里可以实现保存配置到App.config或其他配置文件的逻辑
                // 由于App.config在运行时通常是只读的，可以考虑使用其他配置文件
                
                // 示例：保存到用户配置文件
                var config = ConfigurationManager.OpenExeConfiguration(ConfigurationUserLevel.None);
                
                if (config.AppSettings.Settings[CONFIG_KEY_SERVER_ADDRESS] != null)
                {
                    config.AppSettings.Settings[CONFIG_KEY_SERVER_ADDRESS].Value = _serverAddress;
                }
                else
                {
                    config.AppSettings.Settings.Add(CONFIG_KEY_SERVER_ADDRESS, _serverAddress);
                }
                
                config.Save(ConfigurationSaveMode.Modified);
                ConfigurationManager.RefreshSection("appSettings");
            }
            catch (Exception ex)
            {
                // 保存失败时记录日志，但不抛出异常
                Console.WriteLine($"保存RESServer配置失败: {ex.Message}");
            }
        }
        
        /// <summary>
        /// 测试服务器连接
        /// </summary>
        /// <returns>连接是否成功</returns>
        public async System.Threading.Tasks.Task<bool> TestConnectionAsync()
        {
            try
            {
                using (var client = new System.Net.Http.HttpClient())
                {
                    client.Timeout = TimeSpan.FromSeconds(10); // 短超时用于测试
                    
                    // 尝试访问服务器根路径或健康检查端点
                    var response = await client.GetAsync(_serverAddress);
                    return response.IsSuccessStatusCode;
                }
            }
            catch
            {
                return false;
            }
        }
        
        /// <summary>
        /// 创建带认证的 HttpClient
        /// </summary>
        /// <param name="token">认证令牌</param>
        /// <returns>配置好的 HttpClient</returns>
        public HttpClient CreateAuthenticatedHttpClient(string token = null)
        {
            var client = new HttpClient();
            client.Timeout = TimeSpan.FromMinutes(_requestTimeoutMinutes);

            if (!string.IsNullOrEmpty(token))
            {
                client.DefaultRequestHeaders.Authorization =
                    new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", token);
            }

            return client;
        }

        /// <summary>
        /// 获取配置信息摘要
        /// </summary>
        /// <returns>配置信息</returns>
        public string GetConfigurationSummary()
        {
            return $"RESServer地址: {_serverAddress}, 连接超时: {_connectionTimeoutMinutes}分钟, 请求超时: {_requestTimeoutMinutes}分钟";
        }
    }
}
