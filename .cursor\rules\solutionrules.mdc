---
description: 
globs: 
alwaysApply: true
---
# Solution Rules
## 1. Solution Purpose
- A real estate mapping and measurement solution consisting of server, client, and AutoCAD plugin components
- RESClient synthesizes reports using templates, with data from RESServer and RESCADServerPlugin
- Data flow follows examples in the "实测" (Actual Measurement) directory

## 2. Code Organization
- Follow C# naming conventions and formatting
- Use meaningful variable/method names (English for code, Chinese for UI/reports)
- Document complex operations with comments
- All projects in the solution use Visual Studio 2022 for debugging

## 3. Version Control
- Commit messages should clearly describe changes
- Keep commits focused on single features or fixes

## 4. Sample Data
- The "实测" (Actual Measurement) folder contains sample data and example outputs
- Programming should be based on understanding these examples
- "实测/房产测绘/房产测绘成果报告.doc" is an example output report, not a template

## 5. Industry Context
- This project applies to the real estate mapping industry
- Ensure terminology and outputs conform to industry standards

## 6. Solution Architecture
- **RESServer**: Server component that provides non-CAD services for data processing
- **RESClient**: Client application that synthesizes reports using templates
- **RESCADServerPlugin**: AutoCAD plugin that provides CAD operation services via embedded web API
- Both RESServer and RESCADServerPlugin provide services directly to RESClient
- RESClient is responsible for report synthesis using report section templates
- Client-Server (C/S) architecture for communication between RESClient and both servers
- HTTP/REST communication for all server interactions
- PostgreSQL database for data persistence (to be implemented in future phases)
- Authentication required for client-server communication

## 7. Report Generation Strategy
- Reports are generated by RESClient using a modular approach
- The table of contents structure is fixed as shown in the example report
- RESClient manages report section templates and handles the report synthesis process
- Some report module data is provided or generated by RESCADServerPlugin and RESServer
- Report sections are generated individually according to the table of contents
- Individual sections are later merged into a complete final report by RESClient
- Section titles include: 封面,目录，作业声明, 作业、质量检查与验收, 项目基本信息, 楼栋基本信息, 经主管部门批准的相关证照, 地下室人防区域说明, 项目住地及测绘房屋分布图, 建筑物现状影像图, 房产面积汇总表, 房产分户面积统计表, 房产分层测绘图
- Directory structure includes two types:
  - Template directory: Stores report section templates used by RESClient
  - Production directory structure: 
    - "Production directory" > "User directory" > "Report sections directory", "Report directory"
    - User directories are created based on username or user ID
    - Report sections directory contains individual generated section files
    - Report directory contains complete merged reports
- Production workflow:
  - RESClient reads templates from template directory
  - RESClient requests specific data from RESServer and RESCADServerPlugin as needed
  - RESClient fills templates with the received data
  - RESClient saves completed sections and merges them into final reports
  - Production subdirectories are distinguished by username or user ID
  - If user authentication is not yet implemented, use a test directory
  - Each new generation overwrites previous files

## 8. Extensibility Requirements
- All components should be designed with extensibility in mind
- Framework should allow for future data management capabilities
- Code structure should accommodate future integration of login functionality
- Database access layer should be abstracted to simplify future PostgreSQL implementation

# RESServer Project Rules
## 1. Technology Stack & Environment
- **Primary Language**: C#
- **Framework**: ASP.NET Core Web API
- **CAD Integration**: HTTP client for communicating with RESCADServerPlugin
- **Document Processing**: NPOI 2.7.3 for Excel and Word operations
- **Database**: PostgreSQL (future implementation)
- **Authentication**: User authentication system (future implementation)

## 2. Responsibilities
- Provide non-CAD services directly to RESClient
- Process measurement data received from RESClient
- Generate specific report section data as requested by RESClient
- Handle data transformation and calculation
- Authenticate client connections
- Manage data persistence (future implementation)

# RESClient Project Rules
## 1. Technology Stack & Environment
- **Primary Language**: C#
- **UI Framework**: Windows Forms or WPF
- **Communication**: Network protocols for C/S architecture
- **Asynchronous Programming (async/await)**: Ensure the responsiveness of the UI and avoid UI lag when performing time-consuming operations (such as network requests)

## 2. Responsibilities
- Provide user interface for data input and management
- Manage report section templates
- Request data from RESServer and RESCADServerPlugin as needed
- Generate reports by synthesizing individual sections using templates
- Merge individual sections into complete reports
- Maintain the fixed table of contents structure in all reports
- Display processing results and reports
- Implement login interface for user authentication
- Maintain session with servers after authentication

# RESCADServerPlugin Project Rules
## 1. Technology Stack & Environment
- **Primary Language**: C#
- **Framework**: AutoCAD .NET API for plugin development
- **Web Service**: ASP.NET Core Web API embedded within AutoCAD plugin
- **CAD Version**: AutoCAD 2025

## 2. Responsibilities
- Extend functionality within AutoCAD environment
- Provide CAD operation services via embedded web API directly to RESClient
- Handle DWG file reading, conversion and processing
- Perform area measurements and calculations
- Expose CAD operations through RESTful API endpoints
- Maintain persistent AutoCAD instance for improved performance

## 3. Architecture
- Plugin loads when AutoCAD starts
- Embedded web server starts on plugin initialization
- Exposes HTTP endpoints (default: http://localhost:6060)
- RESClient communicates with plugin via HTTP requests
- Plugin executes operations in the active AutoCAD environment
- All requests should be processed in the background using AutoCAD .NET API's Document without displaying in the AutoCAD UI