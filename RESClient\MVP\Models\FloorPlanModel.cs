using System;
using System.Collections.Generic;
using System.Drawing;
using System.Drawing.Imaging;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using NPOI.XWPF.UserModel;
using RESClient.MVP.Base;

namespace RESClient.MVP.Models
{
    /// <summary>
    /// 房产分层测绘图模型，用于处理房产分层测绘图模板的填充
    /// </summary>
    public class FloorPlanModel : BaseModel
    {
        /// <summary>
        /// 填充房产分层测绘图模板
        /// </summary>
        /// <param name="outputPath">输出路径</param>
        /// <returns>是否成功</returns>
        public async Task<bool> FillFloorPlanTemplateAsync(string outputPath)
        {
            try
            {
                return await Task.Run(() =>
                {
                    // 获取模板路径
                    string templatePath = Path.Combine(
                        AppDomain.CurrentDomain.BaseDirectory,
                        Program.TemplateDirectory,
                        "13_房产分层测绘图",
                        "房产分层测绘图.docx");

                    if (!File.Exists(templatePath))
                    {
                        throw new FileNotFoundException("房产分层测绘图模板文件不存在", templatePath);
                    }

                    // 获取图片文件夹路径
                    string imagesFolder = Path.Combine(
                        AppDomain.CurrentDomain.BaseDirectory,
                        "报告生成",
                        "默认用户",
                        "报告分块",
                        "13_房产分层测绘图",
                        "printgifs");

                    if (!Directory.Exists(imagesFolder))
                    {
                        throw new DirectoryNotFoundException($"图片文件夹不存在: {imagesFolder}");
                    }

                    // 获取所有的GIF图片
                    List<string> imageFiles = Directory.GetFiles(imagesFolder, "*.gif")
                        .OrderBy(f => f)
                        .ToList();

                    if (imageFiles.Count == 0)
                    {
                        throw new FileNotFoundException("未在指定文件夹中找到GIF图片", imagesFolder);
                    }

                    // 确保输出目录存在
                    string outputDir = Path.GetDirectoryName(outputPath);
                    Directory.CreateDirectory(outputDir);

                    // 创建模板的临时副本
                    string tempFilePath = Path.Combine(
                        Path.GetTempPath(),
                        $"temp_floorplan_{DateTime.Now.Ticks}.docx");

                    File.Copy(templatePath, tempFilePath, true);

                    XWPFDocument doc;
                    using (FileStream fs = new FileStream(tempFilePath, FileMode.Open, FileAccess.ReadWrite))
                    {
                        doc = new XWPFDocument(fs);
                    }

                    // 获取模板中的占位图片尺寸信息 (in EMUs)
                    Tuple<long, long> firstPageImageEmuSize = null;
                    Tuple<long, long> secondPageImageEmuSize = null;

                    // 确保文档中至少有图片元素可供查找
                    bool foundFirstPlaceholder = false;
                    bool foundSecondPlaceholder = false;

                    // Iterate over paragraphs and runs to find pictures
                    foreach (var element in doc.BodyElements.OfType<XWPFParagraph>())
                    {
                        foreach (var run in element.Runs)
                        {
                            foreach (var picture in run.GetEmbeddedPictures())
                            {
                                var ctPicture = picture.GetCTPicture();
                                if (ctPicture?.spPr?.xfrm?.ext != null)
                                {
                                    long widthEmu = ctPicture.spPr.xfrm.ext.cx;
                                    long heightEmu = ctPicture.spPr.xfrm.ext.cy;

                                    if (widthEmu > 0 && heightEmu > 0) // Ensure valid dimensions
                                    {
                                        if (!foundFirstPlaceholder)
                                        {
                                            firstPageImageEmuSize = Tuple.Create(widthEmu, heightEmu);
                                            foundFirstPlaceholder = true;
                                        }
                                        else if (!foundSecondPlaceholder)
                                        {
                                            secondPageImageEmuSize = Tuple.Create(widthEmu, heightEmu);
                                            foundSecondPlaceholder = true;
                                            break; // Found both
                                        }
                                    }
                                }
                            }
                            if (foundSecondPlaceholder) break;
                        }
                        if (foundSecondPlaceholder) break;
                    }
                    
                    // If second placeholder is not explicitly different, use first for subsequent.
                    if (foundFirstPlaceholder && !foundSecondPlaceholder)
                    {
                        secondPageImageEmuSize = firstPageImageEmuSize;
                        foundSecondPlaceholder = true; 
                    }

                    if (!foundFirstPlaceholder) // if even the first is not found
                    {
                        throw new InvalidOperationException("无法在模板中找到有效的占位图片尺寸 (第一页)。");
                    }
                     if (!foundSecondPlaceholder) // If second wasn't found (and not defaulted from first)
                    {
                        // This case should be covered by the defaulting logic above, 
                        // but as a safeguard or if defaulting isn't desired for some reason.
                        throw new InvalidOperationException("无法在模板中找到有效的占位图片尺寸 (第二页/后续)。");
                    }

                    // 存储所有包含图片的段落，以便后续替换
                    var paragraphsWithImages = new List<XWPFParagraph>();
                    foreach (var element in doc.BodyElements.OfType<XWPFParagraph>())
                    {
                        foreach (var run in element.Runs)
                        {
                            if (run.GetEmbeddedPictures().Count > 0)
                            {
                                paragraphsWithImages.Add(element);
                                break;
                            }
                        }
                    }

                    // 只删除包含图片的段落，保留其他内容
                    foreach (var paragraph in paragraphsWithImages)
                    {
                        doc.RemoveBodyElement(doc.GetPosOfParagraph(paragraph));
                    }

                    // 如果只有一张图片，确保删除所有除第一页外的内容
                    if (imageFiles.Count == 1)
                    {
                        // 找到所有页面分隔符并删除它们以及它们之后的内容
                        List<int> pageBreakPositions = new List<int>();
                        for (int i = 0; i < doc.BodyElements.Count; i++)
                        {
                            var element = doc.BodyElements[i];
                            if (element is XWPFParagraph paragraph)
                            {
                                foreach (var run in paragraph.Runs)
                                {
                                    if (run.GetCTR().GetBrList().Any(br => br.type == NPOI.OpenXmlFormats.Wordprocessing.ST_BrType.page))
                                    {
                                        pageBreakPositions.Add(i);
                                        break;
                                    }
                                }
                            }
                        }

                        // 从后向前删除页面分隔符和后续内容，避免索引变化的问题
                        pageBreakPositions.Reverse();
                        foreach (int pos in pageBreakPositions)
                        {
                            // 删除页面分隔符及其后的所有元素
                            for (int i = doc.BodyElements.Count - 1; i >= pos; i--)
                            {
                                doc.RemoveBodyElement(i);
                            }
                        }
                    }

                    // 添加新图片
                    for (int i = 0; i < imageFiles.Count; i++)
                    {
                        string imagePath = imageFiles[i];
                        XWPFParagraph paragraph = doc.CreateParagraph();
                        paragraph.Alignment = ParagraphAlignment.CENTER; // Center images
                        XWPFRun run = paragraph.CreateRun();

                        Tuple<long, long> targetEmuSize = (i == 0) ? firstPageImageEmuSize : secondPageImageEmuSize;

                        using (FileStream imageStream = new FileStream(imagePath, FileMode.Open, FileAccess.Read))
                        {
                            int pictureFormat = GetPictureTypeForNPOI(Path.GetExtension(imagePath));
                            
                            using (Image img = Image.FromStream(imageStream, false, false)) // Keep stream open, don't validate image data yet
                            {
                                SizeF originalDpi = new SizeF(img.HorizontalResolution, img.VerticalResolution);
                                if (originalDpi.Width == 0) originalDpi.Width = 96f; // Default DPI if missing
                                if (originalDpi.Height == 0) originalDpi.Height = 96f;

                                Tuple<long, long> finalEmuDimensions = CalculateScaledEmuDimensions(
                                    img.Size, 
                                    originalDpi, 
                                    targetEmuSize);

                                // Reset stream position for AddPicture if Image.FromStream consumed it
                                imageStream.Position = 0; 
                                run.AddPicture(pictureData: imageStream, 
                                               pictureType: pictureFormat, 
                                               filename: Path.GetFileName(imagePath), 
                                               width: (int)finalEmuDimensions.Item1,
                                               height: (int)finalEmuDimensions.Item2);
                            }
                        }

                        // 只有当有多张图片时才添加分页符
                        if (imageFiles.Count > 1 && i < imageFiles.Count - 1)
                        {
                            // Add page break after each image except the last one
                            doc.CreateParagraph().CreateRun().AddBreak(BreakType.PAGE);
                        }
                    }

                    // 保存填充后的文档到指定输出路径
                    using (FileStream fs = new FileStream(outputPath, FileMode.Create, FileAccess.Write))
                    {
                        doc.Write(fs);
                    }

                    // 删除临时文件
                    try
                    {
                        if (File.Exists(tempFilePath))
                        {
                            File.Delete(tempFilePath);
                        }
                    }
                    catch { /* 临时文件删除失败不影响主流程 */ }

                    return true;
                });
            }
            catch (Exception ex)
            {
                HandleException(ex);
                return false;
            }
        }

        /// <summary>
        /// Calculates the proportionally scaled EMU dimensions for an image to fit within target EMU dimensions.
        /// </summary>
        /// <param name="originalImagePixelSize">Original pixel dimensions of the image to scale.</param>
        /// <param name="originalImageDpi">DPI of the original image.</param>
        /// <param name="targetPlaceholderEmuSize">Target EMU dimensions of the placeholder.</param>
        /// <returns>Tuple containing scaled width and height in EMUs.</returns>
        private Tuple<long, long> CalculateScaledEmuDimensions(Size originalImagePixelSize, SizeF originalImageDpi, Tuple<long, long> targetPlaceholderEmuSize)
        {
            if (originalImagePixelSize.Width <= 0 || originalImagePixelSize.Height <= 0 ||
                originalImageDpi.Width <= 0 || originalImageDpi.Height <= 0 ||
                targetPlaceholderEmuSize.Item1 <= 0 || targetPlaceholderEmuSize.Item2 <= 0)
            {
                // Return a small default or throw, avoid division by zero / zero size
                // For now, if target is invalid, use image's natural size up to a sensible max
                // Or, if image itself is invalid, return small size
                if (targetPlaceholderEmuSize.Item1 <= 0 || targetPlaceholderEmuSize.Item2 <= 0)
                   return Tuple.Create( (long)((double)originalImagePixelSize.Width / originalImageDpi.Width * 914400.0),
                                        (long)((double)originalImagePixelSize.Height / originalImageDpi.Height * 914400.0) ); // Fallback to natural size
                // else if original image params are bad, fallback to small
                return Tuple.Create(100000L, 100000L); // fallback small size
            }

            // Calculate the "natural" EMU size of the originalImage
            double naturalWidthEmu = ((double)originalImagePixelSize.Width / originalImageDpi.Width) * 914400.0;
            double naturalHeightEmu = ((double)originalImagePixelSize.Height / originalImageDpi.Height) * 914400.0;

            if (naturalWidthEmu <= 0 || naturalHeightEmu <= 0)
            {
                 return Tuple.Create(targetPlaceholderEmuSize.Item1, targetPlaceholderEmuSize.Item2); // Fallback to target if natural is zero
            }

            // Calculate scaling ratios to fit within the target placeholder
            double widthRatio = (double)targetPlaceholderEmuSize.Item1 / naturalWidthEmu;
            double heightRatio = (double)targetPlaceholderEmuSize.Item2 / naturalHeightEmu;

            // Use the smaller ratio to ensure the image fits proportionally
            double scalingFactor = Math.Min(widthRatio, heightRatio);

            long finalWidthEmu = (long)(naturalWidthEmu * scalingFactor);
            long finalHeightEmu = (long)(naturalHeightEmu * scalingFactor);
            
            // Ensure minimum size if scaling results in too small image (e.g. 1x1 EMU)
            if (finalWidthEmu < 12700) finalWidthEmu = 12700; // 1 point
            if (finalHeightEmu < 12700) finalHeightEmu = 12700;


            return Tuple.Create(finalWidthEmu, finalHeightEmu);
        }

        /// <summary>
        /// 根据文件扩展名获取NPOI的图片类型
        /// </summary>
        private int GetPictureTypeForNPOI(string extension) // Renamed for clarity
        {
            switch (extension.ToLower())
            {
                case ".emf": return (int)PictureType.EMF;
                case ".wmf": return (int)PictureType.WMF;
                case ".pict": return (int)PictureType.PICT;
                case ".jpeg":
                case ".jpg": return (int)PictureType.JPEG;
                case ".png": return (int)PictureType.PNG;
                case ".dib": return (int)PictureType.DIB;
                case ".gif": // NPOI XWPF typically converts GIF to PNG for insertion
                             // Or use PNG as a common well-supported type
                    return (int)PictureType.PNG; 
                case ".tiff": return (int)PictureType.TIFF;
                case ".eps": return (int)PictureType.EPS;
                case ".bmp": return (int)PictureType.BMP;
                case ".wpg": return (int)PictureType.WPG;
                default:
                    // Fallback to PNG if extension is unknown or unsupported for direct embedding
                    return (int)PictureType.PNG;
            }
        }

        /// <summary>
        /// 生成测试数据
        /// </summary>
        /// <returns>是否成功</returns>
        public async Task<bool> GenerateTestAsync()
        {
            try
            {
                // 确保测试目录存在
                string outputDir = Path.Combine(
                    AppDomain.CurrentDomain.BaseDirectory,
                    "报告生成",
                    "默认用户",
                    "报告分块",
                    "13_房产分层测绘图");
                
                Directory.CreateDirectory(outputDir);

                // 输出路径
                string outputPath = Path.Combine(outputDir, "房产分层测绘图.docx");

                // 填充模板
                return await FillFloorPlanTemplateAsync(outputPath);
            }
            catch (Exception ex)
            {
                HandleException(ex);
                return false;
            }
        }
    }
} 