using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.IO;
using Newtonsoft.Json;
using RESClient.MVP.Base;

namespace RESClient.MVP.Models
{
    /// <summary>
    /// 房产面积汇总表参数数据模型
    /// </summary>
    public class EstateAreaSummaryParametersModel : BaseModel
    {
        /// <summary>
        /// 房产面积汇总表参数数据结构
        /// </summary>
        public class EstateAreaSummaryParameters
        {
            [DisplayName("备注")]
            [Description("房产面积汇总表的备注信息")]
            public string Remarks { get; set; } = "";

            /// <summary>
            /// 验证参数完整性
            /// </summary>
            /// <returns>验证结果</returns>
            public (bool IsValid, List<string> Errors) Validate()
            {
                var errors = new List<string>();

                // 允许自由文本输入，不进行验证
                // 备注字段可以为空

                return (errors.Count == 0, errors);
            }

            /// <summary>
            /// 获取变量映射字典
            /// </summary>
            /// <returns>变量映射</returns>
            public Dictionary<string, string> GetVariableMapping()
            {
                return new Dictionary<string, string>
                {
                    { "${备注}", string.IsNullOrWhiteSpace(Remarks) ? "\\" : Remarks }
                };
            }
        }

        private readonly string _configFilePath;
        private EstateAreaSummaryParameters _currentParameters;

        public EstateAreaSummaryParametersModel()
        {
            // 配置文件保存在应用程序数据目录
            string appDataPath = Path.Combine(
                Environment.GetFolderPath(Environment.SpecialFolder.LocalApplicationData),
                "RESClient");
            
            if (!Directory.Exists(appDataPath))
            {
                Directory.CreateDirectory(appDataPath);
            }

            _configFilePath = Path.Combine(appDataPath, "estate_area_summary_parameters.json");
            _currentParameters = LoadParameters();
        }

        /// <summary>
        /// 获取当前参数
        /// </summary>
        /// <returns>当前参数</returns>
        public EstateAreaSummaryParameters GetCurrentParameters()
        {
            return _currentParameters ?? CreateDefaultParameters();
        }

        /// <summary>
        /// 保存参数
        /// </summary>
        /// <param name="parameters">要保存的参数</param>
        /// <returns>是否保存成功</returns>
        public bool SaveParameters(EstateAreaSummaryParameters parameters)
        {
            try
            {
                if (parameters == null)
                {
                    return false;
                }

                // 验证参数
                var validationResult = parameters.Validate();
                if (!validationResult.IsValid)
                {
                    return false;
                }

                // 序列化为JSON
                string json = JsonConvert.SerializeObject(parameters, Formatting.Indented);

                // 写入文件
                File.WriteAllText(_configFilePath, json, System.Text.Encoding.UTF8);

                // 更新当前参数
                _currentParameters = parameters;

                return true;
            }
            catch (Exception ex)
            {
                HandleException(ex);
                return false;
            }
        }

        /// <summary>
        /// 加载参数
        /// </summary>
        /// <returns>加载的参数</returns>
        private EstateAreaSummaryParameters LoadParameters()
        {
            try
            {
                if (!File.Exists(_configFilePath))
                {
                    return CreateDefaultParameters();
                }

                string json = File.ReadAllText(_configFilePath, System.Text.Encoding.UTF8);
                var parameters = JsonConvert.DeserializeObject<EstateAreaSummaryParameters>(json);

                return parameters ?? CreateDefaultParameters();
            }
            catch (Exception ex)
            {
                HandleException(ex);
                return CreateDefaultParameters();
            }
        }

        /// <summary>
        /// 创建默认参数
        /// </summary>
        /// <returns>默认参数</returns>
        private EstateAreaSummaryParameters CreateDefaultParameters()
        {
            return new EstateAreaSummaryParameters
            {
                Remarks = ""
            };
        }
    }
}
