using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;

namespace RESClient.Services
{
    /// <summary>
    /// 模块编号服务 - 为生成的报告模块文件添加编号前缀
    /// </summary>
    public class ModuleNumberingService
    {
        /// <summary>
        /// 模块编号映射表 - 模块名称到编号前缀的映射
        /// </summary>
        private static readonly Dictionary<string, string> MODULE_NUMBER_MAPPING = new Dictionary<string, string>
        {
            { "封面", "01_" },
            { "目录", "02_" },
            { "作业声明", "03_" },
            { "作业、质量检查与验收", "04_" },
            { "项目基本信息", "05_" },
            { "楼栋基本信息", "06_" },
            { "经主管部门批准的相关证照", "07_" },
            { "地下室人防区域说明", "08_" },
            { "项目丘地及测绘房屋分布图", "09_" },
            { "建筑物现状影像图", "10_" },
            { "房产面积汇总表", "11_" },
            { "房产分户面积统计表", "12_" },
            { "房产分层测绘图", "13_" }
        };

        /// <summary>
        /// 为指定目录中的模块文件添加编号前缀
        /// </summary>
        /// <param name="outputDirectory">输出目录路径</param>
        /// <param name="progressCallback">进度回调函数</param>
        /// <returns>操作是否成功</returns>
        public bool AddNumberingPrefixes(string outputDirectory, Action<int, string> progressCallback = null)
        {
            try
            {
                progressCallback?.Invoke(0, "[INFO]开始为模块文件添加编号前缀...");

                if (!Directory.Exists(outputDirectory))
                {
                    progressCallback?.Invoke(0, "[ERROR]输出目录不存在");
                    return false;
                }

                // 查找所有需要编号的模块文件
                var moduleFiles = FindModuleFiles(outputDirectory);
                if (moduleFiles.Count == 0)
                {
                    progressCallback?.Invoke(100, "[INFO]未找到需要编号的模块文件");
                    return true;
                }

                progressCallback?.Invoke(10, $"[INFO]找到 {moduleFiles.Count} 个模块文件需要编号");

                int processedFiles = 0;
                int totalFiles = moduleFiles.Count;
                bool overallSuccess = true;

                foreach (var moduleFile in moduleFiles)
                {
                    try
                    {
                        string moduleName = moduleFile.Key;
                        string currentFilePath = moduleFile.Value;
                        
                        if (MODULE_NUMBER_MAPPING.TryGetValue(moduleName, out string numberPrefix))
                        {
                            string fileName = Path.GetFileName(currentFilePath);
                            string directory = Path.GetDirectoryName(currentFilePath);
                            string newFileName = numberPrefix + fileName;
                            string newFilePath = Path.Combine(directory, newFileName);

                            // 检查目标文件是否已存在
                            if (File.Exists(newFilePath))
                            {
                                progressCallback?.Invoke(
                                    (int)((float)(processedFiles + 1) / totalFiles * 90),
                                    $"[WARNING]跳过 {moduleName}：目标文件已存在");
                            }
                            else
                            {
                                // 重命名文件
                                File.Move(currentFilePath, newFilePath);
                                progressCallback?.Invoke(
                                    (int)((float)(processedFiles + 1) / totalFiles * 90),
                                    $"[SUCCESS]已重命名：{fileName} -> {newFileName}");
                            }
                        }
                        else
                        {
                            progressCallback?.Invoke(
                                (int)((float)(processedFiles + 1) / totalFiles * 90),
                                $"[WARNING]跳过 {moduleName}：未找到对应的编号前缀");
                        }
                    }
                    catch (Exception ex)
                    {
                        overallSuccess = false;
                        progressCallback?.Invoke(
                            (int)((float)(processedFiles + 1) / totalFiles * 90),
                            $"[ERROR]重命名 {moduleFile.Key} 失败：{ex.Message}");
                    }

                    processedFiles++;
                }

                if (overallSuccess)
                {
                    progressCallback?.Invoke(100, $"[SUCCESS]模块文件编号完成，共处理 {processedFiles} 个文件");
                }
                else
                {
                    progressCallback?.Invoke(100, $"[WARNING]模块文件编号完成，但部分文件处理失败");
                }

                return overallSuccess;
            }
            catch (Exception ex)
            {
                progressCallback?.Invoke(100, $"[ERROR]模块文件编号过程中发生异常：{ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 查找输出目录中的模块文件
        /// </summary>
        /// <param name="outputDirectory">输出目录</param>
        /// <returns>模块名称到文件路径的映射</returns>
        private Dictionary<string, string> FindModuleFiles(string outputDirectory)
        {
            var moduleFiles = new Dictionary<string, string>();

            foreach (var moduleName in MODULE_NUMBER_MAPPING.Keys)
            {
                string moduleFilePath = Path.Combine(outputDirectory, $"{moduleName}.docx");
                if (File.Exists(moduleFilePath))
                {
                    moduleFiles[moduleName] = moduleFilePath;
                }
            }

            return moduleFiles;
        }

        /// <summary>
        /// 获取模块的编号前缀
        /// </summary>
        /// <param name="moduleName">模块名称</param>
        /// <returns>编号前缀，如果模块不存在则返回null</returns>
        public string GetModuleNumberPrefix(string moduleName)
        {
            return MODULE_NUMBER_MAPPING.TryGetValue(moduleName, out string prefix) ? prefix : null;
        }

        /// <summary>
        /// 获取所有支持的模块名称
        /// </summary>
        /// <returns>支持的模块名称列表</returns>
        public List<string> GetSupportedModules()
        {
            return MODULE_NUMBER_MAPPING.Keys.ToList();
        }

        /// <summary>
        /// 检查模块是否支持编号
        /// </summary>
        /// <param name="moduleName">模块名称</param>
        /// <returns>是否支持编号</returns>
        public bool IsModuleSupported(string moduleName)
        {
            return MODULE_NUMBER_MAPPING.ContainsKey(moduleName);
        }
    }
}
