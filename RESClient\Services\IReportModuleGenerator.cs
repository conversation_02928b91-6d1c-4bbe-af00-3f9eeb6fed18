using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace RESClient.Services
{
    /// <summary>
    /// 报告模块生成器接口
    /// </summary>
    public interface IReportModuleGenerator
    {
        /// <summary>
        /// 模块名称
        /// </summary>
        string ModuleName { get; }
        
        /// <summary>
        /// 生成报告模块
        /// </summary>
        /// <param name="parameters">生成参数</param>
        /// <param name="progressCallback">进度回调</param>
        /// <returns>生成结果</returns>
        Task<bool> GenerateAsync(Dictionary<string, object> parameters, Action<int, string> progressCallback);
        
        /// <summary>
        /// 是否可用
        /// </summary>
        /// <param name="parameters">检查参数</param>
        /// <returns>是否可用</returns>
        bool IsAvailable(Dictionary<string, object> parameters);
    }
} 