using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Options;
using RESServer.Models;
using RESServer.Services;
using System.Security.Claims;

namespace RESServer.Controllers
{
    /// <summary>
    /// 认证控制器
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    public class AuthController : ControllerBase
    {
        private readonly IAuthService _authService;
        private readonly AuthConfig _authConfig;
        private readonly ILogger<AuthController> _logger;

        public AuthController(
            IAuthService authService,
            IOptions<AuthConfig> authConfig,
            ILogger<AuthController> logger)
        {
            _authService = authService;
            _authConfig = authConfig.Value;
            _logger = logger;
        }

        /// <summary>
        /// 获取服务器状态和认证配置
        /// </summary>
        /// <returns>服务器状态信息</returns>
        [HttpGet("status")]
        [AllowAnonymous]
        public IActionResult GetServerStatus()
        {
            return Ok(new ServerStatusResponse
            {
                Success = true,
                Message = "RESServer 运行正常",
                AuthenticationEnabled = _authConfig.EnableAuthentication,
                ServerTime = DateTime.UtcNow,
                Version = "1.0.0"
            });
        }

        /// <summary>
        /// 用户登录
        /// </summary>
        /// <param name="request">登录请求</param>
        /// <returns>登录结果</returns>
        [HttpPost("login")]
        [AllowAnonymous]
        public async Task<IActionResult> Login([FromBody] LoginRequest request)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(new LoginResponse
                {
                    Success = false,
                    Message = "请求参数无效"
                });
            }

            // 如果认证功能未启用，返回错误
            if (!_authConfig.EnableAuthentication)
            {
                return BadRequest(new LoginResponse
                {
                    Success = false,
                    Message = "服务器未启用认证功能"
                });
            }

            var result = await _authService.LoginAsync(request);
            
            if (result.Success)
            {
                return Ok(result);
            }
            else
            {
                return Unauthorized(result);
            }
        }

        /// <summary>
        /// 获取当前用户信息
        /// </summary>
        /// <returns>当前用户信息</returns>
        [HttpGet("me")]
        [Authorize]
        public async Task<IActionResult> GetCurrentUser()
        {
            var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier);
            if (userIdClaim == null || !int.TryParse(userIdClaim.Value, out int userId))
            {
                return Unauthorized(new ApiResponse
                {
                    Success = false,
                    Message = "无效的用户身份"
                });
            }

            var result = await _authService.GetUserByIdAsync(userId);
            
            if (result.Success)
            {
                return Ok(result);
            }
            else
            {
                return NotFound(result);
            }
        }

        /// <summary>
        /// 修改当前用户密码
        /// </summary>
        /// <param name="request">修改密码请求</param>
        /// <returns>修改结果</returns>
        [HttpPost("change-password")]
        [Authorize]
        public async Task<IActionResult> ChangePassword([FromBody] ChangePasswordRequest request)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(new ApiResponse
                {
                    Success = false,
                    Message = "请求参数无效"
                });
            }

            var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier);
            if (userIdClaim == null || !int.TryParse(userIdClaim.Value, out int userId))
            {
                return Unauthorized(new ApiResponse
                {
                    Success = false,
                    Message = "无效的用户身份"
                });
            }

            var result = await _authService.ChangePasswordAsync(userId, request);
            
            if (result.Success)
            {
                return Ok(result);
            }
            else
            {
                return BadRequest(result);
            }
        }

        /// <summary>
        /// 获取所有用户（仅管理员）
        /// </summary>
        /// <returns>用户列表</returns>
        [HttpGet("users")]
        [Authorize(Roles = "Admin")]
        public async Task<IActionResult> GetUsers()
        {
            var result = await _authService.GetUsersAsync();
            
            if (result.Success)
            {
                return Ok(result);
            }
            else
            {
                return StatusCode(500, result);
            }
        }

        /// <summary>
        /// 创建用户（仅管理员）
        /// </summary>
        /// <param name="request">创建用户请求</param>
        /// <returns>创建结果</returns>
        [HttpPost("users")]
        [Authorize(Roles = "Admin")]
        public async Task<IActionResult> CreateUser([FromBody] CreateUserRequest request)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(new ApiResponse<UserInfo>
                {
                    Success = false,
                    Message = "请求参数无效"
                });
            }

            var result = await _authService.CreateUserAsync(request);
            
            if (result.Success)
            {
                return CreatedAtAction(nameof(GetUserById), new { id = result.Data!.Id }, result);
            }
            else
            {
                return BadRequest(result);
            }
        }

        /// <summary>
        /// 根据ID获取用户（仅管理员）
        /// </summary>
        /// <param name="id">用户ID</param>
        /// <returns>用户信息</returns>
        [HttpGet("users/{id}")]
        [Authorize(Roles = "Admin")]
        public async Task<IActionResult> GetUserById(int id)
        {
            var result = await _authService.GetUserByIdAsync(id);
            
            if (result.Success)
            {
                return Ok(result);
            }
            else
            {
                return NotFound(result);
            }
        }

        /// <summary>
        /// 更新用户信息（仅管理员）
        /// </summary>
        /// <param name="id">用户ID</param>
        /// <param name="request">更新请求</param>
        /// <returns>更新结果</returns>
        [HttpPut("users/{id}")]
        [Authorize(Roles = "Admin")]
        public async Task<IActionResult> UpdateUser(int id, [FromBody] UpdateUserRequest request)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(new ApiResponse<UserInfo>
                {
                    Success = false,
                    Message = "请求参数无效"
                });
            }

            var result = await _authService.UpdateUserAsync(id, request);
            
            if (result.Success)
            {
                return Ok(result);
            }
            else
            {
                return BadRequest(result);
            }
        }

        /// <summary>
        /// 删除用户（仅管理员）
        /// </summary>
        /// <param name="id">用户ID</param>
        /// <returns>删除结果</returns>
        [HttpDelete("users/{id}")]
        [Authorize(Roles = "Admin")]
        public async Task<IActionResult> DeleteUser(int id)
        {
            var result = await _authService.DeleteUserAsync(id);
            
            if (result.Success)
            {
                return Ok(result);
            }
            else
            {
                return BadRequest(result);
            }
        }

        /// <summary>
        /// 验证 Token（用于客户端检查）
        /// </summary>
        /// <returns>验证结果</returns>
        [HttpGet("validate")]
        [Authorize]
        public IActionResult ValidateToken()
        {
            return Ok(new ApiResponse
            {
                Success = true,
                Message = "Token 有效"
            });
        }
    }
}
