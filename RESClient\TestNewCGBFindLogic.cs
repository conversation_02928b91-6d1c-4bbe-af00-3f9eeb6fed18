using System;
using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;
using RESClient.Services.Implementations;

namespace RESClient
{
    /// <summary>
    /// 测试新的CGB文件查找逻辑
    /// </summary>
    public class TestNewCGBFindLogic
    {
        public static async Task Main(string[] args)
        {
            Console.WriteLine("=== 测试新的CGB文件查找逻辑 ===");
            Console.WriteLine();

            await TestBuildingInfoModuleGenerator();
            await TestEstateAreaSummaryModuleGenerator();
            await TestHouseholdAreaStatisticsModuleGenerator();

            Console.WriteLine("\n=== 测试完成 ===");
            Console.WriteLine("按任意键退出...");
            Console.ReadKey();
        }

        /// <summary>
        /// 测试BuildingInfoModuleGenerator的新查找逻辑
        /// </summary>
        private static async Task TestBuildingInfoModuleGenerator()
        {
            Console.WriteLine("1. 测试BuildingInfoModuleGenerator");
            Console.WriteLine("----------------------------------------");

            try
            {
                var generator = new BuildingInfoModuleGenerator();
                
                // 测试数据目录 - 用户需要提供包含测试Excel文件的目录
                string testDataDir = @"C:\TestData"; // 用户需要修改为实际的测试目录
                
                if (!Directory.Exists(testDataDir))
                {
                    Console.WriteLine($"❌ 测试目录不存在: {testDataDir}");
                    Console.WriteLine("请创建测试目录并放入测试Excel文件");
                    return;
                }

                Console.WriteLine($"正在测试目录: {testDataDir}");
                
                var parameters = new Dictionary<string, object>
                {
                    ["DataFolder"] = testDataDir,
                    ["OutputFolder"] = Path.Combine(testDataDir, "Output")
                };

                var progressMessages = new List<string>();
                Action<int, string> progressCallback = (progress, message) =>
                {
                    progressMessages.Add($"[{progress}%] {message}");
                    Console.WriteLine($"  {message}");
                };

                // 测试模块可用性
                bool isAvailable = await generator.IsAvailableAsync(parameters, progressCallback);
                Console.WriteLine($"模块可用性: {(isAvailable ? "✓ 可用" : "❌ 不可用")}");

                if (isAvailable)
                {
                    Console.WriteLine("\n开始生成测试...");
                    bool result = await generator.GenerateAsync(parameters, progressCallback);
                    Console.WriteLine($"生成结果: {(result ? "✓ 成功" : "❌ 失败")}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ 测试BuildingInfoModuleGenerator时出错: {ex.Message}");
            }

            Console.WriteLine();
        }

        /// <summary>
        /// 测试EstateAreaSummaryModuleGenerator的新查找逻辑
        /// </summary>
        private static async Task TestEstateAreaSummaryModuleGenerator()
        {
            Console.WriteLine("2. 测试EstateAreaSummaryModuleGenerator");
            Console.WriteLine("----------------------------------------");

            try
            {
                var generator = new EstateAreaSummaryModuleGenerator();
                
                string testDataDir = @"C:\TestData";
                
                if (!Directory.Exists(testDataDir))
                {
                    Console.WriteLine($"❌ 测试目录不存在: {testDataDir}");
                    return;
                }

                Console.WriteLine($"正在测试目录: {testDataDir}");
                
                var parameters = new Dictionary<string, object>
                {
                    ["DataFolder"] = testDataDir,
                    ["OutputFolder"] = Path.Combine(testDataDir, "Output")
                };

                var progressMessages = new List<string>();
                Action<int, string> progressCallback = (progress, message) =>
                {
                    progressMessages.Add($"[{progress}%] {message}");
                    Console.WriteLine($"  {message}");
                };

                bool isAvailable = await generator.IsAvailableAsync(parameters, progressCallback);
                Console.WriteLine($"模块可用性: {(isAvailable ? "✓ 可用" : "❌ 不可用")}");

                if (isAvailable)
                {
                    Console.WriteLine("\n开始生成测试...");
                    bool result = await generator.GenerateAsync(parameters, progressCallback);
                    Console.WriteLine($"生成结果: {(result ? "✓ 成功" : "❌ 失败")}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ 测试EstateAreaSummaryModuleGenerator时出错: {ex.Message}");
            }

            Console.WriteLine();
        }

        /// <summary>
        /// 测试HouseholdAreaStatisticsModuleGenerator的新查找逻辑
        /// </summary>
        private static async Task TestHouseholdAreaStatisticsModuleGenerator()
        {
            Console.WriteLine("3. 测试HouseholdAreaStatisticsModuleGenerator");
            Console.WriteLine("----------------------------------------");

            try
            {
                var generator = new HouseholdAreaStatisticsModuleGenerator();
                
                string testDataDir = @"C:\TestData";
                
                if (!Directory.Exists(testDataDir))
                {
                    Console.WriteLine($"❌ 测试目录不存在: {testDataDir}");
                    return;
                }

                Console.WriteLine($"正在测试目录: {testDataDir}");
                
                var parameters = new Dictionary<string, object>
                {
                    ["DataFolder"] = testDataDir,
                    ["OutputFolder"] = Path.Combine(testDataDir, "Output")
                };

                var progressMessages = new List<string>();
                Action<int, string> progressCallback = (progress, message) =>
                {
                    progressMessages.Add($"[{progress}%] {message}");
                    Console.WriteLine($"  {message}");
                };

                bool isAvailable = await generator.IsAvailableAsync(parameters, progressCallback);
                Console.WriteLine($"模块可用性: {(isAvailable ? "✓ 可用" : "❌ 不可用")}");

                if (isAvailable)
                {
                    Console.WriteLine("\n开始生成测试...");
                    bool result = await generator.GenerateAsync(parameters, progressCallback);
                    Console.WriteLine($"生成结果: {(result ? "✓ 成功" : "❌ 失败")}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ 测试HouseholdAreaStatisticsModuleGenerator时出错: {ex.Message}");
            }

            Console.WriteLine();
        }
    }
}
