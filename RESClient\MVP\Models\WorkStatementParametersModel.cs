using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.IO;
using Newtonsoft.Json;
using RESClient.MVP.Base;
using RESClient.Utils;

namespace RESClient.MVP.Models
{
    /// <summary>
    /// 作业声明参数数据模型
    /// </summary>
    public class WorkStatementParametersModel : BaseModel
    {
        /// <summary>
        /// 作业声明参数数据结构
        /// </summary>
        public class WorkStatementParameters
        {
            [DisplayName("委托方")]
            [Description("委托测绘的单位名称")]
            public string ClientName { get; set; } = "";

            [DisplayName("项目地址")]
            [Description("测绘项目的具体地址")]
            public string ProjectAddress { get; set; } = "";

            [DisplayName("证书等级")]
            [Description("测绘资质证书等级")]
            public string CertificateLevel { get; set; } = "甲测资字";

            [DisplayName("取得日期")]
            [Description("证书取得日期")]
            public DateTime CertificateObtainDate { get; set; } = new DateTime(2022, 7, 13);

            [DisplayName("证书编号")]
            [Description("测绘资质证书编号")]
            public string CertificateNumber { get; set; } = "51100923";

            [DisplayName("有效期至")]
            [Description("证书有效期截止日期")]
            public DateTime CertificateExpiryDate { get; set; } = new DateTime(2027, 7, 12);

            [DisplayName("专业范围及限额")]
            [Description("测绘专业范围和限额")]
            public string ProfessionalScope { get; set; } = "工程测量、界线与不动产测绘***；不限额";

            [DisplayName("法人代表")]
            [Description("测绘单位法人代表姓名")]
            public string LegalRepresentative { get; set; } = "黄荣";

            [DisplayName("技术负责人")]
            [Description("项目技术负责人姓名")]
            public string TechnicalManager { get; set; } = "包锦";

            [DisplayName("地址")]
            [Description("测绘单位地址")]
            public string CompanyAddress { get; set; } = "成都市高新区天府大道中段688号1栋9、11、12层";

            [DisplayName("电话")]
            [Description("测绘单位联系电话")]
            public string Phone { get; set; } = "028-86916175";

            [DisplayName("传真")]
            [Description("测绘单位传真号码")]
            public string Fax { get; set; } = "028-86916175";

            [DisplayName("测绘机构（盖章）")]
            [Description("测绘机构名称")]
            public string SurveyInstitution { get; set; } = "四川省川建勘察设计院有限公司";

            [DisplayName("报告日期")]
            [Description("报告生成日期")]
            public DateTime ReportDate { get; set; } = DateTime.Now;

            /// <summary>
            /// 获取所有变量的映射字典
            /// </summary>
            /// <returns>变量名到值的映射</returns>
            public Dictionary<string, string> GetVariableMapping()
            {
                return new Dictionary<string, string>
                {
                    { "${委托方}", ClientName },
                    { "${项目地址}", ProjectAddress },
                    { "${证书等级}", CertificateLevel },
                    { "${取得日期}", CertificateObtainDate.ToString("yyyy年M月d日") },
                    { "${证书编号}", CertificateNumber },
                    { "${有效期至}", CertificateExpiryDate.ToString("yyyy年M月d日") },
                    { "${专业范围及限额}", ProfessionalScope },
                    { "${法人代表}", LegalRepresentative },
                    { "${技术负责人}", TechnicalManager },
                    { "${地址}", CompanyAddress },
                    { "${电话}", Phone },
                    { "${传真}", Fax },
                    { "${日期}", ChineseDateFormatter.ToChineseUppercase(ReportDate) }
                };
            }

            /// <summary>
            /// 验证参数完整性
            /// </summary>
            /// <returns>验证结果和错误信息</returns>
            public (bool IsValid, List<string> Errors) Validate()
            {
                var errors = new List<string>();

                if (string.IsNullOrWhiteSpace(ClientName))
                    errors.Add("委托方不能为空");

                if (string.IsNullOrWhiteSpace(ProjectAddress))
                    errors.Add("项目地址不能为空");

                if (string.IsNullOrWhiteSpace(CertificateLevel))
                    errors.Add("证书等级不能为空");

                if (string.IsNullOrWhiteSpace(CertificateNumber))
                    errors.Add("证书编号不能为空");

                if (string.IsNullOrWhiteSpace(ProfessionalScope))
                    errors.Add("专业范围及限额不能为空");

                if (string.IsNullOrWhiteSpace(LegalRepresentative))
                    errors.Add("法人代表不能为空");

                if (string.IsNullOrWhiteSpace(TechnicalManager))
                    errors.Add("技术负责人不能为空");

                if (string.IsNullOrWhiteSpace(CompanyAddress))
                    errors.Add("地址不能为空");

                if (string.IsNullOrWhiteSpace(Phone))
                    errors.Add("电话不能为空");

                if (string.IsNullOrWhiteSpace(SurveyInstitution))
                    errors.Add("测绘机构不能为空");

                if (CertificateExpiryDate <= CertificateObtainDate)
                    errors.Add("证书有效期必须晚于取得日期");

                return (errors.Count == 0, errors);
            }
        }

        private readonly string _configFilePath;
        private WorkStatementParameters _currentParameters;

        public WorkStatementParametersModel()
        {
            // 配置文件保存在应用程序数据目录
            string appDataPath = Path.Combine(
                Environment.GetFolderPath(Environment.SpecialFolder.LocalApplicationData),
                "RESClient");
            
            if (!Directory.Exists(appDataPath))
            {
                Directory.CreateDirectory(appDataPath);
            }

            _configFilePath = Path.Combine(appDataPath, "work_statement_parameters.json");
            _currentParameters = LoadParameters();
        }

        /// <summary>
        /// 获取当前参数
        /// </summary>
        /// <returns>当前作业声明参数</returns>
        public WorkStatementParameters GetCurrentParameters()
        {
            return _currentParameters ?? new WorkStatementParameters();
        }

        /// <summary>
        /// 保存参数
        /// </summary>
        /// <param name="parameters">要保存的参数</param>
        /// <returns>是否保存成功</returns>
        public bool SaveParameters(WorkStatementParameters parameters)
        {
            try
            {
                string json = JsonConvert.SerializeObject(parameters, Formatting.Indented);
                File.WriteAllText(_configFilePath, json, System.Text.Encoding.UTF8);
                _currentParameters = parameters;
                return true;
            }
            catch (Exception ex)
            {
                HandleException(ex);
                return false;
            }
        }

        /// <summary>
        /// 检查配置文件是否存在
        /// </summary>
        /// <returns>配置文件是否存在</returns>
        public bool ConfigFileExists()
        {
            return File.Exists(_configFilePath);
        }

        /// <summary>
        /// 加载参数
        /// </summary>
        /// <returns>加载的参数，如果失败则返回默认参数</returns>
        private WorkStatementParameters LoadParameters()
        {
            try
            {
                if (!File.Exists(_configFilePath))
                {
                    return CreateDefaultParameters();
                }

                string json = File.ReadAllText(_configFilePath, System.Text.Encoding.UTF8);
                var parameters = JsonConvert.DeserializeObject<WorkStatementParameters>(json);
                
                return parameters ?? CreateDefaultParameters();
            }
            catch (Exception ex)
            {
                HandleException(ex);
                return CreateDefaultParameters();
            }
        }

        /// <summary>
        /// 创建默认参数
        /// </summary>
        /// <returns>默认参数</returns>
        private WorkStatementParameters CreateDefaultParameters()
        {
            return new WorkStatementParameters
            {
                SurveyInstitution = "四川省川建勘察设计院有限公司",
                CertificateNumber = "甲测资字：51100923",
                CertificateLevel = "甲测资字",
                ProfessionalScope = "工程测量、界线与不动产测绘***；不限额",
                LegalRepresentative = "黄荣",
                TechnicalManager = "包锦",
                CompanyAddress = "成都市高新区天府大道中段688号1栋9、11、12层",
                Phone = "028-86916175",
                Fax = "028-86916175",
                ReportDate = DateTime.Now,
                CertificateObtainDate = new DateTime(2022, 7, 13),
                CertificateExpiryDate = new DateTime(2027, 7, 12)
            };
        }
    }
}
