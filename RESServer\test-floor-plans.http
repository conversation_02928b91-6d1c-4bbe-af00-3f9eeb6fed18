### Test the new Generate Floor Plans endpoint

# 1. Generate Floor Plans from DWG file
POST https://localhost:7xxx/api/DrawingProcessing/generate-floor-plans
Content-Type: multipart/form-data; boundary=----WebKitFormBoundary7MA4YWxkTrZu0gW

------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="file"; filename="test.dwg"
Content-Type: application/octet-stream

< ./path/to/your/test.dwg
------WebKitFormBoundary7MA4YWxkTrZu0gW--

###

# 2. Check status of floor plan generation (replace {sessionId} with actual session ID from step 1)
GET https://localhost:7xxx/api/DrawingProcessing/status/{{sessionId}}

###

# 3. Get list of generated files (replace {sessionId} with actual session ID from step 1)
GET https://localhost:7xxx/api/DrawingProcessing/files/{{sessionId}}

###

# 4. Download the generated floor plans (replace {sessionId} with actual session ID from step 1)
GET https://localhost:7xxx/api/DrawingProcessing/download/{{sessionId}}

###

# For comparison - the original process endpoint
POST https://localhost:7xxx/api/DrawingProcessing/process
Content-Type: multipart/form-data; boundary=----WebKitFormBoundary7MA4YWxkTrZu0gW

------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="file"; filename="test.dwg"
Content-Type: application/octet-stream

< ./path/to/your/test.dwg
------WebKitFormBoundary7MA4YWxkTrZu0gW--

###
