using System;
using System.IO;
using System.Net;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;

namespace RESCADServerPlugin.Controllers
{
    public class UploadController
    {
        public async Task HandleUploadDwg(HttpListenerContext context)
        {
            if (context.Request.HttpMethod != "POST")
            {
                SendErrorResponse(context, 405, "Method Not Allowed");
                return;
            }
            
            try
            {
                string boundary = context.Request.ContentType.Split('=')[1].Trim();
                string tempDirectory = Path.Combine(Path.GetDirectoryName(System.Reflection.Assembly.GetExecutingAssembly().Location), "TempUploads");
                
                // 确保目录存在
                if (!Directory.Exists(tempDirectory))
                {
                    Directory.CreateDirectory(tempDirectory);
                }
                
                // 创建临时文件名
                string tempFileName = $"upload_{DateTime.Now:yyyyMMdd_HHmmss}_{Guid.NewGuid():N}.dwg";
                string filePath = Path.Combine(tempDirectory, tempFileName);
                
                // 使用MultipartFormDataParser解析上传数据
                using (FileStream fileStream = new FileStream(filePath, FileMode.Create))
                {
                    // 获取请求流
                    Stream requestStream = context.Request.InputStream;
                    
                    // 读取二进制数据
                    byte[] buffer = new byte[8192];
                    int bytesRead;
                    bool fileFound = false;
                    bool headerFound = false;
                    
                    // 寻找文件部分的开始
                    while ((bytesRead = requestStream.Read(buffer, 0, buffer.Length)) > 0)
                    {
                        string content = Encoding.UTF8.GetString(buffer, 0, bytesRead);
                        
                        // 检查是否包含文件头信息
                        if (!fileFound && content.Contains("Content-Type: application/octet-stream"))
                        {
                            headerFound = true;
                        }
                        
                        // 如果找到文件头，检查数据部分的开始
                        if (headerFound && content.Contains("\r\n\r\n"))
                        {
                            fileFound = true;
                            int dataStart = content.IndexOf("\r\n\r\n") + 4;
                            
                            // 写入文件数据
                            fileStream.Write(buffer, dataStart, bytesRead - dataStart);
                            break;
                        }
                    }
                    
                    // 继续写入剩余数据
                    if (fileFound)
                    {
                        while ((bytesRead = requestStream.Read(buffer, 0, buffer.Length)) > 0)
                        {
                            // 检查是否到达边界
                            string content = Encoding.UTF8.GetString(buffer, 0, Math.Min(bytesRead, 256));
                            if (content.Contains($"--{boundary}--") || content.Contains($"--{boundary}"))
                            {
                                // 找到边界，只写入边界之前的数据
                                int boundaryIndex = content.IndexOf("--" + boundary);
                                if (boundaryIndex > 0)
                                {
                                    fileStream.Write(buffer, 0, boundaryIndex - 2); // -2 to exclude CRLF
                                    break;
                                }
                            }
                            
                            // 写入全部缓冲区数据
                            fileStream.Write(buffer, 0, bytesRead);
                        }
                    }
                }
                
                // 发送响应
                var response = new
                {
                    Success = true,
                    path = filePath,
                    FileName = Path.GetFileName(filePath)
                };
                
                string json = JsonSerializer.Serialize(response);
                byte[] responseBuffer = Encoding.UTF8.GetBytes(json);
                
                context.Response.StatusCode = 200;
                context.Response.ContentType = "application/json";
                context.Response.ContentLength64 = responseBuffer.Length;
                context.Response.OutputStream.Write(responseBuffer, 0, responseBuffer.Length);
                context.Response.OutputStream.Close();
            }
            catch (Exception ex)
            {
                SendErrorResponse(context, 500, $"Upload failed: {ex.Message}");
            }
        }
        
        private void SendErrorResponse(HttpListenerContext context, int statusCode, string message)
        {
            var errorResponse = new
            {
                Error = true,
                Message = message
            };
            
            string json = JsonSerializer.Serialize(errorResponse);
            byte[] buffer = Encoding.UTF8.GetBytes(json);
            
            context.Response.StatusCode = statusCode;
            context.Response.ContentType = "application/json";
            context.Response.ContentLength64 = buffer.Length;
            context.Response.OutputStream.Write(buffer, 0, buffer.Length);
            context.Response.OutputStream.Close();
        }
    }
} 