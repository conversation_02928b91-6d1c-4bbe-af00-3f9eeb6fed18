using RESCADServerPlugin.Services;
using System;
using System.IO;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using System.Net;

namespace RESCADServerPlugin.Controllers
{
    public class FrameIsolationController
    {
        private readonly FrameIsolationService _frameIsolationService;
        
        public FrameIsolationController()
        {
            // Use the singleton instance
            _frameIsolationService = FrameIsolationService.Instance;
            
            // Start cleaning up old temporary files
            Task.Run(async () =>
            {
                while (true)
                {
                    _frameIsolationService.CleanupTempFiles(TimeSpan.FromHours(24));
                    await Task.Delay(TimeSpan.FromHours(1));
                }
            });
        }
        
        /// <summary>
        /// Handles the HTTP request to process a DWG file, isolate the print frame and save it
        /// </summary>
        public async Task HandleProcessDwgRequest(HttpListenerContext context)
        {
            try
            {
                // Save the uploaded file
                string uploadedFilePath = await SaveUploadedFile(context.Request);
                
                if (string.IsNullOrEmpty(uploadedFilePath) || !File.Exists(uploadedFilePath))
                {
                    SendErrorResponse(context.Response, 400, "Failed to save uploaded file");
                    return;
                }
                
                // Process the file
                string outputFilePath = await _frameIsolationService.ProcessDwgFile(uploadedFilePath);
                
                if (string.IsNullOrEmpty(outputFilePath) || !File.Exists(outputFilePath))
                {
                    SendErrorResponse(context.Response, 500, "Failed to process DWG file");
                    return;
                }
                
                // Send the processed file
                string fileName = Path.GetFileName(outputFilePath);
                SendFileResponse(context.Response, outputFilePath, "application/acad", fileName);
            }
            catch (Exception ex)
            {
                SendErrorResponse(context.Response, 500, $"Error processing DWG file: {ex.Message}");
            }
        }
        
        /// <summary>
        /// Handles the HTTP request to get the current queue status
        /// </summary>
        public void HandleGetQueueStatus(HttpListenerContext context)
        {
            try
            {
                int queueLength = _frameIsolationService.GetQueueLength();
                
                var response = new
                {
                    QueueLength = queueLength
                };
                
                SendJsonResponse(context.Response, 200, response);
            }
            catch (Exception ex)
            {
                SendErrorResponse(context.Response, 500, $"Error getting queue status: {ex.Message}");
            }
        }
        
        private void SendJsonResponse(HttpListenerResponse response, int statusCode, object data)
        {
            string json = JsonSerializer.Serialize(data);
            byte[] buffer = Encoding.UTF8.GetBytes(json);
            
            response.ContentType = "application/json";
            response.ContentLength64 = buffer.Length;
            response.StatusCode = statusCode;
            
            response.OutputStream.Write(buffer, 0, buffer.Length);
            response.OutputStream.Close();
        }
        
        private async Task<string> SaveUploadedFile(HttpListenerRequest request)
        {
            if (!request.ContentType.StartsWith("multipart/form-data"))
            {
                return null;
            }
            
            string boundary = GetBoundary(request.ContentType);
            if (string.IsNullOrEmpty(boundary))
            {
                return null;
            }
            
            byte[] boundaryBytes = Encoding.ASCII.GetBytes($"--{boundary}\r\n");
            byte[] terminator = Encoding.ASCII.GetBytes($"--{boundary}--\r\n");
            
            using (MemoryStream memStream = new MemoryStream())
            {
                // Copy request stream to memory
                await request.InputStream.CopyToAsync(memStream);
                byte[] requestData = memStream.ToArray();
                
                // Find the file data
                int fileStartIndex = -1;
                string headersContent = string.Empty;
                
                for (int i = 0; i < requestData.Length - boundaryBytes.Length; i++)
                {
                    if (FindBytePattern(requestData, boundaryBytes, i) == i)
                    {
                        // Find the headers end
                        int headersEndIndex = FindBytePattern(requestData, Encoding.ASCII.GetBytes("\r\n\r\n"), i);
                        if (headersEndIndex > 0)
                        {
                            // Extract headers
                            headersContent = Encoding.UTF8.GetString(requestData, i + boundaryBytes.Length, headersEndIndex - i - boundaryBytes.Length);
                            
                            // Check if this is a file
                            if (headersContent.Contains("filename="))
                            {
                                fileStartIndex = headersEndIndex + 4; // Skip \r\n\r\n
                                break;
                            }
                        }
                        
                        // Move to next boundary
                        i += boundaryBytes.Length - 1;
                    }
                }
                
                if (fileStartIndex == -1)
                {
                    return null;
                }
                
                // Find file end
                int fileEndIndex = -1;
                for (int i = fileStartIndex; i < requestData.Length - boundaryBytes.Length; i++)
                {
                    if (FindBytePattern(requestData, terminator, i) == i || 
                        FindBytePattern(requestData, boundaryBytes, i) == i)
                    {
                        fileEndIndex = i - 2; // Exclude \r\n before boundary
                        break;
                    }
                }
                
                if (fileEndIndex == -1)
                {
                    return null;
                }
                
                // Extract file name from headers
                string fileName = ParseFileName(headersContent);
                
                if (string.IsNullOrEmpty(fileName))
                {
                    fileName = $"upload_{Guid.NewGuid()}.dwg";
                }
                
                // Save file to temporary directory
                string tempDir = Path.Combine(Path.GetTempPath(), "CADServerUploads");
                if (!Directory.Exists(tempDir))
                {
                    Directory.CreateDirectory(tempDir);
                }
                
                string filePath = Path.Combine(tempDir, fileName);
                
                using (FileStream fileStream = new FileStream(filePath, FileMode.Create, FileAccess.Write))
                {
                    fileStream.Write(requestData, fileStartIndex, fileEndIndex - fileStartIndex + 1);
                }
                
                return filePath;
            }
        }
        
        private string GetBoundary(string contentType)
        {
            if (string.IsNullOrEmpty(contentType))
            {
                return null;
            }
            
            int index = contentType.IndexOf("boundary=");
            if (index == -1)
            {
                return null;
            }
            
            return contentType.Substring(index + 9).Trim('"');
        }
        
        private string ParseFileName(string headers)
        {
            if (string.IsNullOrEmpty(headers))
            {
                return null;
            }
            
            int fileNameIndex = headers.IndexOf("filename=");
            if (fileNameIndex == -1)
            {
                return null;
            }
            
            string fileName = headers.Substring(fileNameIndex + 9);
            int endQuoteIndex = fileName.IndexOf('"');
            if (endQuoteIndex > 0)
            {
                fileName = fileName.Substring(0, endQuoteIndex);
            }
            
            return fileName;
        }
        
        private int FindBytePattern(byte[] source, byte[] pattern, int startIndex = 0)
        {
            for (int i = startIndex; i <= source.Length - pattern.Length; i++)
            {
                bool found = true;
                for (int j = 0; j < pattern.Length; j++)
                {
                    if (source[i + j] != pattern[j])
                    {
                        found = false;
                        break;
                    }
                }
                
                if (found)
                {
                    return i;
                }
            }
            
            return -1;
        }
        
        private void SendErrorResponse(HttpListenerResponse response, int statusCode, string message)
        {
            var errorResponse = new
            {
                Success = false,
                Message = message
            };
            
            SendJsonResponse(response, statusCode, errorResponse);
        }
        
        private void SendFileResponse(HttpListenerResponse response, string filePath, string contentType, string fileName)
        {
            try
            {
                using (FileStream fileStream = new FileStream(filePath, FileMode.Open, FileAccess.Read))
                {
                    response.ContentType = contentType;
                    response.ContentLength64 = fileStream.Length;
                    response.StatusCode = 200;
                    
                    // 确保设置正确的Content-Disposition头
                    response.AddHeader("Content-Disposition", $"attachment; filename=\"{fileName}\"");
                    response.AddHeader("Access-Control-Expose-Headers", "Content-Disposition");
                    
                    // 禁用缓存
                    response.AddHeader("Cache-Control", "no-cache, no-store, must-revalidate");
                    response.AddHeader("Pragma", "no-cache");
                    response.AddHeader("Expires", "0");
                    
                    byte[] buffer = new byte[4096];
                    int bytesRead;
                    while ((bytesRead = fileStream.Read(buffer, 0, buffer.Length)) > 0)
                    {
                        response.OutputStream.Write(buffer, 0, bytesRead);
                    }
                }
                
                response.OutputStream.Close();
            }
            catch (Exception ex)
            {
                // 如果发送文件时出错，尝试发送错误响应
                SendErrorResponse(response, 500, $"发送文件时出错: {ex.Message}");
            }
        }
    }
} 