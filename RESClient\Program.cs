﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;
using System.IO;
using RESClient.MVP.Views;
using RESClient.MVP.Models;
using RESClient.MVP.Presenters;
using RESClient.MVP.Interfaces;

namespace RESClient
{
    internal static class Program
    {
        // 目录名称常量
        public static readonly string TemplateDirectory = "报告模板";

        // 报告模板分块目录名称
        public static readonly string[] TemplateSectionNames = new string[]
        {
            "01_封面", 
            "02_目录", 
            "03_作业声明", 
            "04_作业、质量检查与验收", 
            "05_项目基本信息", 
            "06_楼栋基本信息", 
            "07_经主管部门批准的相关证照", 
            "08_地下室人防区域说明", 
            "09_项目住地及测绘房屋分布图", 
            "10_建筑物现状影像图", 
            "11_房产面积汇总表", 
            "12_房产分户面积统计表", 
            "13_房产分层测绘图"
        };

        /// <summary>
        /// 应用程序的主入口点。
        /// </summary>
        [STAThread]
        static void Main(string[] args)
        {
            // 检查是否是测试模式
            if (args.Length > 0)
            {
                string command = args[0].ToLower();

                if (command == "test7z")
                {
                    // 专门测试7z.dll库
                    Console.WriteLine("测试7z.dll库...");
                    Test7zDll.TestSevenZipLibrary();
                    return;
                }
                else if (command == "test")
                {
                    // 测试7z.dll库
                    Console.WriteLine("测试7z.dll库...");
                    Test7zDll.TestSevenZipLibrary();
                    Console.WriteLine();

                    // 运行基础测试
                    Console.WriteLine("运行封面参数功能测试...");
                    SimpleTest.TestCoverParameters();
                    SimpleTest.TestFileOperations();

                    // 测试中文日期格式化
                    SimpleTest.TestChineseDateFormatter();

                    // 运行完整测试
                    Console.WriteLine("\n运行完整功能测试...");
                    TestCoverParameters.RunFullTest();

                    // 运行参数验证测试
                    Console.WriteLine("\n运行参数验证和修复测试...");
                    TestParameterValidation.RunAllTests();

                    // 运行输出目录修复测试
                    Console.WriteLine("\n运行输出目录修复测试...");
                    TestOutputDirectoryFix.RunAllTests().GetAwaiter().GetResult();

                    // 运行目录模块测试
                    Console.WriteLine("\n运行目录模块测试...");
                    TestTableOfContentsModule.RunAllTests().GetAwaiter().GetResult();

                    // 运行作业声明模块测试
                    Console.WriteLine("\n运行作业声明模块测试...");
                    TestWorkStatementModule.RunAllTests().GetAwaiter().GetResult();

                    // 运行楼栋基本信息模块测试
                    Console.WriteLine("\n运行楼栋基本信息模块测试...");
                    //TestBuildingInfoModule.TestBuildingInfoGeneration();

                    // 运行错误处理改进测试
                    Console.WriteLine("\n运行错误处理改进测试...");
                    TestErrorHandlingImprovements.TestWarningHandling().GetAwaiter().GetResult();

                    // 运行集成报告管理测试
                    Console.WriteLine("\n运行集成报告管理测试...");
                    TestIntegratedReportManagement.RunAllTests().GetAwaiter().GetResult();

                    // 运行文档合并改进测试
                    Console.WriteLine("\n运行文档合并改进测试...");
                    TestDocumentMergingImprovements.RunAllTests().GetAwaiter().GetResult();

                    // 运行文档合并综合测试
                    Console.WriteLine("\n运行文档合并综合测试...");
                    TestDocumentMergingComprehensive.RunAllTests().GetAwaiter().GetResult();

                    Console.WriteLine("\n按任意键退出...");
                    Console.ReadKey();
                    return;
                }
                else if (command == "test-workstatement-form")
                {
                    // 运行作业声明参数输入窗体测试
                    Console.WriteLine("运行作业声明参数输入窗体测试...");
                    TestWorkStatementParameterForm.RunAllTests();
                    Console.WriteLine("\n按任意键退出...");
                    Console.ReadKey();
                    return;
                }
                else if (command == "test-console")
                {
                    // 运行简单的控制台测试
                    TestConsole.TestWorkStatementDefaults();
                    Console.WriteLine("\n按任意键退出...");
                    Console.ReadKey();
                    return;
                }
                else if (command == "test-integrated-reports")
                {
                    // 运行集成报告管理专项测试
                    Console.WriteLine("运行集成报告管理专项测试...");
                    TestIntegratedReportManagement.DemonstrateNewFeatures();
                    TestIntegratedReportManagement.TestModuleSelectionLogic();
                    TestIntegratedReportManagement.RunAllTests().GetAwaiter().GetResult();
                    Console.WriteLine("\n按任意键退出...");
                    Console.ReadKey();
                    return;
                }
                else if (command == "test-error")
                {
                    // 测试错误报告系统
                    RunErrorReportingTests();
                    return;
                }
                else if (command == "test-remember-password")
                {
                    // 测试记住密码功能
                    Console.WriteLine("运行记住密码功能测试...");
                    TestRememberPasswordFeature.RunTest();
                    TestRememberPasswordFeature.TestPasswordEncryption();
                    Console.WriteLine("\n按任意键退出...");
                    Console.ReadKey();
                    return;
                }
                else if (command == "demo-remember-password")
                {
                    // 演示记住密码功能
                    DemoRememberPasswordFeature.RunDemo();
                    DemoRememberPasswordFeature.DemonstrateSecurityFeatures();
                    Console.WriteLine("\n按任意键退出...");
                    Console.ReadKey();
                    return;
                }
                else if (command == "verify-remember-password")
                {
                    // 快速验证记住密码功能
                    QuickVerifyRememberPassword.QuickVerify();
                    QuickVerifyRememberPassword.ShowFeatureStatus();
                    Console.WriteLine("\n按任意键退出...");
                    Console.ReadKey();
                    return;
                }
                else if (command == "test-auth-logs")
                {
                    // 测试认证日志消息修复
                    Console.WriteLine("运行认证日志消息修复测试...");
                    TestAuthenticationLogMessages.RunTest().GetAwaiter().GetResult();
                    TestAuthenticationLogMessages.DemonstrateFixedBehavior();
                    TestAuthenticationLogMessages.TestScenarioLogOutput();
                    Console.WriteLine("\n按任意键退出...");
                    Console.ReadKey();
                    return;
                }
                else if (command == "test-error-quick")
                {
                    // 快速测试错误报告系统
                    QuickErrorTest.RunQuickTest();
                    Console.WriteLine("\n按任意键退出...");
                    Console.ReadKey();
                    return;
                }
                else if (command == "test-loginform-fix")
                {
                    // 测试修复后的登录窗体
                    Console.WriteLine("运行登录窗体修复测试...");
                    TestLoginFormFix.RunAllTests();
                    Console.WriteLine("\n按任意键退出...");
                    Console.ReadKey();
                    return;
                }
                else if (command == "test-loginform-loop")
                {
                    // 测试登录窗体无限循环修复
                    Console.WriteLine("运行登录窗体无限循环修复测试...");
                    TestLoginFormInfiniteLoopFix.RunAllTests().GetAwaiter().GetResult();
                    Console.WriteLine("\n按任意键退出...");
                    Console.ReadKey();
                    return;
                }
                else if (command == "test-login-cancel")
                {
                    // 测试登录取消行为
                    Console.WriteLine("运行登录取消行为测试...");
                    TestLoginCancellationBehavior.RunAllTests().GetAwaiter().GetResult();
                    Console.WriteLine("\n按任意键退出...");
                    Console.ReadKey();
                    return;
                }
                else if (command == "test-auth")
                {
                    // 运行认证系统测试
                    Console.WriteLine("运行认证系统测试...");
                    TestAuthenticationSystem.RunAuthenticationTests().GetAwaiter().GetResult();
                    Console.WriteLine("\n按任意键退出...");
                    Console.ReadKey();
                    return;
                }
                else if (command == "test-connection")
                {
                    // 运行连接服务测试
                    Console.WriteLine("运行连接服务测试...");
                    TestConnectionService.RunConnectionTests().GetAwaiter().GetResult();
                    Console.WriteLine("\n按任意键退出...");
                    Console.ReadKey();
                    return;
                }
                else if (command == "test-archive")
                {
                    // 运行7z压缩包提取测试
                    Console.WriteLine("运行7z压缩包提取测试...");
                    TestArchiveExtraction.RunArchiveExtractionTests().GetAwaiter().GetResult();
                    Console.WriteLine("\n按任意键退出...");
                    Console.ReadKey();
                    return;
                }
            }

            // 检查和创建所需目录
            CheckAndCreateDirectories();

            Application.EnableVisualStyles();
            Application.SetCompatibleTextRenderingDefault(false);

            IMainView view = new MainForm();
            IMainModel model = new MainModel();
            var presenter = new MainPresenter(view, model);

            Application.Run((Form)view);
        }

        /// <summary>
        /// 检查并创建所需的目录结构
        /// </summary>
        private static void CheckAndCreateDirectories()
        {
            try
            {
                string basePath = Application.StartupPath;

                // 创建报告模板目录
                string templatePath = Path.Combine(basePath, TemplateDirectory);
                if (!Directory.Exists(templatePath))
                    Directory.CreateDirectory(templatePath);

                // 创建模板分块目录
                foreach (string section in TemplateSectionNames)
                {
                    string sectionPath = Path.Combine(templatePath, section);
                    if (!Directory.Exists(sectionPath))
                        Directory.CreateDirectory(sectionPath);
                }

                // 注意：报告生成目录已移除，输出目录将由MainModel动态创建
            }
            catch (Exception ex)
            {
                MessageBox.Show($"创建目录结构时发生错误: {ex.Message}", "目录初始化错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 运行错误报告系统测试
        /// </summary>
        private static void RunErrorReportingTests()
        {
            try
            {
                // 运行控制台版本的完整测试
                TestErrorReportingConsole.RunCompleteTest().Wait();

                Console.WriteLine("\n是否要演示错误摘要对话框？(y/n)");
                var key = Console.ReadKey();
                if (key.KeyChar == 'y' || key.KeyChar == 'Y')
                {
                    Console.WriteLine("\n正在显示错误摘要对话框...");
                    Application.EnableVisualStyles();
                    Application.SetCompatibleTextRenderingDefault(false);
                    TestErrorReporting.DemonstrateErrorSummaryDialog();
                }

                Console.WriteLine("\n错误报告系统测试完成！");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"错误报告系统测试失败: {ex.Message}");
                Console.WriteLine($"详细信息: {ex.StackTrace}");
            }
            finally
            {
                Console.WriteLine("\n按任意键退出...");
                Console.ReadKey();
            }
        }
    }
}
