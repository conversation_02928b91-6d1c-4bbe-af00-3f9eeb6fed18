using System;
using System.Drawing;
using System.Windows.Forms;

namespace RESClient.MVP.Views
{
    /// <summary>
    /// 参数输入对话框统一UI样式配置类
    /// 基于"作业声明"模块的标准样式定义
    /// </summary>
    public static class ParameterInputFormStyleConfig
    {
        #region 窗体设置

        /// <summary>
        /// 标准窗体大小
        /// </summary>
        public static readonly Size StandardFormSize = new Size(620, 700);

        /// <summary>
        /// 最小窗体大小
        /// </summary>
        public static readonly Size MinimumFormSize = new Size(520, 600);

        /// <summary>
        /// 最大窗体大小
        /// </summary>
        public static readonly Size MaximumFormSize = new Size(800, 900);

        /// <summary>
        /// 标准窗体边框样式
        /// </summary>
        public static readonly FormBorderStyle StandardBorderStyle = FormBorderStyle.Sizable;

        /// <summary>
        /// 标准窗体启动位置
        /// </summary>
        public static readonly FormStartPosition StandardStartPosition = FormStartPosition.CenterParent;

        #endregion

        #region 容器设置

        /// <summary>
        /// 主容器内边距
        /// </summary>
        public static readonly Padding MainContainerPadding = new Padding(20, 20, 20, 60);

        /// <summary>
        /// 主面板内边距
        /// </summary>
        public static readonly Padding MainPanelPadding = new Padding(10, 15, 10, 15);

        /// <summary>
        /// 标签列宽度
        /// </summary>
        public static readonly int LabelColumnWidth = 140;

        #endregion

        #region 按钮设置

        /// <summary>
        /// 标准按钮大小
        /// </summary>
        public static readonly Size StandardButtonSize = new Size(80, 32);

        /// <summary>
        /// 按钮边距
        /// </summary>
        public static readonly Padding ButtonMargin = new Padding(5, 0, 0, 0);

        /// <summary>
        /// 按钮容器高度
        /// </summary>
        public static readonly int ButtonContainerHeight = 50;

        /// <summary>
        /// 按钮容器内边距
        /// </summary>
        public static readonly Padding ButtonContainerPadding = new Padding(15, 10, 15, 10);

        /// <summary>
        /// 按钮容器背景色
        /// </summary>
        public static readonly Color ButtonContainerBackColor = SystemColors.Control;

        #endregion

        #region 标签设置

        /// <summary>
        /// 标签容器边距
        /// </summary>
        public static readonly Padding LabelContainerMargin = new Padding(5, 10, 5, 10);

        /// <summary>
        /// 标签容器内边距
        /// </summary>
        public static readonly Padding LabelContainerPadding = new Padding(8, 0, 12, 0);

        /// <summary>
        /// 标签文本对齐方式
        /// </summary>
        public static readonly ContentAlignment LabelTextAlign = ContentAlignment.MiddleRight;

        /// <summary>
        /// 标签停靠方式
        /// </summary>
        public static readonly DockStyle LabelDockStyle = DockStyle.Right;

        #endregion

        #region 输入控件设置

        /// <summary>
        /// 输入控件容器边距
        /// </summary>
        public static readonly Padding InputContainerMargin = new Padding(5, 8, 5, 8);

        /// <summary>
        /// 输入控件容器内边距
        /// </summary>
        public static readonly Padding InputContainerPadding = new Padding(0, 5, 8, 5);

        /// <summary>
        /// 标准行高
        /// </summary>
        public static readonly int StandardRowHeight = 40;

        /// <summary>
        /// 多行文本框行高
        /// </summary>
        public static readonly int MultilineRowHeight = 80;

        /// <summary>
        /// 文本框高度
        /// </summary>
        public static readonly int TextBoxHeight = 25;

        /// <summary>
        /// 文本框文本对齐方式
        /// </summary>
        public static readonly HorizontalAlignment TextBoxTextAlign = HorizontalAlignment.Left;

        /// <summary>
        /// 日期选择器固定宽度
        /// </summary>
        public static readonly int DateTimePickerWidth = 200;

        /// <summary>
        /// 日期选择器最小尺寸
        /// </summary>
        public static readonly Size DateTimePickerMinSize = new Size(200, 25);

        #endregion

        #region 字体设置

        /// <summary>
        /// 获取标准字体
        /// </summary>
        /// <param name="baseFont">基础字体</param>
        /// <param name="style">字体样式</param>
        /// <returns>字体对象</returns>
        public static Font GetStandardFont(Font baseFont, FontStyle style = FontStyle.Regular)
        {
            return new Font(baseFont.FontFamily, baseFont.Size, style);
        }

        /// <summary>
        /// 获取确定按钮字体（粗体）
        /// </summary>
        /// <param name="baseFont">基础字体</param>
        /// <returns>粗体字体对象</returns>
        public static Font GetOkButtonFont(Font baseFont)
        {
            return GetStandardFont(baseFont, FontStyle.Bold);
        }

        #endregion

        #region 工厂方法

        /// <summary>
        /// 应用标准窗体设置
        /// </summary>
        /// <param name="form">窗体对象</param>
        /// <param name="title">窗体标题</param>
        /// <param name="customSize">自定义大小（可选）</param>
        public static void ApplyStandardFormSettings(Form form, string title, Size? customSize = null)
        {
            form.Text = title;
            form.Size = customSize ?? StandardFormSize;
            form.MinimumSize = MinimumFormSize;
            form.MaximumSize = MaximumFormSize;
            form.StartPosition = StandardStartPosition;
            form.FormBorderStyle = StandardBorderStyle;
            form.MaximizeBox = true;
            form.MinimizeBox = false;
            form.ShowInTaskbar = false;
        }

        /// <summary>
        /// 创建标准主容器面板
        /// </summary>
        /// <returns>主容器面板</returns>
        public static Panel CreateMainContainer()
        {
            return new Panel
            {
                Dock = DockStyle.Fill,
                Padding = MainContainerPadding
            };
        }

        /// <summary>
        /// 创建居中的主容器面板，用于更好的内容居中布局
        /// </summary>
        /// <returns>居中的主容器面板</returns>
        public static Panel CreateCenteredMainContainer()
        {
            var outerPanel = new Panel
            {
                Dock = DockStyle.Fill,
                Padding = MainContainerPadding
            };

            var centerPanel = new Panel
            {
                AutoSize = true,
                AutoSizeMode = AutoSizeMode.GrowAndShrink,
                Anchor = AnchorStyles.None  // 居中锚定
            };

            outerPanel.Controls.Add(centerPanel);
            return outerPanel;
        }

        /// <summary>
        /// 获取居中容器的内部面板
        /// </summary>
        /// <param name="centeredContainer">居中容器</param>
        /// <returns>内部面板</returns>
        public static Panel GetCenterPanel(Panel centeredContainer)
        {
            return centeredContainer.Controls[0] as Panel;
        }

        /// <summary>
        /// 创建标准主面板
        /// </summary>
        /// <returns>主面板</returns>
        public static TableLayoutPanel CreateMainPanel()
        {
            var panel = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                AutoScroll = true,
                AutoSize = true,
                AutoSizeMode = AutoSizeMode.GrowAndShrink,
                ColumnCount = 2,
                Padding = MainPanelPadding,
                CellBorderStyle = TableLayoutPanelCellBorderStyle.None
            };

            // 设置列样式
            panel.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, LabelColumnWidth));
            panel.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 100));

            return panel;
        }

        /// <summary>
        /// 创建标准按钮容器
        /// </summary>
        /// <returns>按钮容器</returns>
        public static Panel CreateButtonContainer()
        {
            return new Panel
            {
                Height = ButtonContainerHeight,
                Dock = DockStyle.Bottom,
                BackColor = ButtonContainerBackColor,
                Padding = ButtonContainerPadding
            };
        }

        /// <summary>
        /// 创建标准按钮面板
        /// </summary>
        /// <returns>按钮面板</returns>
        public static FlowLayoutPanel CreateButtonPanel()
        {
            return new FlowLayoutPanel
            {
                Dock = DockStyle.Fill,
                FlowDirection = FlowDirection.RightToLeft,
                WrapContents = false,
                AutoSize = true
            };
        }

        /// <summary>
        /// 创建标准按钮
        /// </summary>
        /// <param name="text">按钮文本</param>
        /// <param name="baseFont">基础字体</param>
        /// <param name="isBold">是否粗体</param>
        /// <param name="dialogResult">对话框结果</param>
        /// <returns>按钮对象</returns>
        public static Button CreateStandardButton(string text, Font baseFont, bool isBold = false, DialogResult dialogResult = DialogResult.None)
        {
            return new Button
            {
                Text = text,
                Size = StandardButtonSize,
                DialogResult = dialogResult,
                Margin = ButtonMargin,
                UseVisualStyleBackColor = true,
                Font = isBold ? GetOkButtonFont(baseFont) : GetStandardFont(baseFont)
            };
        }

        /// <summary>
        /// 创建标准标签容器
        /// </summary>
        /// <returns>标签容器</returns>
        public static Panel CreateLabelContainer()
        {
            return new Panel
            {
                Dock = DockStyle.Fill,
                Margin = LabelContainerMargin,
                Padding = LabelContainerPadding
            };
        }

        /// <summary>
        /// 创建标准标签
        /// </summary>
        /// <param name="text">标签文本</param>
        /// <param name="baseFont">基础字体</param>
        /// <returns>标签对象</returns>
        public static Label CreateStandardLabel(string text, Font baseFont)
        {
            return new Label
            {
                Text = text + ":",
                Dock = LabelDockStyle,
                TextAlign = LabelTextAlign,
                AutoSize = true,
                Font = GetStandardFont(baseFont),
                ForeColor = SystemColors.ControlText
            };
        }

        /// <summary>
        /// 创建标准输入控件容器
        /// </summary>
        /// <returns>输入控件容器</returns>
        public static Panel CreateInputContainer()
        {
            return new Panel
            {
                Dock = DockStyle.Fill,
                Margin = InputContainerMargin,
                Padding = InputContainerPadding
            };
        }

        /// <summary>
        /// 创建标准文本框
        /// </summary>
        /// <param name="baseFont">基础字体</param>
        /// <param name="isMultiline">是否多行</param>
        /// <returns>文本框对象</returns>
        public static TextBox CreateStandardTextBox(Font baseFont, bool isMultiline = false)
        {
            var textBox = new TextBox
            {
                Font = GetStandardFont(baseFont),
                Multiline = isMultiline,
                Height = TextBoxHeight,
                TextAlign = TextBoxTextAlign
            };

            if (isMultiline)
            {
                textBox.ScrollBars = ScrollBars.Vertical;
                textBox.Height = MultilineRowHeight - 20; // 调整多行文本框高度
                textBox.Dock = DockStyle.Fill;
            }
            else
            {
                textBox.Dock = DockStyle.Fill;
                textBox.Anchor = AnchorStyles.Left | AnchorStyles.Right | AnchorStyles.Top;
            }

            return textBox;
        }

        /// <summary>
        /// 创建标准日期选择器
        /// </summary>
        /// <param name="baseFont">基础字体</param>
        /// <returns>日期选择器对象</returns>
        public static DateTimePicker CreateStandardDateTimePicker(Font baseFont)
        {
            return new DateTimePicker
            {
                Format = DateTimePickerFormat.Long,
                ShowUpDown = false,
                Font = GetStandardFont(baseFont),
                Height = TextBoxHeight,
                Width = DateTimePickerWidth,
                MinimumSize = DateTimePickerMinSize,
                Dock = DockStyle.None,
                Anchor = AnchorStyles.Left | AnchorStyles.Top
            };
        }

        #endregion
    }
}
