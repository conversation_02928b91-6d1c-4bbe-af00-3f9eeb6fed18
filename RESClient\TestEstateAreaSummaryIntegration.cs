using System;
using System.Collections.Generic;
using RESClient.MVP.Models;
using RESClient.Services;

namespace RESClient
{
    /// <summary>
    /// 房产面积汇总表模块集成测试
    /// </summary>
    public class TestEstateAreaSummaryIntegration
    {
        /// <summary>
        /// 运行所有测试
        /// </summary>
        public static void RunAllTests()
        {
            Console.WriteLine("=== 房产面积汇总表模块集成测试 ===");
            
            TestParametersModel();
            TestModuleSettingsService();
            TestVariableMapping();
            
            Console.WriteLine("\n=== 房产面积汇总表模块集成测试完成 ===");
        }
        
        /// <summary>
        /// 测试参数模型
        /// </summary>
        private static void TestParametersModel()
        {
            Console.WriteLine("\n--- 测试参数模型 ---");
            
            try
            {
                // 创建参数模型
                var parametersModel = new EstateAreaSummaryParametersModel();
                Console.WriteLine("✓ 参数模型创建成功");
                
                // 获取当前参数
                var currentParams = parametersModel.GetCurrentParameters();
                Console.WriteLine($"✓ 获取当前参数成功");
                Console.WriteLine($"  备注: '{currentParams.Remarks}'");
                
                // 测试参数验证 - 现在允许空值，应该总是有效
                var validationResult = currentParams.Validate();
                Console.WriteLine($"✓ 空参数验证结果: {(validationResult.IsValid ? "有效" : "无效")} (预期: 有效)");
                if (!validationResult.IsValid)
                {
                    Console.WriteLine($"  验证错误: {string.Join(", ", validationResult.Errors)}");
                }
                
                // 设置有效参数
                currentParams.Remarks = "测试备注信息：本表格数据仅供参考，具体以实际测量为准。";
                
                // 再次验证
                validationResult = currentParams.Validate();
                Console.WriteLine($"✓ 有效参数验证结果: {(validationResult.IsValid ? "有效" : "无效")} (预期: 有效)");
                
                // 测试变量映射
                var variableMapping = currentParams.GetVariableMapping();
                Console.WriteLine("✓ 变量映射:");
                foreach (var mapping in variableMapping)
                {
                    Console.WriteLine($"  {mapping.Key} -> '{mapping.Value}'");
                }
                
                Console.WriteLine("✓ 参数模型测试完成");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"✗ 参数模型测试失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 测试模块设置服务
        /// </summary>
        private static void TestModuleSettingsService()
        {
            Console.WriteLine("\n--- 测试模块设置服务 ---");
            
            try
            {
                // 测试获取模块状态
                var moduleStatus = ModuleSettingsService.GetEstateAreaSummaryModuleStatus();
                Console.WriteLine($"✓ 获取模块状态成功");
                Console.WriteLine($"  模块名称: {moduleStatus.ModuleName}");
                Console.WriteLine($"  是否已配置: {moduleStatus.IsConfigured}");
                Console.WriteLine($"  是否有效: {moduleStatus.IsValid}");
                Console.WriteLine($"  是否可用: {moduleStatus.IsAvailable}");
                Console.WriteLine($"  模板是否可用: {moduleStatus.IsTemplateAvailable}");
                Console.WriteLine($"  参数是否有效: {moduleStatus.IsParametersValid}");
                Console.WriteLine($"  模板路径: {moduleStatus.TemplatePath}");
                
                if (moduleStatus.ValidationErrors.Count > 0)
                {
                    Console.WriteLine($"  验证错误: {string.Join(", ", moduleStatus.ValidationErrors)}");
                }
                
                Console.WriteLine("✓ 模块设置服务测试完成");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"✗ 模块设置服务测试失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 测试变量映射功能
        /// </summary>
        private static void TestVariableMapping()
        {
            Console.WriteLine("\n--- 测试变量映射功能 ---");
            
            try
            {
                // 创建测试参数
                var testParams = new EstateAreaSummaryParametersModel.EstateAreaSummaryParameters
                {
                    Remarks = "测试备注2024"
                };
                
                // 保存参数
                var parametersModel = new EstateAreaSummaryParametersModel();
                bool saveResult = parametersModel.SaveParameters(testParams);
                Console.WriteLine($"✓ 参数保存结果: {(saveResult ? "成功" : "失败")}");
                
                if (saveResult)
                {
                    // 重新加载参数验证持久化
                    var newParametersModel = new EstateAreaSummaryParametersModel();
                    var loadedParams = newParametersModel.GetCurrentParameters();
                    
                    Console.WriteLine("✓ 参数重新加载成功");
                    Console.WriteLine($"  备注: '{loadedParams.Remarks}' (预期: '测试备注2024')");
                    
                    // 验证数据一致性
                    bool dataConsistent = loadedParams.Remarks == testParams.Remarks;
                    Console.WriteLine($"✓ 数据一致性: {(dataConsistent ? "通过" : "失败")}");
                    
                    // 测试变量映射
                    var variableMapping = loadedParams.GetVariableMapping();
                    Console.WriteLine("✓ 变量映射测试:");
                    foreach (var mapping in variableMapping)
                    {
                        Console.WriteLine($"  {mapping.Key} -> '{mapping.Value}'");
                    }
                    
                    // 测试空值映射
                    var emptyParams = new EstateAreaSummaryParametersModel.EstateAreaSummaryParameters
                    {
                        Remarks = ""
                    };
                    var emptyMapping = emptyParams.GetVariableMapping();
                    Console.WriteLine("✓ 空值映射测试:");
                    foreach (var mapping in emptyMapping)
                    {
                        Console.WriteLine($"  {mapping.Key} -> '{mapping.Value}' (应该是 '\\')");
                    }
                }
                
                Console.WriteLine("✓ 变量映射功能测试完成");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"✗ 变量映射功能测试失败: {ex.Message}");
            }
        }
    }
}
