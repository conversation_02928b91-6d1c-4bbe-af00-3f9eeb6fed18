# AutoCAD Drawing Processing - Real-time Progress Monitoring

This document describes the real-time progress monitoring implementation for the AutoCAD drawing splitting and conversion process using AccoreConsole.

## Overview

The AccoreConsoleService now supports real-time progress feedback by parsing console output from the AccoreConsole.exe process. This allows clients to monitor the current status of long-running drawing processing operations.

## Features

### 1. Console Output Parsing
- **File Splitting Progress**: Monitors for lines matching `"Saved: [file_path]\[filename].dwg"`
- **Completion Detection**: Watches for `"成功保存 [number] 个打印图到 [directory_path]"`
- **Silent Phase Handling**: Detects when image conversion starts (no console output)

### 2. Progress Reporting
- Real-time progress updates (0-100%)
- Current operation status messages
- Individual file processing notifications
- Error detection and reporting
- Completion status with processed file list

### 3. API Endpoints

#### Process with Progress
```
POST /api/DrawingProcessing/process-with-progress
```
- Uploads DWG file and starts background processing
- Returns session ID immediately for progress tracking
- Non-blocking operation

#### Get Progress
```
GET /api/DrawingProcessing/progress/{sessionId}
```
- Returns current progress information
- Includes percentage, current operation, and processed files
- Updates in real-time

#### Download Results
```
GET /api/DrawingProcessing/download/{sessionId}
```
- Downloads the processed results ZIP file
- Available after processing completion

## Implementation Details

### Progress Phases

1. **Initialization (0-15%)**: File preparation and setup
2. **DWG Splitting (15-80%)**: Individual drawing extraction
3. **Image Conversion (80-90%)**: Converting DWG files to images
4. **Finalization (90-100%)**: Packaging results

### Regex Patterns

```csharp
// File progress pattern
private static readonly Regex FileProgressRegex = new(@"Saved: .*\\(\d+-\d+\.dwg)", RegexOptions.Compiled | RegexOptions.IgnoreCase);

// Completion pattern
private static readonly Regex CompletionRegex = new(@"成功保存 (\d+) 个打印图到 (.+)", RegexOptions.Compiled);
```

### Progress Data Structure

```csharp
public class ProgressInfo
{
    public string SessionId { get; set; }
    public int PercentComplete { get; set; }
    public string CurrentOperation { get; set; }
    public string? CurrentFile { get; set; }
    public int ProcessedCount { get; set; }
    public int TotalCount { get; set; }
    public bool IsCompleted { get; set; }
    public bool HasError { get; set; }
    public string? ErrorMessage { get; set; }
    public DateTime LastUpdated { get; set; }
    public List<string> ProcessedFiles { get; set; }
}
```

## Usage Example

### JavaScript Client
```javascript
// Start processing
const formData = new FormData();
formData.append('file', dwgFile);

const response = await fetch('/api/DrawingProcessing/process-with-progress', {
    method: 'POST',
    body: formData
});

const result = await response.json();
const sessionId = result.sessionId;

// Poll for progress
const progressInterval = setInterval(async () => {
    const progressResponse = await fetch(`/api/DrawingProcessing/progress/${sessionId}`);
    const progress = await progressResponse.json();
    
    updateUI(progress.percentComplete, progress.currentOperation, progress.currentFile);
    
    if (progress.isCompleted) {
        clearInterval(progressInterval);
        if (!progress.hasError) {
            // Download results
            window.location.href = `/api/DrawingProcessing/download/${sessionId}`;
        }
    }
}, 1000);
```

### C# Client
```csharp
// Start processing
var sessionId = await StartProcessingAsync(dwgFilePath);

// Monitor progress
while (true)
{
    var progress = await GetProgressAsync(sessionId);
    Console.WriteLine($"Progress: {progress.PercentComplete}% - {progress.CurrentOperation}");
    
    if (progress.IsCompleted)
    {
        if (!progress.HasError)
        {
            await DownloadResultsAsync(sessionId);
        }
        break;
    }
    
    await Task.Delay(1000);
}
```

## Testing

A test page is available at `/dwg-processing-progress.html` that demonstrates the real-time progress monitoring functionality.

## Configuration

The progress reporter is automatically registered in the dependency injection container:

```csharp
// In Program.cs
builder.Services.AddSingleton<IAccoreProgressReporter, AccoreProgressReporter>();
builder.Services.AddSingleton<AccoreConsoleService>();
```

## Error Handling

- Console output parsing errors are logged but don't stop processing
- Process timeouts are detected and reported
- File access errors are captured and reported to clients
- Network interruptions during progress polling are handled gracefully

## Performance Considerations

- Progress polling interval should be reasonable (1-2 seconds recommended)
- Old progress data is automatically cleaned up
- Console output parsing uses compiled regex for efficiency
- Background processing doesn't block the main thread

## Limitations

- Progress accuracy depends on AccoreConsole output consistency
- Image conversion phase has no detailed progress (silent operation)
- Progress data is stored in memory (not persistent across server restarts)
