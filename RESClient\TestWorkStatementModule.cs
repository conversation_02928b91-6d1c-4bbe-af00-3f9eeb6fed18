using System;
using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;
using RESClient.Services.Implementations;
using RESClient.MVP.Models;

namespace RESClient
{
    /// <summary>
    /// 作业声明模块测试类
    /// </summary>
    public static class TestWorkStatementModule
    {
        /// <summary>
        /// 运行所有测试
        /// </summary>
        public static async Task RunAllTests()
        {
            Console.WriteLine("=== 作业声明模块测试开始 ===");
            
            TestModuleAvailability();
            TestParametersModel();
            TestParameterInputForm();
            await TestModuleGeneration();
            await TestTemplateProcessing();
            
            Console.WriteLine("=== 作业声明模块测试完成 ===");
        }

        /// <summary>
        /// 测试模块可用性检查
        /// </summary>
        public static void TestModuleAvailability()
        {
            Console.WriteLine("\n--- 测试模块可用性检查 ---");
            
            try
            {
                var generator = new WorkStatementModuleGenerator();
                var parameters = new Dictionary<string, object>();
                
                bool isAvailable = generator.IsAvailable(parameters);
                Console.WriteLine($"模块可用性: {(isAvailable ? "✓ 可用" : "✗ 不可用")}");
                
                // 检查模板文件是否存在
                string templateDirectory = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "报告模板");
                string templatePath = Path.Combine(templateDirectory, "03_作业声明", "作业声明.docx");
                Console.WriteLine($"模板文件路径: {templatePath}");
                Console.WriteLine($"模板文件存在: {(File.Exists(templatePath) ? "✓ 是" : "✗ 否")}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"✗ 可用性检查测试失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 测试参数模型功能
        /// </summary>
        public static void TestParametersModel()
        {
            Console.WriteLine("\n--- 测试参数模型功能 ---");
            
            try
            {
                var parametersModel = new WorkStatementParametersModel();
                
                // 测试获取当前参数
                var currentParams = parametersModel.GetCurrentParameters();
                Console.WriteLine($"✓ 成功获取当前参数");
                Console.WriteLine($"  委托方: {currentParams.ClientName}");
                Console.WriteLine($"  测绘机构: {currentParams.SurveyInstitution}");
                Console.WriteLine($"  证书编号: {currentParams.CertificateNumber}");
                
                // 测试参数验证
                var (isValid, errors) = currentParams.Validate();
                Console.WriteLine($"参数验证结果: {(isValid ? "✓ 通过" : "✗ 失败")}");
                if (!isValid)
                {
                    Console.WriteLine($"验证错误: {string.Join(", ", errors)}");
                }
                
                // 测试变量映射
                var variableMapping = currentParams.GetVariableMapping();
                Console.WriteLine($"✓ 变量映射包含 {variableMapping.Count} 个变量");
                
                // 测试保存和加载
                var testParams = new WorkStatementParametersModel.WorkStatementParameters
                {
                    ClientName = "测试委托方",
                    ProjectAddress = "测试项目地址",
                    CertificateLevel = "甲测资字",
                    CertificateNumber = "TEST123456",
                    ProfessionalScope = "测试专业范围",
                    LegalRepresentative = "测试法人",
                    TechnicalManager = "测试技术负责人",
                    CompanyAddress = "测试公司地址",
                    Phone = "028-12345678",
                    SurveyInstitution = "测试测绘机构"
                };
                
                bool saveResult = parametersModel.SaveParameters(testParams);
                Console.WriteLine($"参数保存: {(saveResult ? "✓ 成功" : "✗ 失败")}");
                
                // 重新加载验证
                var reloadedParams = parametersModel.GetCurrentParameters();
                bool dataMatches = reloadedParams.ClientName == testParams.ClientName;
                Console.WriteLine($"参数重新加载: {(dataMatches ? "✓ 成功" : "✗ 失败")}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"✗ 参数模型测试失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 测试模块生成功能
        /// </summary>
        public static async Task TestModuleGeneration()
        {
            Console.WriteLine("\n--- 测试模块生成功能 ---");
            
            try
            {
                var generator = new WorkStatementModuleGenerator();
                
                // 创建测试输出目录
                string testOutputDir = Path.Combine(Path.GetTempPath(), "RESClient_Test_WorkStatement");
                if (Directory.Exists(testOutputDir))
                {
                    Directory.Delete(testOutputDir, true);
                }
                Directory.CreateDirectory(testOutputDir);
                
                Console.WriteLine($"测试输出目录: {testOutputDir}");
                
                // 准备参数
                var parameters = new Dictionary<string, object>
                {
                    ["OutputDir"] = testOutputDir
                };
                
                // 进度回调
                void ProgressCallback(int progress, string message)
                {
                    Console.WriteLine($"[{progress}%] {message}");
                }
                
                // 执行生成
                Console.WriteLine("开始生成作业声明模块...");
                bool result = await generator.GenerateAsync(parameters, ProgressCallback);
                
                Console.WriteLine($"生成结果: {(result ? "✓ 成功" : "✗ 失败")}");
                
                // 检查输出文件
                string outputFile = Path.Combine(testOutputDir, "作业声明.docx");
                bool fileExists = File.Exists(outputFile);
                Console.WriteLine($"输出文件存在: {(fileExists ? "✓ 是" : "✗ 否")}");
                
                if (fileExists)
                {
                    FileInfo fileInfo = new FileInfo(outputFile);
                    Console.WriteLine($"输出文件大小: {fileInfo.Length} 字节");
                    Console.WriteLine($"输出文件路径: {outputFile}");
                }
                
                // 清理测试目录
                try
                {
                    if (Directory.Exists(testOutputDir))
                    {
                        Directory.Delete(testOutputDir, true);
                        Console.WriteLine("✓ 清理测试目录完成");
                    }
                }
                catch (Exception cleanupEx)
                {
                    Console.WriteLine($"⚠ 清理测试目录失败: {cleanupEx.Message}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"✗ 模块生成测试失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 测试模板处理功能
        /// </summary>
        public static async Task TestTemplateProcessing()
        {
            Console.WriteLine("\n--- 测试模板处理功能 ---");
            
            try
            {
                var workStatementModel = new WorkStatementModel();
                
                // 创建测试输出目录
                string testOutputDir = Path.Combine(Path.GetTempPath(), "RESClient_Test_WorkStatement_Template");
                if (Directory.Exists(testOutputDir))
                {
                    Directory.Delete(testOutputDir, true);
                }
                Directory.CreateDirectory(testOutputDir);
                
                string outputFile = Path.Combine(testOutputDir, "测试作业声明.docx");
                
                Console.WriteLine("测试模板填充功能...");
                bool result = await workStatementModel.TestWorkStatementTemplateAsync(outputFile);
                
                Console.WriteLine($"模板填充结果: {(result ? "✓ 成功" : "✗ 失败")}");
                
                if (result && File.Exists(outputFile))
                {
                    FileInfo fileInfo = new FileInfo(outputFile);
                    Console.WriteLine($"生成文件大小: {fileInfo.Length} 字节");
                    Console.WriteLine($"生成文件路径: {outputFile}");
                }
                
                // 清理测试目录
                try
                {
                    if (Directory.Exists(testOutputDir))
                    {
                        Directory.Delete(testOutputDir, true);
                        Console.WriteLine("✓ 清理测试目录完成");
                    }
                }
                catch (Exception cleanupEx)
                {
                    Console.WriteLine($"⚠ 清理测试目录失败: {cleanupEx.Message}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"✗ 模板处理测试失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 测试错误处理
        /// </summary>
        public static async Task TestErrorHandling()
        {
            Console.WriteLine("\n--- 测试错误处理 ---");

            try
            {
                var generator = new WorkStatementModuleGenerator();

                // 测试缺少输出目录参数
                var emptyParameters = new Dictionary<string, object>();

                void ProgressCallback(int progress, string message)
                {
                    Console.WriteLine($"[{progress}%] {message}");
                }

                Console.WriteLine("测试缺少输出目录参数...");
                bool result = await generator.GenerateAsync(emptyParameters, ProgressCallback);
                Console.WriteLine($"预期失败结果: {(result ? "✗ 意外成功" : "✓ 正确失败")}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"✗ 错误处理测试失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 测试参数输入窗体
        /// </summary>
        public static void TestParameterInputForm()
        {
            Console.WriteLine("\n--- 测试参数输入窗体 ---");

            try
            {
                var parametersModel = new WorkStatementParametersModel();
                var currentParams = parametersModel.GetCurrentParameters();

                Console.WriteLine("✓ 参数输入窗体类型匹配测试通过");
                Console.WriteLine($"  参数类型: {currentParams.GetType().Name}");
                Console.WriteLine($"  窗体支持: WorkStatementParameterInputForm");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"✗ 参数输入窗体测试失败: {ex.Message}");
            }
        }
    }
}
