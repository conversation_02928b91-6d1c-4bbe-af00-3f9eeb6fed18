using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Configuration;

namespace RESServer.Services
{
    /// <summary>
    /// 临时文件清理服务，定期清理过期的临时文件
    /// </summary>
    public class TempFileCleanupService : BackgroundService
    {
        private readonly ILogger<TempFileCleanupService> _logger;
        private readonly IConfiguration _configuration;
        private readonly string _tempDirectory;
        private readonly TimeSpan _cleanupInterval;
        private readonly TimeSpan _fileMaxAge;

        public TempFileCleanupService(ILogger<TempFileCleanupService> logger, IConfiguration configuration)
        {
            _logger = logger;
            _configuration = configuration;
            
            // 临时文件目录
            _tempDirectory = Path.Combine(Path.GetTempPath(), "AccoreConsoleService");
            
            // 清理间隔，默认每小时运行一次
            _cleanupInterval = TimeSpan.FromMinutes(
                configuration.GetValue<int>("TempFileCleanup:IntervalMinutes", 60));
            
            // 文件最大保留时间，默认24小时
            _fileMaxAge = TimeSpan.FromHours(
                configuration.GetValue<int>("TempFileCleanup:MaxAgeHours", 24));
            
            _logger.LogInformation("临时文件清理服务已初始化，清理间隔: {Interval}, 文件最大保留时间: {MaxAge}",
                _cleanupInterval, _fileMaxAge);
        }

        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            _logger.LogInformation("临时文件清理服务已启动");
            
            while (!stoppingToken.IsCancellationRequested)
            {
                try
                {
                    await CleanupTempFilesAsync();
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "清理临时文件时出错");
                }
                
                // 等待下一次清理
                await Task.Delay(_cleanupInterval, stoppingToken);
            }
            
            _logger.LogInformation("临时文件清理服务已停止");
        }
        
        /// <summary>
        /// 清理过期的临时文件
        /// </summary>
        private async Task CleanupTempFilesAsync()
        {
            _logger.LogInformation("开始清理临时文件...");
            
            if (!Directory.Exists(_tempDirectory))
            {
                _logger.LogInformation("临时目录不存在: {Directory}", _tempDirectory);
                return;
            }
            
            var cutoffTime = DateTime.Now - _fileMaxAge;
            int deletedCount = 0;
            int errorCount = 0;
            
            // 获取所有会话目录
            var sessionDirs = Directory.GetDirectories(_tempDirectory);
            
            foreach (var sessionDir in sessionDirs)
            {
                try
                {
                    var dirInfo = new DirectoryInfo(sessionDir);
                    
                    // 如果目录创建时间早于截止时间，则删除目录
                    if (dirInfo.CreationTime < cutoffTime)
                    {
                        // 删除目录之前，尝试删除可能被锁定的文件
                        await UnlockAndDeleteDirectoryAsync(sessionDir);
                        
                        // 删除整个目录
                        Directory.Delete(sessionDir, true);
                        deletedCount++;
                        
                        _logger.LogDebug("已删除过期会话目录: {Directory}, 创建时间: {CreationTime}", 
                            sessionDir, dirInfo.CreationTime);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "删除临时目录时出错: {Directory}", sessionDir);
                    errorCount++;
                }
            }
            
            _logger.LogInformation("临时文件清理完成. 已删除 {DeletedCount} 个目录，失败 {ErrorCount} 个", 
                deletedCount, errorCount);
        }
        
        /// <summary>
        /// 尝试解锁和删除可能被锁定的文件
        /// </summary>
        private async Task UnlockAndDeleteDirectoryAsync(string directory)
        {
            try
            {
                // 运行 GC 收集来释放文件句柄
                GC.Collect();
                GC.WaitForPendingFinalizers();
                
                // 等待一段时间，让文件句柄释放
                await Task.Delay(100);
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "尝试解锁目录文件时出错: {Directory}", directory);
            }
        }
    }
} 