using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace RESServer.Models
{
    /// <summary>
    /// 用户实体模型
    /// </summary>
    public class User
    {
        [Key]
        public int Id { get; set; }

        [Required]
        [StringLength(50)]
        public string Username { get; set; } = string.Empty;

        [Required]
        [StringLength(255)]
        public string PasswordHash { get; set; } = string.Empty;

        [StringLength(100)]
        public string? Email { get; set; }

        [StringLength(50)]
        public string? FullName { get; set; }

        [Required]
        [StringLength(20)]
        public string Role { get; set; } = "User";

        public bool IsActive { get; set; } = true;

        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        public DateTime? LastLoginAt { get; set; }

        [StringLength(500)]
        public string? Description { get; set; }
    }

    /// <summary>
    /// 登录请求模型
    /// </summary>
    public class LoginRequest
    {
        [Required]
        [StringLength(50)]
        public string Username { get; set; } = string.Empty;

        [Required]
        [StringLength(100)]
        public string Password { get; set; } = string.Empty;
    }

    /// <summary>
    /// 登录响应模型
    /// </summary>
    public class LoginResponse
    {
        public bool Success { get; set; }
        public string Message { get; set; } = string.Empty;
        public string? Token { get; set; }
        public DateTime? ExpiresAt { get; set; }
        public UserInfo? User { get; set; }
    }

    /// <summary>
    /// 用户信息模型（不包含敏感信息）
    /// </summary>
    public class UserInfo
    {
        public int Id { get; set; }
        public string Username { get; set; } = string.Empty;
        public string? Email { get; set; }
        public string? FullName { get; set; }
        public string Role { get; set; } = string.Empty;
        public bool IsActive { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime? LastLoginAt { get; set; }
    }

    /// <summary>
    /// 创建用户请求模型
    /// </summary>
    public class CreateUserRequest
    {
        [Required]
        [StringLength(50)]
        public string Username { get; set; } = string.Empty;

        [Required]
        [StringLength(100)]
        public string Password { get; set; } = string.Empty;

        [StringLength(100)]
        public string? Email { get; set; }

        [StringLength(50)]
        public string? FullName { get; set; }

        [StringLength(20)]
        public string Role { get; set; } = "User";

        [StringLength(500)]
        public string? Description { get; set; }
    }

    /// <summary>
    /// 更新用户请求模型
    /// </summary>
    public class UpdateUserRequest
    {
        [StringLength(100)]
        public string? Email { get; set; }

        [StringLength(50)]
        public string? FullName { get; set; }

        [StringLength(20)]
        public string? Role { get; set; }

        public bool? IsActive { get; set; }

        [StringLength(500)]
        public string? Description { get; set; }
    }

    /// <summary>
    /// 修改密码请求模型
    /// </summary>
    public class ChangePasswordRequest
    {
        [Required]
        [StringLength(100)]
        public string CurrentPassword { get; set; } = string.Empty;

        [Required]
        [StringLength(100)]
        public string NewPassword { get; set; } = string.Empty;
    }

    /// <summary>
    /// JWT 配置模型
    /// </summary>
    public class JwtConfig
    {
        public string SecretKey { get; set; } = string.Empty;
        public string Issuer { get; set; } = string.Empty;
        public string Audience { get; set; } = string.Empty;
        public int ExpirationMinutes { get; set; } = 60;
    }

    /// <summary>
    /// 认证配置模型
    /// </summary>
    public class AuthConfig
    {
        public bool EnableAuthentication { get; set; } = false;
        public JwtConfig Jwt { get; set; } = new JwtConfig();
    }

    /// <summary>
    /// 服务器状态响应模型
    /// </summary>
    public class ServerStatusResponse
    {
        public bool Success { get; set; }
        public string Message { get; set; } = string.Empty;
        public bool AuthenticationEnabled { get; set; }
        public DateTime ServerTime { get; set; } = DateTime.UtcNow;
        public string Version { get; set; } = "1.0.0";
    }

    /// <summary>
    /// 通用 API 响应模型
    /// </summary>
    public class ApiResponse<T>
    {
        public bool Success { get; set; }
        public string Message { get; set; } = string.Empty;
        public T? Data { get; set; }
        public string? Error { get; set; }
    }

    /// <summary>
    /// 通用 API 响应模型（无数据）
    /// </summary>
    public class ApiResponse : ApiResponse<object>
    {
    }
}
