using System;
using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;
using RESClient.Services;
using RESClient.Services.Implementations;

namespace RESClient
{
    /// <summary>
    /// 测试7z压缩包提取功能
    /// </summary>
    public class TestArchiveExtraction
    {
        /// <summary>
        /// 运行7z压缩包提取功能测试
        /// </summary>
        public static async Task RunArchiveExtractionTests()
        {
            Console.WriteLine("=== 7z压缩包提取功能测试 ===");
            Console.WriteLine();

            try
            {
                await TestArchiveExtractionService();
                await TestModuleGeneratorsWithArchives();
                Console.WriteLine("\n=== 所有测试完成 ===");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"测试过程中发生错误: {ex.Message}");
                Console.WriteLine($"详细信息: {ex}");
            }
        }

        /// <summary>
        /// 测试ArchiveExtractionService基本功能
        /// </summary>
        private static async Task TestArchiveExtractionService()
        {
            Console.WriteLine("1. 测试ArchiveExtractionService基本功能");
            Console.WriteLine("----------------------------------------");

            try
            {
                using (var service = new ArchiveExtractionService())
                {
                    Console.WriteLine("✓ ArchiveExtractionService创建成功");

                    // 测试查找7z文件功能
                    string testDataDir = @"C:\TestData"; // 用户需要提供包含成果包.7z的测试目录
                    
                    if (Directory.Exists(testDataDir))
                    {
                        Console.WriteLine($"正在测试目录: {testDataDir}");
                        
                        var archiveFiles = service.FindArchiveFiles(testDataDir);
                        Console.WriteLine($"找到 {archiveFiles.Count} 个成果包.7z文件:");
                        
                        foreach (var file in archiveFiles)
                        {
                            Console.WriteLine($"  - {Path.GetFileName(file)}");
                        }

                        if (archiveFiles.Count > 0)
                        {
                            // 测试提取功能
                            Console.WriteLine("\n正在测试提取功能...");
                            
                            var progressMessages = new List<string>();
                            Action<int, string> progressCallback = (progress, message) =>
                            {
                                progressMessages.Add($"[{progress}%] {message}");
                                Console.WriteLine($"  {message}");
                            };

                            string cgbFile = service.ExtractAndFindCGBFile(testDataDir, progressCallback);
                            
                            if (!string.IsNullOrEmpty(cgbFile))
                            {
                                Console.WriteLine($"✓ 成功提取并找到CGB文件: {Path.GetFileName(cgbFile)}");
                                Console.WriteLine($"  完整路径: {cgbFile}");
                                Console.WriteLine($"  文件存在: {File.Exists(cgbFile)}");
                                
                                if (File.Exists(cgbFile))
                                {
                                    var fileInfo = new FileInfo(cgbFile);
                                    Console.WriteLine($"  文件大小: {fileInfo.Length} 字节");
                                    Console.WriteLine($"  修改时间: {fileInfo.LastWriteTime}");
                                }
                            }
                            else
                            {
                                Console.WriteLine("✗ 未能找到CGB文件");
                            }
                        }
                        else
                        {
                            Console.WriteLine("⚠ 测试目录中没有找到成果包.7z文件");
                            Console.WriteLine("  请确保测试目录包含成果包.7z文件");
                        }
                    }
                    else
                    {
                        Console.WriteLine($"⚠ 测试目录不存在: {testDataDir}");
                        Console.WriteLine("  请创建测试目录并放入成果包.7z文件进行测试");
                    }
                }
                
                Console.WriteLine("✓ ArchiveExtractionService测试完成");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"✗ ArchiveExtractionService测试失败: {ex.Message}");
                Console.WriteLine($"  详细信息: {ex}");
            }
        }

        /// <summary>
        /// 测试模块生成器与7z压缩包的集成
        /// </summary>
        private static async Task TestModuleGeneratorsWithArchives()
        {
            Console.WriteLine("\n2. 测试模块生成器与7z压缩包的集成");
            Console.WriteLine("----------------------------------------");

            try
            {
                // 测试EstateAreaSummaryModuleGenerator
                await TestEstateAreaSummaryGenerator();
                
                // 测试BuildingInfoModuleGenerator
                await TestBuildingInfoGenerator();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"✗ 模块生成器测试失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 测试房产面积汇总表模块生成器
        /// </summary>
        private static async Task TestEstateAreaSummaryGenerator()
        {
            Console.WriteLine("\n2.1 测试房产面积汇总表模块生成器");
            Console.WriteLine("  ----------------------------------");

            try
            {
                var generator = new EstateAreaSummaryModuleGenerator();
                
                string testDataDir = @"C:\TestData";
                string testOutputDir = Path.Combine(Path.GetTempPath(), "RESClient_Test_EstateArea_" + Guid.NewGuid().ToString("N").Substring(0, 8));
                Directory.CreateDirectory(testOutputDir);

                var parameters = new Dictionary<string, object>
                {
                    ["DataFolder"] = testDataDir,
                    ["OutputDir"] = testOutputDir
                };

                // 测试IsAvailable方法
                bool isAvailable = generator.IsAvailable(parameters);
                Console.WriteLine($"  IsAvailable结果: {isAvailable}");

                if (isAvailable)
                {
                    Console.WriteLine("  正在测试生成功能...");
                    
                    var progressMessages = new List<string>();
                    Action<int, string> progressCallback = (progress, message) =>
                    {
                        progressMessages.Add($"[{progress}%] {message}");
                        Console.WriteLine($"    {message}");
                    };

                    bool result = await generator.GenerateAsync(parameters, progressCallback);
                    Console.WriteLine($"  生成结果: {(result ? "成功" : "失败")}");

                    if (result)
                    {
                        string outputFile = Path.Combine(testOutputDir, "房产面积汇总表.docx");
                        Console.WriteLine($"  输出文件: {outputFile}");
                        Console.WriteLine($"  文件存在: {File.Exists(outputFile)}");
                        
                        if (File.Exists(outputFile))
                        {
                            var fileInfo = new FileInfo(outputFile);
                            Console.WriteLine($"  文件大小: {fileInfo.Length} 字节");
                        }
                    }
                }
                else
                {
                    Console.WriteLine("  ⚠ 模块不可用，可能是缺少必要的数据文件");
                }

                // 清理测试目录
                try
                {
                    if (Directory.Exists(testOutputDir))
                    {
                        Directory.Delete(testOutputDir, true);
                    }
                }
                catch
                {
                    Console.WriteLine($"  ⚠ 无法清理测试目录: {testOutputDir}");
                }

                Console.WriteLine("  ✓ 房产面积汇总表模块测试完成");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"  ✗ 房产面积汇总表模块测试失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 测试楼栋基本信息模块生成器
        /// </summary>
        private static async Task TestBuildingInfoGenerator()
        {
            Console.WriteLine("\n2.2 测试楼栋基本信息模块生成器");
            Console.WriteLine("  ----------------------------------");

            try
            {
                var generator = new BuildingInfoModuleGenerator();
                
                string testDataDir = @"C:\TestData";
                string testOutputDir = Path.Combine(Path.GetTempPath(), "RESClient_Test_BuildingInfo_" + Guid.NewGuid().ToString("N").Substring(0, 8));
                Directory.CreateDirectory(testOutputDir);

                var parameters = new Dictionary<string, object>
                {
                    ["DataFolder"] = testDataDir,
                    ["OutputDir"] = testOutputDir
                };

                // 测试IsAvailable方法
                bool isAvailable = generator.IsAvailable(parameters);
                Console.WriteLine($"  IsAvailable结果: {isAvailable}");

                if (isAvailable)
                {
                    Console.WriteLine("  正在测试生成功能...");
                    
                    var progressMessages = new List<string>();
                    Action<int, string> progressCallback = (progress, message) =>
                    {
                        progressMessages.Add($"[{progress}%] {message}");
                        Console.WriteLine($"    {message}");
                    };

                    bool result = await generator.GenerateAsync(parameters, progressCallback);
                    Console.WriteLine($"  生成结果: {(result ? "成功" : "失败")}");

                    if (result)
                    {
                        string outputFile = Path.Combine(testOutputDir, "楼栋基本信息.docx");
                        Console.WriteLine($"  输出文件: {outputFile}");
                        Console.WriteLine($"  文件存在: {File.Exists(outputFile)}");
                        
                        if (File.Exists(outputFile))
                        {
                            var fileInfo = new FileInfo(outputFile);
                            Console.WriteLine($"  文件大小: {fileInfo.Length} 字节");
                        }
                    }
                }
                else
                {
                    Console.WriteLine("  ⚠ 模块不可用，可能是缺少必要的数据文件");
                }

                // 清理测试目录
                try
                {
                    if (Directory.Exists(testOutputDir))
                    {
                        Directory.Delete(testOutputDir, true);
                    }
                }
                catch
                {
                    Console.WriteLine($"  ⚠ 无法清理测试目录: {testOutputDir}");
                }

                Console.WriteLine("  ✓ 楼栋基本信息模块测试完成");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"  ✗ 楼栋基本信息模块测试失败: {ex.Message}");
            }
        }
    }
}
