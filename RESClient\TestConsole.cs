using System;
using RESClient.MVP.Models;

namespace RESClient
{
    /// <summary>
    /// 简单的控制台测试
    /// </summary>
    public static class TestConsole
    {
        /// <summary>
        /// 测试作业声明参数默认值
        /// </summary>
        public static void TestWorkStatementDefaults()
        {
            try
            {
                Console.WriteLine("=== 测试作业声明参数默认值 ===");
                
                var parametersModel = new WorkStatementParametersModel();
                var parameters = parametersModel.GetCurrentParameters();

                Console.WriteLine("默认参数值:");
                Console.WriteLine($"  测绘机构: {parameters.SurveyInstitution}");
                Console.WriteLine($"  证书编号: {parameters.CertificateNumber}");
                Console.WriteLine($"  证书等级: {parameters.CertificateLevel}");
                Console.WriteLine($"  法人代表: {parameters.LegalRepresentative}");
                Console.WriteLine($"  技术负责人: {parameters.TechnicalManager}");
                Console.WriteLine($"  公司地址: {parameters.CompanyAddress}");
                Console.WriteLine($"  电话: {parameters.Phone}");
                Console.WriteLine($"  传真: {parameters.Fax}");
                Console.WriteLine($"  专业范围: {parameters.ProfessionalScope}");
                Console.WriteLine($"  取得日期: {parameters.CertificateObtainDate:yyyy年M月d日}");
                Console.WriteLine($"  有效期至: {parameters.CertificateExpiryDate:yyyy年M月d日}");

                // 测试参数验证
                Console.WriteLine("\n=== 测试参数验证 ===");
                
                // 测试空参数
                parameters.ClientName = "";
                parameters.ProjectAddress = "";
                
                var validationResult = parameters.Validate();
                Console.WriteLine($"空参数验证结果: {validationResult.IsValid}");
                
                if (!validationResult.IsValid)
                {
                    Console.WriteLine("验证错误:");
                    foreach (var error in validationResult.Errors)
                    {
                        Console.WriteLine($"  - {error}");
                    }
                }

                // 测试有效参数
                parameters.ClientName = "测试委托方";
                parameters.ProjectAddress = "测试项目地址";
                
                validationResult = parameters.Validate();
                Console.WriteLine($"有效参数验证结果: {validationResult.IsValid}");

                Console.WriteLine("=== 测试完成 ===");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"测试时发生错误: {ex.Message}");
                Console.WriteLine($"详细错误: {ex}");
            }
        }
    }
}
