using System;
using System.IO;
using RESClient.MVP.Models;
using RESClient.Utils;

namespace RESClient
{
    /// <summary>
    /// 简单测试程序，验证封面参数功能
    /// </summary>
    public class SimpleTest
    {
        public static void TestCoverParameters()
        {
            try
            {
                Console.WriteLine("=== 封面参数功能测试 ===");

                // 测试1: 创建参数模型
                Console.WriteLine("1. 创建参数模型...");
                var parametersModel = new CoverParametersModel();
                Console.WriteLine("   ✓ 参数模型创建成功");

                // 测试2: 获取当前参数
                Console.WriteLine("2. 获取当前参数...");
                var currentParams = parametersModel.GetCurrentParameters();
                Console.WriteLine($"   ✓ 当前测绘公司: {currentParams.SurveyCompany}");
                Console.WriteLine($"   ✓ 当前资格证书号: {currentParams.QualificationNumber}");

                // 测试3: 设置测试数据
                Console.WriteLine("3. 设置测试数据...");
                currentParams.SurveyNumber = "SC20241225001";
                currentParams.ProjectNumber = "PJ202412-123";
                currentParams.ProjectName = "锦江区测试花园小区";
                currentParams.ProjectAddress = "成都市锦江区测试路123号";
                currentParams.ConstructionUnit = "成都市测试建设有限公司";
                currentParams.SurveyBuilding = "1号楼、2号楼、3号楼";
                Console.WriteLine("   ✓ 测试数据设置完成");

                // 测试4: 验证参数
                Console.WriteLine("4. 验证参数...");
                var validationResult = currentParams.Validate();
                Console.WriteLine($"   验证结果: {(validationResult.IsValid ? "✓ 通过" : "✗ 失败")}");
                if (!validationResult.IsValid)
                {
                    foreach (var error in validationResult.Errors)
                    {
                        Console.WriteLine($"   - {error}");
                    }
                }

                // 测试5: 保存参数
                Console.WriteLine("5. 保存参数...");
                bool saveResult = parametersModel.SaveParameters(currentParams);
                Console.WriteLine($"   保存结果: {(saveResult ? "✓ 成功" : "✗ 失败")}");
                if (saveResult)
                {
                    Console.WriteLine($"   配置文件路径: {parametersModel.GetConfigFilePath()}");
                }

                // 测试6: 重新加载参数
                Console.WriteLine("6. 重新加载参数...");
                var newParametersModel = new CoverParametersModel();
                var reloadedParams = newParametersModel.GetCurrentParameters();
                Console.WriteLine($"   ✓ 重新加载的项目名称: {reloadedParams.ProjectName}");

                // 测试7: 获取变量映射
                Console.WriteLine("7. 获取变量映射...");
                var variableMapping = reloadedParams.GetVariableMapping();
                Console.WriteLine("   变量映射:");
                foreach (var mapping in variableMapping)
                {
                    Console.WriteLine($"   {mapping.Key} = {mapping.Value}");
                }

                Console.WriteLine("\n=== 所有测试完成 ===");
                Console.WriteLine("✓ 封面参数功能正常工作");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"\n✗ 测试过程中发生错误:");
                Console.WriteLine($"   错误信息: {ex.Message}");
                Console.WriteLine($"   详细信息: {ex}");
            }
        }

        public static void TestFileOperations()
        {
            try
            {
                Console.WriteLine("\n=== 文件操作测试 ===");

                var parametersModel = new CoverParametersModel();
                string configPath = parametersModel.GetConfigFilePath();
                
                Console.WriteLine($"配置文件路径: {configPath}");
                Console.WriteLine($"配置文件存在: {File.Exists(configPath)}");
                Console.WriteLine($"配置目录存在: {Directory.Exists(Path.GetDirectoryName(configPath))}");

                if (File.Exists(configPath))
                {
                    string content = File.ReadAllText(configPath);
                    Console.WriteLine($"配置文件大小: {content.Length} 字符");
                    Console.WriteLine("配置文件内容预览:");
                    Console.WriteLine(content.Substring(0, Math.Min(200, content.Length)) + "...");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"文件操作测试失败: {ex.Message}");
            }
        }

        public static void TestChineseDateFormatter()
        {
            try
            {
                Console.WriteLine("\n=== 中文日期格式化测试 ===");

                // 测试日期转换
                Console.WriteLine(ChineseDateFormatter.TestConversion());

                // 测试当前日期
                Console.WriteLine($"当前日期: {DateTime.Now:yyyy-MM-dd}");
                Console.WriteLine($"中文格式: {ChineseDateFormatter.ToChineseUppercase(DateTime.Now)}");

                // 测试参数模型中的日期格式
                var parametersModel = new CoverParametersModel();
                var currentParams = parametersModel.GetCurrentParameters();
                var variableMapping = currentParams.GetVariableMapping();

                Console.WriteLine($"参数模型中的日期变量: {variableMapping["${日期}"]}");

                Console.WriteLine("✓ 中文日期格式化功能正常");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"✗ 中文日期格式化测试失败: {ex.Message}");
            }
        }
    }
}
