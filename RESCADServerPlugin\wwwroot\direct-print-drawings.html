<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>直接处理打印图</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', <PERSON>l, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .card {
            background-color: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input[type="file"] {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        button {
            background-color: #0078d4;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            transition: background-color 0.2s;
        }
        button:hover {
            background-color: #005a9e;
        }
        button:disabled {
            background-color: #cccccc;
            cursor: not-allowed;
        }
        .progress-container {
            margin-top: 20px;
            display: none;
        }
        .progress-bar {
            background-color: #f0f0f0;
            border-radius: 4px;
            height: 20px;
            overflow: hidden;
        }
        .progress-bar-fill {
            background-color: #0078d4;
            height: 100%;
            width: 0%;
            transition: width 0.3s;
        }
        .status-message {
            margin-top: 10px;
            font-weight: bold;
        }
        .error-message {
            color: #d83b01;
            margin-top: 10px;
        }
    </style>
</head>
<body>
    <h1>直接处理打印图</h1>
    
    <div class="card">
        <h2>上传DWG文件</h2>
        <p>上传一个DWG文件以直接提取其中的打印图（无需后台队列）</p>
        
        <div class="form-group">
            <label for="dwgFile">选择DWG文件：</label>
            <input type="file" id="dwgFile" accept=".dwg">
        </div>
        
        <button id="uploadBtn" onclick="uploadFile()">上传并处理</button>
        
        <div id="progressContainer" class="progress-container">
            <div class="progress-bar">
                <div id="progressBar" class="progress-bar-fill"></div>
            </div>
            <div id="statusMessage" class="status-message"></div>
            <div id="errorMessage" class="error-message"></div>
        </div>
    </div>
    
    <div class="card">
        <h2>说明</h2>
        <p>此功能将直接在当前打开的AutoCAD实例中处理DWG文件，无需等待队列。</p>
        <p>处理完成后，将自动下载包含所有提取的打印图的压缩包。</p>
    </div>

    <script>
        function uploadFile() {
            const fileInput = document.getElementById('dwgFile');
            const progressContainer = document.getElementById('progressContainer');
            const progressBar = document.getElementById('progressBar');
            const statusMessage = document.getElementById('statusMessage');
            const errorMessage = document.getElementById('errorMessage');
            const uploadBtn = document.getElementById('uploadBtn');
            
            // 检查是否选择了文件
            if (!fileInput.files.length) {
                errorMessage.textContent = '请先选择一个DWG文件';
                progressContainer.style.display = 'block';
                return;
            }
            
            const file = fileInput.files[0];
            if (!file.name.toLowerCase().endsWith('.dwg')) {
                errorMessage.textContent = '请选择有效的DWG文件';
                progressContainer.style.display = 'block';
                return;
            }
            
            // 重置状态
            errorMessage.textContent = '';
            uploadBtn.disabled = true;
            progressContainer.style.display = 'block';
            statusMessage.textContent = '正在上传文件...';
            progressBar.style.width = '10%';
            
            // 创建FormData对象
            const formData = new FormData();
            formData.append('dwgFile', file);
            
            // 创建XMLHttpRequest
            const xhr = new XMLHttpRequest();
            
            // 获取当前URL的主机名和端口
            const baseUrl = window.location.origin;
            
            // 发送请求
            xhr.open('POST', `${baseUrl}/api/print-drawings/process-direct`, true);
            
            // 设置进度事件
            xhr.upload.onprogress = function(event) {
                if (event.lengthComputable) {
                    const percentComplete = (event.loaded / event.total) * 90; // 保留最后10%给下载进度
                    progressBar.style.width = percentComplete + '%';
                }
            };
            
            // 请求完成处理
            xhr.onload = function() {
                if (xhr.status === 200) {
                    // 下载文件
                    statusMessage.textContent = '处理成功，正在下载打印图...';
                    progressBar.style.width = '100%';
                    
                    // 创建隐藏的下载链接
                    const blob = xhr.response;
                    const downloadLink = document.createElement('a');
                    downloadLink.href = URL.createObjectURL(blob);
                    downloadLink.download = 'print_drawings.zip';
                    document.body.appendChild(downloadLink);
                    downloadLink.click();
                    document.body.removeChild(downloadLink);
                    
                    setTimeout(() => {
                        statusMessage.textContent = '处理完成！';
                        uploadBtn.disabled = false;
                    }, 1000);
                } else {
                    try {
                        const response = JSON.parse(xhr.responseText);
                        errorMessage.textContent = '处理失败: ' + (response.message || xhr.statusText);
                    } catch (e) {
                        errorMessage.textContent = `处理失败: ${xhr.status} ${xhr.statusText}`;
                    }
                    progressBar.style.width = '0%';
                    uploadBtn.disabled = false;
                }
            };
            
            // 请求错误处理
            xhr.onerror = function() {
                errorMessage.textContent = '网络错误，请检查服务器是否运行';
                progressBar.style.width = '0%';
                uploadBtn.disabled = false;
            };
            
            // 响应类型设为blob用于文件下载
            xhr.responseType = 'blob';
            
            // 发送请求
            xhr.send(formData);
        }
    </script>
</body>
</html> 