using System;
using System.Windows.Forms;
using RESClient.Services;
using RESClient.Views;

namespace RESClient
{
    /// <summary>
    /// 测试记住密码功能
    /// </summary>
    public static class TestRememberPasswordFeature
    {
        /// <summary>
        /// 运行记住密码功能测试
        /// </summary>
        public static void RunTest()
        {
            Console.WriteLine("=== 记住密码功能测试 ===");
            
            try
            {
                // 测试AuthConfigService的密码存储功能
                TestAuthConfigService();
                
                // 测试LoginForm的UI功能
                TestLoginFormUI();
                
                Console.WriteLine("✅ 记住密码功能测试完成");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ 测试失败: {ex.Message}");
                Console.WriteLine($"详细错误: {ex}");
            }
        }
        
        /// <summary>
        /// 测试AuthConfigService的密码存储功能
        /// </summary>
        private static void TestAuthConfigService()
        {
            Console.WriteLine("1. 测试AuthConfigService密码存储功能...");
            
            var authConfig = AuthConfigService.Instance;
            
            // 测试保存凭据
            Console.WriteLine("   保存测试凭据...");
            authConfig.SaveRememberedCredentials("testuser", "testpass123", true);
            
            // 测试加载凭据
            Console.WriteLine("   加载保存的凭据...");
            var credentials = authConfig.LoadRememberedCredentials();
            
            if (credentials.HasValue)
            {
                Console.WriteLine($"   ✅ 成功加载凭据: 用户名={credentials.Value.Username}");
                Console.WriteLine($"   密码长度: {credentials.Value.Password.Length}");
                
                if (credentials.Value.Username == "testuser" && credentials.Value.Password == "testpass123")
                {
                    Console.WriteLine("   ✅ 凭据内容验证成功");
                }
                else
                {
                    Console.WriteLine("   ❌ 凭据内容验证失败");
                }
            }
            else
            {
                Console.WriteLine("   ❌ 未能加载凭据");
            }
            
            // 测试清除凭据
            Console.WriteLine("   清除保存的凭据...");
            authConfig.ClearRememberedCredentials();
            
            var clearedCredentials = authConfig.LoadRememberedCredentials();
            if (!clearedCredentials.HasValue)
            {
                Console.WriteLine("   ✅ 凭据清除成功");
            }
            else
            {
                Console.WriteLine("   ❌ 凭据清除失败");
            }
            
            Console.WriteLine();
        }
        
        /// <summary>
        /// 测试LoginForm的UI功能
        /// </summary>
        private static void TestLoginFormUI()
        {
            Console.WriteLine("2. 测试LoginForm UI功能...");
            
            try
            {
                Application.EnableVisualStyles();
                Application.SetCompatibleTextRenderingDefault(false);
                
                // 先保存一些测试凭据
                var authConfig = AuthConfigService.Instance;
                authConfig.SaveRememberedCredentials("admin", "admin123", true);
                
                var configService = RESServerConfigService.Instance;
                var authService = new AuthService(configService);
                
                using (var loginForm = new LoginForm(authService))
                {
                    Console.WriteLine("   正在显示登录窗体进行UI测试...");
                    Console.WriteLine("   请检查以下功能:");
                    Console.WriteLine("   - 用户名和密码是否自动填充");
                    Console.WriteLine("   - '记住密码'复选框是否已勾选");
                    Console.WriteLine("   - 取消勾选'记住密码'时是否显示'清除'按钮");
                    Console.WriteLine("   - 点击'清除'按钮是否能清除保存的凭据");
                    
                    var result = loginForm.ShowDialog();
                    Console.WriteLine($"   窗体关闭，结果: {result}");
                }
                
                // 清理测试数据
                authConfig.ClearRememberedCredentials();
                Console.WriteLine("   ✅ UI测试完成，测试数据已清理");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"   ❌ UI测试失败: {ex.Message}");
            }
            
            Console.WriteLine();
        }
        
        /// <summary>
        /// 测试密码加密解密功能
        /// </summary>
        public static void TestPasswordEncryption()
        {
            Console.WriteLine("=== 密码加密解密测试 ===");
            
            try
            {
                var authConfig = AuthConfigService.Instance;
                
                // 测试多个密码
                string[] testPasswords = { "simple", "complex@123", "中文密码", "!@#$%^&*()", "" };
                
                foreach (var password in testPasswords)
                {
                    Console.WriteLine($"测试密码: '{password}'");
                    
                    // 保存并加载
                    authConfig.SaveRememberedCredentials("testuser", password, true);
                    var credentials = authConfig.LoadRememberedCredentials();
                    
                    if (credentials.HasValue && credentials.Value.Password == password)
                    {
                        Console.WriteLine("   ✅ 加密解密成功");
                    }
                    else
                    {
                        Console.WriteLine("   ❌ 加密解密失败");
                    }
                }
                
                // 清理
                authConfig.ClearRememberedCredentials();
                Console.WriteLine("✅ 密码加密解密测试完成");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ 密码加密解密测试失败: {ex.Message}");
            }
        }
    }
}
