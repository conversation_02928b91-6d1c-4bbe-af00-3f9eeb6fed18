using System;
using Autodesk.AutoCAD.ApplicationServices;
using Autodesk.AutoCAD.DatabaseServices;
using Autodesk.AutoCAD.Geometry;
using Autodesk.AutoCAD.EditorInput;
using Autodesk.AutoCAD.Runtime;
using System.Threading;

namespace RESCADServerPlugin.Services
{
    public class CADDrawingService
    {
        // 在AutoCAD中绘制圆
        public bool DrawCircle(double x, double y, double radius, out string message)
        {
            try
            {
                Document doc = Application.DocumentManager.MdiActiveDocument;
                if (doc == null)
                {
                    message = "无法获取当前AutoCAD文档";
                    return false;
                }

                // 使用委托在正确的线程上执行AutoCAD操作
                bool success = false;
                string resultMessage = "";

                // 确保在主线程执行
                if (Autodesk.AutoCAD.ApplicationServices.Core.Application.MainWindow.Handle != IntPtr.Zero)
                {
                    // 使用文档锁和事务来安全操作
                    using (DocumentLock docLock = doc.LockDocument())
                    {
                        Database db = doc.Database;
                        using (Transaction trans = db.TransactionManager.StartTransaction())
                        {
                            try
                            {
                                // 打开当前空间（模型空间或图纸空间）
                                BlockTable bt = trans.GetObject(db.BlockTableId, OpenMode.ForRead) as BlockTable;
                                BlockTableRecord btr = trans.GetObject(bt[BlockTableRecord.ModelSpace], OpenMode.ForWrite) as BlockTableRecord;

                                // 创建一个圆
                                Point3d center = new Point3d(x, y, 0);
                                Circle circle = new Circle(center, Vector3d.ZAxis, radius);

                                // 添加到数据库
                                btr.AppendEntity(circle);
                                trans.AddNewlyCreatedDBObject(circle, true);

                                // 提交事务
                                trans.Commit();
                                
                                resultMessage = $"成功在坐标({x},{y})创建半径为{radius}的圆";
                                success = true;
                            }
                            catch (System.Exception ex)
                            {
                                resultMessage = $"绘制圆时发生错误: {ex.Message}";
                                trans.Abort();
                                success = false;
                            }
                        }
                    }
                }
                else
                {
                    resultMessage = "无法在AutoCAD主窗口中执行命令";
                    success = false;
                }

                message = resultMessage;
                return success;
            }
            catch (System.Exception ex)
            {
                message = $"执行绘图操作时发生错误: {ex.Message}";
                return false;
            }
        }
        
        // 在AutoCAD中绘制直线
        public bool DrawLine(double x1, double y1, double x2, double y2, out string message)
        {
            try
            {
                Document doc = Application.DocumentManager.MdiActiveDocument;
                if (doc == null)
                {
                    message = "无法获取当前AutoCAD文档";
                    return false;
                }

                // 使用委托在正确的线程上执行AutoCAD操作
                bool success = false;
                string resultMessage = "";

                // 确保在主线程执行
                if (Autodesk.AutoCAD.ApplicationServices.Core.Application.MainWindow.Handle != IntPtr.Zero)
                {
                    // 使用文档锁和事务来安全操作
                    using (DocumentLock docLock = doc.LockDocument())
                    {
                        Database db = doc.Database;
                        using (Transaction trans = db.TransactionManager.StartTransaction())
                        {
                            try
                            {
                                // 打开当前空间（模型空间或图纸空间）
                                BlockTable bt = trans.GetObject(db.BlockTableId, OpenMode.ForRead) as BlockTable;
                                BlockTableRecord btr = trans.GetObject(bt[BlockTableRecord.ModelSpace], OpenMode.ForWrite) as BlockTableRecord;

                                // 创建一条直线
                                Point3d startPoint = new Point3d(x1, y1, 0);
                                Point3d endPoint = new Point3d(x2, y2, 0);
                                Line line = new Line(startPoint, endPoint);

                                // 添加到数据库
                                btr.AppendEntity(line);
                                trans.AddNewlyCreatedDBObject(line, true);

                                // 提交事务
                                trans.Commit();
                                
                                resultMessage = $"成功创建从({x1},{y1})到({x2},{y2})的直线";
                                success = true;
                            }
                            catch (System.Exception ex)
                            {
                                resultMessage = $"绘制直线时发生错误: {ex.Message}";
                                trans.Abort();
                                success = false;
                            }
                        }
                    }
                }
                else
                {
                    resultMessage = "无法在AutoCAD主窗口中执行命令";
                    success = false;
                }

                message = resultMessage;
                return success;
            }
            catch (System.Exception ex)
            {
                message = $"执行绘图操作时发生错误: {ex.Message}";
                return false;
            }
        }
        
        // 在AutoCAD中添加文字
        public bool AddText(double x, double y, string text, double height, out string message)
        {
            try
            {
                Document doc = Application.DocumentManager.MdiActiveDocument;
                if (doc == null)
                {
                    message = "无法获取当前AutoCAD文档";
                    return false;
                }

                // 使用委托在正确的线程上执行AutoCAD操作
                bool success = false;
                string resultMessage = "";

                // 确保在主线程执行
                if (Autodesk.AutoCAD.ApplicationServices.Core.Application.MainWindow.Handle != IntPtr.Zero)
                {
                    // 使用文档锁和事务来安全操作
                    using (DocumentLock docLock = doc.LockDocument())
                    {
                        Database db = doc.Database;
                        using (Transaction trans = db.TransactionManager.StartTransaction())
                        {
                            try
                            {
                                // 打开当前空间（模型空间或图纸空间）
                                BlockTable bt = trans.GetObject(db.BlockTableId, OpenMode.ForRead) as BlockTable;
                                BlockTableRecord btr = trans.GetObject(bt[BlockTableRecord.ModelSpace], OpenMode.ForWrite) as BlockTableRecord;

                                // 创建文字
                                Point3d insertPoint = new Point3d(x, y, 0);
                                DBText dbText = new DBText();
                                dbText.Position = insertPoint;
                                dbText.TextString = text;
                                dbText.Height = height;

                                // 添加到数据库
                                btr.AppendEntity(dbText);
                                trans.AddNewlyCreatedDBObject(dbText, true);

                                // 提交事务
                                trans.Commit();
                                
                                resultMessage = $"成功在坐标({x},{y})添加文字: {text}";
                                success = true;
                            }
                            catch (System.Exception ex)
                            {
                                resultMessage = $"添加文字时发生错误: {ex.Message}";
                                trans.Abort();
                                success = false;
                            }
                        }
                    }
                }
                else
                {
                    resultMessage = "无法在AutoCAD主窗口中执行命令";
                    success = false;
                }

                message = resultMessage;
                return success;
            }
            catch (System.Exception ex)
            {
                message = $"执行文字添加操作时发生错误: {ex.Message}";
                return false;
            }
        }
    }
} 