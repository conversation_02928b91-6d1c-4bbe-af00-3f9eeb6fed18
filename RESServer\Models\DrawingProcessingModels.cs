namespace RESServer.Models
{
    /// <summary>
    /// 图纸处理结果
    /// </summary>
    public class DrawingProcessingResult
    {
        /// <summary>
        /// 处理是否成功
        /// </summary>
        public bool Success { get; set; }
        
        /// <summary>
        /// 处理结果消息
        /// </summary>
        public string? Message { get; set; }
        
        /// <summary>
        /// 处理会话ID，用于后续查询状态或下载结果
        /// </summary>
        public string? SessionId { get; set; }
        
        /// <summary>
        /// 结果文件类型 (zip, dwg, pdf 等)
        /// </summary>
        public string? FileType { get; set; }
        
        /// <summary>
        /// 如果结果是压缩包，其中包含的文件数量
        /// </summary>
        public int FileCount { get; set; }
    }
    
    /// <summary>
    /// 处理任务状态信息
    /// </summary>
    public class ProcessingStatusInfo
    {
        /// <summary>
        /// 处理会话ID
        /// </summary>
        public string? SessionId { get; set; }
        
        /// <summary>
        /// 完成百分比 (0-100)
        /// </summary>
        public int PercentComplete { get; set; }
        
        /// <summary>
        /// 当前操作描述
        /// </summary>
        public string? CurrentOperation { get; set; }
        
        /// <summary>
        /// 是否已完成
        /// </summary>
        public bool IsCompleted { get; set; }
        
        /// <summary>
        /// 是否发生错误
        /// </summary>
        public bool HasError { get; set; }
        
        /// <summary>
        /// 错误信息
        /// </summary>
        public string? ErrorMessage { get; set; }
        
        /// <summary>
        /// 最后更新时间
        /// </summary>
        public DateTime LastUpdated { get; set; }
        
        /// <summary>
        /// 结果文件路径
        /// </summary>
        public string? ResultFilePath { get; set; }
        
        /// <summary>
        /// 结果文件名
        /// </summary>
        public string? FileName { get; set; }
        
        /// <summary>
        /// 结果文件大小（字节）
        /// </summary>
        public long FileSize { get; set; }
        
        /// <summary>
        /// 如果结果是压缩包，其中包含的文件数量
        /// </summary>
        public int FileCount { get; set; }
        
        /// <summary>
        /// DWG文件数量
        /// </summary>
        public int DwgFileCount { get; set; }
        
        /// <summary>
        /// GIF图片文件数量
        /// </summary>
        public int GifFileCount { get; set; }
    }
    
    /// <summary>
    /// 目录文件信息
    /// </summary>
    public class DirectoryFileInfo
    {
        /// <summary>
        /// 处理会话ID
        /// </summary>
        public string? SessionId { get; set; }
        
        /// <summary>
        /// 会话目录路径
        /// </summary>
        public string? DirectoryPath { get; set; }
        
        /// <summary>
        /// 处理目录路径（PrintDrawingsOutput中）
        /// </summary>
        public string? ProcessingDirectoryPath { get; set; }
        
        /// <summary>
        /// 最后更新时间
        /// </summary>
        public DateTime LastUpdated { get; set; }
        
        /// <summary>
        /// 文件列表
        /// </summary>
        public List<FileDetails> Files { get; set; } = new List<FileDetails>();
    }
    
    /// <summary>
    /// 文件详细信息
    /// </summary>
    public class FileDetails
    {
        /// <summary>
        /// 文件名
        /// </summary>
        public string? FileName { get; set; }
        
        /// <summary>
        /// 文件路径
        /// </summary>
        public string? FilePath { get; set; }
        
        /// <summary>
        /// 文件大小（字节）
        /// </summary>
        public long FileSize { get; set; }
        
        /// <summary>
        /// 最后修改时间
        /// </summary>
        public DateTime LastModified { get; set; }
        
        /// <summary>
        /// 是否为目录
        /// </summary>
        public bool IsDirectory { get; set; }
        
        /// <summary>
        /// 是否在处理目录中（而不是会话目录）
        /// </summary>
        public bool IsInProcessingDir { get; set; }
    }
} 