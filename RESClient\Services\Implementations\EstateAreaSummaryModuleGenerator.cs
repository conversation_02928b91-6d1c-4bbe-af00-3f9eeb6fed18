using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using NPOI.SS.UserModel;
using NPOI.XWPF.UserModel;
using RESClient.Services;

namespace RESClient.Services.Implementations
{
    /// <summary>
    /// 房产面积汇总表模块生成器
    /// </summary>
    public class EstateAreaSummaryModuleGenerator : IReportModuleGenerator
    {
        private Dictionary<string, string> _totalData;
        private ArchiveExtractionService _archiveExtractionService;

        /// <summary>
        /// 模块名称
        /// </summary>
        public string ModuleName => "房产面积汇总表";

        /// <summary>
        /// 生成报告模块
        /// </summary>
        /// <param name="parameters">生成参数</param>
        /// <param name="progressCallback">进度回调</param>
        /// <returns>生成结果</returns>
        public async Task<bool> GenerateAsync(Dictionary<string, object> parameters, Action<int, string> progressCallback)
        {
            _archiveExtractionService = new ArchiveExtractionService();

            try
            {
                _totalData = null;
                // 记录开始进度
                progressCallback?.Invoke(0, $"[INFO]开始生成{ModuleName}...");

                // 1. 获取选择的数据路径
                if (!parameters.TryGetValue("DataFolder", out object dataDirectoryObj) || dataDirectoryObj == null)
                {
                    progressCallback?.Invoke(0, "[ERROR]未找到数据目录");
                    return false;
                }

                string dataDirectory = dataDirectoryObj.ToString();
                if (!Directory.Exists(dataDirectory))
                {
                    progressCallback?.Invoke(0, $"[ERROR]数据目录不存在: {dataDirectory}");
                    return false;
                }

                progressCallback?.Invoke(5, "[INFO]查找CGB.xls文件...");

                // 2. 查找CGB.xls文件（从7z压缩包中提取）
                string excelFile = FindCGBFile(dataDirectory, progressCallback);
                if (string.IsNullOrEmpty(excelFile))
                {
                    progressCallback?.Invoke(25, "[ERROR]未找到CGB.xls文件");
                    return false;
                }

                progressCallback?.Invoke(30, $"[INFO]找到CGB文件: {Path.GetFileName(excelFile)}");

                // 3. 读取Excel数据
                progressCallback?.Invoke(30, "[INFO]读取Excel数据...");
                var summaryData = ReadExcelData(excelFile, progressCallback);
                if (summaryData == null || summaryData.Count == 0)
                {
                    progressCallback?.Invoke(30, "[ERROR]未能读取到有效数据");
                    return false;
                }

                progressCallback?.Invoke(50, $"[SUCCESS]成功读取{summaryData.Count}条房产面积汇总数据");

                // 4. 获取模板路径
                string templateDirectory = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "报告模板");
                string templatePath = Path.Combine(templateDirectory, "11_房产面积汇总表", "房产面积汇总表.docx");
                if (!File.Exists(templatePath))
                {
                    progressCallback?.Invoke(50, $"[ERROR]模板文件不存在: {templatePath}");
                    return false;
                }

                // 5. 获取输出目录
                if (!parameters.TryGetValue("OutputDir", out object outputDirectoryObj) || outputDirectoryObj == null)
                {
                    progressCallback?.Invoke(50, "[ERROR]未找到输出目录");
                    return false;
                }

                string outputDirectory = outputDirectoryObj.ToString();
                if (!Directory.Exists(outputDirectory))
                {
                    Directory.CreateDirectory(outputDirectory);
                }

                // 6. 获取用户参数
                progressCallback?.Invoke(55, "[INFO]获取用户参数...");
                string userRemarks = "";

                try
                {
                    // 尝试加载用户参数，如果失败则使用默认值
                    var parametersModel = new RESClient.MVP.Models.EstateAreaSummaryParametersModel();
                    var userParameters = parametersModel.GetCurrentParameters();
                    userRemarks = userParameters.Remarks ?? "";
                    progressCallback?.Invoke(58, "[SUCCESS]用户参数加载成功");
                }
                catch (Exception ex)
                {
                    progressCallback?.Invoke(58, $"[WARNING]用户参数加载失败，使用默认值: {ex.Message}");
                    userRemarks = "";
                }

                // 7. 生成Word文档
                progressCallback?.Invoke(60, "[INFO]生成Word文档...");
                string outputPath = Path.Combine(outputDirectory, $"{ModuleName}.docx");
                bool result = GenerateWordDocument(templatePath, outputPath, summaryData, userRemarks, progressCallback);

                progressCallback?.Invoke(100, result ? $"[SUCCESS]{ModuleName}生成完成" : $"[ERROR]{ModuleName}生成失败");
                return result;
            }
            catch (Exception ex)
            {
                progressCallback?.Invoke(100, $"[ERROR]{ModuleName}生成失败: {ex.Message}");
                return false;
            }
            finally
            {
                // 清理临时文件
                _archiveExtractionService?.Dispose();
            }
        }

        /// <summary>
        /// 查找CGB.xls文件（支持从7z压缩包中提取）
        /// </summary>
        /// <param name="directoryPath">目录路径</param>
        /// <param name="progressCallback">进度回调</param>
        /// <returns>文件路径</returns>
        private string FindCGBFile(string directoryPath, Action<int, string> progressCallback = null)
        {
            // 第一步：尝试查找包含4个指定工作表的Excel文件（最高优先级）
            progressCallback?.Invoke(5, "[INFO]查找包含标准工作表的Excel文件...");
            string standardExcelFile = FindStandardExcelFile(directoryPath, progressCallback);
            if (!string.IsNullOrEmpty(standardExcelFile))
            {
                progressCallback?.Invoke(25, "[INFO]找到包含标准工作表的Excel文件");
                return standardExcelFile;
            }

            // 第二步：尝试直接查找CGB.xls文件（向后兼容）
            progressCallback?.Invoke(10, "[INFO]查找CGB.xls文件...");
            var files = Directory.GetFiles(directoryPath, "*.xls*", SearchOption.AllDirectories)
                .Where(file => Path.GetFileName(file).ToLower().Contains("cgb"))
                .ToArray();

            if (files.Any())
            {
                progressCallback?.Invoke(25, "[INFO]找到直接的CGB.xls文件");
                return files[0];
            }

            // 第三步：如果没有找到直接的CGB文件，尝试从7z压缩包中提取
            progressCallback?.Invoke(15, "[INFO]未找到直接的CGB文件，尝试从成果包.7z中提取...");
            return _archiveExtractionService?.ExtractAndFindCGBFile(directoryPath, progressCallback);
        }

        /// <summary>
        /// 查找包含标准工作表的Excel文件
        /// </summary>
        /// <param name="directoryPath">目录路径</param>
        /// <param name="progressCallback">进度回调</param>
        /// <returns>符合条件的Excel文件路径，如果未找到则返回null</returns>
        private string FindStandardExcelFile(string directoryPath, Action<int, string> progressCallback = null)
        {
            try
            {
                // 查找所有Excel文件
                var excelFiles = Directory.GetFiles(directoryPath, "*.xls*", SearchOption.AllDirectories);

                progressCallback?.Invoke(7, $"[INFO]找到 {excelFiles.Length} 个Excel文件，开始验证工作表...");

                foreach (string filePath in excelFiles)
                {
                    try
                    {
                        if (ValidateExcelFileStructure(filePath))
                        {
                            progressCallback?.Invoke(20, $"[INFO]验证通过: {Path.GetFileName(filePath)}");
                            return filePath;
                        }
                    }
                    catch (Exception ex)
                    {
                        progressCallback?.Invoke(8, $"[WARNING]验证文件失败 {Path.GetFileName(filePath)}: {ex.Message}");
                        continue;
                    }
                }

                progressCallback?.Invoke(10, "[INFO]未找到包含标准工作表的Excel文件");
                return null;
            }
            catch (Exception ex)
            {
                progressCallback?.Invoke(10, $"[WARNING]查找标准Excel文件时出错: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 验证Excel文件是否包含4个指定的工作表
        /// </summary>
        /// <param name="filePath">Excel文件路径</param>
        /// <returns>是否符合条件</returns>
        private bool ValidateExcelFileStructure(string filePath)
        {
            // 定义必需的工作表名称
            var requiredSheets = new HashSet<string>
            {
                "房屋建筑面积总表",
                "共有建筑面积的分摊",
                "房产分户面积统计表",
                "楼盘表信息"
            };

            try
            {
                using (var stream = new FileStream(filePath, FileMode.Open, FileAccess.Read, FileShare.ReadWrite))
                {
                    IWorkbook workbook = WorkbookFactory.Create(stream);

                    // 检查工作表数量是否为4
                    if (workbook.NumberOfSheets != 4)
                    {
                        return false;
                    }

                    // 检查工作表名称是否完全匹配
                    var actualSheets = new HashSet<string>();
                    for (int i = 0; i < workbook.NumberOfSheets; i++)
                    {
                        actualSheets.Add(workbook.GetSheetName(i));
                    }

                    // 必须完全匹配所有4个工作表名称
                    return requiredSheets.SetEquals(actualSheets);
                }
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 读取Excel数据
        /// </summary>
        /// <param name="filePath">Excel文件路径</param>
        /// <param name="progressCallback">进度回调</param>
        /// <returns>汇总数据</returns>
        private List<Dictionary<string, string>> ReadExcelData(string filePath, Action<int, string> progressCallback)
        {
            var summaryData = new List<Dictionary<string, string>>();
            _totalData = null;
            
            try
            {
                if (string.IsNullOrWhiteSpace(filePath) || !File.Exists(filePath))
                {
                    progressCallback?.Invoke(10, $"[ERROR]Excel文件不存在：{filePath}");
                    return summaryData;
                }

                using (var stream = new FileStream(filePath, FileMode.Open, FileAccess.Read, FileShare.ReadWrite))
                {
                    progressCallback?.Invoke(10, "[INFO]正在打开Excel文件...");
                    IWorkbook workbook = WorkbookFactory.Create(stream);
                    
                    ISheet worksheet = FindSheet(workbook, "房屋建筑面积总表");
                    
                    if (worksheet == null)
                    {
                        progressCallback?.Invoke(20, "[ERROR]未找到房屋建筑面积总表工作表");
                        return summaryData;
                    }

                    progressCallback?.Invoke(30, "[INFO]找到房屋建筑面积总表，开始解析数据...");
                    
                    // 从第4行开始遍历数据
                    string currentBuilding = "";
                    string currentBuildingArea = "";
                    
                    for (int rowIndex = 3; rowIndex <= worksheet.LastRowNum; rowIndex++)
                    {
                        IRow row = worksheet.GetRow(rowIndex);
                        if (row == null) continue;
                        
                        // 检查是否为合计行
                        string firstCell = GetCellValueAsString(row.GetCell(0)).Trim();
                        if (firstCell == "合计")
                        {
                            ProcessTotalRow(row);
                            continue;
                        }
                        
                        // 获取栋号和栋建筑面积
                        string buildingNumber = GetCellValueAsString(row.GetCell(0));
                        string buildingArea = GetCellValueAsString(row.GetCell(1));
                        
                        // 如果栋号不为空，更新当前栋号和栋建筑面积
                        if (!string.IsNullOrWhiteSpace(buildingNumber))
                        {
                            currentBuilding = buildingNumber;
                            currentBuildingArea = buildingArea;
                        }
                        
                        // 处理商业数据
                        ProcessTypeData(summaryData, row, currentBuilding, currentBuildingArea, 
                                      2, 3, 4, "商业");
                        
                        // 处理住宅数据
                        ProcessTypeData(summaryData, row, currentBuilding, currentBuildingArea, 
                                      5, 6, 7, "住宅");
                        
                        // 处理车位数据
                        ProcessTypeData(summaryData, row, currentBuilding, currentBuildingArea, 
                                      8, 9, 10, "车位");
                        
                        // 处理公寓数据
                        ProcessTypeData(summaryData, row, currentBuilding, currentBuildingArea, 
                                      11, 12, 13, "公寓");
                        
                        // 处理其他数据
                        ProcessTypeData(summaryData, row, currentBuilding, currentBuildingArea, 
                                      14, 15, 16, "其他", 17);
                        
                        // 处理设备用房数据
                        ProcessTypeData(summaryData, row, currentBuilding, currentBuildingArea, 
                                      18, 19, 20, "设备用房", 21);
                    }
                    
                    progressCallback?.Invoke(40, $"[SUCCESS]数据解析完成，共{summaryData.Count}行数据");
                }
            }
            catch (Exception ex)
            {
                progressCallback?.Invoke(10, $"[ERROR]读取Excel出错: {ex.Message}");
            }
            
            return summaryData;
        }

        /// <summary>
        /// 处理合计行数据
        /// </summary>
        /// <param name="row">合计行</param>
        private void ProcessTotalRow(IRow row)
        {
            if (row == null) return;

            _totalData = new Dictionary<string, string>();

            string totalBuildingArea = GetCellValueAsString(row.GetCell(1));

            double totalArea = 0;
            // 面积列: 商业(3), 住宅(6), 车位(9), 公寓(12), 其他(15), 设备用房(19)
            int[] areaColumns = { 3, 6, 9, 12, 15, 19 };
            foreach (var colIndex in areaColumns)
            {
                string cellValue = GetCellValueAsString(row.GetCell(colIndex));
                if (double.TryParse(cellValue, out double area))
                {
                    totalArea += area;
                }
            }

            double totalCount = 0;
            // 套数列: 商业(4), 住宅(7), 车位(10), 公寓(13), 其他(16), 设备用房(20)
            int[] countColumns = { 4, 7, 10, 13, 16, 20 };
            foreach (var colIndex in countColumns)
            {
                string cellValue = GetCellValueAsString(row.GetCell(colIndex));
                if (double.TryParse(cellValue, out double count))
                {
                    totalCount += count;
                }
            }
            
            _totalData["栋号"] = "合计";
            _totalData["栋建筑面积"] = totalBuildingArea;
            _totalData["面积"] = totalArea.ToString("F2");
            _totalData["套数"] = ((int)totalCount).ToString();
        }

        /// <summary>
        /// 处理各类型数据
        /// </summary>
        private void ProcessTypeData(List<Dictionary<string, string>> summaryData, IRow row, 
                                   string buildingNumber, string buildingArea,
                                   int floorColIndex, int areaColIndex, int countColIndex, 
                                   string type, int purposeColIndex = -1)
        {
            try
            {
                // 如果栋号为空，不处理数据
                if (string.IsNullOrWhiteSpace(buildingNumber))
                    return;
                    
                // 安全获取单元格值
                string floor = SafeGetCellValue(row, floorColIndex);
                
                // 如果楼层为空，跳过
                if (string.IsNullOrWhiteSpace(floor))
                    return;
                    
                string area = SafeGetCellValue(row, areaColIndex);
                string count = SafeGetCellValue(row, countColIndex);
                string purpose = purposeColIndex >= 0 ? SafeGetCellValue(row, purposeColIndex) : type;
                
                // 创建数据项
                var item = new Dictionary<string, string>
                {
                    { "栋号", buildingNumber },
                    { "栋建筑面积", buildingArea },
                    { "楼层", floor },
                    { "面积", area },
                    { "套数", count },
                    { "设计用途", purpose }
                };
                
                summaryData.Add(item);
            }
            catch
            {
                // 捕获任何异常但继续处理
            }
        }
        
        /// <summary>
        /// 安全获取单元格值
        /// </summary>
        private string SafeGetCellValue(IRow row, int columnIndex)
        {
            if (row == null || columnIndex < 0 || columnIndex >= row.LastCellNum)
                return string.Empty;
                
            NPOI.SS.UserModel.ICell cell = row.GetCell(columnIndex);
            return GetCellValueAsString(cell);
        }

        /// <summary>
        /// 查找工作表
        /// </summary>
        /// <param name="workbook">Excel工作簿</param>
        /// <param name="sheetName">工作表名</param>
        /// <returns>工作表</returns>
        private ISheet FindSheet(IWorkbook workbook, string sheetName)
        {
            // 首先尝试通过名称直接查找工作表
            ISheet sheet = workbook.GetSheet(sheetName);
            if (sheet != null) return sheet;

            // 其次尝试通过包含名称查找
            for (int i = 0; i < workbook.NumberOfSheets; i++)
            {
                if (workbook.GetSheetName(i).Contains(sheetName))
                {
                    return workbook.GetSheetAt(i);
                }
            }
            
            // 如果通过名称未找到，检查每个工作表的内容
            for (int i = 0; i < workbook.NumberOfSheets; i++)
            {
                ISheet currentSheet = workbook.GetSheetAt(i);
                for (int rowIndex = 0; rowIndex <= Math.Min(currentSheet.LastRowNum, 5); rowIndex++)
                {
                    IRow row = currentSheet.GetRow(rowIndex);
                    if (row == null) continue;
                    for (int colIndex = 0; colIndex < Math.Min((int)row.LastCellNum, (int)10); colIndex++)
                    {
                        NPOI.SS.UserModel.ICell cell = row.GetCell(colIndex);
                        if (cell != null)
                        {
                            string cellText = GetCellValueAsString(cell);
                            if (cellText.Contains("房屋建筑面积总表") || cellText.Contains("房产面积汇总"))
                            {
                                return currentSheet;
                            }
                        }
                    }
                }
            }
            
            // 如果没有找到匹配的工作表，返回第一个工作表（如果存在）
            return workbook.NumberOfSheets > 0 ? workbook.GetSheetAt(0) : null;
        }

        /// <summary>
        /// 获取单元格的字符串值
        /// </summary>
        /// <param name="cell">单元格</param>
        /// <returns>字符串值</returns>
        private string GetCellValueAsString(NPOI.SS.UserModel.ICell cell)
        {
            if (cell == null)
                return string.Empty;

            try
            {
                switch (cell.CellType)
                {
                    case CellType.String:
                        return cell.StringCellValue;
                    case CellType.Numeric:
                        if (DateUtil.IsCellDateFormatted(cell))
                        {
                            DateTime? dateTimeValue = cell.DateCellValue;
                            if (dateTimeValue.HasValue)
                            {
                                DateTime dateValue = dateTimeValue.Value;
                                return dateValue.Year + "-" + dateValue.Month.ToString("00") + "-" + dateValue.Day.ToString("00");
                            }
                            else
                            {
                                return string.Empty;
                            }
                        }
                        return cell.NumericCellValue.ToString();
                    case CellType.Boolean:
                        return cell.BooleanCellValue.ToString();
                    case CellType.Formula:
                        try
                        {
                            // 尝试获取公式计算后的数值结果
                            return cell.NumericCellValue.ToString();
                        }
                        catch
                        {
                            try
                            {
                                // 如果数值获取失败，尝试获取字符串结果
                                return cell.StringCellValue;
                            }
                            catch
                            {
                                return string.Empty;
                            }
                        }
                    default:
                        return string.Empty;
                }
            }
            catch
            {
                return string.Empty;
            }
        }

        /// <summary>
        /// 生成Word文档
        /// </summary>
        /// <param name="templatePath">模板路径</param>
        /// <param name="outputPath">输出路径</param>
        /// <param name="summaryData">汇总数据</param>
        /// <param name="userRemarks">用户输入的备注</param>
        /// <param name="progressCallback">进度回调</param>
        /// <returns>是否成功</returns>
        private bool GenerateWordDocument(string templatePath, string outputPath, List<Dictionary<string, string>> summaryData, string userRemarks, Action<int, string> progressCallback)
        {
            string tempPath = Path.Combine(Path.GetDirectoryName(outputPath), $"temp_{Guid.NewGuid()}.docx");
            FileStream fileStream = null;
            
            try
            {
                // 复制模板文件到临时文件
                File.Copy(templatePath, tempPath, true);

                // 打开临时文件
                fileStream = new FileStream(tempPath, FileMode.Open, FileAccess.ReadWrite);
                using (var document = new XWPFDocument(fileStream))
                {
                    progressCallback?.Invoke(70, $"[INFO]处理 {summaryData.Count} 条数据");
                    
                    // 查找模板中的表格
                    if (document.Tables.Count == 0)
                    {
                        progressCallback?.Invoke(70, "[ERROR]错误：模板中没有找到表格");
                        return false;
                    }
                    
                    // 使用第一个表格作为模板
                    var table = document.Tables[0];
                    
                    // 确保表格至少有两行（表头行和数据模板行）
                    if (table.NumberOfRows < 2)
                    {
                        progressCallback?.Invoke(70, "[ERROR]表格模板需要至少2行（1行表头和1行数据模板）");
                        return false;
                    }
                    
                    // 第二行为数据模板行
                    XWPFTableRow templateRow = table.GetRow(1);
                    
                    // 移除模板行中的默认内容（保留格式）
                    ClearRowContent(templateRow);
                    
                    // 填充数据
                    for (int i = 0; i < summaryData.Count; i++)
                    {
                        var rowData = summaryData[i];
                        XWPFTableRow tableRow;
                        
                        if (i == 0)
                        {
                            // 第一行数据直接使用模板行
                            tableRow = templateRow;
                        }
                        else
                        {
                            // 创建新行并应用模板行的格式
                            tableRow = table.CreateRow();
                            CopyRowFormat(templateRow, tableRow);
                        }
                        
                        // 填充单元格数据
                        FillRowData(tableRow, rowData);
                    }

                    // 添加合计行
                    if (_totalData != null)
                    {
                        XWPFTableRow totalRow = table.CreateRow();
                        CopyRowFormat(templateRow, totalRow);
                        FillTotalRowData(totalRow, _totalData);
                    }
                    
                    // 合并单元格
                    MergeColumnCells(table, 0); // 合并"栋号"列
                    MergeColumnCells(table, 1); // 合并"栋建筑面积"列
                    
                    // 确保表格有边框
                    EnsureTableHasBorders(table);

                    // 处理变量替换
                    progressCallback?.Invoke(90, "[INFO]处理变量替换...");
                    ReplaceVariablesInDocument(document, userRemarks);

                    // 保存到最终输出文件
                    using (var outStream = new FileStream(outputPath, FileMode.Create, FileAccess.Write))
                    {
                        document.Write(outStream);
                    }
                }
                
                progressCallback?.Invoke(95, $"[SUCCESS]成功生成面积汇总表，共{summaryData.Count}行数据");
                return true;
            }
            catch (Exception ex)
            {
                progressCallback?.Invoke(80, $"[ERROR]生成Word文档失败: {ex.Message}");
                return false;
            }
            finally
            {
                // 关闭文件流
                if (fileStream != null)
                {
                    try
                    {
                        fileStream.Close();
                        fileStream.Dispose();
                    }
                    catch { /* 忽略关闭文件流错误 */ }
                }
                
                // 删除临时文件
                try
                {
                    if (File.Exists(tempPath))
                    {
                        File.Delete(tempPath);
                    }
                }
                catch { /* 忽略临时文件删除失败的错误 */ }
            }
        }

        /// <summary>
        /// 确保表格有可见边框
        /// </summary>
        /// <param name="table">表格</param>
        private void EnsureTableHasBorders(XWPFTable table)
        {
            try
            {
                // 确保表格有tblPr
                if (table.GetCTTbl().tblPr == null)
                {
                    table.GetCTTbl().AddNewTblPr();
                }
                
                // 检查是否有边框设置
                if (table.GetCTTbl().tblPr.tblBorders == null)
                {
                    // 创建默认边框设置
                    var borders = table.GetCTTbl().tblPr.AddNewTblBorders();
                    
                    // 设置上边框
                    borders.AddNewTop().val = NPOI.OpenXmlFormats.Wordprocessing.ST_Border.single;
                    borders.top.sz = 4;
                    borders.top.space = 0;
                    borders.top.color = "auto";
                    
                    // 设置下边框
                    borders.AddNewBottom().val = NPOI.OpenXmlFormats.Wordprocessing.ST_Border.single;
                    borders.bottom.sz = 4;
                    borders.bottom.space = 0;
                    borders.bottom.color = "auto";
                    
                    // 设置左边框
                    borders.AddNewLeft().val = NPOI.OpenXmlFormats.Wordprocessing.ST_Border.single;
                    borders.left.sz = 4;
                    borders.left.space = 0;
                    borders.left.color = "auto";
                    
                    // 设置右边框
                    borders.AddNewRight().val = NPOI.OpenXmlFormats.Wordprocessing.ST_Border.single;
                    borders.right.sz = 4;
                    borders.right.space = 0;
                    borders.right.color = "auto";
                    
                    // 设置内部水平边框
                    borders.AddNewInsideH().val = NPOI.OpenXmlFormats.Wordprocessing.ST_Border.single;
                    borders.insideH.sz = 4;
                    borders.insideH.space = 0;
                    borders.insideH.color = "auto";
                    
                    // 设置内部垂直边框
                    borders.AddNewInsideV().val = NPOI.OpenXmlFormats.Wordprocessing.ST_Border.single;
                    borders.insideV.sz = 4;
                    borders.insideV.space = 0;
                    borders.insideV.color = "auto";
                }
            }
            catch (Exception ex)
            {
                // 捕获异常但不中断流程
                System.Diagnostics.Debug.WriteLine($"设置表格边框失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 清除行内容但保留格式
        /// </summary>
        /// <param name="row">表格行</param>
        private void ClearRowContent(XWPFTableRow row)
        {
            if (row == null) return;
            
            foreach (var cell in row.GetTableCells())
            {
                // 保留单元格但清空文本
                if (cell.Paragraphs.Count > 0)
                {
                    foreach (var paragraph in cell.Paragraphs)
                    {
                        while (paragraph.Runs.Count > 0)
                        {
                            paragraph.RemoveRun(0);
                        }
                        paragraph.CreateRun().SetText(" ");
                    }
                }
            }
        }

        /// <summary>
        /// 复制行格式
        /// </summary>
        /// <param name="source">源行</param>
        /// <param name="target">目标行</param>
        private void CopyRowFormat(XWPFTableRow source, XWPFTableRow target)
        {
            if (source == null || target == null) return;
            
            try
            {
                // 复制行高
                target.Height = source.Height;
                
                // 确保目标行有足够的单元格
                while (target.GetTableCells().Count < source.GetTableCells().Count)
                {
                    target.CreateCell();
                }
                
                // 复制单元格格式
                for (int i = 0; i < Math.Min(source.GetTableCells().Count, target.GetTableCells().Count); i++)
                {
                    XWPFTableCell sourceCell = source.GetCell(i);
                    XWPFTableCell targetCell = target.GetCell(i);
                    
                    if (sourceCell == null || targetCell == null) continue;
                    
                    // 复制颜色
                    try
                    {
                        targetCell.SetColor(sourceCell.GetColor());
                    }
                    catch { /* 忽略颜色设置错误 */ }
                    
                    // 复制垂直对齐
                    try
                    {
                        if (sourceCell.GetVerticalAlignment() != null)
                        {
                            targetCell.SetVerticalAlignment(sourceCell.GetVerticalAlignment().Value);
                        }
                    }
                    catch { /* 忽略垂直对齐设置错误 */ }
                    
                    // 清空目标单元格内容但保留格式
                    while (targetCell.Paragraphs.Count > 0)
                    {
                        targetCell.RemoveParagraph(0);
                    }
                    
                    // 创建新段落并复制格式
                    var paragraph = targetCell.AddParagraph();
                    if (sourceCell.Paragraphs.Count > 0)
                    {
                        var sourcePara = sourceCell.Paragraphs[0];
                        try
                        {
                            paragraph.Alignment = sourcePara.Alignment;
                            paragraph.SpacingBefore = sourcePara.SpacingBefore;
                            paragraph.SpacingAfter = sourcePara.SpacingAfter;
                            
                            if (sourcePara.IsWordWrapped)
                                paragraph.IsWordWrapped = sourcePara.IsWordWrapped;
                        }
                        catch { /* 忽略段落格式设置错误 */ }
                    }
                }
            }
            catch { /* 忽略行格式复制错误 */ }
        }

        /// <summary>
        /// 填充行数据
        /// </summary>
        /// <param name="row">表格行</param>
        /// <param name="data">数据</param>
        private void FillRowData(XWPFTableRow row, Dictionary<string, string> data)
        {
            if (row == null || data == null)
                return;
                
            try
            {
                // 固定列映射
                var columnMapping = new Dictionary<string, int>
                {
                    { "栋号", 0 },
                    { "栋建筑面积", 1 },
                    { "楼层", 2 },
                    { "面积", 3 },
                    { "套数", 4 },
                    { "设计用途", 5 }
                };
                
                // 填充单元格
                foreach (var item in data)
                {
                    if (columnMapping.TryGetValue(item.Key, out int columnIndex))
                    {
                        try
                        {
                            // 确保列存在
                            while (columnIndex >= row.GetTableCells().Count)
                            {
                                row.CreateCell();
                            }
                            
                            // 设置单元格值
                            var cell = row.GetCell(columnIndex);
                            if (cell != null)
                            {
                                SetCellText(cell, item.Value);
                            }
                        }
                        catch { /* 忽略单元格填充错误 */ }
                    }
                }
            }
            catch { /* 忽略行数据填充错误 */ }
        }
        
        /// <summary>
        /// 设置单元格文本，保留格式
        /// </summary>
        /// <param name="cell">单元格</param>
        /// <param name="text">文本内容</param>
        private void SetCellText(XWPFTableCell cell, string text)
        {
            if (cell == null) return;
            
            try
            {
                // 确保单元格有段落
                var paragraph = cell.Paragraphs.Count > 0 ? 
                    cell.Paragraphs[0] : 
                    cell.AddParagraph();
                
                // 清空现有文本
                while (paragraph.Runs.Count > 0)
                {
                    paragraph.RemoveRun(0);
                }
                
                // 确保文本不是null
                string safeText = text ?? string.Empty;
                
                // 添加新文本，确保空文本显示为空格
                var run = paragraph.CreateRun();
                run.SetText(string.IsNullOrEmpty(safeText) ? " " : safeText);
                run.FontFamily = "仿宋";
                run.FontSize = 12;

                // 设置文本在单元格中的对齐方式（居中）
                paragraph.Alignment = ParagraphAlignment.CENTER;
            }
            catch { /* 忽略单元格文本设置错误 */ }
        }

        /// <summary>
        /// 填充合计行数据
        /// </summary>
        /// <param name="row">表格行</param>
        /// <param name="data">数据</param>
        private void FillTotalRowData(XWPFTableRow row, Dictionary<string, string> data)
        {
            if (row == null || data == null)
                return;

            data.TryGetValue("栋号", out string donghao);
            data.TryGetValue("栋建筑面积", out string dongjianzhumianji);
            data.TryGetValue("面积", out string mianji);
            data.TryGetValue("套数", out string taoshu);

            SetCellText(row.GetCell(0), donghao ?? "");
            SetCellText(row.GetCell(1), dongjianzhumianji ?? "");
            SetCellText(row.GetCell(2), ""); // 楼层
            SetCellText(row.GetCell(3), mianji ?? "");
            SetCellText(row.GetCell(4), taoshu ?? "");
            SetCellText(row.GetCell(5), ""); // 设计用途
        }

        /// <summary>
        /// 判断是否为空行
        /// </summary>
        /// <param name="row">表格行</param>
        /// <returns>是否为空行</returns>
        private bool IsEmptyRow(IRow row)
        {
            if (row == null) return true;
            
            try
            {
                // 检查关键列是否全为空：栋号(0), 商业楼层(2), 住宅楼层(5), 车位楼层(8), 公寓楼层(11), 其他楼层(14), 设备用房楼层(18)
                int[] keyColumns = new int[] { 0, 2, 5, 8, 11, 14, 18 };
                
                foreach (int colIndex in keyColumns)
                {
                    var cell = row.GetCell(colIndex);
                    if (cell != null && !string.IsNullOrWhiteSpace(GetCellValueAsString(cell)))
                    {
                        return false;  // 只要有一个关键列有数据，就不是空行
                    }
                }
            }
            catch { /* 忽略检查错误 */ }
            
            return true;  // 所有关键列都为空，视为空行
        }

        /// <summary>
        /// 合并表格列中垂直相邻的相同单元格
        /// </summary>
        /// <param name="table">要操作的表格</param>
        /// <param name="colIndex">要合并的列索引</param>
        private void MergeColumnCells(XWPFTable table, int colIndex)
        {
            // 数据行从索引1开始
            for (int rowIndex = 1; rowIndex < table.NumberOfRows; rowIndex++)
            {
                var startCell = table.GetRow(rowIndex)?.GetCell(colIndex);
                if (startCell == null) continue;

                string startText = startCell.GetText().Trim();
                if (string.IsNullOrWhiteSpace(startText)) continue;

                int mergeEndRow = rowIndex;
                for (int nextRowIndex = rowIndex + 1; nextRowIndex < table.NumberOfRows; nextRowIndex++)
                {
                    var nextCell = table.GetRow(nextRowIndex)?.GetCell(colIndex);
                    if (nextCell == null || nextCell.GetText().Trim() != startText)
                    {
                        break;
                    }
                    mergeEndRow = nextRowIndex;
                }

                if (mergeEndRow > rowIndex)
                {
                    // 合并单元格
                    var startTcPr = startCell.GetCTTc().tcPr ?? startCell.GetCTTc().AddNewTcPr();
                    if (startTcPr.vMerge == null)
                    {
                        startTcPr.AddNewVMerge().val = NPOI.OpenXmlFormats.Wordprocessing.ST_Merge.restart;
                    }

                    for (int i = rowIndex + 1; i <= mergeEndRow; i++)
                    {
                        var cellToMerge = table.GetRow(i)?.GetCell(colIndex);
                        if(cellToMerge != null)
                        {
                            var tcPr = cellToMerge.GetCTTc().tcPr ?? cellToMerge.GetCTTc().AddNewTcPr();
                            if (tcPr.vMerge == null)
                            {
                                tcPr.AddNewVMerge().val = NPOI.OpenXmlFormats.Wordprocessing.ST_Merge.@continue;
                            }
                        }
                    }
                    
                    // 跳过已合并的行
                    rowIndex = mergeEndRow;
                }
            }
        }

        /// <summary>
        /// 是否可用
        /// </summary>
        /// <param name="parameters">检查参数</param>
        /// <returns>是否可用</returns>
        public bool IsAvailable(Dictionary<string, object> parameters)
        {
            if (parameters == null)
                return false;

            // 检查必要参数
            if (!parameters.ContainsKey("DataFolder") || !parameters.ContainsKey("OutputDir"))
                return false;

            string dataDirectory = parameters["DataFolder"]?.ToString();

            // 检查数据目录
            if (string.IsNullOrEmpty(dataDirectory) || !Directory.Exists(dataDirectory))
                return false;

            // 检查模板文件
            string templatePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "报告模板", "11_房产面积汇总表", "房产面积汇总表.docx");
            if (!File.Exists(templatePath))
                return false;

            // 检查是否存在可用的Excel文件（标准Excel文件、CGB.xls文件或成果包.7z文件）
            try
            {
                // 首先检查标准Excel文件（包含4个指定工作表）
                var excelFiles = Directory.GetFiles(dataDirectory, "*.xls*", SearchOption.AllDirectories);
                foreach (string filePath in excelFiles)
                {
                    try
                    {
                        if (ValidateExcelFileStructure(filePath))
                        {
                            return true; // 找到标准Excel文件
                        }
                    }
                    catch
                    {
                        // 忽略验证失败的文件，继续检查其他文件
                        continue;
                    }
                }

                // 检查直接的CGB文件
                var cgbFiles = Directory.GetFiles(dataDirectory, "*.xls*", SearchOption.AllDirectories)
                    .Where(file => Path.GetFileName(file).ToLower().Contains("cgb"))
                    .ToArray();

                if (cgbFiles.Any())
                    return true;

                // 检查是否存在成果包.7z文件
                var archiveFiles = Directory.GetFiles(dataDirectory, "*.7z", SearchOption.AllDirectories)
                    .Where(file => Path.GetFileName(file).Contains("成果包"))
                    .ToArray();

                return archiveFiles.Any();
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 在文档中替换变量
        /// </summary>
        /// <param name="document">Word文档</param>
        /// <param name="userRemarks">用户备注</param>
        private void ReplaceVariablesInDocument(XWPFDocument document, string userRemarks)
        {
            try
            {
                // 创建变量映射
                var variables = new Dictionary<string, string>
                {
                    { "${备注}", string.IsNullOrWhiteSpace(userRemarks) ? "\\" : userRemarks }
                };

                // 替换文档中所有段落的变量
                foreach (XWPFParagraph paragraph in document.Paragraphs)
                {
                    ReplaceVariablesInParagraph(paragraph, variables);
                }

                // 替换表格中的变量
                foreach (XWPFTable table in document.Tables)
                {
                    foreach (XWPFTableRow row in table.Rows)
                    {
                        foreach (XWPFTableCell cell in row.GetTableCells())
                        {
                            foreach (XWPFParagraph paragraph in cell.Paragraphs)
                            {
                                ReplaceVariablesInParagraph(paragraph, variables);
                            }
                        }
                    }
                }

                // 替换页眉中的变量
                foreach (XWPFHeader header in document.HeaderList)
                {
                    foreach (XWPFParagraph paragraph in header.Paragraphs)
                    {
                        ReplaceVariablesInParagraph(paragraph, variables);
                    }
                }

                // 替换页脚中的变量
                foreach (XWPFFooter footer in document.FooterList)
                {
                    foreach (XWPFParagraph paragraph in footer.Paragraphs)
                    {
                        ReplaceVariablesInParagraph(paragraph, variables);
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"变量替换失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 在段落中替换变量
        /// </summary>
        /// <param name="paragraph">段落</param>
        /// <param name="variables">变量映射</param>
        private void ReplaceVariablesInParagraph(XWPFParagraph paragraph, Dictionary<string, string> variables)
        {
            if (paragraph == null || variables == null)
                return;

            try
            {
                string fullText = paragraph.Text;
                if (string.IsNullOrEmpty(fullText))
                    return;

                // 检查是否包含需要替换的变量
                bool hasVariables = false;
                foreach (var variable in variables.Keys)
                {
                    if (fullText.Contains(variable))
                    {
                        hasVariables = true;
                        break;
                    }
                }

                if (!hasVariables)
                    return;

                // 执行替换
                string newText = fullText;
                foreach (var variable in variables)
                {
                    newText = newText.Replace(variable.Key, variable.Value);
                }

                if (newText != fullText)
                {
                    // 保存第一个运行的格式
                    XWPFRun templateRun = paragraph.Runs.Count > 0 ? paragraph.Runs[0] : null;

                    // 清除所有运行
                    for (int i = paragraph.Runs.Count - 1; i >= 0; i--)
                    {
                        paragraph.RemoveRun(i);
                    }

                    // 创建新运行并设置文本
                    var newRun = paragraph.CreateRun();
                    newRun.SetText(newText);

                    // 复制格式
                    if (templateRun != null)
                    {
                        try
                        {
                            newRun.FontFamily = templateRun.FontFamily ?? "仿宋";
                            newRun.FontSize = templateRun.FontSize > 0 ? templateRun.FontSize : 12;
                            newRun.IsBold = templateRun.IsBold;
                            newRun.IsItalic = templateRun.IsItalic;
                            newRun.SetColor(templateRun.GetColor());
                        }
                        catch
                        {
                            // 如果格式复制失败，使用默认格式
                            newRun.FontFamily = "仿宋";
                            newRun.FontSize = 12;
                        }
                    }
                    else
                    {
                        // 使用默认格式
                        newRun.FontFamily = "仿宋";
                        newRun.FontSize = 12;
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"段落变量替换失败: {ex.Message}");
            }
        }
    }
}