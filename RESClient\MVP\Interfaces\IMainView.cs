using System;
using System.Collections.Generic;
using System.Windows.Forms;
using RESClient.MVP.Views;

namespace RESClient.MVP.Interfaces
{
    /// <summary>
    /// 主视图接口
    /// </summary>
    public interface IMainView : IView
    {
        /// <summary>
        /// 获取窗体所在线程是否需要调用Invoke
        /// </summary>
        bool InvokeRequired { get; }

        /// <summary>
        /// 在UI线程中执行方法
        /// </summary>
        /// <param name="method">要执行的方法</param>
        void Invoke(Action method);

        /// <summary>
        /// 生成报告请求事件
        /// </summary>
        event EventHandler GenerateReportRequested;

        /// <summary>
        /// 测试分层图请求事件
        /// </summary>
        event EventHandler TestFloorPlanRequested;

        /// <summary>
        /// 管理模板请求事件
        /// </summary>
        event EventHandler ManageTemplatesRequested;

        /// <summary>
        /// 更新目录信息
        /// </summary>
        /// <param name="directoryInfo">目录信息字典</param>
        void UpdateDirectoryInfo(Dictionary<string, string> directoryInfo);

        /// <summary>
        /// 更新模板列表
        /// </summary>
        /// <param name="templates">模板列表</param>
        void UpdateTemplateList(List<string> templates);

        /// <summary>
        /// 获取选中的模块
        /// </summary>
        /// <returns>选中的模块名称列表</returns>
        List<string> GetSelectedModules();

        /// <summary>
        /// 获取选择的数据文件夹
        /// </summary>
        /// <returns>数据文件夹路径</returns>
        string GetSelectedDataFolder();

        /// <summary>
        /// 显示进度条
        /// </summary>
        /// <param name="visible">是否显示</param>
        void ShowProgressBar(bool visible);

        /// <summary>
        /// 更新进度
        /// </summary>
        /// <param name="percent">百分比</param>
        /// <param name="status">状态信息</param>
        void UpdateProgress(int percent, string status);

        /// <summary>
        /// 更新模块状态
        /// </summary>
        /// <param name="moduleName">模块名称</param>
        /// <param name="status">状态</param>
        void UpdateModuleStatus(string moduleName, MainForm.ModuleStatus status);

        /// <summary>
        /// 更新所有模块状态
        /// </summary>
        /// <param name="status">状态</param>
        void UpdateAllModulesStatus(MainForm.ModuleStatus status);

        /// <summary>
        /// 重置所有模块状态为"无"
        /// </summary>
        void ResetAllModulesStatus();

        /// <summary>
        /// 更新特定模块的处理结果
        /// </summary>
        /// <param name="moduleName">模块名称</param>
        /// <param name="success">是否成功</param>
        /// <param name="message">消息</param>
        void UpdateModuleResult(string moduleName, bool success, string message = null);

        /// <summary>
        /// 显示消息
        /// </summary>
        /// <param name="message">消息内容</param>
        void ShowMessage(string message);

        /// <summary>
        /// 显示错误消息
        /// </summary>
        /// <param name="error">错误信息</param>
        void ShowError(string error);
    }
} 