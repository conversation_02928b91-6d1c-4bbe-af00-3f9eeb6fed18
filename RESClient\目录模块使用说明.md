# 目录模块使用说明

## 📋 功能概述

目录模块生成器现在已经实现了**直接复制**功能，可以将模板文件直接复制到输出目录，无需进行复杂的内容处理。

## 📁 模板文件要求

### 模板文件位置
```
报告模板/
└── 02_目录/
    └── 目录.docx    ← 目录模板文件
```

### 模板文件说明
- **文件名**: `目录.docx`
- **位置**: `报告模板/02_目录/` 目录下
- **内容**: 包含完整的目录页面格式和内容
- **格式**: Microsoft Word文档 (.docx)

## ⚙️ 工作原理

1. **检查模板**: 验证 `报告模板/02_目录/目录.docx` 文件是否存在
2. **创建输出目录**: 确保输出目录存在
3. **直接复制**: 将模板文件复制到输出目录，重命名为 `目录.docx`
4. **完成**: 生成完成，无需额外处理

## 🔧 使用方法

### 在主程序中使用
目录模块会在报告生成过程中自动调用，用户只需：
1. 确保模板文件存在
2. 选择"目录"复选框
3. 点击"生成报告"

### 测试功能
可以通过测试模式验证功能：
```bash
RESClient.exe test
```

## ✅ 优势特点

1. **简单高效**: 直接复制，无需复杂处理
2. **快速执行**: 复制操作速度快
3. **格式保持**: 完全保持原模板格式
4. **易于维护**: 只需更新模板文件即可

## 📝 注意事项

1. **模板文件必须存在**: 如果模板文件不存在，模块将报错
2. **文件权限**: 确保程序有读取模板文件和写入输出目录的权限
3. **文件覆盖**: 如果输出目录已存在同名文件，将被覆盖
4. **模板内容**: 模板文件应包含完整的目录内容，因为不会进行动态生成

## 🚀 扩展可能

如果将来需要动态生成目录内容，可以在复制后添加以下功能：
- 读取其他模块信息
- 动态更新页码
- 自动生成目录项
- 格式化目录样式

## 🔍 故障排除

### 常见问题

1. **模板文件不存在**
   - 错误信息: `[ERROR]模板文件不存在`
   - 解决方案: 确保 `报告模板/02_目录/目录.docx` 文件存在

2. **输出目录权限问题**
   - 错误信息: 文件复制失败
   - 解决方案: 检查输出目录的写入权限

3. **文件被占用**
   - 错误信息: 文件复制失败
   - 解决方案: 关闭可能打开目标文件的程序

### 调试信息
程序会输出详细的进度信息：
- `[INFO]开始生成目录...`
- `[INFO]输出目录: [路径]`
- `[INFO]找到模板文件`
- `[INFO]复制模板文件到输出目录`
- `[SUCCESS]目录生成完成`
