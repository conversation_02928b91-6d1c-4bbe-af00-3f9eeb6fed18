using System;
using System.Windows.Forms;

namespace RESClient
{
    /// <summary>
    /// UI优化效果测试运行程序
    /// </summary>
    public static class TestUIOptimizations
    {
        /// <summary>
        /// 程序入口点 - 测试UI优化效果
        /// </summary>
        [STAThread]
        public static void Main(string[] args)
        {
            // 设置应用程序的视觉样式
            Application.EnableVisualStyles();
            Application.SetCompatibleTextRenderingDefault(false);

            Console.WriteLine("=== 参数输入对话框UI优化效果测试 ===\n");

            try
            {
                // 运行UI一致性和优化效果测试
                TestParameterInputFormsUI.TestAllParameterInputForms();

                Console.WriteLine("\n=== 测试完成 ===");
                Console.WriteLine("按任意键退出...");
                Console.ReadKey();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ 测试运行失败: {ex.Message}");
                Console.WriteLine($"详细信息: {ex}");
                Console.WriteLine("\n按任意键退出...");
                Console.ReadKey();
            }
        }

        /// <summary>
        /// 交互式测试 - 显示实际的对话框供用户查看
        /// </summary>
        public static void RunInteractiveTest()
        {
            Console.WriteLine("=== 交互式UI测试 ===");
            Console.WriteLine("将依次显示各个参数输入对话框，请检查UI效果...\n");

            try
            {
                // 1. 楼栋基本信息（已优化高度）
                Console.WriteLine("1. 显示楼栋基本信息参数输入对话框（已优化高度）...");
                ShowBuildingInfoDialog();

                // 2. 房产面积汇总表（已优化高度）
                Console.WriteLine("2. 显示房产面积汇总表参数输入对话框（已优化高度）...");
                ShowEstateAreaSummaryDialog();

                // 3. 经主管部门批准的相关证照（已修复文本框布局）
                Console.WriteLine("3. 显示经主管部门批准的相关证照参数输入对话框（已修复文本框布局）...");
                ShowCertificatesDialog();

                Console.WriteLine("\n✅ 交互式测试完成");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ 交互式测试失败: {ex.Message}");
            }
        }

        private static void ShowBuildingInfoDialog()
        {
            try
            {
                var model = new RESClient.MVP.Models.BuildingInfoParametersModel();
                var parameters = model.GetCurrentParameters();

                using (var form = new RESClient.MVP.Views.BuildingInfoParameterInputForm(parameters))
                {
                    Console.WriteLine($"   窗体大小: {form.Size} (优化后应该较小，适合2个字段)");
                    var result = form.ShowDialog();
                    Console.WriteLine($"   用户操作结果: {result}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"   ❌ 显示楼栋基本信息对话框失败: {ex.Message}");
            }
        }

        private static void ShowEstateAreaSummaryDialog()
        {
            try
            {
                var model = new RESClient.MVP.Models.EstateAreaSummaryParametersModel();
                var parameters = model.GetCurrentParameters();

                using (var form = new RESClient.MVP.Views.EstateAreaSummaryParameterInputForm(parameters))
                {
                    Console.WriteLine($"   窗体大小: {form.Size} (优化后应该较小，适合1个多行字段)");
                    var result = form.ShowDialog();
                    Console.WriteLine($"   用户操作结果: {result}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"   ❌ 显示房产面积汇总表对话框失败: {ex.Message}");
            }
        }

        private static void ShowCertificatesDialog()
        {
            try
            {
                var model = new RESClient.MVP.Models.CertificatesParametersModel();
                var parameters = model.GetCurrentParameters();

                using (var form = new RESClient.MVP.Views.CertificatesParameterInputForm(parameters))
                {
                    Console.WriteLine($"   窗体大小: {form.Size} (应该有统一的文本框宽度布局)");
                    var result = form.ShowDialog();
                    Console.WriteLine($"   用户操作结果: {result}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"   ❌ 显示经主管部门批准的相关证照对话框失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 显示优化前后对比
        /// </summary>
        public static void ShowOptimizationComparison()
        {
            Console.WriteLine("=== UI优化前后对比 ===\n");

            Console.WriteLine("📋 优化内容总结:");
            Console.WriteLine("1. **楼栋基本信息参数输入对话框**:");
            Console.WriteLine("   - 优化前: 使用标准高度700px，只有2个字段却占用过多垂直空间");
            Console.WriteLine("   - 优化后: 调整为300px高度，适应实际内容，避免不必要的空白区域");

            Console.WriteLine("\n2. **经主管部门批准的相关证照参数输入对话框**:");
            Console.WriteLine("   - 优化前: 多行文本框与单行文本框宽度可能不一致");
            Console.WriteLine("   - 优化后: 统一所有文本框的宽度布局，使用标准配置确保一致性");
            Console.WriteLine("   - 优化后: 为重置按钮添加确认对话框，提升用户体验");

            Console.WriteLine("\n3. **房产面积汇总表参数输入对话框**:");
            Console.WriteLine("   - 优化前: 使用标准高度700px，只有1个字段却占用过多垂直空间");
            Console.WriteLine("   - 优化后: 调整为280px高度，适应实际内容（1个多行文本框）");

            Console.WriteLine("\n4. **整体布局优化**:");
            Console.WriteLine("   - 优化前: 主容器边距较小(15px)，内容可能显得拥挤");
            Console.WriteLine("   - 优化后: 增加主容器边距(20px)和面板内边距，提供更好的视觉平衡");
            Console.WriteLine("   - 优化后: 调整标签和输入控件的边距，确保更好的居中对齐效果");

            Console.WriteLine("\n✅ 所有优化都保持与统一UI样式配置(ParameterInputFormStyleConfig)的一致性");
        }
    }
}
