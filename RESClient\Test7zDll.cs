using System;
using System.IO;
using SevenZip;

namespace RESClient
{
    /// <summary>
    /// 测试7z.dll是否正常工作
    /// </summary>
    public class Test7zDll
    {
        public static void TestSevenZipLibrary()
        {
            Console.WriteLine("开始测试7z.dll库...");

            try
            {
                // 使用与ArchiveExtractionService相同的逻辑
                string appDirectory = AppDomain.CurrentDomain.BaseDirectory;

                // 根据当前进程架构选择正确的7z.dll
                bool is64Bit = Environment.Is64BitProcess;
                string architectureFolder = is64Bit ? "x64" : "x86";
                string sevenZipLibraryPath = Path.Combine(appDirectory, architectureFolder, "7z.dll");

                Console.WriteLine($"当前进程架构: {(is64Bit ? "64-bit" : "32-bit")}");
                Console.WriteLine($"主要7z.dll路径: {sevenZipLibraryPath}");

                // 如果找不到，尝试旧的路径作为备用
                if (!File.Exists(sevenZipLibraryPath))
                {
                    sevenZipLibraryPath = Path.Combine(appDirectory, "tool", "7z", "7z.dll");
                    Console.WriteLine($"备用7z.dll路径: {sevenZipLibraryPath}");
                }

                if (!File.Exists(sevenZipLibraryPath))
                {
                    Console.WriteLine("❌ 7z.dll文件不存在！");
                    Console.WriteLine($"搜索路径:");
                    Console.WriteLine($"1. {Path.Combine(appDirectory, architectureFolder, "7z.dll")}");
                    Console.WriteLine($"2. {Path.Combine(appDirectory, "tool", "7z", "7z.dll")}");
                    return;
                }

                Console.WriteLine("✓ 7z.dll文件存在");

                // 获取文件信息
                FileInfo fileInfo = new FileInfo(sevenZipLibraryPath);
                Console.WriteLine($"文件大小: {fileInfo.Length:N0} 字节");
                Console.WriteLine($"修改时间: {fileInfo.LastWriteTime}");

                // 初始化SevenZipSharp库
                SevenZipBase.SetLibraryPath(sevenZipLibraryPath);
                Console.WriteLine("✓ SevenZipSharp库初始化成功");

                // 测试库是否可以正常工作
                Console.WriteLine($"✓ 7z库版本信息: {SevenZipBase.CurrentLibraryFeatures}");

                Console.WriteLine("🎉 7z.dll测试成功！库可以正常使用。");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ 7z.dll测试失败: {ex.Message}");
                Console.WriteLine($"详细错误: {ex}");
            }
        }
    }
}
