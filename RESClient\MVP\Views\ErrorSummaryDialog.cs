using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;
using RESClient.Services;

namespace RESClient.MVP.Views
{
    /// <summary>
    /// 错误摘要对话框
    /// </summary>
    public partial class ErrorSummaryDialog : Form
    {
        private readonly List<ErrorInfo> _errors;
        private TreeView _errorTreeView;
        private RichTextBox _detailsTextBox;
        private Label _summaryLabel;
        private Button _okButton;
        private Button _copyButton;
        private Button _saveButton;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="errors">错误列表</param>
        public ErrorSummaryDialog(List<ErrorInfo> errors)
        {
            _errors = errors ?? new List<ErrorInfo>();
            InitializeComponent();
            InitializeCustomControls();
            LoadErrorData();
        }

        /// <summary>
        /// 初始化自定义控件
        /// </summary>
        private void InitializeCustomControls()
        {
            // 设置窗体属性
            this.Text = "报告生成错误摘要";
            this.Size = new Size(800, 600);
            this.StartPosition = FormStartPosition.CenterParent;
            this.MinimumSize = new Size(600, 400);
            this.MaximizeBox = true;
            this.MinimizeBox = false;
            this.ShowInTaskbar = false;

            // 创建主面板
            var mainPanel = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 1,
                RowCount = 4,
                Padding = new Padding(10)
            };

            // 设置行高
            mainPanel.RowStyles.Add(new RowStyle(SizeType.Absolute, 40)); // 摘要标签
            mainPanel.RowStyles.Add(new RowStyle(SizeType.Percent, 60));  // 错误树
            mainPanel.RowStyles.Add(new RowStyle(SizeType.Percent, 40));  // 详细信息
            mainPanel.RowStyles.Add(new RowStyle(SizeType.Absolute, 50)); // 按钮

            // 创建摘要标签
            _summaryLabel = new Label
            {
                Dock = DockStyle.Fill,
                Font = new Font("Microsoft YaHei", 10, FontStyle.Bold),
                ForeColor = Color.DarkRed,
                TextAlign = ContentAlignment.MiddleLeft
            };
            mainPanel.Controls.Add(_summaryLabel, 0, 0);

            // 创建错误树视图
            _errorTreeView = new TreeView
            {
                Dock = DockStyle.Fill,
                Font = new Font("Microsoft YaHei", 9),
                ShowLines = true,
                ShowPlusMinus = true,
                ShowRootLines = true,
                FullRowSelect = true,
                HideSelection = false
            };
            _errorTreeView.AfterSelect += ErrorTreeView_AfterSelect;
            
            var treePanel = new Panel
            {
                Dock = DockStyle.Fill,
                BorderStyle = BorderStyle.FixedSingle
            };
            treePanel.Controls.Add(_errorTreeView);
            mainPanel.Controls.Add(treePanel, 0, 1);

            // 创建详细信息文本框
            _detailsTextBox = new RichTextBox
            {
                Dock = DockStyle.Fill,
                Font = new Font("Microsoft YaHei", 9),
                ReadOnly = true,
                BackColor = Color.White,
                BorderStyle = BorderStyle.None
            };
            
            var detailsPanel = new Panel
            {
                Dock = DockStyle.Fill,
                BorderStyle = BorderStyle.FixedSingle
            };
            detailsPanel.Controls.Add(_detailsTextBox);
            mainPanel.Controls.Add(detailsPanel, 0, 2);

            // 创建按钮面板
            var buttonPanel = new FlowLayoutPanel
            {
                Dock = DockStyle.Fill,
                FlowDirection = FlowDirection.RightToLeft,
                Padding = new Padding(0, 10, 0, 0)
            };

            _okButton = new Button
            {
                Text = "确定",
                Size = new Size(80, 30),
                DialogResult = DialogResult.OK,
                UseVisualStyleBackColor = true
            };

            _copyButton = new Button
            {
                Text = "复制详情",
                Size = new Size(80, 30),
                UseVisualStyleBackColor = true
            };
            _copyButton.Click += CopyButton_Click;

            _saveButton = new Button
            {
                Text = "保存报告",
                Size = new Size(80, 30),
                UseVisualStyleBackColor = true
            };
            _saveButton.Click += SaveButton_Click;

            buttonPanel.Controls.Add(_okButton);
            buttonPanel.Controls.Add(_copyButton);
            buttonPanel.Controls.Add(_saveButton);
            mainPanel.Controls.Add(buttonPanel, 0, 3);

            this.Controls.Add(mainPanel);
            this.AcceptButton = _okButton;
        }

        /// <summary>
        /// 加载错误数据
        /// </summary>
        private void LoadErrorData()
        {
            // 更新摘要标签
            UpdateSummaryLabel();

            // 清空树视图
            _errorTreeView.Nodes.Clear();

            if (_errors.Count == 0)
            {
                var noErrorNode = new TreeNode("没有发现错误")
                {
                    ForeColor = Color.Green,
                    NodeFont = new Font(_errorTreeView.Font, FontStyle.Bold)
                };
                _errorTreeView.Nodes.Add(noErrorNode);
                return;
            }

            // 按错误类型分组
            var errorsByType = _errors.GroupBy(e => e.Type).OrderBy(g => g.Key);

            foreach (var typeGroup in errorsByType)
            {
                var typeNode = new TreeNode($"{GetErrorTypeDisplayName(typeGroup.Key)} ({typeGroup.Count()})")
                {
                    ForeColor = GetErrorTypeColor(typeGroup.Key),
                    NodeFont = new Font(_errorTreeView.Font, FontStyle.Bold),
                    Tag = typeGroup.Key
                };

                // 按模块分组
                var errorsByModule = typeGroup.GroupBy(e => e.ModuleName ?? "系统").OrderBy(g => g.Key);

                foreach (var moduleGroup in errorsByModule)
                {
                    var moduleNode = new TreeNode($"{moduleGroup.Key} ({moduleGroup.Count()})")
                    {
                        ForeColor = Color.DarkBlue,
                        Tag = moduleGroup.Key
                    };

                    // 添加具体错误
                    foreach (var error in moduleGroup.OrderByDescending(e => e.Severity))
                    {
                        var errorNode = new TreeNode($"[{GetSeverityDisplayName(error.Severity)}] {error.Message}")
                        {
                            ForeColor = GetSeverityColor(error.Severity),
                            Tag = error
                        };
                        moduleNode.Nodes.Add(errorNode);
                    }

                    typeNode.Nodes.Add(moduleNode);
                }

                _errorTreeView.Nodes.Add(typeNode);
            }

            // 展开所有节点
            _errorTreeView.ExpandAll();
        }

        /// <summary>
        /// 更新摘要标签
        /// </summary>
        private void UpdateSummaryLabel()
        {
            if (_errors.Count == 0)
            {
                _summaryLabel.Text = "报告生成完成，没有发现错误。";
                _summaryLabel.ForeColor = Color.Green;
                return;
            }

            var criticalCount = _errors.Count(e => e.Severity == ErrorSeverity.Critical);
            var errorCount = _errors.Count(e => e.Severity == ErrorSeverity.Error);
            var warningCount = _errors.Count(e => e.Severity == ErrorSeverity.Warning);

            var summaryText = $"报告生成过程中发现 {_errors.Count} 个问题";
            if (criticalCount > 0) summaryText += $"，其中严重错误 {criticalCount} 个";
            if (errorCount > 0) summaryText += $"，错误 {errorCount} 个";
            if (warningCount > 0) summaryText += $"，警告 {warningCount} 个";
            summaryText += "。";

            _summaryLabel.Text = summaryText;
            _summaryLabel.ForeColor = criticalCount > 0 || errorCount > 0 ? Color.DarkRed : Color.Orange;
        }

        /// <summary>
        /// 错误树选择事件处理
        /// </summary>
        private void ErrorTreeView_AfterSelect(object sender, TreeViewEventArgs e)
        {
            if (e.Node?.Tag is ErrorInfo error)
            {
                ShowErrorDetails(error);
            }
            else
            {
                _detailsTextBox.Clear();
            }
        }

        /// <summary>
        /// 显示错误详细信息
        /// </summary>
        /// <param name="error">错误信息</param>
        private void ShowErrorDetails(ErrorInfo error)
        {
            _detailsTextBox.Clear();

            // 添加基本信息
            AppendFormattedText("错误类型：", Color.Black, FontStyle.Bold);
            AppendFormattedText($"{GetErrorTypeDisplayName(error.Type)}\n", GetErrorTypeColor(error.Type));

            AppendFormattedText("严重程度：", Color.Black, FontStyle.Bold);
            AppendFormattedText($"{GetSeverityDisplayName(error.Severity)}\n", GetSeverityColor(error.Severity));

            if (!string.IsNullOrEmpty(error.ModuleName))
            {
                AppendFormattedText("相关模块：", Color.Black, FontStyle.Bold);
                AppendFormattedText($"{error.ModuleName}\n", Color.DarkBlue);
            }

            AppendFormattedText("发生时间：", Color.Black, FontStyle.Bold);
            AppendFormattedText($"{error.Timestamp:yyyy-MM-dd HH:mm:ss}\n", Color.Gray);

            AppendFormattedText("\n错误描述：\n", Color.Black, FontStyle.Bold);
            AppendFormattedText($"{error.Message}\n", Color.Black);

            if (!string.IsNullOrEmpty(error.Details))
            {
                AppendFormattedText("\n详细信息：\n", Color.Black, FontStyle.Bold);
                AppendFormattedText($"{error.Details}\n", Color.DarkGray);
            }

            if (!string.IsNullOrEmpty(error.SuggestedSolution))
            {
                AppendFormattedText("\n建议解决方案：\n", Color.Green, FontStyle.Bold);
                AppendFormattedText($"{error.SuggestedSolution}\n", Color.DarkGreen);
            }

            if (error.Exception != null)
            {
                AppendFormattedText("\n技术详情：\n", Color.Red, FontStyle.Bold);
                AppendFormattedText($"{error.Exception.Message}\n", Color.DarkRed);
            }
        }

        /// <summary>
        /// 添加格式化文本
        /// </summary>
        private void AppendFormattedText(string text, Color color, FontStyle style = FontStyle.Regular)
        {
            var start = _detailsTextBox.TextLength;
            _detailsTextBox.AppendText(text);
            _detailsTextBox.Select(start, text.Length);
            _detailsTextBox.SelectionColor = color;
            _detailsTextBox.SelectionFont = new Font(_detailsTextBox.Font, style);
            _detailsTextBox.Select(_detailsTextBox.TextLength, 0);
        }

        /// <summary>
        /// 复制按钮点击事件
        /// </summary>
        private void CopyButton_Click(object sender, EventArgs e)
        {
            try
            {
                var errorReport = GenerateErrorReport();
                Clipboard.SetText(errorReport);
                MessageBox.Show("错误详情已复制到剪贴板", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"复制失败：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 保存按钮点击事件
        /// </summary>
        private void SaveButton_Click(object sender, EventArgs e)
        {
            try
            {
                using (var saveDialog = new SaveFileDialog())
                {
                    saveDialog.Filter = "文本文件 (*.txt)|*.txt|所有文件 (*.*)|*.*";
                    saveDialog.DefaultExt = "txt";
                    saveDialog.FileName = $"错误报告_{DateTime.Now:yyyyMMdd_HHmmss}.txt";

                    if (saveDialog.ShowDialog() == DialogResult.OK)
                    {
                        var errorReport = GenerateErrorReport();
                        System.IO.File.WriteAllText(saveDialog.FileName, errorReport, System.Text.Encoding.UTF8);
                        MessageBox.Show($"错误报告已保存到：{saveDialog.FileName}", "保存成功",
                            MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"保存失败：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 生成错误报告文本
        /// </summary>
        /// <returns>错误报告文本</returns>
        private string GenerateErrorReport()
        {
            var report = new System.Text.StringBuilder();
            report.AppendLine("报告生成错误摘要");
            report.AppendLine("=" + new string('=', 50));
            report.AppendLine($"生成时间：{DateTime.Now:yyyy-MM-dd HH:mm:ss}");
            report.AppendLine($"错误总数：{_errors.Count}");
            report.AppendLine();

            if (_errors.Count == 0)
            {
                report.AppendLine("没有发现错误。");
                return report.ToString();
            }

            // 统计信息
            var severityStats = _errors.GroupBy(e => e.Severity).ToDictionary(g => g.Key, g => g.Count());
            report.AppendLine("错误统计：");
            foreach (var stat in severityStats.OrderByDescending(s => s.Key))
            {
                report.AppendLine($"  {GetSeverityDisplayName(stat.Key)}：{stat.Value} 个");
            }
            report.AppendLine();

            // 按类型分组显示错误
            var errorsByType = _errors.GroupBy(e => e.Type).OrderBy(g => g.Key);
            foreach (var typeGroup in errorsByType)
            {
                report.AppendLine($"{GetErrorTypeDisplayName(typeGroup.Key)} ({typeGroup.Count()} 个)");
                report.AppendLine("-" + new string('-', 30));

                var errorsByModule = typeGroup.GroupBy(e => e.ModuleName ?? "系统").OrderBy(g => g.Key);
                foreach (var moduleGroup in errorsByModule)
                {
                    report.AppendLine($"  模块：{moduleGroup.Key}");

                    foreach (var error in moduleGroup.OrderByDescending(e => e.Severity))
                    {
                        report.AppendLine($"    [{GetSeverityDisplayName(error.Severity)}] {error.Message}");
                        if (!string.IsNullOrEmpty(error.Details))
                        {
                            report.AppendLine($"      详情：{error.Details}");
                        }
                        if (!string.IsNullOrEmpty(error.SuggestedSolution))
                        {
                            report.AppendLine($"      建议：{error.SuggestedSolution}");
                        }
                        report.AppendLine($"      时间：{error.Timestamp:yyyy-MM-dd HH:mm:ss}");
                        report.AppendLine();
                    }
                }
                report.AppendLine();
            }

            return report.ToString();
        }

        /// <summary>
        /// 获取错误类型显示名称
        /// </summary>
        private string GetErrorTypeDisplayName(ErrorType type)
        {
            switch (type)
            {
                case ErrorType.MissingFile: return "缺失文件";
                case ErrorType.DataValidation: return "数据验证";
                case ErrorType.TemplateProcessing: return "模板处理";
                case ErrorType.ParameterConfiguration: return "参数配置";
                case ErrorType.DocumentGeneration: return "文档生成";
                case ErrorType.DocumentMerging: return "文档合并";
                case ErrorType.SystemException: return "系统异常";
                default: return "其他错误";
            }
        }

        /// <summary>
        /// 获取严重程度显示名称
        /// </summary>
        private string GetSeverityDisplayName(ErrorSeverity severity)
        {
            switch (severity)
            {
                case ErrorSeverity.Info: return "信息";
                case ErrorSeverity.Warning: return "警告";
                case ErrorSeverity.Error: return "错误";
                case ErrorSeverity.Critical: return "严重错误";
                default: return "未知";
            }
        }

        /// <summary>
        /// 获取错误类型颜色
        /// </summary>
        private Color GetErrorTypeColor(ErrorType type)
        {
            switch (type)
            {
                case ErrorType.MissingFile: return Color.Red;
                case ErrorType.DataValidation: return Color.Orange;
                case ErrorType.TemplateProcessing: return Color.Purple;
                case ErrorType.ParameterConfiguration: return Color.Blue;
                case ErrorType.DocumentGeneration: return Color.DarkRed;
                case ErrorType.DocumentMerging: return Color.Brown;
                case ErrorType.SystemException: return Color.Crimson;
                default: return Color.Gray;
            }
        }

        /// <summary>
        /// 获取严重程度颜色
        /// </summary>
        private Color GetSeverityColor(ErrorSeverity severity)
        {
            switch (severity)
            {
                case ErrorSeverity.Info: return Color.Blue;
                case ErrorSeverity.Warning: return Color.Orange;
                case ErrorSeverity.Error: return Color.Red;
                case ErrorSeverity.Critical: return Color.DarkRed;
                default: return Color.Black;
            }
        }
    }
}
