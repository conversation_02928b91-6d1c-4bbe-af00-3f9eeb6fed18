using Microsoft.Extensions.Options;
using RESServer.Models;
using System.Net.Http.Json;
using System.Text;
using System.Text.Json;
using System.Text.Json.Serialization;
using Microsoft.Extensions.Logging;

namespace RESServer.Services
{
    public class GeminiService
    {
        private readonly GeminiConfig _config;
        private readonly IHttpClientFactory _httpClientFactory;
        private readonly ILogger<GeminiService> _logger;
        private readonly string _baseUrl = "https://generativelanguage.googleapis.com/v1beta";

        public GeminiService(
            IOptions<GeminiConfig> config, 
            IHttpClientFactory httpClientFactory,
            ILogger<GeminiService> logger)
        {
            _config = config.Value;
            _httpClientFactory = httpClientFactory;
            _logger = logger;
        }

        public async Task<string> AnalyzeFilesAsync(IEnumerable<FileContentData> fileContents, string prompt)
        {
            try
            {
                _logger.LogInformation("开始处理文件分析请求");
                
                var fileContentsList = fileContents.ToList();
                if (fileContentsList.Count == 0)
                {
                    return "没有提供文件";
                }

                _logger.LogInformation($"准备处理 {fileContentsList.Count} 个文件");
                
                // 增强提示词，包含文件名信息
                string enhancedPrompt = EnhancePromptWithFileNames(prompt, fileContentsList);
                
                // 直接使用内联数据方式分析所有文件
                return await ProcessWithInlineDataAsync(fileContentsList, enhancedPrompt);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "分析文件过程中出现异常");
                return $"分析文件时出错: {ex.Message}";
            }
        }
        
        private string EnhancePromptWithFileNames(string originalPrompt, List<FileContentData> files)
        {
            if (files.Count == 0)
                return originalPrompt;
                
            // 构建包含文件名和类型的详细信息
            var fileInfoList = new List<string>();
            
            foreach (var file in files)
            {
                string fileType = GetFriendlyFileType(file.MimeType);
                fileInfoList.Add($"{file.FileName} ({fileType})");
            }
            
            string fileInfo = string.Join("\n- ", fileInfoList);
            
            // 将文件信息添加到提示词中
            string enhancedPrompt = $"{originalPrompt}\n\n文件信息：请分析以下文件：\n- {fileInfo}";
            
            // 根据文件类型优化提示词
            if (files.All(f => f.MimeType.StartsWith("image/")))
            {
                enhancedPrompt += "\n\n这些都是图像文件，请详细分析图像内容、颜色、构图和可能的含义。";
            }
            else if (files.All(f => f.MimeType.Contains("text/") || f.MimeType.Contains("application/pdf")))
            {
                enhancedPrompt += "\n\n这些都是文本或文档文件，请分析其内容、结构和主要观点。";
            }
            else if (files.Any(f => f.MimeType.Contains("javascript") || f.MimeType.Contains("python") || f.MimeType.Contains("css") || f.MimeType.Contains("xml")))
            {
                enhancedPrompt += "\n\n文件中包含代码文件，请分析代码结构、功能和可能的用途。";
            }
            
            _logger.LogInformation($"增强后的提示词: {enhancedPrompt}");
            
            return enhancedPrompt;
        }
        
        private string GetFriendlyFileType(string mimeType)
        {
            return mimeType.ToLowerInvariant() switch
            {
                "image/jpeg" or "image/jpg" => "JPEG图像",
                "image/png" => "PNG图像",
                "image/gif" => "GIF图像",
                "image/webp" => "WebP图像",
                "application/pdf" => "PDF文档",
                "text/plain" => "文本文件",
                "text/csv" => "CSV表格文件",
                "application/vnd.openxmlformats-officedocument.wordprocessingml.document" => "Word文档",
                "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" => "Excel表格",
                "application/vnd.openxmlformats-officedocument.presentationml.presentation" => "PowerPoint演示文稿",
                "application/json" => "JSON数据",
                "text/html" => "HTML网页",
                "text/markdown" or "text/md" => "Markdown文档",
                "application/x-javascript" or "text/javascript" => "JavaScript代码",
                "application/x-python" or "text/x-python" => "Python代码",
                "text/css" => "CSS样式表",
                "text/xml" => "XML文档",
                "text/rtf" => "富文本格式文档",
                _ => mimeType // 如果没有友好名称，则返回原始MIME类型
            };
        }

        private async Task<string> ProcessWithInlineDataAsync(List<FileContentData> fileContents, string prompt)
        {
            try
            {
                var client = _httpClientFactory.CreateClient();
                
                // 构建请求内容 - 包含提示词和所有文件
                var parts = new List<object>();
                
                // 首先添加提示词
                parts.Add(new { text = prompt });
                
                // 然后添加所有文件，包含文件名
                foreach (var fileContent in fileContents)
                {
                    _logger.LogInformation($"处理文件: {fileContent.FileName}, 类型: {fileContent.MimeType}, 大小: {fileContent.Data.Length} 字节");
                    // 对文件内容进行Base64编码
                    var fileBase64 = Convert.ToBase64String(fileContent.Data);
                    _logger.LogInformation($"文件 {fileContent.FileName} 已转换为Base64编码");
                    
                    // 添加内联数据部分
                    parts.Add(new
                    {
                        inline_data = new
                        {
                            mime_type = fileContent.MimeType,
                            data = fileBase64
                        }
                    });
                }
                
                // 检查请求是否可能超过API大小限制
                long totalSize = 0;
                foreach (var file in fileContents)
                {
                    totalSize += file.Data.Length;
                }
                if (totalSize > 18_000_000) // 18MB 作为一个安全阈值，因为还有JSON结构和提示词
                {
                    _logger.LogWarning($"请求可能超过Gemini API 20MB限制，当前文件总大小: {totalSize / 1024 / 1024}MB");
                }
                
                // 创建完整请求结构
                var requestObj = new
                {
                    contents = new[]
                    {
                        new
                        {
                            parts = parts.ToArray()
                        }
                    },
                    // 禁用流式输出
                    generationConfig = new
                    {
                        temperature = 0.4,
                        topK = 32,
                        topP = 1,
                        maxOutputTokens = 8192
                    }
                };

                var requestJson = JsonSerializer.Serialize(requestObj);
                
                var generateUrl = $"{_baseUrl}/models/{_config.ModelName}:generateContent?key={_config.ApiKey}";
                _logger.LogInformation($"使用内联数据生成内容: {generateUrl}");
                _logger.LogInformation($"请求包含 {fileContents.Count} 个文件");
                
                // 使用StringContent而不是PostAsJsonAsync以便完全控制请求格式
                var content = new StringContent(requestJson, Encoding.UTF8, "application/json");
                
                _logger.LogDebug($"请求长度: {requestJson.Length} 字符");
                
                var response = await client.PostAsync(generateUrl, content);
                
                var responseText = await response.Content.ReadAsStringAsync();
                
                if (!response.IsSuccessStatusCode)
                {
                    _logger.LogError($"内联文件内容生成失败: {response.StatusCode}, {responseText}");
                    return $"内联文件内容生成失败: {responseText}";
                }
                
                // 只记录响应的开头部分，但保留完整响应用于处理
                _logger.LogInformation($"内联文件内容生成成功: {responseText.Substring(0, Math.Min(responseText.Length, 100))}...");
                
                try {
                    // 解析响应
                    var options = new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    };
                    var contentResponse = JsonSerializer.Deserialize<GenerateContentResponse>(responseText, options);
                    var resultText = contentResponse?.Candidates?.FirstOrDefault()?.Content?.Parts?.FirstOrDefault()?.Text;
                    
                    if (string.IsNullOrEmpty(resultText))
                    {
                        _logger.LogWarning("解析响应后文本为空，返回原始响应");
                        return $"API返回了结果，但无法解析文本内容。原始响应: {responseText}";
                    }
                    
                    return resultText;
                }
                catch (Exception parseEx)
                {
                    _logger.LogError(parseEx, $"解析API响应时出错: {parseEx.Message}");
                    return $"解析API响应时出错: {parseEx.Message}\n原始响应: {responseText}";
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"内联文件分析失败: {ex.Message}");
                return $"内联文件分析失败: {ex.Message}";
            }
        }

        private async Task<string> GenerateContentAsync(string prompt, List<string> fileUris)
        {
            var client = _httpClientFactory.CreateClient();
            
            // 检查是否使用内联数据模式
            bool usingInlineData = fileUris.Count > 0 && fileUris[0].StartsWith("INLINE_DATA_");
            
            if (usingInlineData)
            {
                // 如果我们前面使用了内联数据，这里直接返回结果
                return "文件已经通过内联数据方式成功处理，请查看前面的分析结果。";
            }
            
            // 否则使用文件URI方式
            // 构建请求内容
            var parts = new List<object>
            {
                new { text = prompt }
            };

            // 添加文件引用
            foreach (var uri in fileUris)
            {
                parts.Add(new
                {
                    file_data = new
                    {
                        file_uri = uri
                    }
                });
            }

            var requestContent = new
            {
                contents = new[]
                {
                    new
                    {
                        parts = parts
                    }
                }
            };

            var url = $"{_baseUrl}/models/{_config.ModelName}:generateContent?key={_config.ApiKey}";
            _logger.LogInformation($"发送内容生成请求: {url}");
            
            var jsonContent = JsonSerializer.Serialize(requestContent);
            _logger.LogInformation($"请求内容: {jsonContent}");
            
            var response = await client.PostAsJsonAsync(url, requestContent);
            if (!response.IsSuccessStatusCode)
            {
                var errorContent = await response.Content.ReadAsStringAsync();
                _logger.LogError($"生成内容请求失败: {response.StatusCode}, {errorContent}");
                throw new Exception($"生成内容请求失败: {errorContent}");
            }

            var responseContent = await response.Content.ReadAsStringAsync();
            _logger.LogInformation($"生成内容响应: {responseContent}");
            
            var contentResponse = JsonSerializer.Deserialize<GenerateContentResponse>(responseContent);
            return contentResponse?.Candidates?.FirstOrDefault()?.Content?.Parts?.FirstOrDefault()?.Text ?? "未能获取分析结果";
        }
    }

    public class FileContentData
    {
        public string MimeType { get; set; } = string.Empty;
        public byte[] Data { get; set; } = Array.Empty<byte>();
        public string FileName { get; set; } = string.Empty;
    }

    // 响应类
    public class FileUploadResponse
    {
        public FileData? File { get; set; }
    }

    public class FileData
    {
        public string? Uri { get; set; }
    }

    public class GenerateContentResponse
    {
        public List<CandidateResponse>? Candidates { get; set; }
    }

    public class CandidateResponse
    {
        public ContentResponse? Content { get; set; }
    }

    public class ContentResponse
    {
        public List<PartResponse>? Parts { get; set; }
    }

    public class PartResponse
    {
        [JsonPropertyName("text")]
        public string? Text { get; set; }
    }
} 