using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using RESClient.Services.Implementations;

namespace RESClient.Services
{
    /// <summary>
    /// 报告生成服务
    /// </summary>
    public class ReportGeneratorService
    {
        private readonly List<IReportModuleGenerator> _moduleGenerators;

        /// <summary>
        /// 构造函数
        /// </summary>
        public ReportGeneratorService()
        {
            // 初始化所有模块生成器
            _moduleGenerators = new List<IReportModuleGenerator>
            {
                new CoverModuleGenerator(),
                new TableOfContentsModuleGenerator(),
                new WorkStatementModuleGenerator(),
                new WorkQualityModuleGenerator(),
                new ProjectInfoModuleGenerator(),
                new BuildingInfoModuleGenerator(),
                new CertificatesModuleGenerator(),
                new BasementDefenseModuleGenerator(),
                new ProjectDistributionMapModuleGenerator(),
                new BuildingImageModuleGenerator(),
                new EstateAreaSummaryModuleGenerator(),
                new HouseholdAreaStatisticsModuleGenerator(),
                new FloorPlanModuleGenerator()
            };
        }

        /// <summary>
        /// 获取所有模块生成器
        /// </summary>
        /// <returns>模块生成器列表</returns>
        public List<IReportModuleGenerator> GetAllModuleGenerators()
        {
            return _moduleGenerators;
        }

        /// <summary>
        /// 根据模块名称获取生成器
        /// </summary>
        /// <param name="moduleName">模块名称</param>
        /// <returns>模块生成器</returns>
        public IReportModuleGenerator GetModuleGenerator(string moduleName)
        {
            return _moduleGenerators.FirstOrDefault(m => m.ModuleName == moduleName);
        }

        /// <summary>
        /// 生成指定的报告模块
        /// </summary>
        /// <param name="moduleNames">要生成的模块名称列表</param>
        /// <param name="parameters">生成参数</param>
        /// <param name="progressCallback">进度回调</param>
        /// <returns>生成结果</returns>
        public async Task<bool> GenerateReportModulesAsync(
            List<string> moduleNames,
            Dictionary<string, object> parameters,
            Action<int, string> progressCallback)
        {
            if (moduleNames == null || moduleNames.Count == 0)
            {
                progressCallback?.Invoke(0, "未选择要生成的模块");
                return false;
            }

            int totalModules = moduleNames.Count;
            int completedModules = 0;
            bool overallSuccess = true;

            foreach (string moduleName in moduleNames)
            {
                var generator = GetModuleGenerator(moduleName);
                if (generator == null)
                {
                    progressCallback?.Invoke(
                        (int)((float)(completedModules + 1) / totalModules * 100),
                        $"[MODULE_FAILED]{moduleName}:未找到模块生成器");
                    overallSuccess = false;
                    completedModules++;
                    continue;
                }

                if (!generator.IsAvailable(parameters))
                {
                    progressCallback?.Invoke(
                        (int)((float)(completedModules + 1) / totalModules * 100),
                        $"[MODULE_SKIPPED]{moduleName}:模块不可用，跳过生成");
                    completedModules++;
                    continue;
                }

                Action<int, string> moduleProgressCallback = (percent, message) =>
                {
                    // 计算总体进度
                    int overallPercent = (int)((float)completedModules / totalModules * 100) +
                                        (int)((float)percent / totalModules);
                    progressCallback?.Invoke(overallPercent, message);
                };

                bool result = await generator.GenerateAsync(parameters, moduleProgressCallback);

                // Report individual module result
                if (result)
                {
                    progressCallback?.Invoke(
                        (int)((float)(completedModules + 1) / totalModules * 100),
                        $"[MODULE_SUCCESS]{moduleName}:模块生成成功");
                }
                else
                {
                    progressCallback?.Invoke(
                        (int)((float)(completedModules + 1) / totalModules * 100),
                        $"[MODULE_FAILED]{moduleName}:模块生成失败");
                    overallSuccess = false;
                }

                completedModules++;
            }

            progressCallback?.Invoke(100, overallSuccess ? "[SUCCESS]所有选中模块生成完成" : "[WARNING]部分模块生成失败");
            return overallSuccess;
        }
    }
} 