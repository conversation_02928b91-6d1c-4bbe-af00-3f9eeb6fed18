using System;
using System.Collections.Generic;

namespace UnitCountTest
{
    /// <summary>
    /// Test program to verify the unit count calculation fix
    /// </summary>
    class Program
    {
        static void Main(string[] args)
        {
            Console.WriteLine("Testing Unit Count Calculation Fix");
            Console.WriteLine("==================================");

            // Test Case 1: Distinct numeric units
            var testCase1 = new BuildingInfo { BuildingNumber = "1号楼" };
            AddUnit(testCase1, "1");
            AddUnit(testCase1, "2");
            AddUnit(testCase1, "1"); // Duplicate
            AddUnit(testCase1, "3");
            AddUnit(testCase1, ""); // Empty - should be ignored

            Console.WriteLine($"Test Case 1 - Building {testCase1.BuildingNumber}:");
            Console.WriteLine($"  Units added: 1, 2, 1 (duplicate), 3, '' (empty)");
            Console.WriteLine($"  Expected count: 3 (distinct: 1, 2, 3)");
            Console.WriteLine($"  Actual count: {testCase1.GetUnitCountText()}");
            Console.WriteLine($"  Units in set: [{string.Join(", ", testCase1.Units)}]");
            Console.WriteLine();

            // Test Case 2: No valid units
            var testCase2 = new BuildingInfo { BuildingNumber = "2号楼" };
            AddUnit(testCase2, "");
            AddUnit(testCase2, null);
            AddUnit(testCase2, "   "); // Whitespace only

            Console.WriteLine($"Test Case 2 - Building {testCase2.BuildingNumber}:");
            Console.WriteLine($"  Units added: '', null, '   ' (whitespace)");
            Console.WriteLine($"  Expected count: \\ (no valid units)");
            Console.WriteLine($"  Actual count: {testCase2.GetUnitCountText()}");
            Console.WriteLine($"  Units in set: [{string.Join(", ", testCase2.Units)}]");
            Console.WriteLine();

            // Test Case 3: Mixed alphanumeric units
            var testCase3 = new BuildingInfo { BuildingNumber = "3号楼" };
            AddUnit(testCase3, "A");
            AddUnit(testCase3, "B");
            AddUnit(testCase3, "A"); // Duplicate
            AddUnit(testCase3, "1");
            AddUnit(testCase3, "2");

            Console.WriteLine($"Test Case 3 - Building {testCase3.BuildingNumber}:");
            Console.WriteLine($"  Units added: A, B, A (duplicate), 1, 2");
            Console.WriteLine($"  Expected count: 4 (distinct: A, B, 1, 2)");
            Console.WriteLine($"  Actual count: {testCase3.GetUnitCountText()}");
            Console.WriteLine($"  Units in set: [{string.Join(", ", testCase3.Units)}]");
            Console.WriteLine();

            // Test Case 4: Units with whitespace (should be trimmed)
            var testCase4 = new BuildingInfo { BuildingNumber = "4号楼" };
            AddUnit(testCase4, " 1 ");
            AddUnit(testCase4, "1"); // Should be same as " 1 " after trimming
            AddUnit(testCase4, "  2  ");
            AddUnit(testCase4, "3");

            Console.WriteLine($"Test Case 4 - Building {testCase4.BuildingNumber}:");
            Console.WriteLine($"  Units added: ' 1 ', '1', '  2  ', '3'");
            Console.WriteLine($"  Expected count: 3 (distinct after trim: 1, 2, 3)");
            Console.WriteLine($"  Actual count: {testCase4.GetUnitCountText()}");
            Console.WriteLine($"  Units in set: [{string.Join(", ", testCase4.Units)}]");
            Console.WriteLine();

            Console.WriteLine("Press any key to exit...");
            Console.ReadKey();
        }

        /// <summary>
        /// Simulate adding a unit (mimics the ProcessDataRow logic)
        /// </summary>
        static void AddUnit(BuildingInfo building, string unit)
        {
            // Mimic the logic from ProcessDataRow
            if (!string.IsNullOrWhiteSpace(unit))
            {
                building.Units.Add(unit.Trim());
                building.UnitCount = building.Units.Count;
            }
        }

        /// <summary>
        /// Simplified BuildingInfo class for testing
        /// </summary>
        private class BuildingInfo
        {
            public string BuildingNumber { get; set; } = "";
            public int UnitCount { get; set; } = 0;
            public HashSet<string> Units { get; set; } = new HashSet<string>();

            /// <summary>
            /// 获取单元数显示文本
            /// </summary>
            public string GetUnitCountText()
            {
                // 使用不重复单元号的数量，而不是简单的计数
                int distinctUnitCount = Units.Count;
                return distinctUnitCount == 0 ? "\\" : distinctUnitCount.ToString();
            }
        }
    }
}
