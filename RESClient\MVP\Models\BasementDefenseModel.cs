using System;
using System.IO;
using System.Threading.Tasks;
using NPOI.XWPF.UserModel;
using System.Collections.Generic;
using RESClient.MVP.Base;

namespace RESClient.MVP.Models
{
    /// <summary>
    /// 地下室人防区域说明模型，用于处理地下室人防区域说明模板的填充
    /// </summary>
    public class BasementDefenseModel : BaseModel
    {
        /// <summary>
        /// 地下室人防区域说明数据结构
        /// </summary>
        public class BasementDefenseData
        {
            public string DataProvider { get; set; } // 依据提供方
            public string DrawingName { get; set; } // 图纸名称
            public string Description { get; set; } // 说明
        }

        /// <summary>
        /// 填充地下室人防区域说明模板
        /// </summary>
        /// <param name="basementDefenseData">地下室人防区域说明数据</param>
        /// <param name="outputPath">输出文件路径</param>
        /// <returns>是否成功</returns>
        public async Task<bool> FillBasementDefenseTemplateAsync(BasementDefenseData basementDefenseData, string outputPath)
        {
            try
            {
                // 获取模板文件路径
                string templatePath = Path.Combine(
                    AppDomain.CurrentDomain.BaseDirectory,
                    Program.TemplateDirectory,
                    "08_地下室人防区域说明",
                    "地下室人防区域说明.docx");

                if (!File.Exists(templatePath))
                {
                    HandleException(new FileNotFoundException($"模板文件不存在: {templatePath}"));
                    return false;
                }

                // 创建临时文件
                string tempPath = Path.GetTempFileName();
                File.Copy(templatePath, tempPath, true);

                try
                {
                    // 使用NPOI处理Word文档
                    using (var fs = new FileStream(tempPath, FileMode.Open, FileAccess.ReadWrite))
                    {
                        var doc = new XWPFDocument(fs);

                        // 创建变量映射
                        var variableMap = new Dictionary<string, string>
                        {
                            { "${依据提供方}", basementDefenseData.DataProvider ?? "" },
                            { "${图纸名称}", basementDefenseData.DrawingName ?? "" },
                            { "${说明}", basementDefenseData.Description ?? "" }
                        };

                        // 替换段落中的占位符
                        await Task.Run(() => ReplaceVariablesInParagraphs(doc, variableMap));

                        // 替换表格中的占位符
                        await Task.Run(() => ReplaceVariablesInTables(doc, variableMap));

                        // 保存修改后的文档
                        using (var outputStream = new FileStream(tempPath, FileMode.Create, FileAccess.Write))
                        {
                            doc.Write(outputStream);
                        }
                    }

                    // 将临时文件复制到最终输出路径
                    File.Copy(tempPath, outputPath, true);
                    return true;
                }
                finally
                {
                    // 清理临时文件
                    if (File.Exists(tempPath))
                    {
                        File.Delete(tempPath);
                    }
                }
            }
            catch (Exception ex)
            {
                HandleException(ex);
                return false;
            }
        }

        /// <summary>
        /// 替换段落中的变量
        /// </summary>
        /// <param name="doc">Word文档</param>
        /// <param name="variableMap">变量映射</param>
        private void ReplaceVariablesInParagraphs(XWPFDocument doc, Dictionary<string, string> variableMap)
        {
            foreach (var paragraph in doc.Paragraphs)
            {
                foreach (var variable in variableMap)
                {
                    if (paragraph.Text.Contains(variable.Key))
                    {
                        ReplaceTextInParagraph(paragraph, variable.Key, variable.Value);
                    }
                }
            }
        }

        /// <summary>
        /// 替换表格中的变量
        /// </summary>
        /// <param name="doc">Word文档</param>
        /// <param name="variableMap">变量映射</param>
        private void ReplaceVariablesInTables(XWPFDocument doc, Dictionary<string, string> variableMap)
        {
            foreach (var table in doc.Tables)
            {
                foreach (var row in table.Rows)
                {
                    foreach (var cell in row.GetTableCells())
                    {
                        foreach (var paragraph in cell.Paragraphs)
                        {
                            foreach (var variable in variableMap)
                            {
                                if (paragraph.Text.Contains(variable.Key))
                                {
                                    ReplaceTextInParagraph(paragraph, variable.Key, variable.Value);
                                }
                            }
                        }
                    }
                }
            }
        }

        /// <summary>
        /// 替换段落中的文本，保留原始格式
        /// </summary>
        /// <param name="paragraph">段落</param>
        /// <param name="searchText">要搜索的文本</param>
        /// <param name="replaceText">替换文本</param>
        private void ReplaceTextInParagraph(XWPFParagraph paragraph, string searchText, string replaceText)
        {
            // 查找包含占位符的运行
            for (int i = 0; i < paragraph.Runs.Count; i++)
            {
                var run = paragraph.Runs[i];
                string runText = run.Text;

                if (runText != null && runText.Contains(searchText))
                {
                    // 保留格式，仅替换文本
                    run.SetText(runText.Replace(searchText, replaceText), 0);
                    return; // 替换完成
                }

                // 处理跨多个运行的占位符情况
                if (i < paragraph.Runs.Count - 1 && runText != null)
                {
                    // 查找是否占位符跨多个运行
                    string combinedText = runText;
                    int startRun = i;
                    int j = i + 1;

                    // 尝试组合多个运行的文本查找占位符
                    while (j < paragraph.Runs.Count && !combinedText.Contains(searchText))
                    {
                        string nextRunText = paragraph.Runs[j].Text ?? "";
                        combinedText += nextRunText;

                        if (combinedText.Contains(searchText))
                        {
                            // 找到了跨越多个运行的占位符
                            // 现在需要删除整个占位符并在第一个运行位置插入替换文本

                            // 在第一个运行中放入替换文本
                            int placeholderStart = combinedText.IndexOf(searchText);
                            int firstRunLength = runText.Length;

                            if (placeholderStart < firstRunLength)
                            {
                                // 占位符开始于第一个运行
                                string beforePlaceholder = runText.Substring(0, placeholderStart);
                                run.SetText(beforePlaceholder + replaceText, 0);

                                // 计算占位符末尾位置
                                int placeholderEnd = placeholderStart + searchText.Length;

                                // 如果占位符结束于当前运行之后，需要调整后面的运行
                                if (placeholderEnd > firstRunLength)
                                {
                                    int currentEnd = firstRunLength;
                                    int currentRun = i + 1;

                                    // 处理或删除跨越的运行
                                    while (currentRun <= j && currentEnd < placeholderEnd)
                                    {
                                        string currentText = paragraph.Runs[currentRun].Text ?? "";
                                        int currentLength = currentText.Length;

                                        if (currentEnd + currentLength > placeholderEnd)
                                        {
                                            // 这个运行包含占位符结束后的文本
                                            string afterPlaceholder = currentText.Substring(
                                                placeholderEnd - currentEnd);
                                            paragraph.Runs[currentRun].SetText(afterPlaceholder, 0);
                                            break;
                                        }
                                        else
                                        {
                                            // 这个运行完全在占位符内，清空它
                                            paragraph.Runs[currentRun].SetText("", 0);
                                        }

                                        currentEnd += currentLength;
                                        currentRun++;
                                    }
                                }

                                return; // 替换完成
                            }
                        }

                        j++;
                    }
                }
            }
        }
    }
}
