using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.IO;
using Newtonsoft.Json;
using RESClient.Utils;
using RESClient.MVP.Base;

namespace RESClient.MVP.Models
{
    /// <summary>
    /// 地下室人防区域说明参数数据模型
    /// </summary>
    public class BasementDefenseParametersModel : BaseModel
    {
        /// <summary>
        /// 地下室人防区域说明参数数据结构
        /// </summary>
        public class BasementDefenseParameters
        {
            [DisplayName("依据提供方")]
            [Description("提供依据资料的单位或机构")]
            public string DataProvider { get; set; } = "/";

            [DisplayName("图纸名称")]
            [Description("相关图纸或资料的名称")]
            public string DrawingName { get; set; } = "/";

            [DisplayName("说明")]
            [Description("地下室人防区域的详细说明")]
            public string Description { get; set; } = "/";

            /// <summary>
            /// 获取所有变量的映射字典
            /// </summary>
            /// <returns>变量名到值的映射</returns>
            public Dictionary<string, string> GetVariableMapping()
            {
                return new Dictionary<string, string>
                {
                    { "${依据提供方}", DataProvider },
                    { "${图纸名称}", DrawingName },
                    { "${说明}", Description }
                };
            }

            /// <summary>
            /// 验证参数完整性
            /// </summary>
            /// <returns>验证结果和错误信息</returns>
            public (bool IsValid, List<string> Errors) Validate()
            {
                var errors = new List<string>();

                if (string.IsNullOrWhiteSpace(DataProvider))
                    errors.Add("依据提供方不能为空");

                if (string.IsNullOrWhiteSpace(DrawingName))
                    errors.Add("图纸名称不能为空");

                if (string.IsNullOrWhiteSpace(Description))
                    errors.Add("说明不能为空");

                return (errors.Count == 0, errors);
            }
        }

        private readonly string _configFilePath;
        private BasementDefenseParameters _currentParameters;

        /// <summary>
        /// 构造函数
        /// </summary>
        public BasementDefenseParametersModel()
        {
            // 设置配置文件路径
            string appDataPath = Environment.GetFolderPath(Environment.SpecialFolder.LocalApplicationData);
            string appFolder = Path.Combine(appDataPath, "RESClient");
            
            if (!Directory.Exists(appFolder))
            {
                Directory.CreateDirectory(appFolder);
            }
            
            _configFilePath = Path.Combine(appFolder, "basement_defense_parameters.json");
            
            // 加载参数
            _currentParameters = LoadParameters();
        }

        /// <summary>
        /// 获取当前参数
        /// </summary>
        /// <returns>当前参数</returns>
        public BasementDefenseParameters GetCurrentParameters()
        {
            return _currentParameters;
        }

        /// <summary>
        /// 保存参数
        /// </summary>
        /// <param name="parameters">要保存的参数</param>
        /// <returns>保存是否成功</returns>
        public bool SaveParameters(BasementDefenseParameters parameters)
        {
            try
            {
                _currentParameters = parameters;
                string json = JsonConvert.SerializeObject(parameters, Formatting.Indented);
                File.WriteAllText(_configFilePath, json, System.Text.Encoding.UTF8);
                return true;
            }
            catch (Exception ex)
            {
                HandleException(ex);
                return false;
            }
        }

        /// <summary>
        /// 加载参数
        /// </summary>
        /// <returns>加载的参数，如果失败则返回默认参数</returns>
        private BasementDefenseParameters LoadParameters()
        {
            try
            {
                if (!File.Exists(_configFilePath))
                {
                    return CreateDefaultParameters();
                }

                string json = File.ReadAllText(_configFilePath, System.Text.Encoding.UTF8);
                var parameters = JsonConvert.DeserializeObject<BasementDefenseParameters>(json);
                
                return parameters ?? CreateDefaultParameters();
            }
            catch (Exception ex)
            {
                HandleException(ex);
                return CreateDefaultParameters();
            }
        }

        /// <summary>
        /// 创建默认参数
        /// </summary>
        /// <returns>默认参数</returns>
        private BasementDefenseParameters CreateDefaultParameters()
        {
            return new BasementDefenseParameters
            {
                DataProvider = "/",
                DrawingName = "/",
                Description = "/"
            };
        }
    }
}
