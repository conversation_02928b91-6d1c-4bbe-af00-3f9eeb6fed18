using System;
using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;

namespace RESClient.Services.Implementations
{
    /// <summary>
    /// 目录模块生成器
    /// </summary>
    public class TableOfContentsModuleGenerator : IReportModuleGenerator
    {
        /// <summary>
        /// 模块名称
        /// </summary>
        public string ModuleName => "目录";

        /// <summary>
        /// 生成报告模块
        /// </summary>
        /// <param name="parameters">生成参数</param>
        /// <param name="progressCallback">进度回调</param>
        /// <returns>生成结果</returns>
        public Task<bool> GenerateAsync(Dictionary<string, object> parameters, Action<int, string> progressCallback)
        {
            try
            {
                // 记录开始进度
                progressCallback?.Invoke(0, $"[INFO]开始生成{ModuleName}...");

                // 1. 获取输出目录
                if (!parameters.TryGetValue("OutputDir", out object outputDirectoryObj) || outputDirectoryObj == null)
                {
                    progressCallback?.Invoke(0, "[ERROR]未找到输出目录");
                    return Task.FromResult(false);
                }

                string outputDirectory = outputDirectoryObj.ToString();
                progressCallback?.Invoke(10, $"[INFO]输出目录: {outputDirectory}");

                // 2. 确保输出目录存在
                if (!Directory.Exists(outputDirectory))
                {
                    Directory.CreateDirectory(outputDirectory);
                    progressCallback?.Invoke(20, "[INFO]创建输出目录");
                }

                // 3. 获取模板文件路径
                string templateDirectory = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "报告模板");
                string templatePath = Path.Combine(templateDirectory, "02_目录", "目录.docx");

                if (!File.Exists(templatePath))
                {
                    progressCallback?.Invoke(30, $"[ERROR]模板文件不存在: {templatePath}");
                    return Task.FromResult(false);
                }

                progressCallback?.Invoke(40, "[INFO]找到模板文件");

                // 4. 创建输出文件路径
                string outputPath = Path.Combine(outputDirectory, $"{ModuleName}.docx");

                // 5. 直接复制模板文件到输出目录
                File.Copy(templatePath, outputPath, true);
                progressCallback?.Invoke(80, "[INFO]复制模板文件到输出目录");

                // 记录完成进度
                progressCallback?.Invoke(100, $"[SUCCESS]{ModuleName}生成完成");

                return Task.FromResult(true);
            }
            catch (Exception ex)
            {
                progressCallback?.Invoke(100, $"[ERROR]{ModuleName}生成过程中发生异常: {ex.Message}");
                return Task.FromResult(false);
            }
        }

        /// <summary>
        /// 是否可用
        /// </summary>
        /// <param name="parameters">检查参数</param>
        /// <returns>是否可用</returns>
        public bool IsAvailable(Dictionary<string, object> parameters)
        {
            try
            {
                // 检查模板文件是否存在
                string templateDirectory = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "报告模板");
                string templatePath = Path.Combine(templateDirectory, "02_目录", "目录.docx");

                return File.Exists(templatePath);
            }
            catch
            {
                return false;
            }
        }
    }
} 