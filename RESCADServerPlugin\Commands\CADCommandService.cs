using System;
using Autodesk.AutoCAD.Runtime;
using AcadApp = Autodesk.AutoCAD.ApplicationServices;
using RESCADServerPlugin.Commands.Implementations;

[assembly: CommandClass(typeof(RESCADServerPlugin.Commands.CADCommandService))]

namespace RESCADServerPlugin.Commands
{
    /// <summary>
    /// 主命令服务类，负责注册和管理所有AutoCAD命令
    /// </summary>
    public class CADCommandService : IExtensionApplication
    {
        public void Initialize()
        {
            // 在这里进行插件初始化
            AcadApp.Application.DocumentManager.MdiActiveDocument?.Editor.WriteMessage("\nRESCADServerPlugin 命令已加载.");
        }

        public void Terminate()
        {
            // 在这里进行插件清理
        }

        #region 命令注册

        // 测试命令
        [CommandMethod("RESTestCommand", CommandFlags.Modal)]
        public void TestCommand() => new TestCommands().ExecuteTestCommand();

        // 显示Web API服务状态的命令
        [CommandMethod("RESAPIStatus", CommandFlags.Modal)]
        public void APIStatus() => new StatusCommands().ExecuteAPIStatus();
        
        // 演示绘图服务的命令
        [CommandMethod("RESDrawDemo", CommandFlags.Modal)]
        public void DrawDemo() => new DrawingCommands().ExecuteDrawDemo();

        // 获取实体扩展属性数据的命令
        [CommandMethod("RESGetXData", CommandFlags.Modal)]
        public void GetEntityExtensionData() => new EntityDataCommands().ExecuteGetEntityExtensionData();

        // 获取实体Xrecord扩展记录的命令
        [CommandMethod("RESGetXrecord", CommandFlags.Modal)]
        public void GetEntityXrecord() => new EntityDataCommands().ExecuteGetEntityXrecord();

        // 新增命令：查找打印图并保存为独立文件
        [CommandMethod("RESSavePrintDrawings", CommandFlags.Modal)]
        public void SavePrintDrawings() => new PrintDrawingCommands().ExecuteSavePrintDrawings();

        // 获取块参照内所有实体扩展属性的命令
        [CommandMethod("RESGetBlockXData", CommandFlags.Modal)]
        public void GetBlockReferenceEntitiesXData() => new EntityDataCommands().ExecuteGetBlockReferenceEntitiesXData();

        // 获取块参照内所有实体扩展记录（Xrecord）的命令
        [CommandMethod("RESGetBlockXrecord", CommandFlags.Modal)]
        public void GetBlockReferenceEntitiesXrecord() => new EntityDataCommands().ExecuteGetBlockReferenceEntitiesXrecord();

        // 实体数据全面分析命令 - 基于DataDumper功能
        [CommandMethod("RES_DUMP_ENTITY_DATA", CommandFlags.Modal)]
        public void DumpEntityData() => new EntityDataCommands().ExecuteDumpEntityData();

        // 生成分层平面图命令
        [CommandMethod("RES_GENERATE_FLOOR_PLANS", CommandFlags.Modal)]
        public void GenerateFloorPlans() => new FloorPlanCommands().ExecuteGenerateFloorPlans();

        // 调试CDCH HI实体命令
        [CommandMethod("RES_DEBUG_CDCH_HI_ENTITIES", CommandFlags.Modal)]
        public void DebugCDCHHIEntities() => new FloorPlanCommands().ExecuteDebugCDCHHIEntities();

        #endregion
    }
} 