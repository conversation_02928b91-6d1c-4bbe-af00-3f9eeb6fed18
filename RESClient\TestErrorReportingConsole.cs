using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using RESClient.Services;
using RESClient.MVP.Models;

namespace RESClient
{
    /// <summary>
    /// 控制台版本的错误报告系统测试
    /// </summary>
    public class TestErrorReportingConsole
    {
        /// <summary>
        /// 运行完整的错误报告系统测试
        /// </summary>
        public static async Task RunCompleteTest()
        {
            Console.WriteLine("=== 错误报告系统完整测试 ===");
            Console.WriteLine();

            try
            {
                // 1. 测试基本错误收集功能
                Console.WriteLine("1. 测试基本错误收集功能");
                Console.WriteLine("----------------------------------------");
                TestBasicErrorCollection();
                Console.WriteLine();

                // 2. 测试进度消息解析
                Console.WriteLine("2. 测试进度消息解析");
                Console.WriteLine("----------------------------------------");
                TestProgressMessageParsing();
                Console.WriteLine();

                // 3. 测试错误统计和分类
                Console.WriteLine("3. 测试错误统计和分类");
                Console.WriteLine("----------------------------------------");
                TestErrorStatisticsAndCategorization();
                Console.WriteLine();

                // 4. 模拟报告生成失败场景
                Console.WriteLine("4. 模拟报告生成失败场景");
                Console.WriteLine("----------------------------------------");
                await SimulateReportGenerationFailure();
                Console.WriteLine();

                // 5. 测试错误报告生成
                Console.WriteLine("5. 测试错误报告生成");
                Console.WriteLine("----------------------------------------");
                TestErrorReportGeneration();
                Console.WriteLine();

                Console.WriteLine("✅ 所有测试完成！错误报告系统工作正常。");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ 测试过程中发生错误: {ex.Message}");
                Console.WriteLine($"详细信息: {ex.StackTrace}");
            }
        }

        /// <summary>
        /// 测试基本错误收集功能
        /// </summary>
        private static void TestBasicErrorCollection()
        {
            var errorCollector = new ErrorCollector();

            // 添加各种类型的错误
            errorCollector.AddError(ErrorType.MissingFile, ErrorSeverity.Error, 
                "封面", "模板文件不存在", 
                details: "找不到封面模板文件 cover_template.docx",
                suggestedSolution: "请检查模板目录中是否包含封面模板文件");

            errorCollector.AddError(ErrorType.DataValidation, ErrorSeverity.Warning, 
                "项目基本信息", "项目名称为空", 
                suggestedSolution: "请在参数设置中填写项目名称");

            errorCollector.AddError(ErrorType.DocumentGeneration, ErrorSeverity.Error, 
                "楼栋基本信息", "Excel数据读取失败",
                details: "无法读取CGB.xls文件中的楼栋信息",
                suggestedSolution: "请检查Excel文件格式和内容是否正确");

            // 验证错误收集结果
            Console.WriteLine($"   总错误数: {errorCollector.GetErrorCount()}");
            Console.WriteLine($"   是否有严重错误: {errorCollector.HasCriticalErrors()}");
            Console.WriteLine($"   错误模块数: {errorCollector.GetAllErrors().GroupBy(e => e.ModuleName).Count()}");
            Console.WriteLine("   ✅ 基本错误收集功能正常");
        }

        /// <summary>
        /// 测试进度消息解析
        /// </summary>
        private static void TestProgressMessageParsing()
        {
            var errorCollector = new ErrorCollector();

            // 模拟各种进度消息
            var progressMessages = new[]
            {
                "[MODULE_FAILED]封面:模板文件不存在",
                "[ERROR]资源文件验证失败: 找不到CGB.xls文件",
                "[WARNING]用户参数加载失败，使用默认值",
                "[MODULE_SUCCESS]目录:模块生成成功",
                "[INFO]开始生成报告模块..."
            };

            Console.WriteLine("   解析进度消息:");
            foreach (var message in progressMessages)
            {
                errorCollector.ParseAndAddErrorFromProgressMessage(message, "测试模块");
                Console.WriteLine($"   - {message}");
            }

            var parsedErrors = errorCollector.GetAllErrors();
            Console.WriteLine($"   成功解析出 {parsedErrors.Count} 个错误");
            Console.WriteLine("   ✅ 进度消息解析功能正常");
        }

        /// <summary>
        /// 测试错误统计和分类
        /// </summary>
        private static void TestErrorStatisticsAndCategorization()
        {
            var errorCollector = new ErrorCollector();

            // 添加多种类型和严重程度的错误
            var testErrors = new[]
            {
                (ErrorType.MissingFile, ErrorSeverity.Error, "封面", "模板文件缺失"),
                (ErrorType.MissingFile, ErrorSeverity.Error, "目录", "模板文件缺失"),
                (ErrorType.DataValidation, ErrorSeverity.Warning, "项目信息", "数据验证警告"),
                (ErrorType.DocumentGeneration, ErrorSeverity.Error, "楼栋信息", "生成失败"),
                (ErrorType.SystemException, ErrorSeverity.Critical, "系统", "内存不足")
            };

            foreach (var (type, severity, module, message) in testErrors)
            {
                errorCollector.AddError(type, severity, module, message);
            }

            // 显示统计信息
            var typeStats = errorCollector.GetErrorStatistics();
            var severityStats = errorCollector.GetSeverityStatistics();

            Console.WriteLine("   错误类型统计:");
            foreach (var stat in typeStats)
            {
                Console.WriteLine($"   - {GetErrorTypeDisplayName(stat.Key)}: {stat.Value} 个");
            }

            Console.WriteLine("   严重程度统计:");
            foreach (var stat in severityStats)
            {
                Console.WriteLine($"   - {GetSeverityDisplayName(stat.Key)}: {stat.Value} 个");
            }

            Console.WriteLine("   ✅ 错误统计和分类功能正常");
        }

        /// <summary>
        /// 模拟报告生成失败场景
        /// </summary>
        private static async Task SimulateReportGenerationFailure()
        {
            Console.WriteLine("   模拟报告生成过程...");

            var mainModel = new MainModel();
            var progressMessages = new List<string>();

            // 模拟进度回调
            Action<int, string> progressCallback = (percent, message) =>
            {
                progressMessages.Add(message);
                Console.WriteLine($"   [{percent}%] {message}");
            };

            // 模拟失败的参数（缺少必要参数）
            var parameters = new Dictionary<string, object>();

            // 尝试生成报告（预期会失败）
            bool result = await mainModel.GenerateReportAsync(parameters, progressCallback);

            Console.WriteLine($"   生成结果: {(result ? "成功" : "失败")}");
            Console.WriteLine($"   收集到的错误数: {mainModel.ErrorCollector.GetErrorCount()}");

            // 显示收集到的错误
            var errors = mainModel.ErrorCollector.GetAllErrors();
            foreach (var error in errors)
            {
                Console.WriteLine($"   - [{GetSeverityDisplayName(error.Severity)}] {error.Message}");
                if (!string.IsNullOrEmpty(error.SuggestedSolution))
                {
                    Console.WriteLine($"     建议: {error.SuggestedSolution}");
                }
            }

            Console.WriteLine("   ✅ 报告生成失败场景模拟完成");
        }

        /// <summary>
        /// 测试错误报告生成
        /// </summary>
        private static void TestErrorReportGeneration()
        {
            var errorCollector = new ErrorCollector();

            // 添加一些测试错误
            errorCollector.AddError(ErrorType.MissingFile, ErrorSeverity.Error, 
                "封面", "模板文件不存在", 
                details: "找不到封面模板文件 cover_template.docx",
                suggestedSolution: "请检查模板目录中是否包含封面模板文件");

            errorCollector.AddError(ErrorType.DataValidation, ErrorSeverity.Warning, 
                "项目基本信息", "项目名称为空", 
                suggestedSolution: "请在参数设置中填写项目名称");

            // 生成错误报告文本（模拟ErrorSummaryDialog的报告生成功能）
            var report = GenerateSimpleErrorReport(errorCollector.GetAllErrors());
            
            Console.WriteLine("   生成的错误报告:");
            Console.WriteLine("   " + new string('-', 50));
            foreach (var line in report.Split('\n'))
            {
                if (!string.IsNullOrWhiteSpace(line))
                    Console.WriteLine($"   {line}");
            }
            Console.WriteLine("   " + new string('-', 50));
            Console.WriteLine("   ✅ 错误报告生成功能正常");
        }

        /// <summary>
        /// 生成简单的错误报告文本
        /// </summary>
        private static string GenerateSimpleErrorReport(List<ErrorInfo> errors)
        {
            var report = new System.Text.StringBuilder();
            report.AppendLine("错误摘要报告");
            report.AppendLine($"生成时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss}");
            report.AppendLine($"错误总数: {errors.Count}");
            report.AppendLine();

            if (errors.Count == 0)
            {
                report.AppendLine("没有发现错误。");
                return report.ToString();
            }

            // 按类型分组显示错误
            var errorsByType = errors.GroupBy(e => e.Type);
            foreach (var typeGroup in errorsByType)
            {
                report.AppendLine($"{GetErrorTypeDisplayName(typeGroup.Key)} ({typeGroup.Count()} 个):");
                
                foreach (var error in typeGroup)
                {
                    report.AppendLine($"  [{GetSeverityDisplayName(error.Severity)}] {error.ModuleName}: {error.Message}");
                    if (!string.IsNullOrEmpty(error.SuggestedSolution))
                    {
                        report.AppendLine($"    建议: {error.SuggestedSolution}");
                    }
                }
                report.AppendLine();
            }

            return report.ToString();
        }

        /// <summary>
        /// 获取错误类型显示名称
        /// </summary>
        private static string GetErrorTypeDisplayName(ErrorType type)
        {
            switch (type)
            {
                case ErrorType.MissingFile: return "缺失文件";
                case ErrorType.DataValidation: return "数据验证";
                case ErrorType.TemplateProcessing: return "模板处理";
                case ErrorType.ParameterConfiguration: return "参数配置";
                case ErrorType.DocumentGeneration: return "文档生成";
                case ErrorType.DocumentMerging: return "文档合并";
                case ErrorType.SystemException: return "系统异常";
                default: return "其他错误";
            }
        }

        /// <summary>
        /// 获取严重程度显示名称
        /// </summary>
        private static string GetSeverityDisplayName(ErrorSeverity severity)
        {
            switch (severity)
            {
                case ErrorSeverity.Info: return "信息";
                case ErrorSeverity.Warning: return "警告";
                case ErrorSeverity.Error: return "错误";
                case ErrorSeverity.Critical: return "严重错误";
                default: return "未知";
            }
        }
    }
}
