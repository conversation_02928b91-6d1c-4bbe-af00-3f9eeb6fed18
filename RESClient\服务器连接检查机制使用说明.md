# RESClient 服务器连接检查机制使用说明

## 概述

RESClient 现在具备了完整的服务器连接检查机制，确保在无法与 RESServer 通信时完全停止工作，强制用户解决连接问题后才能使用系统。

## 功能特性

### 🔍 启动时连接检查
- **强制检查**：RESClient 启动时必须首先检查与 RESServer 的连接状态
- **阻断机制**：如果无法连接到服务器，所有业务功能将被禁用
- **状态显示**：实时显示连接检查进度和结果

### ❌ 连接失败处理
- **明确提示**：显示详细的错误提示信息和解决建议
- **功能禁用**：禁用所有业务功能按钮和菜单项
- **操作阻止**：阻止用户执行任何业务逻辑操作

### 🔄 重连机制
- **重试按钮**：提供"重试连接"按钮，允许用户手动重新尝试连接
- **自动重试**：在某些情况下自动重试连接
- **状态更新**：实时更新连接状态和重试结果

### 📊 状态指示
- **状态栏**：在主界面底部显示当前连接状态
- **颜色指示**：使用不同颜色表示连接状态（绿色=已连接，红色=连接失败）
- **详细信息**：显示响应时间、服务器版本等详细信息

### 🚫 功能限制范围
- **模块生成功能**：禁用所有报告模块生成功能
- **文件处理功能**：禁用 DWG 文件处理和转换功能
- **报告生成功能**：禁用集成报告生成功能
- **保留功能**：仅保留基本的界面显示和连接重试功能

## 界面说明

### 主界面状态栏
位于主界面底部的状态栏显示：
- **连接状态图标**：● 圆点图标，颜色表示状态
- **状态文本**：显示当前连接状态和响应时间
- **重试按钮**：连接失败时显示，点击重新尝试连接

### 连接错误对话框
当连接失败时显示的详细错误对话框包含：
- **错误信息**：详细的连接错误信息
- **错误详情**：技术性错误详情和诊断信息
- **解决建议**：分步骤的问题解决指南
- **操作按钮**：重试连接、打开设置、退出程序

## 连接状态说明

### 状态类型
| 状态 | 图标颜色 | 说明 | 功能可用性 |
|------|----------|------|------------|
| 未知 | 灰色 | 尚未检查连接状态 | 禁用 |
| 连接中 | 橙色 | 正在检查连接状态 | 禁用 |
| 已连接 | 绿色 | 服务器连接正常 | 启用 |
| 连接失败 | 红色 | 服务器连接失败 | 禁用 |
| 连接超时 | 红色 | 连接超时 | 禁用 |
| 服务器不可达 | 红色 | 无法访问服务器 | 禁用 |

### 状态检查流程
1. **启动检查**：应用程序启动时自动检查
2. **超时设置**：默认15秒连接超时
3. **状态更新**：实时更新UI显示
4. **功能控制**：根据状态启用/禁用功能
5. **错误处理**：显示错误对话框和解决建议

## 使用场景

### 场景 1：正常启动
1. RESClient 启动
2. 自动检查服务器连接
3. 连接成功，显示绿色状态
4. 所有功能正常可用

### 场景 2：服务器未启动
1. RESClient 启动
2. 检查服务器连接失败
3. 显示连接错误对话框
4. 用户可以选择：
   - 重试连接
   - 查看设置
   - 退出程序

### 场景 3：网络问题
1. 连接检查超时
2. 状态栏显示红色超时状态
3. 功能被禁用
4. 用户点击"重试连接"按钮
5. 重新检查连接状态

### 场景 4：运行中断线
1. 运行过程中服务器停止
2. 下次操作时检测到连接失败
3. 显示操作被阻止的提示
4. 引导用户检查连接状态

## 错误解决指南

### 常见问题及解决方案

**1. 服务器未启动**
- 问题：RESServer 应用程序未运行
- 解决：启动 RESServer 应用程序

**2. 网络连接问题**
- 问题：网络不通或防火墙阻止
- 解决：检查网络连接，配置防火墙规则

**3. 服务器地址错误**
- 问题：配置的服务器地址不正确
- 解决：检查 App.config 中的服务器地址配置

**4. 端口被占用**
- 问题：服务器端口被其他程序占用
- 解决：更改服务器端口或停止占用端口的程序

**5. 连接超时**
- 问题：服务器响应缓慢
- 解决：检查服务器性能，增加超时时间

### 配置文件检查
检查 `App.config` 文件中的服务器配置：
```xml
<appSettings>
  <add key="RESServerAddress" value="http://localhost:5104" />
  <add key="RESServerConnectionTimeout" value="15" />
  <add key="RESServerRequestTimeout" value="10" />
</appSettings>
```

### 服务器状态检查
手动检查服务器状态：
1. 打开浏览器
2. 访问：`http://localhost:5104/api/auth/status`
3. 应该看到 JSON 格式的状态响应

## 技术实现

### 核心组件
- **ServerConnectionService**：连接检查服务
- **ConnectionErrorForm**：错误提示窗体
- **主界面状态栏**：状态显示组件

### 检查机制
- **HTTP 请求**：使用 HTTP GET 请求检查服务器状态
- **超时控制**：可配置的连接超时时间
- **错误分类**：详细的错误状态分类
- **重试逻辑**：智能的重试机制

### 安全特性
- **功能隔离**：连接失败时完全隔离业务功能
- **状态验证**：每次操作前验证连接状态
- **用户提示**：清晰的错误提示和解决建议

## 配置选项

### 连接超时设置
在 `App.config` 中配置：
```xml
<!-- 连接超时时间（分钟） -->
<add key="RESServerConnectionTimeout" value="15" />
<!-- 请求超时时间（分钟） -->
<add key="RESServerRequestTimeout" value="10" />
```

### 自动检查设置
```xml
<!-- 是否启用自动连接检查 -->
<add key="EnableAutoConnectionCheck" value="true" />
```

## 总结

RESClient 的服务器连接检查机制确保了：

✅ **强制连接验证**：启动时必须验证服务器连接
✅ **完整功能控制**：连接失败时禁用所有业务功能
✅ **清晰错误提示**：详细的错误信息和解决建议
✅ **便捷重试机制**：简单的重试连接操作
✅ **实时状态显示**：直观的连接状态指示

这个机制确保了 RESClient 在无法与 RESServer 通信时完全停止工作，强制用户解决连接问题后才能使用系统，提高了系统的可靠性和用户体验。
