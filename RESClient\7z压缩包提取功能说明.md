# 7z压缩包提取功能说明

## 概述

本功能实现了对"成果包.7z"压缩文件的自动提取和CGB.xls文件查找，支持现有的CGB.xls文件处理模块无缝升级到压缩包模式。

## 功能特性

### 1. 自动检测和提取
- 自动检测资源目录中的"成果包.7z"文件
- 支持多个成果包文件，自动选择第一个进行提取
- 提取到临时目录，避免污染原始数据目录

### 2. 向后兼容
- 优先查找直接的CGB.xls文件（保持向后兼容）
- 如果没有找到直接文件，则尝试从7z压缩包中提取
- 无需修改现有的数据目录结构

### 3. 自动清理
- 自动管理临时文件和目录
- 在模块生成完成后自动清理提取的临时文件
- 支持异常情况下的资源清理

## 技术实现

### 核心服务类

#### ArchiveExtractionService
位置：`RESClient/Services/ArchiveExtractionService.cs`

主要方法：
- `FindArchiveFiles(string dataDirectory)` - 查找成果包.7z文件
- `ExtractArchive(string archiveFilePath, Action<int, string> progressCallback)` - 提取7z文件
- `FindCGBFileInExtracted(string extractedDirectory)` - 在提取目录中查找CGB文件
- `ExtractAndFindCGBFile(string dataDirectory, Action<int, string> progressCallback)` - 完整的提取和查找流程
- `CleanupTempDirectories()` - 清理临时目录
- `Dispose()` - 释放资源

### 修改的模块

#### 1. EstateAreaSummaryModuleGenerator（房产面积汇总表）
位置：`RESClient/Services/Implementations/EstateAreaSummaryModuleGenerator.cs`

修改内容：
- 添加了ArchiveExtractionService字段
- 修改了FindCGBFile方法，支持从7z压缩包提取
- 更新了IsAvailable方法，检查7z文件存在性
- 添加了资源清理逻辑

#### 2. BuildingInfoModuleGenerator（楼栋基本信息）
位置：`RESClient/Services/Implementations/BuildingInfoModuleGenerator.cs`

修改内容：
- 添加了ArchiveExtractionService字段
- 修改了FindCGBFile方法，支持从7z压缩包提取
- 更新了IsAvailable方法，检查7z文件存在性
- 添加了资源清理逻辑

## 使用方法

### 1. 数据目录结构

支持以下两种数据目录结构：

#### 传统结构（向后兼容）
```
数据目录/
├── CGB.xls
└── 其他文件...
```

#### 新的压缩包结构
```
数据目录/
├── 成果包-1.7z
├── 成果包-2.7z
└── 其他文件...
```

其中成果包.7z内部包含：
```
成果包.7z
├── CGB.xls
└── 其他相关文件...
```

### 2. 模块调用

模块调用方式保持不变，系统会自动检测和处理：

```csharp
var generator = new EstateAreaSummaryModuleGenerator();
var parameters = new Dictionary<string, object>
{
    ["DataFolder"] = @"C:\数据目录",
    ["OutputDir"] = @"C:\输出目录"
};

bool result = await generator.GenerateAsync(parameters, progressCallback);
```

### 3. 进度反馈

系统会提供详细的进度反馈：
- 查找成果包.7z文件
- 提取压缩包进度
- 查找CGB.xls文件
- 文件处理进度

## 依赖库

### SevenZipSharp
- 版本：********
- 用途：处理7z压缩文件的提取
- 配置：已在packages.config和项目文件中配置

### 7z.dll
- 位置：`tool/7z/7z.dll`
- 用途：SevenZipSharp的底层库
- 部署：自动复制到输出目录

## 测试

### 测试文件
位置：`RESClient/TestArchiveExtraction.cs`

测试内容：
1. ArchiveExtractionService基本功能测试
2. 模块生成器与7z压缩包的集成测试
3. 房产面积汇总表模块测试
4. 楼栋基本信息模块测试

### 运行测试

1. 创建测试目录：`C:\TestData`
2. 放入包含CGB.xls的成果包.7z文件
3. 运行TestArchiveExtraction.cs

## 错误处理

### 常见错误及解决方案

1. **7z.dll not found**
   - 确保7z.dll在tool/7z/目录下
   - 检查文件是否正确复制到输出目录

2. **无法提取7z文件**
   - 检查7z文件是否损坏
   - 确认文件权限是否正确
   - 检查磁盘空间是否充足

3. **未找到CGB.xls文件**
   - 检查7z文件内部是否包含CGB.xls
   - 确认文件名是否正确（包含"cgb"字符）

4. **临时目录清理失败**
   - 通常是文件被占用，系统会在后续自动清理
   - 可以手动删除临时目录

## 性能考虑

1. **内存使用**：提取过程会占用额外内存，大文件时需注意
2. **磁盘空间**：临时提取需要额外磁盘空间
3. **处理时间**：压缩包提取会增加处理时间，但提供了进度反馈

## 维护说明

1. **定期清理**：系统会自动清理临时文件，但建议定期检查临时目录
2. **版本升级**：升级SevenZipSharp时需要同时更新7z.dll
3. **扩展支持**：如需支持其他压缩格式，可以扩展ArchiveExtractionService

## 总结

本功能实现了对7z压缩包的完整支持，同时保持了向后兼容性。用户可以无缝从传统的CGB.xls文件模式升级到压缩包模式，系统会自动处理所有的提取和清理工作。
