using NPOI.HSSF.UserModel;
using NPOI.SS.UserModel;
using NPOI.XSSF.UserModel;
using System.IO;

namespace RESServer.Services
{
    public class FileConversionService
    {
        private readonly ILogger<FileConversionService> _logger;

        public FileConversionService(ILogger<FileConversionService> logger)
        {
            _logger = logger;
        }

        /// <summary>
        /// 将.xls文件转换为.xlsx格式
        /// </summary>
        /// <param name="xlsData">原始.xls文件的字节数据</param>
        /// <param name="fileName">原始文件名</param>
        /// <returns>转换后的.xlsx文件数据和新的MIME类型</returns>
        public Task<(byte[] Data, string MimeType, string FileName)> ConvertXlsToXlsxAsync(byte[] xlsData, string fileName)
        {
            try
            {
                _logger.LogInformation($"开始转换.xls文件: {fileName}");

                using var inputStream = new MemoryStream(xlsData);
                using var outputStream = new MemoryStream();

                // 读取.xls文件
                var xlsWorkbook = new HSSFWorkbook(inputStream);
                
                // 创建新的.xlsx工作簿
                var xlsxWorkbook = new XSSFWorkbook();

                // 复制所有工作表
                for (int i = 0; i < xlsWorkbook.NumberOfSheets; i++)
                {
                    var sourceSheet = xlsWorkbook.GetSheetAt(i);
                    var targetSheet = xlsxWorkbook.CreateSheet(sourceSheet.SheetName);

                    CopySheet(sourceSheet, targetSheet, xlsxWorkbook);
                }

                // 写入输出流
                xlsxWorkbook.Write(outputStream, false);
                
                // 生成新的文件名
                string newFileName = Path.ChangeExtension(fileName, ".xlsx");
                
                _logger.LogInformation($"成功转换文件: {fileName} -> {newFileName}");

                return Task.FromResult((outputStream.ToArray(), "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", newFileName));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"转换.xls文件失败: {fileName}");
                throw new InvalidOperationException($"无法转换Excel文件 {fileName}: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 复制工作表内容
        /// </summary>
        private void CopySheet(ISheet sourceSheet, ISheet targetSheet, XSSFWorkbook targetWorkbook)
        {
            // 复制行数据
            for (int rowIndex = 0; rowIndex <= sourceSheet.LastRowNum; rowIndex++)
            {
                var sourceRow = sourceSheet.GetRow(rowIndex);
                if (sourceRow == null) continue;

                var targetRow = targetSheet.CreateRow(rowIndex);
                
                // 设置行高
                targetRow.Height = sourceRow.Height;

                // 复制单元格数据
                for (int cellIndex = 0; cellIndex < sourceRow.LastCellNum; cellIndex++)
                {
                    var sourceCell = sourceRow.GetCell(cellIndex);
                    if (sourceCell == null) continue;

                    var targetCell = targetRow.CreateCell(cellIndex);
                    CopyCell(sourceCell, targetCell, targetWorkbook);
                }
            }

            // 复制列宽
            for (int colIndex = 0; colIndex < 256; colIndex++) // Excel最大列数限制
            {
                if (sourceSheet.GetColumnWidth(colIndex) != sourceSheet.DefaultColumnWidth)
                {
                    targetSheet.SetColumnWidth(colIndex, sourceSheet.GetColumnWidth(colIndex));
                }
            }
        }

        /// <summary>
        /// 复制单元格内容
        /// </summary>
        private void CopyCell(ICell sourceCell, ICell targetCell, XSSFWorkbook targetWorkbook)
        {
            // 复制单元格类型和值
            switch (sourceCell.CellType)
            {
                case CellType.String:
                    targetCell.SetCellValue(sourceCell.StringCellValue);
                    break;
                case CellType.Numeric:
                    if (DateUtil.IsCellDateFormatted(sourceCell))
                    {
                        var dateValue = sourceCell.DateCellValue;
                        if (dateValue.HasValue)
                        {
                            targetCell.SetCellValue(dateValue.Value);
                        }
                        else
                        {
                            targetCell.SetCellValue(sourceCell.NumericCellValue);
                        }
                    }
                    else
                    {
                        targetCell.SetCellValue(sourceCell.NumericCellValue);
                    }
                    break;
                case CellType.Boolean:
                    targetCell.SetCellValue(sourceCell.BooleanCellValue);
                    break;
                case CellType.Formula:
                    targetCell.SetCellFormula(sourceCell.CellFormula);
                    break;
                case CellType.Blank:
                    targetCell.SetCellType(CellType.Blank);
                    break;
                default:
                    // 对于其他类型，尝试作为字符串处理
                    try
                    {
                        targetCell.SetCellValue(sourceCell.ToString());
                    }
                    catch
                    {
                        targetCell.SetCellType(CellType.Blank);
                    }
                    break;
            }

            // 复制单元格样式（简化版本）
            try
            {
                var sourceCellStyle = sourceCell.CellStyle;
                if (sourceCellStyle != null)
                {
                    var targetCellStyle = targetWorkbook.CreateCellStyle();
                    
                    // 复制基本样式属性
                    targetCellStyle.DataFormat = sourceCellStyle.DataFormat;
                    targetCellStyle.Alignment = sourceCellStyle.Alignment;
                    targetCellStyle.VerticalAlignment = sourceCellStyle.VerticalAlignment;
                    targetCellStyle.WrapText = sourceCellStyle.WrapText;
                    
                    targetCell.CellStyle = targetCellStyle;
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning($"复制单元格样式时出现警告: {ex.Message}");
                // 样式复制失败不影响数据转换
            }
        }

        /// <summary>
        /// 检查文件是否需要转换
        /// </summary>
        /// <param name="fileName">文件名</param>
        /// <param name="mimeType">MIME类型</param>
        /// <returns>是否需要转换</returns>
        public bool NeedsConversion(string fileName, string mimeType)
        {
            return mimeType.Equals("application/vnd.ms-excel", StringComparison.OrdinalIgnoreCase) ||
                   fileName.EndsWith(".xls", StringComparison.OrdinalIgnoreCase);
        }

        /// <summary>
        /// 获取Excel文件的工作表数量（用于快速分析）
        /// </summary>
        /// <param name="excelData">Excel文件数据</param>
        /// <param name="isXlsx">是否为.xlsx格式</param>
        /// <returns>工作表数量</returns>
        public int GetSheetCount(byte[] excelData, bool isXlsx = true)
        {
            try
            {
                using var stream = new MemoryStream(excelData);
                IWorkbook workbook = isXlsx ? new XSSFWorkbook(stream) : new HSSFWorkbook(stream);
                return workbook.NumberOfSheets;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取Excel工作表数量失败");
                return 0;
            }
        }

        /// <summary>
        /// 获取Excel文件的工作表名称列表
        /// </summary>
        /// <param name="excelData">Excel文件数据</param>
        /// <param name="isXlsx">是否为.xlsx格式</param>
        /// <returns>工作表名称列表</returns>
        public List<string> GetSheetNames(byte[] excelData, bool isXlsx = true)
        {
            var sheetNames = new List<string>();
            try
            {
                using var stream = new MemoryStream(excelData);
                IWorkbook workbook = isXlsx ? new XSSFWorkbook(stream) : new HSSFWorkbook(stream);
                
                for (int i = 0; i < workbook.NumberOfSheets; i++)
                {
                    sheetNames.Add(workbook.GetSheetName(i));
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取Excel工作表名称失败");
            }
            return sheetNames;
        }
    }
}
