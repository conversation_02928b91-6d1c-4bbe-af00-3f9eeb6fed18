实测/
secret_data/
RESClient/bin/
RESCADServerPlugin/bin/
RESServer/bin/
packages/

# Visual Studio
.vs/
[Bb]in/
[Oo]bj/
*.user
*.suo
*.sln.docstates

# User-specific files
*.[Cc]ache
*.[Ll]og

# Build results
[Dd]ebug/
[Rr]elease/
x64/
x86/

# RESClient specific cache and temporary files
RESClient/obj/
RESClient/bin/Debug/
RESClient/bin/Release/
RESClient/bin/Debug/*.exe
RESClient/bin/Debug/*.pdb
RESClient/bin/Debug/*.dll
RESClient/bin/Debug/*.config
RESClient/bin/Debug/*.log
RESClient/bin/Release/*.exe
RESClient/bin/Release/*.pdb
RESClient/bin/Release/*.dll
RESClient/bin/Release/*.config
RESClient/bin/Release/*.log

# RESClient application output directories
RESClient/bin/Debug/报告生成/
RESClient/bin/Debug/输出/
RESClient/bin/Release/报告生成/
RESClient/bin/Release/输出/

# Additional cache files
*.tmp
*.temp
*~
*.swp
*.swo
~$*.docx
~$*.xlsx
~$*.pptx

# Rider
.idea/