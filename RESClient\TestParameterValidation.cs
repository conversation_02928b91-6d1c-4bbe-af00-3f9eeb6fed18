using System;
using System.Windows.Forms;
using RESClient.MVP.Models;
using RESClient.MVP.Views;

namespace RESClient
{
    /// <summary>
    /// 参数验证测试类
    /// </summary>
    public class TestParameterValidation
    {
        /// <summary>
        /// 测试默认值设置
        /// </summary>
        public static void TestDefaultValues()
        {
            try
            {
                Console.WriteLine("=== 测试默认值设置 ===");
                
                var parametersModel = new CoverParametersModel();
                var defaultParams = parametersModel.GetCurrentParameters();
                
                Console.WriteLine($"测绘公司默认值: {defaultParams.SurveyCompany}");
                Console.WriteLine($"测绘资格证书号默认值: {defaultParams.QualificationNumber}");
                Console.WriteLine($"报告日期默认值: {defaultParams.ReportDate:yyyy-MM-dd}");
                
                // 验证默认值是否正确
                bool surveyCompanyCorrect = defaultParams.SurveyCompany == "四川省川建勘察设计院有限公司";
                bool qualificationCorrect = defaultParams.QualificationNumber == "甲测资字：51100923";
                
                Console.WriteLine($"测绘公司默认值正确: {(surveyCompanyCorrect ? "✓" : "✗")}");
                Console.WriteLine($"资格证书号默认值正确: {(qualificationCorrect ? "✓" : "✗")}");
                
                if (surveyCompanyCorrect && qualificationCorrect)
                {
                    Console.WriteLine("✓ 默认值设置测试通过");
                }
                else
                {
                    Console.WriteLine("✗ 默认值设置测试失败");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"✗ 默认值测试发生错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 测试参数验证逻辑
        /// </summary>
        public static void TestParameterValidationLogic()
        {
            try
            {
                Console.WriteLine("\n=== 测试参数验证逻辑 ===");
                
                var parametersModel = new CoverParametersModel();
                var testParams = parametersModel.GetCurrentParameters();
                
                // 测试1: 完整参数验证
                Console.WriteLine("1. 测试完整参数验证...");
                testParams.SurveyNumber = "SC20241225001";
                testParams.ProjectNumber = "PJ202412-123";
                testParams.ProjectName = "测试项目";
                testParams.ProjectAddress = "测试地址";
                testParams.ConstructionUnit = "测试建设单位";
                
                var result1 = testParams.Validate();
                Console.WriteLine($"   完整参数验证结果: {(result1.IsValid ? "✓ 通过" : "✗ 失败")}");
                
                // 测试2: 缺少必填字段
                Console.WriteLine("2. 测试缺少必填字段...");
                testParams.SurveyNumber = "";  // 清空必填字段
                testParams.ProjectName = "";   // 清空必填字段
                
                var result2 = testParams.Validate();
                Console.WriteLine($"   缺少必填字段验证结果: {(result2.IsValid ? "✗ 应该失败但通过了" : "✓ 正确失败")}");
                if (!result2.IsValid)
                {
                    Console.WriteLine("   验证错误信息:");
                    foreach (var error in result2.Errors)
                    {
                        Console.WriteLine($"   - {error}");
                    }
                }
                
                // 测试3: 部分字段为空
                Console.WriteLine("3. 测试部分字段为空...");
                testParams.SurveyNumber = "SC20241225001";  // 恢复一个字段
                
                var result3 = testParams.Validate();
                Console.WriteLine($"   部分字段为空验证结果: {(result3.IsValid ? "✗ 应该失败但通过了" : "✓ 正确失败")}");
                if (!result3.IsValid)
                {
                    Console.WriteLine($"   剩余错误数量: {result3.Errors.Count}");
                }
                
                Console.WriteLine("✓ 参数验证逻辑测试完成");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"✗ 参数验证测试发生错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 测试参数输入窗体的验证行为
        /// </summary>
        public static void TestParameterInputFormValidation()
        {
            try
            {
                Console.WriteLine("\n=== 测试参数输入窗体验证行为 ===");
                Console.WriteLine("注意: 这个测试需要手动交互");
                Console.WriteLine("测试步骤:");
                Console.WriteLine("1. 窗体打开后，故意留空一些必填字段");
                Console.WriteLine("2. 点击'确定'按钮");
                Console.WriteLine("3. 应该弹出验证失败提示");
                Console.WriteLine("4. 点击提示框的'确定'按钮");
                Console.WriteLine("5. 确认参数输入窗体仍然保持打开状态");
                Console.WriteLine("6. 填写完整信息后再次点击'确定'");
                Console.WriteLine("7. 窗体应该正常关闭");
                
                Application.EnableVisualStyles();
                Application.SetCompatibleTextRenderingDefault(false);
                
                var parametersModel = new CoverParametersModel();
                var currentParams = parametersModel.GetCurrentParameters();
                
                // 故意清空一些字段来测试验证
                currentParams.SurveyNumber = "";
                currentParams.ProjectName = "";
                
                using (var form = new ParameterInputForm(currentParams))
                {
                    Console.WriteLine("正在显示参数输入窗体...");
                    var result = form.ShowDialog();
                    
                    Console.WriteLine($"窗体关闭，返回结果: {result}");
                    Console.WriteLine($"用户是否确认: {form.IsConfirmed}");
                    
                    if (result == DialogResult.OK && form.IsConfirmed)
                    {
                        Console.WriteLine("✓ 用户完成了参数设置");
                        
                        // 验证最终参数
                        var finalValidation = form.Parameters.Validate();
                        Console.WriteLine($"最终参数验证: {(finalValidation.IsValid ? "✓ 通过" : "✗ 失败")}");
                    }
                    else
                    {
                        Console.WriteLine("用户取消了参数设置");
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"✗ 窗体验证测试发生错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 运行所有测试
        /// </summary>
        public static void RunAllTests()
        {
            Console.WriteLine("开始运行参数验证相关测试...\n");
            
            TestDefaultValues();
            TestParameterValidationLogic();
            
            Console.WriteLine("\n是否要测试参数输入窗体的交互验证? (y/n)");
            string input = Console.ReadLine();
            if (input?.ToLower() == "y" || input?.ToLower() == "yes")
            {
                TestParameterInputFormValidation();
            }
            
            Console.WriteLine("\n所有测试完成!");
        }
    }
}
