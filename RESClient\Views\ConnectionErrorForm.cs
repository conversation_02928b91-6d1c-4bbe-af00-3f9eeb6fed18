using System;
using System.Drawing;
using System.Windows.Forms;
using RESClient.Models;
using RESClient.Services;

namespace RESClient.Views
{
    /// <summary>
    /// 连接错误提示窗体
    /// </summary>
    public partial class ConnectionErrorForm : Form
    {
        private readonly ServerConnectionInfo _connectionInfo;
        private readonly RESServerConfigService _configService;

        // 控件声明
        private Panel panelMain;
        private PictureBox iconPictureBox;
        private Label titleLabel;
        private Label messageLabel;
        private Label detailsLabel;
        private TextBox detailsTextBox;
        private Button retryButton;
        private Button settingsButton;
        private Button exitButton;
        private Label solutionsLabel;
        private RichTextBox solutionsTextBox;

        public ConnectionErrorForm(ServerConnectionInfo connectionInfo)
        {
            _connectionInfo = connectionInfo;
            _configService = RESServerConfigService.Instance;
            InitializeComponent();
            InitializeUI();
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();

            // 窗体设置
            this.Text = "服务器连接失败";
            this.Size = new Size(500, 600);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.ShowInTaskbar = true;
            this.TopMost = true;

            // 主面板
            panelMain = new Panel
            {
                Dock = DockStyle.Fill,
                Padding = new Padding(20),
                BackColor = Color.White
            };

            // 错误图标
            iconPictureBox = new PictureBox
            {
                Location = new Point(20, 20),
                Size = new Size(48, 48),
                SizeMode = PictureBoxSizeMode.CenterImage,
                Image = SystemIcons.Error.ToBitmap()
            };

            // 标题
            titleLabel = new Label
            {
                Text = "无法连接到 RESServer",
                Font = new Font("Microsoft YaHei", 14F, FontStyle.Bold),
                ForeColor = Color.FromArgb(192, 57, 43),
                Location = new Point(80, 25),
                Size = new Size(380, 30),
                TextAlign = ContentAlignment.MiddleLeft
            };

            // 主要消息
            messageLabel = new Label
            {
                Text = GetMainMessage(),
                Font = new Font("Microsoft YaHei", 10F),
                ForeColor = Color.FromArgb(51, 51, 51),
                Location = new Point(20, 70),
                Size = new Size(440, 60),
                TextAlign = ContentAlignment.TopLeft
            };

            // 详细信息标签
            detailsLabel = new Label
            {
                Text = "错误详情:",
                Font = new Font("Microsoft YaHei", 9F, FontStyle.Bold),
                Location = new Point(20, 140),
                Size = new Size(100, 20)
            };

            // 详细信息文本框
            detailsTextBox = new TextBox
            {
                Location = new Point(20, 165),
                Size = new Size(440, 80),
                Multiline = true,
                ReadOnly = true,
                ScrollBars = ScrollBars.Vertical,
                Font = new Font("Consolas", 8F),
                BackColor = Color.FromArgb(248, 248, 248),
                Text = GetErrorDetails()
            };

            // 解决方案标签
            solutionsLabel = new Label
            {
                Text = "解决建议:",
                Font = new Font("Microsoft YaHei", 9F, FontStyle.Bold),
                Location = new Point(20, 260),
                Size = new Size(100, 20)
            };

            // 解决方案文本框
            solutionsTextBox = new RichTextBox
            {
                Location = new Point(20, 285),
                Size = new Size(440, 200),
                ReadOnly = true,
                Font = new Font("Microsoft YaHei", 9F),
                BackColor = Color.FromArgb(252, 252, 252),
                BorderStyle = BorderStyle.FixedSingle,
                Text = GetSolutionSuggestions()
            };

            // 重试按钮
            retryButton = new Button
            {
                Text = "重试连接",
                Font = new Font("Microsoft YaHei", 9F),
                Location = new Point(20, 500),
                Size = new Size(100, 35),
                BackColor = Color.FromArgb(52, 152, 219),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                UseVisualStyleBackColor = false
            };
            retryButton.FlatAppearance.BorderSize = 0;

            // 设置按钮
            settingsButton = new Button
            {
                Text = "连接设置",
                Font = new Font("Microsoft YaHei", 9F),
                Location = new Point(140, 500),
                Size = new Size(100, 35),
                BackColor = Color.FromArgb(149, 165, 166),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                UseVisualStyleBackColor = false
            };
            settingsButton.FlatAppearance.BorderSize = 0;

            // 退出按钮
            exitButton = new Button
            {
                Text = "退出程序",
                Font = new Font("Microsoft YaHei", 9F),
                Location = new Point(360, 500),
                Size = new Size(100, 35),
                BackColor = Color.FromArgb(192, 57, 43),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                UseVisualStyleBackColor = false
            };
            exitButton.FlatAppearance.BorderSize = 0;

            // 添加控件到主面板
            panelMain.Controls.AddRange(new Control[]
            {
                iconPictureBox, titleLabel, messageLabel,
                detailsLabel, detailsTextBox,
                solutionsLabel, solutionsTextBox,
                retryButton, settingsButton, exitButton
            });

            // 添加主面板到窗体
            this.Controls.Add(panelMain);

            this.ResumeLayout(false);
        }

        private void InitializeUI()
        {
            // 绑定事件
            retryButton.Click += RetryButton_Click;
            settingsButton.Click += SettingsButton_Click;
            exitButton.Click += ExitButton_Click;

            // 设置默认按钮
            this.AcceptButton = retryButton;
            this.CancelButton = exitButton;
        }

        private string GetMainMessage()
        {
            var serverUrl = _configService.ServerAddress;
            return $"RESClient 无法连接到 RESServer ({serverUrl})。\n\n" +
                   "所有业务功能已被禁用，请解决连接问题后重试。";
        }

        private string GetErrorDetails()
        {
            var details = $"服务器地址: {_connectionInfo.ServerUrl}\n";
            details += $"连接状态: {ServerConnectionService.GetStatusDescription(_connectionInfo.Status)}\n";
            details += $"检查时间: {_connectionInfo.LastCheckTime:yyyy-MM-dd HH:mm:ss}\n";
            
            if (_connectionInfo.ResponseTime.TotalMilliseconds > 0)
            {
                details += $"响应时间: {_connectionInfo.ResponseTime.TotalMilliseconds:F0}ms\n";
            }
            
            details += $"错误消息: {_connectionInfo.Message}\n";
            
            if (!string.IsNullOrEmpty(_connectionInfo.ErrorDetails))
            {
                details += $"详细错误: {_connectionInfo.ErrorDetails}";
            }
            
            return details;
        }

        private string GetSolutionSuggestions()
        {
            var suggestions = "请尝试以下解决方案:\n\n";
            
            suggestions += "1. 检查 RESServer 是否正在运行\n";
            suggestions += "   • 启动 RESServer 应用程序\n";
            suggestions += "   • 确认服务器进程正常运行\n\n";
            
            suggestions += "2. 检查网络连接\n";
            suggestions += "   • 确认网络连接正常\n";
            suggestions += "   • 检查防火墙设置\n";
            suggestions += "   • 尝试 ping 服务器地址\n\n";
            
            suggestions += "3. 验证服务器地址配置\n";
            suggestions += $"   • 当前配置: {_configService.ServerAddress}\n";
            suggestions += "   • 确认地址和端口号正确\n";
            suggestions += "   • 检查 App.config 配置文件\n\n";
            
            suggestions += "4. 检查服务器端口\n";
            suggestions += "   • 确认服务器端口未被占用\n";
            suggestions += "   • 检查端口是否被防火墙阻止\n\n";
            
            suggestions += "5. 查看服务器日志\n";
            suggestions += "   • 检查 RESServer 控制台输出\n";
            suggestions += "   • 查看错误日志文件\n\n";
            
            if (_connectionInfo.Status == ServerConnectionStatus.Timeout)
            {
                suggestions += "6. 连接超时处理\n";
                suggestions += "   • 服务器可能响应较慢\n";
                suggestions += "   • 检查服务器性能和负载\n";
                suggestions += "   • 尝试增加超时时间\n\n";
            }
            
            return suggestions;
        }

        private void RetryButton_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Retry;
            this.Close();
        }

        private void SettingsButton_Click(object sender, EventArgs e)
        {
            try
            {
                // 打开配置文件或显示设置对话框
                var configPath = System.IO.Path.Combine(
                    Application.StartupPath, "App.config");
                
                if (System.IO.File.Exists(configPath))
                {
                    System.Diagnostics.Process.Start("notepad.exe", configPath);
                }
                else
                {
                    MessageBox.Show(
                        "配置文件不存在，请检查应用程序安装。",
                        "配置文件错误",
                        MessageBoxButtons.OK,
                        MessageBoxIcon.Warning
                    );
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(
                    $"无法打开配置文件：{ex.Message}",
                    "错误",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Error
                );
            }
        }

        private void ExitButton_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }
    }
}
