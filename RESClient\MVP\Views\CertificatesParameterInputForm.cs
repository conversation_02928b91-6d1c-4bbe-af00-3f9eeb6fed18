using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Reflection;
using System.Windows.Forms;
using RESClient.MVP.Models;
using RESClient.MVP.Presenters;

namespace RESClient.MVP.Views
{
    /// <summary>
    /// 经主管部门批准的相关证照参数输入窗体
    /// </summary>
    public partial class CertificatesParameterInputForm : Form
    {
        private CertificatesParametersModel.CertificatesParameters _parameters;
        private Dictionary<string, Control> _inputControls;
        private TableLayoutPanel _mainPanel;
        private Button _okButton;
        private Button _cancelButton;
        private Button _resetButton;

        /// <summary>
        /// 是否已确认
        /// </summary>
        public bool IsConfirmed { get; private set; } = false;

        /// <summary>
        /// 参数对象
        /// </summary>
        public CertificatesParametersModel.CertificatesParameters Parameters => _parameters;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="parameters">初始参数</param>
        public CertificatesParameterInputForm(CertificatesParametersModel.CertificatesParameters parameters)
        {
            _parameters = parameters ?? new CertificatesParametersModel.CertificatesParameters();
            _inputControls = new Dictionary<string, Control>();

            InitializeComponent();
            InitializeCustomControls();
            AddInputControls();
            AddButtonPanel();
            LoadParameterValues();
        }

        /// <summary>
        /// 初始化组件
        /// </summary>
        private void InitializeComponent()
        {
            this.SuspendLayout();
            this.ResumeLayout(false);
        }

        private void InitializeCustomControls()
        {
            // 应用标准窗体设置
            ParameterInputFormStyleConfig.ApplyStandardFormSettings(this, "经主管部门批准的相关证照参数设置", new Size(620, 480));

            // 创建主容器面板
            var mainContainer = ParameterInputFormStyleConfig.CreateMainContainer();

            // 创建可滚动的主面板
            _mainPanel = ParameterInputFormStyleConfig.CreateMainPanel();

            mainContainer.Controls.Add(_mainPanel);
            this.Controls.Add(mainContainer);
        }

        /// <summary>
        /// 添加输入控件
        /// </summary>
        private void AddInputControls()
        {
            var fields = new[]
            {
                new { Property = "PlanningPermit", Label = "规划许可证", IsMultiline = false },
                new { Property = "PlanningLayout", Label = "规划总平图", IsMultiline = false },
                new { Property = "ConstructionPermit", Label = "施工许可证", IsMultiline = false },
                new { Property = "ReviewReportIssuer", Label = "审查报告出具方", IsMultiline = false },
                new { Property = "ProjectNumber", Label = "项目编号", IsMultiline = false },
                new { Property = "PoliceStationAddressProof", Label = "派出所地址证明", IsMultiline = true },
                new { Property = "BuildingNameRegistrationNotice", Label = "建筑物名称备案通知", IsMultiline = true }
            };

            _mainPanel.RowCount = fields.Length + 1; // +1 for button panel

            for (int i = 0; i < fields.Length; i++)
            {
                var field = fields[i];

                // 设置行样式
                int rowHeight = field.IsMultiline ? ParameterInputFormStyleConfig.MultilineRowHeight : ParameterInputFormStyleConfig.StandardRowHeight;
                _mainPanel.RowStyles.Add(new RowStyle(SizeType.Absolute, rowHeight));

                // 创建标签容器
                var labelContainer = ParameterInputFormStyleConfig.CreateLabelContainer();
                var label = ParameterInputFormStyleConfig.CreateStandardLabel(field.Label, this.Font);
                labelContainer.Controls.Add(label);

                // 创建输入控件容器
                var inputContainer = ParameterInputFormStyleConfig.CreateInputContainer();
                var inputControl = ParameterInputFormStyleConfig.CreateStandardTextBox(this.Font, field.IsMultiline);
                inputContainer.Controls.Add(inputControl);

                _inputControls[field.Property] = inputControl;

                _mainPanel.Controls.Add(labelContainer, 0, i);
                _mainPanel.Controls.Add(inputContainer, 1, i);
            }
        }

        /// <summary>
        /// 添加按钮面板
        /// </summary>
        private void AddButtonPanel()
        {
            // 创建固定在底部的按钮面板
            var buttonContainer = ParameterInputFormStyleConfig.CreateButtonContainer();
            var buttonPanel = ParameterInputFormStyleConfig.CreateButtonPanel();

            // 取消按钮
            _cancelButton = ParameterInputFormStyleConfig.CreateStandardButton("取消", this.Font, false, DialogResult.Cancel);
            _cancelButton.Click += CancelButton_Click;

            // 确定按钮
            _okButton = ParameterInputFormStyleConfig.CreateStandardButton("确定", this.Font, true);
            _okButton.Click += OkButton_Click;

            // 重置按钮
            _resetButton = ParameterInputFormStyleConfig.CreateStandardButton("重置", this.Font);
            _resetButton.Click += ResetButton_Click;

            // 按钮添加顺序：取消、确定、重置（从右到左）
            buttonPanel.Controls.Add(_cancelButton);
            buttonPanel.Controls.Add(_okButton);
            buttonPanel.Controls.Add(_resetButton);

            buttonContainer.Controls.Add(buttonPanel);

            // 将按钮容器添加到窗体而不是TableLayoutPanel
            this.Controls.Add(buttonContainer);

            // 设置默认按钮
            this.CancelButton = _cancelButton;
        }

        /// <summary>
        /// 保存控件值到参数对象
        /// </summary>
        private void SaveParameterValues()
        {
            _parameters.PlanningPermit = GetTextValueWithPlaceholder("PlanningPermit");
            _parameters.PlanningLayout = GetTextValueWithPlaceholder("PlanningLayout");
            _parameters.ConstructionPermit = GetTextValueWithPlaceholder("ConstructionPermit");
            _parameters.ReviewReportIssuer = GetTextValueWithPlaceholder("ReviewReportIssuer");
            _parameters.ProjectNumber = GetTextValueWithPlaceholder("ProjectNumber");
            _parameters.PoliceStationAddressProof = GetTextValueWithPlaceholder("PoliceStationAddressProof");
            _parameters.BuildingNameRegistrationNotice = GetTextValueWithPlaceholder("BuildingNameRegistrationNotice");
        }

        /// <summary>
        /// 获取文本值，如果为空则使用占位符并记录警告
        /// </summary>
        /// <param name="controlName">控件名称</param>
        /// <returns>文本值或占位符</returns>
        private string GetTextValueWithPlaceholder(string controlName)
        {
            string textValue = _inputControls[controlName].Text?.Trim() ?? "";
            if (string.IsNullOrEmpty(textValue))
            {
                textValue = "\\";
                // 记录参数为空的警告到错误收集器
                LogEmptyParameterWarning(controlName, "经主管部门批准的相关证照");
            }
            return textValue;
        }

        /// <summary>
        /// 记录空参数警告
        /// </summary>
        /// <param name="parameterName">参数名称</param>
        /// <param name="moduleName">模块名称</param>
        private void LogEmptyParameterWarning(string parameterName, string moduleName)
        {
            try
            {
                // 获取主窗体的错误收集器
                var mainForm = Application.OpenForms.OfType<Form>()
                    .FirstOrDefault(f => f.GetType().Name == "MainForm");

                if (mainForm != null)
                {
                    // 通过反射获取MainPresenter和ErrorCollector
                    var presenterField = mainForm.GetType().GetField("_presenter",
                        System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);

                    if (presenterField?.GetValue(mainForm) is MainPresenter presenter)
                    {
                        var modelProperty = presenter.GetType().GetProperty("Model",
                            System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);

                        if (modelProperty?.GetValue(presenter) is MainModel model)
                        {
                            model.ErrorCollector.AddError(
                                Services.ErrorType.ParameterConfiguration,
                                Services.ErrorSeverity.Warning,
                                moduleName,
                                $"参数 '{GetParameterDisplayName(parameterName)}' 未填写",
                                details: $"用户未填写参数 '{parameterName}'，已自动使用占位符 '\\'",
                                suggestedSolution: "建议在参数设置中填写完整的参数信息以获得更好的报告质量");
                        }
                    }
                }
            }
            catch (Exception)
            {
                // 忽略警告记录失败，不影响主要功能
            }
        }

        /// <summary>
        /// 获取参数显示名称
        /// </summary>
        /// <param name="parameterName">参数名称</param>
        /// <returns>显示名称</returns>
        private string GetParameterDisplayName(string parameterName)
        {
            // 通过反射获取DisplayName特性
            try
            {
                var property = typeof(CertificatesParametersModel.CertificatesParameters).GetProperty(parameterName);
                var displayNameAttr = property?.GetCustomAttribute<System.ComponentModel.DisplayNameAttribute>();
                return displayNameAttr?.DisplayName ?? parameterName;
            }
            catch
            {
                return parameterName;
            }
        }

        /// <summary>
        /// 重置按钮点击事件
        /// </summary>
        private void ResetButton_Click(object sender, EventArgs e)
        {
            try
            {
                var result = MessageBox.Show(
                    "确定要重置所有参数为默认值吗？\n\n此操作将清除当前所有输入的内容。",
                    "确认重置",
                    MessageBoxButtons.YesNo,
                    MessageBoxIcon.Question);

                if (result == DialogResult.Yes)
                {
                    // 重置为默认参数
                    _parameters = new CertificatesParametersModel.CertificatesParameters();
                    LoadParameterValues();

                    MessageBox.Show("参数已重置为默认值", "重置完成",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"重置参数时发生错误: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 确定按钮点击事件
        /// </summary>
        private void OkButton_Click(object sender, EventArgs e)
        {
            try
            {
                // 保存当前输入的参数值
                SaveParameterValues();

                // 验证参数
                var validationResult = _parameters.Validate();
                if (!validationResult.IsValid)
                {
                    // 构建详细的错误信息
                    string errorMessage = "参数验证失败，请检查以下问题：\n\n";
                    for (int i = 0; i < validationResult.Errors.Count; i++)
                    {
                        errorMessage += $"{i + 1}. {validationResult.Errors[i]}\n";
                    }
                    errorMessage += "\n请修正上述问题后重试。";

                    // 显示错误提示，用户点击确定后返回到输入窗体继续编辑
                    MessageBox.Show(errorMessage, "参数验证失败",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);

                    // 验证失败时不关闭窗体，让用户继续编辑
                    return;
                }

                // 验证成功，设置确认标志并关闭窗体
                IsConfirmed = true;
                this.DialogResult = DialogResult.OK;
                this.Close();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"保存参数时发生错误：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 取消按钮点击事件
        /// </summary>
        private void CancelButton_Click(object sender, EventArgs e)
        {
            IsConfirmed = false;
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }

        /// <summary>
        /// 加载参数值到控件
        /// </summary>
        private void LoadParameterValues()
        {
            if (_inputControls.ContainsKey("PlanningPermit"))
                _inputControls["PlanningPermit"].Text = _parameters.PlanningPermit ?? "";
            if (_inputControls.ContainsKey("PlanningLayout"))
                _inputControls["PlanningLayout"].Text = _parameters.PlanningLayout ?? "";
            if (_inputControls.ContainsKey("ConstructionPermit"))
                _inputControls["ConstructionPermit"].Text = _parameters.ConstructionPermit ?? "";
            if (_inputControls.ContainsKey("ReviewReportIssuer"))
                _inputControls["ReviewReportIssuer"].Text = _parameters.ReviewReportIssuer ?? "";
            if (_inputControls.ContainsKey("ProjectNumber"))
                _inputControls["ProjectNumber"].Text = _parameters.ProjectNumber ?? "";
            if (_inputControls.ContainsKey("PoliceStationAddressProof"))
                _inputControls["PoliceStationAddressProof"].Text = _parameters.PoliceStationAddressProof ?? "";
            if (_inputControls.ContainsKey("BuildingNameRegistrationNotice"))
                _inputControls["BuildingNameRegistrationNotice"].Text = _parameters.BuildingNameRegistrationNotice ?? "";
        }
    }
}
