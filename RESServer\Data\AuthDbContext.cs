using Microsoft.EntityFrameworkCore;
using RESServer.Models;

namespace RESServer.Data
{
    /// <summary>
    /// 认证数据库上下文
    /// </summary>
    public class AuthDbContext : DbContext
    {
        public AuthDbContext(DbContextOptions<AuthDbContext> options) : base(options)
        {
        }

        public DbSet<User> Users { get; set; }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            // 配置用户表
            modelBuilder.Entity<User>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.HasIndex(e => e.Username).IsUnique();
                entity.HasIndex(e => e.Email).IsUnique();
                
                entity.Property(e => e.Username)
                    .IsRequired()
                    .HasMaxLength(50);
                
                entity.Property(e => e.PasswordHash)
                    .IsRequired()
                    .HasMaxLength(255);
                
                entity.Property(e => e.Email)
                    .HasMaxLength(100);
                
                entity.Property(e => e.FullName)
                    .HasMaxLength(50);
                
                entity.Property(e => e.Role)
                    .IsRequired()
                    .HasMaxLength(20)
                    .HasDefaultValue("User");
                
                entity.Property(e => e.IsActive)
                    .HasDefaultValue(true);
                
                entity.Property(e => e.CreatedAt)
                    .HasDefaultValueSql("datetime('now')");
                
                entity.Property(e => e.Description)
                    .HasMaxLength(500);
            });

            // 添加默认管理员用户
            SeedDefaultUsers(modelBuilder);
        }

        /// <summary>
        /// 添加默认用户数据
        /// </summary>
        /// <param name="modelBuilder"></param>
        private void SeedDefaultUsers(ModelBuilder modelBuilder)
        {
            // 创建默认管理员用户
            // 密码: admin123 (使用 BCrypt 哈希)
            var adminPasswordHash = BCrypt.Net.BCrypt.HashPassword("admin123");
            
            modelBuilder.Entity<User>().HasData(
                new User
                {
                    Id = 1,
                    Username = "admin",
                    PasswordHash = adminPasswordHash,
                    Email = "<EMAIL>",
                    FullName = "系统管理员",
                    Role = "Admin",
                    IsActive = true,
                    CreatedAt = DateTime.UtcNow,
                    Description = "系统默认管理员账户"
                }
            );

            // 创建默认普通用户
            // 密码: user123 (使用 BCrypt 哈希)
            var userPasswordHash = BCrypt.Net.BCrypt.HashPassword("user123");
            
            modelBuilder.Entity<User>().HasData(
                new User
                {
                    Id = 2,
                    Username = "user",
                    PasswordHash = userPasswordHash,
                    Email = "<EMAIL>",
                    FullName = "普通用户",
                    Role = "User",
                    IsActive = true,
                    CreatedAt = DateTime.UtcNow,
                    Description = "系统默认普通用户账户"
                }
            );
        }
    }
}
