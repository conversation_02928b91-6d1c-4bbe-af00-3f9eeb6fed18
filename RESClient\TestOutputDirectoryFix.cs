using System;
using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;
using RESClient.Services;
using RESClient.Services.Implementations;

namespace RESClient
{
    /// <summary>
    /// 测试输出目录修复
    /// </summary>
    public class TestOutputDirectoryFix
    {
        /// <summary>
        /// 测试封面模块的输出目录参数处理
        /// </summary>
        public static async Task TestCoverModuleOutputDirectory()
        {
            try
            {
                Console.WriteLine("=== 测试封面模块输出目录参数处理 ===");
                
                var coverGenerator = new CoverModuleGenerator();
                
                // 创建测试输出目录
                string testOutputDir = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "测试输出");
                if (!Directory.Exists(testOutputDir))
                {
                    Directory.CreateDirectory(testOutputDir);
                }
                
                // 测试1: 使用 OutputDirectory 参数
                Console.WriteLine("\n1. 测试使用 OutputDirectory 参数...");
                var parameters1 = new Dictionary<string, object>
                {
                    ["OutputDirectory"] = testOutputDir
                };
                
                bool result1 = await TestModuleGeneration(coverGenerator, parameters1, "OutputDirectory");
                Console.WriteLine($"   使用 OutputDirectory 参数: {(result1 ? "✓ 成功" : "✗ 失败")}");
                
                // 测试2: 使用 OutputDir 参数
                Console.WriteLine("\n2. 测试使用 OutputDir 参数...");
                var parameters2 = new Dictionary<string, object>
                {
                    ["OutputDir"] = testOutputDir
                };
                
                bool result2 = await TestModuleGeneration(coverGenerator, parameters2, "OutputDir");
                Console.WriteLine($"   使用 OutputDir 参数: {(result2 ? "✓ 成功" : "✗ 失败")}");
                
                // 测试3: 不提供输出目录参数
                Console.WriteLine("\n3. 测试不提供输出目录参数...");
                var parameters3 = new Dictionary<string, object>();
                
                bool result3 = await TestModuleGeneration(coverGenerator, parameters3, "无参数");
                Console.WriteLine($"   不提供输出目录参数: {(result3 ? "✗ 应该失败但成功了" : "✓ 正确失败")}");
                
                // 测试4: 提供空的输出目录参数
                Console.WriteLine("\n4. 测试提供空的输出目录参数...");
                var parameters4 = new Dictionary<string, object>
                {
                    ["OutputDir"] = ""
                };
                
                bool result4 = await TestModuleGeneration(coverGenerator, parameters4, "空参数");
                Console.WriteLine($"   提供空的输出目录参数: {(result4 ? "✗ 应该失败但成功了" : "✓ 正确失败")}");
                
                // 总结
                Console.WriteLine("\n=== 测试总结 ===");
                int passedTests = 0;
                if (result1) passedTests++;
                if (result2) passedTests++;
                if (!result3) passedTests++; // 这个应该失败
                if (!result4) passedTests++; // 这个应该失败
                
                Console.WriteLine($"通过测试: {passedTests}/4");
                if (passedTests == 4)
                {
                    Console.WriteLine("✓ 所有测试通过，输出目录参数处理修复成功！");
                }
                else
                {
                    Console.WriteLine("✗ 部分测试失败，需要进一步检查");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"✗ 测试过程中发生错误: {ex.Message}");
            }
        }
        
        /// <summary>
        /// 测试模块生成
        /// </summary>
        /// <param name="generator">模块生成器</param>
        /// <param name="parameters">参数</param>
        /// <param name="testName">测试名称</param>
        /// <returns>是否成功</returns>
        private static async Task<bool> TestModuleGeneration(
            IReportModuleGenerator generator, 
            Dictionary<string, object> parameters, 
            string testName)
        {
            try
            {
                var messages = new List<string>();
                
                bool result = await generator.GenerateAsync(parameters, (progress, message) =>
                {
                    messages.Add($"[{progress}%] {message}");
                });
                
                Console.WriteLine($"   {testName} 生成结果: {result}");
                Console.WriteLine($"   最后消息: {(messages.Count > 0 ? messages[messages.Count - 1] : "无消息")}");
                
                return result;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"   {testName} 生成异常: {ex.Message}");
                return false;
            }
        }
        
        /// <summary>
        /// 测试所有模块生成器的参数一致性
        /// </summary>
        public static void TestAllModuleParameterConsistency()
        {
            try
            {
                Console.WriteLine("\n=== 测试所有模块生成器参数一致性 ===");
                
                var reportGeneratorService = new ReportGeneratorService();
                var allGenerators = reportGeneratorService.GetAllModuleGenerators();
                
                Console.WriteLine($"找到 {allGenerators.Count} 个模块生成器:");
                foreach (var generator in allGenerators)
                {
                    Console.WriteLine($"- {generator.ModuleName}");
                }
                
                Console.WriteLine("\n建议:");
                Console.WriteLine("1. 所有模块生成器应该统一使用 'OutputDir' 作为输出目录参数键");
                Console.WriteLine("2. 或者都支持 'OutputDir' 和 'OutputDirectory' 两种参数键");
                Console.WriteLine("3. MainModel 当前使用 'OutputDir'，这是正确的");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"✗ 测试参数一致性时发生错误: {ex.Message}");
            }
        }
        
        /// <summary>
        /// 运行所有测试
        /// </summary>
        public static async Task RunAllTests()
        {
            Console.WriteLine("开始运行输出目录修复测试...\n");
            
            await TestCoverModuleOutputDirectory();
            TestAllModuleParameterConsistency();
            
            Console.WriteLine("\n所有测试完成!");
        }
    }
}
