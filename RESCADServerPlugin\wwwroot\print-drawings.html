<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>打印图处理 - CAD 服务插件</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background-color: #fff;
            padding: 20px;
            border-radius: 5px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        h1 {
            color: #333;
            margin-top: 0;
            border-bottom: 1px solid #eee;
            padding-bottom: 10px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input[type="file"] {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        button {
            background-color: #4CAF50;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover {
            background-color: #45a049;
        }
        button:disabled {
            background-color: #cccccc;
            cursor: not-allowed;
        }
        .progress-container {
            margin-top: 20px;
            display: none;
        }
        .progress-bar {
            width: 100%;
            height: 20px;
            background-color: #f3f3f3;
            border-radius: 4px;
            overflow: hidden;
        }
        .progress {
            height: 100%;
            background-color: #4CAF50;
            width: 0%;
            transition: width 0.3s ease;
        }
        .status-text {
            margin-top: 10px;
            font-size: 14px;
            color: #666;
        }
        .error-message {
            color: #d9534f;
            margin-top: 10px;
            padding: 10px;
            background-color: #f9f2f2;
            border-radius: 4px;
            display: none;
        }
        .queue-status {
            margin-top: 20px;
            padding: 10px;
            background-color: #f8f9fa;
            border-radius: 4px;
            display: none;
        }
        .queue-info {
            font-weight: bold;
            color: #6c757d;
        }
        .poll-status {
            font-size: 12px;
            color: #999;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>CAD 打印图处理</h1>
        <p>上传 DWG 文件，系统将自动识别并提取所有打印图，转换为 GIF 格式后打包下载。</p>
        
        <form id="uploadForm">
            <div class="form-group">
                <label for="dwgFile">选择 DWG 文件:</label>
                <input type="file" id="dwgFile" name="file" accept=".dwg" required>
            </div>
            
            <button type="submit" id="submitBtn">上传并处理</button>
        </form>
        
        <div class="progress-container" id="progressContainer">
            <div class="progress-bar">
                <div class="progress" id="progressBar"></div>
            </div>
            <div class="status-text" id="statusText">处理中...</div>
        </div>
        
        <div class="queue-status" id="queueStatus">
            <div class="queue-info" id="queueInfo">当前队列中有 0 个文件正在等待处理</div>
            <div class="poll-status" id="pollStatus"></div>
        </div>
        
        <div class="error-message" id="errorMessage"></div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const form = document.getElementById('uploadForm');
            const fileInput = document.getElementById('dwgFile');
            const submitBtn = document.getElementById('submitBtn');
            const progressContainer = document.getElementById('progressContainer');
            const progressBar = document.getElementById('progressBar');
            const statusText = document.getElementById('statusText');
            const errorMessage = document.getElementById('errorMessage');
            const queueStatus = document.getElementById('queueStatus');
            const queueInfo = document.getElementById('queueInfo');
            const pollStatus = document.getElementById('pollStatus');
            
            // 获取当前服务器端口
            const serverPort = window.location.port || '6060';
            const apiUrl = `http://localhost:${serverPort}/api/print-drawings/process`;
            const queueStatusUrl = `http://localhost:${serverPort}/api/print-drawings/queue-status`;
            
            // 轮询变量
            let pollingInterval = null;
            let jobId = null;
            
            // 检查队列状态
            checkQueueStatus();
            
            // 定期检查队列状态
            setInterval(checkQueueStatus, 10000);
            
            // 检查队列状态函数
            function checkQueueStatus() {
                fetch(queueStatusUrl)
                    .then(response => response.json())
                    .then(data => {
                        if (data.queueLength > 0) {
                            queueStatus.style.display = 'block';
                            queueInfo.textContent = `当前队列中有 ${data.queueLength} 个文件正在等待处理，预计等待时间: ${data.estimatedWaitTime}`;
                        } else {
                            queueStatus.style.display = 'none';
                        }
                    })
                    .catch(error => {
                        console.error('Error checking queue status:', error);
                    });
            }
            
            form.addEventListener('submit', function(e) {
                e.preventDefault();
                
                if (!fileInput.files.length) {
                    showError('请选择一个 DWG 文件');
                    return;
                }
                
                const file = fileInput.files[0];
                if (!file.name.toLowerCase().endsWith('.dwg')) {
                    showError('请选择有效的 DWG 文件');
                    return;
                }
                
                // 生成当前上传的唯一ID
                jobId = generateUUID();
                
                // 准备表单数据
                const formData = new FormData();
                formData.append('file', file);
                
                // 禁用提交按钮并显示进度条
                submitBtn.disabled = true;
                progressContainer.style.display = 'block';
                errorMessage.style.display = 'none';
                
                // 设置初始状态
                progressBar.style.width = '0%';
                statusText.textContent = '上传中...';
                
                // 创建请求
                const xhr = new XMLHttpRequest();
                
                // 监听上传进度
                xhr.upload.addEventListener('progress', function(e) {
                    if (e.lengthComputable) {
                        const percentComplete = (e.loaded / e.total) * 100;
                        progressBar.style.width = percentComplete + '%';
                        statusText.textContent = `上传中: ${Math.round(percentComplete)}%`;
                    }
                });
                
                // 监听请求完成
                xhr.addEventListener('load', function() {
                    if (xhr.status === 200) {
                        // 立即下载文件 - 没有队列等待
                        handleSuccessfulDownload(xhr);
                    } 
                    else if (xhr.status === 202) {
                        // 文件已加入队列，需要等待处理
                        try {
                            const response = JSON.parse(xhr.responseText);
                            progressBar.style.width = '100%';
                            statusText.textContent = response.message;
                            
                            // 设置轮询以检查文件是否处理完成
                            startPolling();
                        } catch (e) {
                            showError('服务器响应无效');
                            resetForm();
                        }
                    }
                    else {
                        // 处理错误
                        handleError(xhr);
                    }
                });
                
                // 监听错误
                xhr.addEventListener('error', function() {
                    showError('网络错误，请检查服务器是否正在运行');
                    resetForm();
                });
                
                // 监听请求中止
                xhr.addEventListener('abort', function() {
                    showError('请求已取消');
                    resetForm();
                });
                
                // 发送请求
                xhr.open('POST', apiUrl);
                xhr.responseType = 'blob';
                xhr.send(formData);
            });
            
            // 启动轮询检查处理状态
            function startPolling() {
                // 每10秒检查一次队列状态
                pollingInterval = setInterval(function() {
                    pollStatus.textContent = `正在检查处理状态... 上次检查: ${new Date().toLocaleTimeString()}`;
                    
                    fetch(queueStatusUrl)
                        .then(response => response.json())
                        .then(data => {
                            // 如果队列为空，文件可能已处理完成，尝试下载
                            if (data.queueLength === 0) {
                                pollStatus.textContent = `队列为空，尝试下载文件...`;
                                tryDownload();
                            } else {
                                pollStatus.textContent = `仍在队列中，位置: ${data.queueLength}，预计等待时间: ${data.estimatedWaitTime}。上次检查: ${new Date().toLocaleTimeString()}`;
                            }
                        })
                        .catch(error => {
                            pollStatus.textContent = `检查状态出错: ${error.message}`;
                        });
                }, 10000);
            }
            
            // 尝试下载已处理的文件
            function tryDownload() {
                // 停止轮询
                clearInterval(pollingInterval);
                
                // 尝试下载文件
                fetch(apiUrl, {
                    method: 'GET',
                    headers: {
                        'X-Job-ID': jobId
                    }
                })
                .then(response => {
                    if (response.ok) {
                        return response.blob();
                    } else {
                        throw new Error('文件处理中，请稍后再试');
                    }
                })
                .then(blob => {
                    // 创建下载链接
                    const url = window.URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.style.display = 'none';
                    a.href = url;
                    a.download = 'print_drawings.zip';
                    
                    // 触发下载
                    document.body.appendChild(a);
                    a.click();
                    
                    // 清理
                    window.URL.revokeObjectURL(url);
                    statusText.textContent = '下载完成！';
                    
                    // 延迟重置表单
                    setTimeout(resetForm, 2000);
                })
                .catch(error => {
                    // 如果还未处理完成，继续轮询
                    pollStatus.textContent = `${error.message}，继续等待...`;
                    startPolling();
                });
            }
            
            // 处理成功下载的情况
            function handleSuccessfulDownload(xhr) {
                progressBar.style.width = '100%';
                statusText.textContent = '处理完成，准备下载...';
                
                // 获取响应作为 Blob
                const blob = xhr.response;
                
                // 从响应头中获取文件名
                let filename = 'print_drawings.zip';
                const disposition = xhr.getResponseHeader('Content-Disposition');
                if (disposition && disposition.includes('filename=')) {
                    const filenameRegex = /filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/;
                    const matches = filenameRegex.exec(disposition);
                    if (matches != null && matches[1]) {
                        filename = matches[1].replace(/['"]/g, '');
                    }
                }
                
                // 创建下载链接
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.style.display = 'none';
                a.href = url;
                a.download = filename;
                
                // 触发下载
                document.body.appendChild(a);
                a.click();
                
                // 清理
                window.URL.revokeObjectURL(url);
                statusText.textContent = '下载完成！';
                
                // 延迟重置表单
                setTimeout(resetForm, 2000);
            }
            
            // 处理错误情况
            function handleError(xhr) {
                try {
                    // 尝试解析 JSON 错误消息
                    if (xhr.responseType === 'blob') {
                        // 如果响应类型是blob，需要先读取为文本
                        const reader = new FileReader();
                        reader.onload = function() {
                            try {
                                const response = JSON.parse(reader.result);
                                showError(response.message || '处理文件时出错');
                            } catch (e) {
                                showError('服务器响应无效');
                            }
                            resetForm();
                        };
                        reader.readAsText(xhr.response);
                    } else {
                        const response = JSON.parse(xhr.responseText);
                        showError(response.message || '处理文件时出错');
                        resetForm();
                    }
                } catch (e) {
                    showError('服务器响应无效');
                    resetForm();
                }
            }
            
            function showError(message) {
                errorMessage.textContent = message;
                errorMessage.style.display = 'block';
                progressContainer.style.display = 'none';
            }
            
            function resetForm() {
                submitBtn.disabled = false;
                progressContainer.style.display = 'none';
                fileInput.value = '';
                jobId = null;
                clearInterval(pollingInterval);
            }
            
            // 生成UUID
            function generateUUID() {
                return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
                    const r = Math.random() * 16 | 0;
                    const v = c === 'x' ? r : (r & 0x3 | 0x8);
                    return v.toString(16);
                });
            }
        });
    </script>
</body>
</html> 