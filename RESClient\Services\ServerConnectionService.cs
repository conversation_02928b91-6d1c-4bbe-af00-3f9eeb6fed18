using System;
using System.Diagnostics;
using System.Net.Http;
using System.Threading.Tasks;
using System.Text.Json;
using RESClient.Models;

namespace RESClient.Services
{
    /// <summary>
    /// 服务器连接检查服务接口
    /// </summary>
    public interface IServerConnectionService
    {
        Task<ServerConnectionInfo> CheckConnectionAsync();
        Task<ServerConnectionInfo> CheckConnectionAsync(int timeoutSeconds);
        bool IsConnected { get; }
        ServerConnectionInfo LastConnectionInfo { get; }
        event EventHandler<ServerConnectionInfo> ConnectionStatusChanged;
    }

    /// <summary>
    /// 服务器连接检查服务实现
    /// </summary>
    public class ServerConnectionService : IServerConnectionService
    {
        private readonly RESServerConfigService _configService;
        private ServerConnectionInfo _lastConnectionInfo;
        private bool _isConnected;

        public event EventHandler<ServerConnectionInfo> ConnectionStatusChanged;

        public ServerConnectionService(RESServerConfigService configService)
        {
            _configService = configService;
            _lastConnectionInfo = new ServerConnectionInfo
            {
                Status = ServerConnectionStatus.Unknown,
                Message = "尚未检查连接状态",
                ServerUrl = _configService.ServerAddress
            };
        }

        public bool IsConnected => _isConnected;
        public ServerConnectionInfo LastConnectionInfo => _lastConnectionInfo;

        /// <summary>
        /// 检查服务器连接状态（使用默认超时时间）
        /// </summary>
        public async Task<ServerConnectionInfo> CheckConnectionAsync()
        {
            return await CheckConnectionAsync(10); // 默认10秒超时
        }

        /// <summary>
        /// 检查服务器连接状态（指定超时时间）
        /// </summary>
        /// <param name="timeoutSeconds">超时时间（秒）</param>
        public async Task<ServerConnectionInfo> CheckConnectionAsync(int timeoutSeconds)
        {
            var connectionInfo = new ServerConnectionInfo
            {
                ServerUrl = _configService.ServerAddress,
                LastCheckTime = DateTime.Now
            };

            var stopwatch = Stopwatch.StartNew();

            try
            {
                connectionInfo.Status = ServerConnectionStatus.Connecting;
                connectionInfo.Message = "正在连接服务器...";
                OnConnectionStatusChanged(connectionInfo);

                using (var client = new HttpClient())
                {
                    client.Timeout = TimeSpan.FromSeconds(timeoutSeconds);
                    
                    // 尝试访问服务器状态接口
                    var statusUrl = _configService.GetApiUrl("/api/auth/status");
                    var response = await client.GetAsync(statusUrl);
                    
                    stopwatch.Stop();
                    connectionInfo.ResponseTime = stopwatch.Elapsed;

                    if (response.IsSuccessStatusCode)
                    {
                        var content = await response.Content.ReadAsStringAsync();
                        
                        try
                        {
                            var statusDoc = JsonDocument.Parse(content);
                            var success = statusDoc.RootElement.GetProperty("success").GetBoolean();
                            var message = statusDoc.RootElement.GetProperty("message").GetString();
                            var version = statusDoc.RootElement.GetProperty("version").GetString();

                            if (success)
                            {
                                connectionInfo.Status = ServerConnectionStatus.Connected;
                                connectionInfo.Message = "服务器连接正常";
                                connectionInfo.IsHealthy = true;
                                connectionInfo.ServerVersion = version;
                                _isConnected = true;
                            }
                            else
                            {
                                connectionInfo.Status = ServerConnectionStatus.Failed;
                                connectionInfo.Message = $"服务器响应异常：{message}";
                                connectionInfo.IsHealthy = false;
                                _isConnected = false;
                            }
                        }
                        catch (JsonException)
                        {
                            // 如果不是 JSON 响应，可能是其他类型的服务器
                            connectionInfo.Status = ServerConnectionStatus.Connected;
                            connectionInfo.Message = "服务器连接正常（非标准响应）";
                            connectionInfo.IsHealthy = true;
                            _isConnected = true;
                        }
                    }
                    else
                    {
                        connectionInfo.Status = ServerConnectionStatus.Failed;
                        connectionInfo.Message = $"服务器响应错误：{response.StatusCode}";
                        connectionInfo.ErrorDetails = $"HTTP {(int)response.StatusCode} - {response.ReasonPhrase}";
                        connectionInfo.IsHealthy = false;
                        _isConnected = false;
                    }
                }
            }
            catch (TaskCanceledException ex) when (ex.InnerException is TimeoutException || ex.CancellationToken.IsCancellationRequested)
            {
                stopwatch.Stop();
                connectionInfo.Status = ServerConnectionStatus.Timeout;
                connectionInfo.Message = $"连接超时（{timeoutSeconds}秒）";
                connectionInfo.ErrorDetails = "请检查网络连接或服务器是否正在运行";
                connectionInfo.ResponseTime = stopwatch.Elapsed;
                connectionInfo.IsHealthy = false;
                _isConnected = false;
            }
            catch (HttpRequestException ex)
            {
                stopwatch.Stop();
                connectionInfo.Status = ServerConnectionStatus.Unreachable;
                connectionInfo.Message = "无法连接到服务器";
                connectionInfo.ErrorDetails = ex.Message;
                connectionInfo.ResponseTime = stopwatch.Elapsed;
                connectionInfo.IsHealthy = false;
                _isConnected = false;
            }
            catch (Exception ex)
            {
                stopwatch.Stop();
                connectionInfo.Status = ServerConnectionStatus.Failed;
                connectionInfo.Message = "连接检查失败";
                connectionInfo.ErrorDetails = ex.Message;
                connectionInfo.ResponseTime = stopwatch.Elapsed;
                connectionInfo.IsHealthy = false;
                _isConnected = false;
            }

            _lastConnectionInfo = connectionInfo;
            OnConnectionStatusChanged(connectionInfo);
            
            return connectionInfo;
        }

        /// <summary>
        /// 触发连接状态变更事件
        /// </summary>
        private void OnConnectionStatusChanged(ServerConnectionInfo connectionInfo)
        {
            ConnectionStatusChanged?.Invoke(this, connectionInfo);
        }

        /// <summary>
        /// 获取连接状态的友好描述
        /// </summary>
        public static string GetStatusDescription(ServerConnectionStatus status)
        {
            switch (status)
            {
                case ServerConnectionStatus.Unknown:
                    return "未知";
                case ServerConnectionStatus.Connecting:
                    return "连接中";
                case ServerConnectionStatus.Connected:
                    return "已连接";
                case ServerConnectionStatus.Failed:
                    return "连接失败";
                case ServerConnectionStatus.Timeout:
                    return "连接超时";
                case ServerConnectionStatus.Unreachable:
                    return "服务器不可达";
                default:
                    return "未知状态";
            }
        }

        /// <summary>
        /// 获取连接状态的颜色（用于UI显示）
        /// </summary>
        public static System.Drawing.Color GetStatusColor(ServerConnectionStatus status)
        {
            switch (status)
            {
                case ServerConnectionStatus.Connected:
                    return System.Drawing.Color.Green;
                case ServerConnectionStatus.Connecting:
                    return System.Drawing.Color.Orange;
                case ServerConnectionStatus.Failed:
                case ServerConnectionStatus.Timeout:
                case ServerConnectionStatus.Unreachable:
                    return System.Drawing.Color.Red;
                default:
                    return System.Drawing.Color.Gray;
            }
        }

        /// <summary>
        /// 检查是否需要显示详细错误信息
        /// </summary>
        public static bool ShouldShowErrorDetails(ServerConnectionStatus status)
        {
            return status == ServerConnectionStatus.Failed ||
                   status == ServerConnectionStatus.Timeout ||
                   status == ServerConnectionStatus.Unreachable;
        }
    }
}
