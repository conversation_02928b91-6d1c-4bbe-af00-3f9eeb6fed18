using System;
using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;
using RESClient.Services;

namespace RESClient
{
    /// <summary>
    /// 测试文档合并改进功能
    /// </summary>
    public class TestDocumentMergingImprovements
    {
        /// <summary>
        /// 运行所有文档合并测试
        /// </summary>
        public static async Task RunAllTests()
        {
            Console.WriteLine("=== 测试文档合并改进功能 ===");
            
            try
            {
                // 1. 测试空文档处理
                Console.WriteLine("\n1. 测试空文档处理...");
                await TestEmptyDocumentHandling();
                
                // 2. 测试错误恢复机制
                Console.WriteLine("\n2. 测试错误恢复机制...");
                await TestErrorRecovery();
                
                // 3. 测试空值处理
                Console.WriteLine("\n3. 测试空值处理...");
                await TestNullHandling();
                
                // 4. 测试文件访问错误处理
                Console.WriteLine("\n4. 测试文件访问错误处理...");
                await TestFileAccessErrors();
                
                Console.WriteLine("\n✓ 所有文档合并改进测试完成");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"\n✗ 测试过程中发生错误: {ex.Message}");
                Console.WriteLine($"详细信息: {ex.StackTrace}");
            }
        }

        /// <summary>
        /// 测试空文档处理
        /// </summary>
        private static async Task TestEmptyDocumentHandling()
        {
            try
            {
                Console.WriteLine("   测试空模块列表处理...");
                
                var documentMergingService = new DocumentMergingService();
                string testOutputDir = Path.Combine(Path.GetTempPath(), "RESClient_EmptyTest_" + Guid.NewGuid().ToString("N").Substring(0, 8));
                Directory.CreateDirectory(testOutputDir);
                
                var progressMessages = new List<string>();
                Action<int, string> progressCallback = (progress, message) =>
                {
                    progressMessages.Add(message);
                    Console.WriteLine($"     {message}");
                };
                
                // 测试空目录
                bool result = await documentMergingService.MergeDocumentsAsync(testOutputDir, "测试报告.docx", progressCallback);
                
                Console.WriteLine($"   空目录合并结果: {(result ? "成功" : "失败")} (预期: 失败)");
                Console.WriteLine($"   进度消息数量: {progressMessages.Count}");
                
                // 清理测试目录
                Directory.Delete(testOutputDir, true);
                Console.WriteLine("   ✓ 空文档处理测试通过");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"   ✗ 空文档处理测试失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 测试错误恢复机制
        /// </summary>
        private static async Task TestErrorRecovery()
        {
            try
            {
                Console.WriteLine("   测试错误恢复机制...");
                
                var documentMergingService = new DocumentMergingService();
                string testOutputDir = Path.Combine(Path.GetTempPath(), "RESClient_ErrorTest_" + Guid.NewGuid().ToString("N").Substring(0, 8));
                Directory.CreateDirectory(testOutputDir);
                
                // 创建一些测试文件（包括损坏的文件）
                string validFile = Path.Combine(testOutputDir, "封面.docx");
                string invalidFile = Path.Combine(testOutputDir, "目录.docx");
                
                // 创建一个有效的最小Word文档
                File.WriteAllBytes(validFile, CreateMinimalWordDocument());
                
                // 创建一个无效文件
                File.WriteAllText(invalidFile, "这不是一个有效的Word文档");
                
                var progressMessages = new List<string>();
                Action<int, string> progressCallback = (progress, message) =>
                {
                    progressMessages.Add(message);
                    Console.WriteLine($"     {message}");
                };
                
                bool result = await documentMergingService.MergeDocumentsAsync(testOutputDir, "测试报告.docx", progressCallback);
                
                Console.WriteLine($"   错误恢复测试结果: {(result ? "成功" : "失败")}");
                Console.WriteLine($"   进度消息数量: {progressMessages.Count}");
                
                // 检查是否有警告消息
                bool hasWarnings = progressMessages.Exists(msg => msg.Contains("[WARNING]"));
                Console.WriteLine($"   包含警告消息: {hasWarnings}");
                
                // 清理测试目录
                Directory.Delete(testOutputDir, true);
                Console.WriteLine("   ✓ 错误恢复机制测试通过");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"   ✗ 错误恢复机制测试失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 测试空值处理
        /// </summary>
        private static async Task TestNullHandling()
        {
            try
            {
                Console.WriteLine("   测试空值处理...");
                
                var documentMergingService = new DocumentMergingService();
                
                var progressMessages = new List<string>();
                Action<int, string> progressCallback = (progress, message) =>
                {
                    progressMessages.Add(message);
                    Console.WriteLine($"     {message}");
                };
                
                // 测试空路径
                bool result1 = await documentMergingService.MergeDocumentsAsync("", "测试报告.docx", progressCallback);
                Console.WriteLine($"   空输出目录测试结果: {(result1 ? "成功" : "失败")} (预期: 失败)");
                
                // 测试空文件名
                bool result2 = await documentMergingService.MergeDocumentsAsync(Path.GetTempPath(), "", progressCallback);
                Console.WriteLine($"   空文件名测试结果: {(result2 ? "成功" : "失败")} (预期: 失败)");
                
                Console.WriteLine("   ✓ 空值处理测试通过");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"   ✗ 空值处理测试失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 测试文件访问错误处理
        /// </summary>
        private static async Task TestFileAccessErrors()
        {
            try
            {
                Console.WriteLine("   测试文件访问错误处理...");
                
                var documentMergingService = new DocumentMergingService();
                string testOutputDir = Path.Combine(Path.GetTempPath(), "RESClient_AccessTest_" + Guid.NewGuid().ToString("N").Substring(0, 8));
                Directory.CreateDirectory(testOutputDir);
                
                // 创建一个不存在的文件引用
                string nonExistentFile = Path.Combine(testOutputDir, "不存在的文件.docx");
                
                var progressMessages = new List<string>();
                Action<int, string> progressCallback = (progress, message) =>
                {
                    progressMessages.Add(message);
                    Console.WriteLine($"     {message}");
                };
                
                bool result = await documentMergingService.MergeDocumentsAsync(testOutputDir, "测试报告.docx", progressCallback);
                
                Console.WriteLine($"   文件访问错误测试结果: {(result ? "成功" : "失败")} (预期: 失败)");
                Console.WriteLine($"   进度消息数量: {progressMessages.Count}");
                
                // 清理测试目录
                Directory.Delete(testOutputDir, true);
                Console.WriteLine("   ✓ 文件访问错误处理测试通过");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"   ✗ 文件访问错误处理测试失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 创建最小的Word文档字节数组
        /// </summary>
        private static byte[] CreateMinimalWordDocument()
        {
            // 这是一个最小的有效Word文档的字节表示
            // 实际应用中，这里应该创建一个真正的Word文档
            return new byte[] 
            {
                0x50, 0x4B, 0x03, 0x04, 0x14, 0x00, 0x06, 0x00, 0x08, 0x00, 0x00, 0x00, 0x21, 0x00,
                // ... 更多字节数据 ...
                // 为了简化，这里只是一个占位符
            };
        }

        /// <summary>
        /// 演示文档合并改进的主要特点
        /// </summary>
        public static void DemonstrateImprovements()
        {
            Console.WriteLine("=== 文档合并改进功能演示 ===");
            Console.WriteLine();
            Console.WriteLine("主要改进:");
            Console.WriteLine("1. 增强的错误处理");
            Console.WriteLine("   - 空值检查和验证");
            Console.WriteLine("   - 文件访问错误恢复");
            Console.WriteLine("   - 单个模块失败不影响整体合并");
            Console.WriteLine();
            Console.WriteLine("2. 安全的属性访问");
            Console.WriteLine("   - 所有NPOI属性访问都有try-catch保护");
            Console.WriteLine("   - 可空属性的安全检查");
            Console.WriteLine("   - 格式复制失败时的优雅降级");
            Console.WriteLine();
            Console.WriteLine("3. 资源管理改进");
            Console.WriteLine("   - 确保所有文档都正确关闭");
            Console.WriteLine("   - 异常情况下的资源清理");
            Console.WriteLine("   - 内存泄漏防护");
            Console.WriteLine();
            Console.WriteLine("4. 详细的进度反馈");
            Console.WriteLine("   - 区分错误、警告和信息消息");
            Console.WriteLine("   - 具体的失败原因说明");
            Console.WriteLine("   - 部分成功的清晰指示");
            Console.WriteLine();
        }

        /// <summary>
        /// 测试改进的错误消息
        /// </summary>
        public static void TestImprovedErrorMessages()
        {
            Console.WriteLine("=== 测试改进的错误消息 ===");
            
            var testMessages = new List<string>
            {
                "[ERROR]没有可合并的模块",
                "[ERROR]最终报告路径为空", 
                "[WARNING]跳过不存在的模块文件: 封面",
                "[WARNING]无法读取模块文档: 目录",
                "[WARNING]合并模块 作业声明 时出错: 可为空的对象必须具有一个值",
                "[WARNING]处理模块 项目基本信息 时出错: 文件被另一个进程使用",
                "[ERROR]保存最终报告时出错: 磁盘空间不足",
                "[SUCCESS]最终报告已保存: 完整报告_20240115_143022.docx"
            };
            
            Console.WriteLine("改进后的错误消息示例:");
            foreach (var message in testMessages)
            {
                Console.WriteLine($"  {message}");
            }
            
            Console.WriteLine();
            Console.WriteLine("消息分类:");
            Console.WriteLine("- [ERROR]: 导致整个操作失败的严重错误");
            Console.WriteLine("- [WARNING]: 部分失败但不影响整体操作的警告");
            Console.WriteLine("- [INFO]: 正常进度信息");
            Console.WriteLine("- [SUCCESS]: 成功完成的操作");
        }
    }
}
