using System;

namespace RESClient.Utils
{
    /// <summary>
    /// 中文大写日期格式化工具类
    /// </summary>
    public static class ChineseDateFormatter
    {
        // 中文大写数字映射
        private static readonly string[] ChineseNumbers = 
        {
            "〇", "一", "二", "三", "四", "五", "六", "七", "八", "九"
        };

        // 中文月份映射
        private static readonly string[] ChineseMonths = 
        {
            "", "一", "二", "三", "四", "五", "六", 
            "七", "八", "九", "十", "十一", "十二"
        };

        /// <summary>
        /// 将日期转换为中文大写格式
        /// </summary>
        /// <param name="date">要转换的日期</param>
        /// <returns>中文大写格式的日期字符串，如"二〇二四年十二月二十五日"</returns>
        public static string ToChineseUppercase(DateTime date)
        {
            string year = ConvertYearToChinese(date.Year);
            string month = ConvertMonthToChinese(date.Month);
            string day = ConvertDayToChinese(date.Day);

            return $"{year}年{month}月{day}日";
        }

        /// <summary>
        /// 转换年份为中文大写
        /// </summary>
        /// <param name="year">年份</param>
        /// <returns>中文大写年份</returns>
        private static string ConvertYearToChinese(int year)
        {
            string yearStr = year.ToString();
            string result = "";

            foreach (char digit in yearStr)
            {
                int num = int.Parse(digit.ToString());
                result += ChineseNumbers[num];
            }

            return result;
        }

        /// <summary>
        /// 转换月份为中文大写
        /// </summary>
        /// <param name="month">月份 (1-12)</param>
        /// <returns>中文大写月份</returns>
        private static string ConvertMonthToChinese(int month)
        {
            if (month < 1 || month > 12)
                throw new ArgumentOutOfRangeException(nameof(month), "月份必须在1-12之间");

            return ChineseMonths[month];
        }

        /// <summary>
        /// 转换日期为中文大写
        /// </summary>
        /// <param name="day">日期 (1-31)</param>
        /// <returns>中文大写日期</returns>
        private static string ConvertDayToChinese(int day)
        {
            if (day < 1 || day > 31)
                throw new ArgumentOutOfRangeException(nameof(day), "日期必须在1-31之间");

            if (day <= 10)
            {
                // 1-10: 一、二、三...十
                return day == 10 ? "十" : ChineseNumbers[day];
            }
            else if (day < 20)
            {
                // 11-19: 十一、十二...十九
                return "十" + ChineseNumbers[day - 10];
            }
            else if (day == 20)
            {
                // 20: 二十
                return "二十";
            }
            else if (day < 30)
            {
                // 21-29: 二十一、二十二...二十九
                return "二十" + ChineseNumbers[day - 20];
            }
            else if (day == 30)
            {
                // 30: 三十
                return "三十";
            }
            else
            {
                // 31: 三十一
                return "三十一";
            }
        }

        /// <summary>
        /// 测试方法，验证日期转换的正确性
        /// </summary>
        /// <returns>测试结果</returns>
        public static string TestConversion()
        {
            var testCases = new[]
            {
                new DateTime(2024, 1, 1),   // 二〇二四年一月一日
                new DateTime(2024, 12, 25), // 二〇二四年十二月二十五日
                new DateTime(2023, 10, 31), // 二〇二三年十月三十一日
                new DateTime(2025, 6, 15),  // 二〇二五年六月十五日
                new DateTime(2024, 2, 29),  // 二〇二四年二月二十九日
            };

            string result = "中文日期转换测试结果:\n";
            foreach (var date in testCases)
            {
                string chineseDate = ToChineseUppercase(date);
                result += $"{date:yyyy-MM-dd} -> {chineseDate}\n";
            }

            return result;
        }
    }
}
