<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RESServer 功能入口</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', 'Segoe UI', sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        h1 {
            color: #333;
            text-align: center;
        }
        .feature-list {
            margin-top: 30px;
        }
        .feature-card {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.1);
        }
        .feature-card h2 {
            margin-top: 0;
            color: #4CAF50;
        }
        .feature-card p {
            color: #666;
            margin-bottom: 20px;
        }
        .feature-card a {
            display: inline-block;
            background-color: #4CAF50;
            color: white;
            padding: 10px 15px;
            border-radius: 4px;
            text-decoration: none;
            font-weight: bold;
            transition: background-color 0.3s;
        }
        .feature-card a:hover {
            background-color: #45a049;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input[type="file"] {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        textarea {
            width: 100%;
            min-height: 100px;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        button {
            background-color: #4CAF50;
            color: white;
            padding: 10px 15px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover {
            background-color: #45a049;
        }
        #result {
            margin-top: 20px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background-color: #f9f9f9;
            min-height: 100px;
            white-space: pre-wrap;
        }
        .loader {
            border: 5px solid #f3f3f3;
            border-top: 5px solid #3498db;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            animation: spin 2s linear infinite;
            margin: 20px auto;
            display: none;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <h1>RESServer 功能入口</h1>
    
    <div class="feature-list">
        <div class="feature-card">
            <h2>DWG 文件处理 - 拆分图纸</h2>
            <p>上传 DWG 文件，自动识别并拆分出其中包含的所有图纸。系统会将拆分后的图纸打包为 ZIP 文件供下载。</p>
            <a href="/dwg-processing.html">前往使用</a>
        </div>

        <div class="feature-card">
            <h2>Gemini 文件分析</h2>
            <p>使用 Google Gemini AI 分析上传的文件内容，生成智能摘要和见解。支持多种文件格式。</p>
            <a href="/gemini-analysis.html">前往使用</a>
        </div>
    </div>

    <script>
        // 将原有的 Gemini 功能保存到新页面
        document.addEventListener('DOMContentLoaded', function() {
            const geminiContent = `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gemini 文件分析</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', 'Segoe UI', sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        h1 {
            color: #333;
            text-align: center;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input[type="file"] {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        textarea {
            width: 100%;
            min-height: 100px;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        button {
            background-color: #4CAF50;
            color: white;
            padding: 10px 15px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover {
            background-color: #45a049;
        }
        #result {
            margin-top: 20px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background-color: #f9f9f9;
            min-height: 100px;
            white-space: pre-wrap;
        }
        .loader {
            border: 5px solid #f3f3f3;
            border-top: 5px solid #3498db;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            animation: spin 2s linear infinite;
            margin: 20px auto;
            display: none;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <h1>Gemini 文件分析工具</h1>
    
    <div class="form-group">
        <label for="files">上传文件（可多选）：</label>
        <input type="file" id="files" name="files" multiple>
    </div>
    
    <div class="form-group">
        <label for="prompt">分析提示（可选）：</label>
        <textarea id="prompt" name="prompt" placeholder="请输入分析提示，例如：分析这些文件并提供详细摘要"></textarea>
    </div>
    
    <button id="submitBtn">上传并分析</button>
    
    <div class="loader" id="loader"></div>
    
    <div id="result"></div>

    <script>
        document.getElementById('submitBtn').addEventListener('click', async () => {
            const files = document.getElementById('files').files;
            if (files.length === 0) {
                alert('请至少选择一个文件');
                return;
            }

            const prompt = document.getElementById('prompt').value;
            const formData = new FormData();
            
            for (let i = 0; i < files.length; i++) {
                formData.append('Files', files[i]);
            }
            
            if (prompt) {
                formData.append('Prompt', prompt);
            }

            const resultDiv = document.getElementById('result');
            const loader = document.getElementById('loader');
            const submitBtn = document.getElementById('submitBtn');
            
            resultDiv.textContent = '';
            loader.style.display = 'block';
            submitBtn.disabled = true;
            
            try {
                const response = await fetch('/api/GeminiAnalysis/analyze', {
                    method: 'POST',
                    body: formData
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    resultDiv.textContent = data.analysis;
                } else {
                    resultDiv.textContent = \`错误: \${data.message || '未知错误'}\`;
                }
            } catch (error) {
                resultDiv.textContent = \`请求错误: \${error.message}\`;
            } finally {
                loader.style.display = 'none';
                submitBtn.disabled = false;
            }
        });
    <\/script>
</body>
</html>`;

            // 创建 Blob 对象
            const blob = new Blob([geminiContent], { type: 'text/html' });
            
            // 创建下载链接（仅供调试，实际部署时应由服务器生成文件）
            const a = document.createElement('a');
            a.href = URL.createObjectURL(blob);
            a.download = 'gemini-analysis.html';
            a.style.display = 'none';
            document.body.appendChild(a);
            
            // 自动点击下载链接
            // a.click(); // 注释掉自动下载
            
            // 清理
            document.body.removeChild(a);
            URL.revokeObjectURL(a.href);
        });
    </script>
</body>
</html> 