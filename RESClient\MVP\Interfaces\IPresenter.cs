using System;

namespace RESClient.MVP.Interfaces
{
    /// <summary>
    /// 控制器接口基类，所有控制器接口都应继承此接口
    /// </summary>
    public interface IPresenter
    {
        /// <summary>
        /// 初始化控制器
        /// </summary>
        void Initialize();
        
        /// <summary>
        /// 释放控制器资源
        /// </summary>
        void Dispose();
    }
    
    /// <summary>
    /// 泛型控制器接口，用于绑定特定视图
    /// </summary>
    /// <typeparam name="TView">视图接口类型</typeparam>
    public interface IPresenter<TView> : IPresenter where TView : IView
    {
        /// <summary>
        /// 获取关联的视图
        /// </summary>
        TView View { get; }
    }
} 