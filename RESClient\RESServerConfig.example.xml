<?xml version="1.0" encoding="utf-8"?>
<!-- RESServer配置示例文件 -->
<!-- 复制此文件为 RESServerConfig.xml 并根据实际情况修改配置 -->
<configuration>
    <appSettings>
        <!-- RESServer服务器地址配置 -->
        <!-- 本地服务器 -->
        <add key="RESServerAddress" value="http://localhost:5104" />
        
        <!-- 远程服务器示例 -->
        <!-- <add key="RESServerAddress" value="http://*************:5104" /> -->
        <!-- <add key="RESServerAddress" value="http://server.company.com:5104" /> -->
        
        <!-- 连接超时时间（分钟） -->
        <!-- 用于长时间的DWG处理操作 -->
        <add key="RESServerConnectionTimeout" value="15" />
        
        <!-- 请求超时时间（分钟） -->
        <!-- 用于文件上传和下载操作 -->
        <add key="RESServerRequestTimeout" value="10" />
    </appSettings>
</configuration>

<!-- 
配置说明：

1. RESServerAddress: RESServer服务器地址
   - 格式: http://[IP地址或域名]:[端口号]
   - 默认端口: 5104
   - 示例:
     * 本地服务器: http://localhost:5104
     * 局域网服务器: http://*************:5104
     * 远程服务器: http://server.company.com:5104

2. RESServerConnectionTimeout: 连接超时时间（分钟）
   - 用于长时间的DWG处理操作
   - 建议值: 15-30分钟
   - 根据DWG文件大小和服务器性能调整

3. RESServerRequestTimeout: 请求超时时间（分钟）
   - 用于文件上传和下载操作
   - 建议值: 10-20分钟
   - 根据网络速度和文件大小调整

使用方法：
1. 将此文件复制为 App.config 中的 appSettings 部分
2. 或者修改现有的 App.config 文件
3. 重启应用程序使配置生效

注意事项：
- 确保RESServer服务正在运行
- 检查网络连接和防火墙设置
- 服务器地址不要以 / 结尾
- 端口号必须与RESServer实际运行端口一致
-->
