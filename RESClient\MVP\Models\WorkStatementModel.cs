using System;
using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;
using NPOI.XWPF.UserModel;
using RESClient.MVP.Base;

namespace RESClient.MVP.Models
{
    /// <summary>
    /// 作业声明模型，用于处理作业声明模板的填充
    /// </summary>
    public class WorkStatementModel : BaseModel
    {
        /// <summary>
        /// 作业声明信息数据结构
        /// </summary>
        public class WorkStatementData
        {
            public string ClientName { get; set; } // 委托方
            public string ProjectAddress { get; set; } // 项目地址
            public string CertificateLevel { get; set; } // 证书等级
            public string CertificateObtainDate { get; set; } // 取得日期
            public string CertificateNumber { get; set; } // 证书编号
            public string CertificateExpiryDate { get; set; } // 有效期至
            public string ProfessionalScope { get; set; } // 专业范围及限额
            public string LegalRepresentative { get; set; } // 法人代表
            public string TechnicalManager { get; set; } // 技术负责人
            public string CompanyAddress { get; set; } // 地址
            public string Phone { get; set; } // 电话
            public string Fax { get; set; } // 传真
            public string ReportSigner { get; set; } // 测绘成果报告书签发人
            public string SurveyInstitution { get; set; } // 测绘机构（盖章）
            public string ReportDate { get; set; } // 报告日期
        }

        /// <summary>
        /// 生成测试用的作业声明数据
        /// </summary>
        /// <returns>作业声明测试数据</returns>
        public WorkStatementData GenerateTestWorkStatementData()
        {
            return new WorkStatementData
            {
                ClientName = "成都华熙同桂轨道城市发展有限公司",
                ProjectAddress = "桂龙西一路360号的龙潭九重院苑项目1-29栋、地下室",
                CertificateLevel = "甲测资字",
                CertificateObtainDate = "2022年7月13日",
                CertificateNumber = "51100923",
                CertificateExpiryDate = "2027年7月12日",
                ProfessionalScope = "工程测量、界线与不动产测绘***；不限额",
                LegalRepresentative = "黄荣",
                TechnicalManager = "包锦",
                CompanyAddress = "成都市高新区天府大道中段688号1栋9、11、12层",
                Phone = "028-86916175",
                Fax = "028-86916175",
                ReportSigner = "",
                SurveyInstitution = "四川省川建勘察设计院有限公司",
                ReportDate = "二〇二三年七月二十六日"
            };
        }

        /// <summary>
        /// 填充作业声明模板
        /// </summary>
        /// <param name="data">作业声明数据</param>
        /// <param name="outputPath">输出路径</param>
        /// <returns>是否成功</returns>
        public async Task<bool> FillWorkStatementTemplateAsync(WorkStatementData data, string outputPath)
        {
            try
            {
                return await Task.Run(() =>
                {
                    // 获取模板路径
                    string templatePath = Path.Combine(
                        AppDomain.CurrentDomain.BaseDirectory,
                        Program.TemplateDirectory,
                        "03_作业声明",
                        "作业声明.docx");

                    if (!File.Exists(templatePath))
                    {
                        throw new FileNotFoundException("作业声明模板文件不存在", templatePath);
                    }

                    // 确保输出目录存在
                    string outputDir = Path.GetDirectoryName(outputPath);
                    Directory.CreateDirectory(outputDir);

                    // 创建模板的临时副本
                    string tempFilePath = Path.Combine(
                        Path.GetTempPath(),
                        $"temp_work_statement_{DateTime.Now.Ticks}.docx");

                    // 复制模板到临时文件
                    File.Copy(templatePath, tempFilePath, true);

                    // 使用NPOI打开临时Word文档
                    XWPFDocument doc;
                    using (FileStream fs = new FileStream(tempFilePath, FileMode.Open, FileAccess.Read))
                    {
                        doc = new XWPFDocument(fs);
                    }

                    // 创建变量映射
                    var variableMapping = new Dictionary<string, string>
                    {
                        { "${委托方}", data.ClientName },
                        { "${项目地址}", data.ProjectAddress },
                        { "${证书等级}", data.CertificateLevel },
                        { "${取得日期}", data.CertificateObtainDate },
                        { "${证书编号}", data.CertificateNumber },
                        { "${有效期至}", data.CertificateExpiryDate },
                        { "${专业范围及限额}", data.ProfessionalScope },
                        { "${法人代表}", data.LegalRepresentative },
                        { "${技术负责人}", data.TechnicalManager },
                        { "${地址}", data.CompanyAddress },
                        { "${电话}", data.Phone },
                        { "${传真}", data.Fax },
                        { "${日期}", data.ReportDate }
                    };

                    // 替换所有文本中的占位符
                    foreach (var paragraph in doc.Paragraphs)
                    {
                        ReplaceVariablesInParagraph(paragraph, variableMapping);
                    }

                    // 表格中的占位符替换
                    foreach (var table in doc.Tables)
                    {
                        foreach (var row in table.Rows)
                        {
                            foreach (var cell in row.GetTableCells())
                            {
                                foreach (var paragraph in cell.Paragraphs)
                                {
                                    ReplaceVariablesInParagraph(paragraph, variableMapping);
                                }
                            }
                        }
                    }

                    // 保存填充后的文档到指定输出路径
                    using (FileStream fs = new FileStream(outputPath, FileMode.Create, FileAccess.Write))
                    {
                        doc.Write(fs);
                    }

                    doc.Close();

                    // 清理临时文件
                    try
                    {
                        if (File.Exists(tempFilePath))
                        {
                            File.Delete(tempFilePath);
                        }
                    }
                    catch
                    {
                        // 忽略临时文件清理错误
                    }

                    return true;
                });
            }
            catch (Exception ex)
            {
                HandleException(ex);
                return false;
            }
        }

        /// <summary>
        /// 在段落中替换变量，保留原始格式
        /// </summary>
        /// <param name="paragraph">段落</param>
        /// <param name="variableMapping">变量映射</param>
        private void ReplaceVariablesInParagraph(XWPFParagraph paragraph, Dictionary<string, string> variableMapping)
        {
            // 替换所有变量
            foreach (var variable in variableMapping)
            {
                if (paragraph.Text.Contains(variable.Key))
                {
                    ReplaceTextInParagraph(paragraph, variable.Key, variable.Value ?? "");
                }
            }
        }

        /// <summary>
        /// 替换段落中的文本，保留原始格式
        /// </summary>
        /// <param name="paragraph">段落</param>
        /// <param name="searchText">要搜索的文本</param>
        /// <param name="replaceText">替换文本</param>
        private void ReplaceTextInParagraph(XWPFParagraph paragraph, string searchText, string replaceText)
        {
            // 查找包含占位符的运行
            for (int i = 0; i < paragraph.Runs.Count; i++)
            {
                var run = paragraph.Runs[i];
                string runText = run.Text;

                if (runText != null && runText.Contains(searchText))
                {
                    // 保留格式，仅替换文本
                    run.SetText(runText.Replace(searchText, replaceText), 0);
                    return; // 替换完成
                }

                // 处理跨多个运行的占位符情况
                if (i < paragraph.Runs.Count - 1 && runText != null)
                {
                    // 查找是否占位符跨多个运行
                    string combinedText = runText;
                    int startRun = i;
                    int j = i + 1;

                    // 尝试组合多个运行的文本查找占位符
                    while (j < paragraph.Runs.Count && !combinedText.Contains(searchText))
                    {
                        string nextRunText = paragraph.Runs[j].Text ?? "";
                        combinedText += nextRunText;

                        if (combinedText.Contains(searchText))
                        {
                            // 找到了跨越多个运行的占位符
                            // 现在需要删除整个占位符并在第一个运行位置插入替换文本

                            // 在第一个运行中放入替换文本
                            int placeholderStart = combinedText.IndexOf(searchText);
                            int firstRunLength = runText.Length;

                            if (placeholderStart < firstRunLength)
                            {
                                // 占位符开始于第一个运行
                                string beforePlaceholder = runText.Substring(0, placeholderStart);
                                run.SetText(beforePlaceholder + replaceText, 0);

                                // 计算占位符末尾位置
                                int placeholderEnd = placeholderStart + searchText.Length;

                                // 如果占位符结束于当前运行之后，需要调整后面的运行
                                if (placeholderEnd > firstRunLength)
                                {
                                    int currentEnd = firstRunLength;
                                    int currentRun = i + 1;

                                    // 处理或删除跨越的运行
                                    while (currentRun <= j && currentEnd < placeholderEnd)
                                    {
                                        string currentText = paragraph.Runs[currentRun].Text ?? "";
                                        int currentLength = currentText.Length;

                                        if (currentEnd + currentLength > placeholderEnd)
                                        {
                                            // 这个运行包含占位符结束后的文本
                                            string afterPlaceholder = currentText.Substring(
                                                placeholderEnd - currentEnd);
                                            paragraph.Runs[currentRun].SetText(afterPlaceholder, 0);
                                            break;
                                        }
                                        else
                                        {
                                            // 这个运行完全在占位符内，清空它
                                            paragraph.Runs[currentRun].SetText("", 0);
                                        }

                                        currentEnd += currentLength;
                                        currentRun++;
                                    }
                                }

                                return; // 替换完成
                            }
                        }

                        j++;
                    }
                }
            }
        }

        /// <summary>
        /// 测试作业声明模板填充功能
        /// </summary>
        /// <param name="outputPath">输出路径</param>
        /// <returns>是否成功</returns>
        public async Task<bool> TestWorkStatementTemplateAsync(string outputPath)
        {
            var testData = GenerateTestWorkStatementData();
            return await FillWorkStatementTemplateAsync(testData, outputPath);
        }
    }
}
