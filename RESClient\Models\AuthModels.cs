using System;
using System.ComponentModel.DataAnnotations;

namespace RESClient.Models
{
    /// <summary>
    /// 登录请求模型
    /// </summary>
    public class LoginRequest
    {
        [Required]
        public string Username { get; set; } = string.Empty;

        [Required]
        public string Password { get; set; } = string.Empty;
    }

    /// <summary>
    /// 登录响应模型
    /// </summary>
    public class LoginResponse
    {
        public bool Success { get; set; }
        public string Message { get; set; } = string.Empty;
        public string Token { get; set; }
        public DateTime? ExpiresAt { get; set; }
        public UserInfo User { get; set; }
    }

    /// <summary>
    /// 用户信息模型
    /// </summary>
    public class UserInfo
    {
        public int Id { get; set; }
        public string Username { get; set; } = string.Empty;
        public string Email { get; set; }
        public string FullName { get; set; }
        public string Role { get; set; } = string.Empty;
        public bool IsActive { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime? LastLoginAt { get; set; }
    }

    /// <summary>
    /// 服务器状态响应模型
    /// </summary>
    public class ServerStatusResponse
    {
        public bool Success { get; set; }
        public string Message { get; set; } = string.Empty;
        public bool AuthenticationEnabled { get; set; }
        public DateTime ServerTime { get; set; }
        public string Version { get; set; } = string.Empty;
    }

    /// <summary>
    /// 通用 API 响应模型
    /// </summary>
    public class ApiResponse<T>
    {
        public bool Success { get; set; }
        public string Message { get; set; } = string.Empty;
        public T Data { get; set; }
        public string Error { get; set; }
    }

    /// <summary>
    /// 通用 API 响应模型（无数据）
    /// </summary>
    public class ApiResponse
    {
        public bool Success { get; set; }
        public string Message { get; set; } = string.Empty;
        public string Error { get; set; }
    }

    /// <summary>
    /// 认证状态枚举
    /// </summary>
    public enum AuthenticationStatus
    {
        /// <summary>
        /// 未知状态
        /// </summary>
        Unknown,
        
        /// <summary>
        /// 认证功能已禁用
        /// </summary>
        Disabled,
        
        /// <summary>
        /// 认证功能已启用但未登录
        /// </summary>
        RequiredButNotLoggedIn,
        
        /// <summary>
        /// 已登录
        /// </summary>
        LoggedIn,
        
        /// <summary>
        /// Token 已过期
        /// </summary>
        TokenExpired,
        
        /// <summary>
        /// 服务器连接失败
        /// </summary>
        ServerConnectionFailed
    }

    /// <summary>
    /// 认证状态信息
    /// </summary>
    public class AuthenticationStatusInfo
    {
        public AuthenticationStatus Status { get; set; }
        public string Message { get; set; } = string.Empty;
        public UserInfo User { get; set; }
        public DateTime? TokenExpiresAt { get; set; }
        public bool IsAuthenticationEnabled { get; set; }
    }

    /// <summary>
    /// 服务器连接状态枚举
    /// </summary>
    public enum ServerConnectionStatus
    {
        /// <summary>
        /// 未知状态
        /// </summary>
        Unknown,

        /// <summary>
        /// 连接中
        /// </summary>
        Connecting,

        /// <summary>
        /// 已连接
        /// </summary>
        Connected,

        /// <summary>
        /// 连接失败
        /// </summary>
        Failed,

        /// <summary>
        /// 连接超时
        /// </summary>
        Timeout,

        /// <summary>
        /// 服务器不可达
        /// </summary>
        Unreachable
    }

    /// <summary>
    /// 服务器连接状态信息
    /// </summary>
    public class ServerConnectionInfo
    {
        public ServerConnectionStatus Status { get; set; }
        public string Message { get; set; } = string.Empty;
        public string ServerUrl { get; set; } = string.Empty;
        public DateTime LastCheckTime { get; set; }
        public TimeSpan ResponseTime { get; set; }
        public string ErrorDetails { get; set; } = string.Empty;
        public bool IsHealthy { get; set; }
        public string ServerVersion { get; set; } = string.Empty;
    }
}
