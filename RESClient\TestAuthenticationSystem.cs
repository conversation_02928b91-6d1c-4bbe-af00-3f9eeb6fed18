using System;
using System.Threading.Tasks;
using System.Windows.Forms;
using RESClient.Services;
using RESClient.Models;
using RESClient.Views;

namespace RESClient
{
    /// <summary>
    /// 认证系统测试程序
    /// </summary>
    public class TestAuthenticationSystem
    {
        private IAuthService _authService;
        private RESServerConfigService _configService;

        public TestAuthenticationSystem()
        {
            _configService = RESServerConfigService.Instance;
            _authService = new AuthService(_configService);
        }

        /// <summary>
        /// 运行所有测试
        /// </summary>
        public async Task RunAllTests()
        {
            Console.WriteLine("=== RESClient 认证系统测试 ===");
            Console.WriteLine($"服务器地址: {_configService.ServerAddress}");
            Console.WriteLine();

            try
            {
                // 测试 1: 检查服务器状态
                await TestServerStatus();

                // 测试 2: 测试登录功能
                await TestLogin();

                // 测试 3: 测试 Token 验证
                await TestTokenValidation();

                // 测试 4: 测试登出功能
                await TestLogout();

                // 测试 5: 测试 HTTP 客户端工厂
                await TestHttpClientFactory();

                // 测试 6: 测试登录界面
                TestLoginForm();

                Console.WriteLine("\n=== 所有测试完成 ===");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"测试过程中发生错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 测试服务器状态
        /// </summary>
        private async Task TestServerStatus()
        {
            Console.WriteLine("1. 测试服务器状态检查...");
            
            try
            {
                var status = await _authService.GetServerStatusAsync();
                
                Console.WriteLine($"   服务器状态: {status.Status}");
                Console.WriteLine($"   认证启用: {status.IsAuthenticationEnabled}");
                Console.WriteLine($"   消息: {status.Message}");
                
                if (status.Status == AuthenticationStatus.ServerConnectionFailed)
                {
                    Console.WriteLine("   ⚠️  无法连接到服务器，请确保 RESServer 正在运行");
                    return;
                }
                
                Console.WriteLine("   ✅ 服务器状态检查成功");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"   ❌ 服务器状态检查失败: {ex.Message}");
            }
            
            Console.WriteLine();
        }

        /// <summary>
        /// 测试登录功能
        /// </summary>
        private async Task TestLogin()
        {
            Console.WriteLine("2. 测试登录功能...");
            
            try
            {
                // 测试有效登录
                var loginRequest = new LoginRequest
                {
                    Username = "admin",
                    Password = "admin123"
                };
                
                Console.WriteLine("   尝试使用 admin/admin123 登录...");
                var response = await _authService.LoginAsync(loginRequest);
                
                if (response.Success)
                {
                    Console.WriteLine("   ✅ 登录成功");
                    Console.WriteLine($"   用户: {response.User?.Username}");
                    Console.WriteLine($"   角色: {response.User?.Role}");
                    Console.WriteLine($"   Token 过期时间: {response.ExpiresAt}");
                }
                else
                {
                    Console.WriteLine($"   ❌ 登录失败: {response.Message}");
                }
                
                // 测试无效登录
                Console.WriteLine("   尝试使用错误密码登录...");
                var invalidRequest = new LoginRequest
                {
                    Username = "admin",
                    Password = "wrongpassword"
                };
                
                var invalidResponse = await _authService.LoginAsync(invalidRequest);
                if (!invalidResponse.Success)
                {
                    Console.WriteLine("   ✅ 错误密码登录正确被拒绝");
                }
                else
                {
                    Console.WriteLine("   ❌ 错误密码登录应该被拒绝");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"   ❌ 登录测试失败: {ex.Message}");
            }
            
            Console.WriteLine();
        }

        /// <summary>
        /// 测试 Token 验证
        /// </summary>
        private async Task TestTokenValidation()
        {
            Console.WriteLine("3. 测试 Token 验证...");
            
            try
            {
                if (_authService.IsLoggedIn)
                {
                    Console.WriteLine("   验证当前 Token...");
                    var isValid = await _authService.ValidateTokenAsync();
                    
                    if (isValid)
                    {
                        Console.WriteLine("   ✅ Token 验证成功");
                    }
                    else
                    {
                        Console.WriteLine("   ❌ Token 验证失败");
                    }
                }
                else
                {
                    Console.WriteLine("   ⚠️  当前未登录，跳过 Token 验证测试");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"   ❌ Token 验证测试失败: {ex.Message}");
            }
            
            Console.WriteLine();
        }

        /// <summary>
        /// 测试登出功能
        /// </summary>
        private async Task TestLogout()
        {
            Console.WriteLine("4. 测试登出功能...");
            
            try
            {
                if (_authService.IsLoggedIn)
                {
                    Console.WriteLine("   执行登出...");
                    _authService.Logout();
                    
                    if (!_authService.IsLoggedIn)
                    {
                        Console.WriteLine("   ✅ 登出成功");
                    }
                    else
                    {
                        Console.WriteLine("   ❌ 登出失败");
                    }
                }
                else
                {
                    Console.WriteLine("   ⚠️  当前未登录，跳过登出测试");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"   ❌ 登出测试失败: {ex.Message}");
            }
            
            Console.WriteLine();
        }

        /// <summary>
        /// 测试 HTTP 客户端工厂
        /// </summary>
        private async Task TestHttpClientFactory()
        {
            Console.WriteLine("5. 测试 HTTP 客户端工厂...");
            
            try
            {
                // 初始化工厂
                HttpClientFactoryInstance.Initialize(_configService, _authService);
                
                // 测试创建状态检查客户端
                using (var statusClient = HttpClientFactoryInstance.Instance.CreateStatusClient())
                {
                    Console.WriteLine("   ✅ 状态检查客户端创建成功");
                }
                
                // 测试创建认证客户端（如果已登录）
                if (_authService.IsLoggedIn)
                {
                    using (var authClient = await HttpClientFactoryInstance.Instance.CreateClientAsync())
                    {
                        Console.WriteLine("   ✅ 认证客户端创建成功");
                    }
                }
                else
                {
                    Console.WriteLine("   ⚠️  未登录，跳过认证客户端测试");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"   ❌ HTTP 客户端工厂测试失败: {ex.Message}");
            }
            
            Console.WriteLine();
        }

        /// <summary>
        /// 测试登录界面
        /// </summary>
        private void TestLoginForm()
        {
            Console.WriteLine("6. 测试登录界面...");
            
            try
            {
                Console.WriteLine("   创建登录窗体...");
                using (var loginForm = new LoginForm(_authService))
                {
                    Console.WriteLine("   ✅ 登录窗体创建成功");
                    Console.WriteLine("   💡 可以手动显示登录窗体进行 UI 测试");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"   ❌ 登录界面测试失败: {ex.Message}");
            }
            
            Console.WriteLine();
        }

        /// <summary>
        /// 运行认证系统测试的静态方法
        /// </summary>
        public static async Task RunAuthenticationTests()
        {
            var tester = new TestAuthenticationSystem();
            await tester.RunAllTests();
        }
    }
}
