using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using NPOI.SS.UserModel;
using RESClient.Services.Implementations;

namespace RESClient
{
    /// <summary>
    /// 模块诊断工具 - 用于诊断楼栋基本信息和房产面积汇总表模块生成失败的问题
    /// </summary>
    public class ModuleDiagnosticTool
    {
        public static async Task Main(string[] args)
        {
            Console.WriteLine("=== 模块诊断工具 ===");
            Console.WriteLine("诊断楼栋基本信息和房产面积汇总表模块生成失败的问题");
            Console.WriteLine();

            // 获取用户输入的数据目录
            Console.Write("请输入数据目录路径: ");
            string dataDirectory = Console.ReadLine();
            
            if (string.IsNullOrEmpty(dataDirectory))
            {
                dataDirectory = @"C:\Users\<USER>\source\repos\XSENTRYCAD\实测";
                Console.WriteLine($"使用默认数据目录: {dataDirectory}");
            }

            Console.Write("请输入输出目录路径: ");
            string outputDirectory = Console.ReadLine();
            
            if (string.IsNullOrEmpty(outputDirectory))
            {
                outputDirectory = @"C:\Users\<USER>\source\repos\XSENTRYCAD\实测\Output";
                Console.WriteLine($"使用默认输出目录: {outputDirectory}");
            }

            Console.WriteLine();

            var parameters = new Dictionary<string, object>
            {
                ["DataFolder"] = dataDirectory,
                ["OutputDir"] = outputDirectory
            };

            await DiagnoseBuildingInfoModule(parameters);
            Console.WriteLine();
            await DiagnoseEstateAreaSummaryModule(parameters);

            Console.WriteLine("\n=== 诊断完成 ===");
            Console.WriteLine("按任意键退出...");
            Console.ReadKey();
        }

        /// <summary>
        /// 诊断楼栋基本信息模块
        /// </summary>
        private static async Task DiagnoseBuildingInfoModule(Dictionary<string, object> parameters)
        {
            Console.WriteLine("1. 诊断楼栋基本信息模块");
            Console.WriteLine("----------------------------------------");

            try
            {
                var generator = new BuildingInfoModuleGenerator();
                
                // 1. 检查模块可用性
                Console.WriteLine("步骤1: 检查模块可用性...");
                bool isAvailable = generator.IsAvailable(parameters);
                Console.WriteLine($"模块可用性: {(isAvailable ? "✓ 可用" : "❌ 不可用")}");

                if (!isAvailable)
                {
                    Console.WriteLine("❌ 模块不可用，进行详细检查:");
                    await DiagnoseModuleAvailability("楼栋基本信息", parameters);
                    return;
                }

                // 2. 检查数据文件查找
                Console.WriteLine("\n步骤2: 检查数据文件查找...");
                await TestDataFileSearch(generator, parameters);

                // 3. 尝试生成模块
                Console.WriteLine("\n步骤3: 尝试生成模块...");
                var progressMessages = new List<string>();
                Action<int, string> progressCallback = (progress, message) =>
                {
                    progressMessages.Add($"[{progress}%] {message}");
                    Console.WriteLine($"  {message}");
                };

                bool result = await generator.GenerateAsync(parameters, progressCallback);
                Console.WriteLine($"生成结果: {(result ? "✓ 成功" : "❌ 失败")}");

                if (!result)
                {
                    Console.WriteLine("\n❌ 生成失败，错误信息:");
                    foreach (var msg in progressMessages)
                    {
                        if (msg.Contains("[ERROR]") || msg.Contains("[WARNING]"))
                        {
                            Console.WriteLine($"  {msg}");
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ 诊断楼栋基本信息模块时出错: {ex.Message}");
                Console.WriteLine($"详细错误: {ex}");
            }
        }

        /// <summary>
        /// 诊断房产面积汇总表模块
        /// </summary>
        private static async Task DiagnoseEstateAreaSummaryModule(Dictionary<string, object> parameters)
        {
            Console.WriteLine("2. 诊断房产面积汇总表模块");
            Console.WriteLine("----------------------------------------");

            try
            {
                var generator = new EstateAreaSummaryModuleGenerator();
                
                // 1. 检查模块可用性
                Console.WriteLine("步骤1: 检查模块可用性...");
                bool isAvailable = generator.IsAvailable(parameters);
                Console.WriteLine($"模块可用性: {(isAvailable ? "✓ 可用" : "❌ 不可用")}");

                if (!isAvailable)
                {
                    Console.WriteLine("❌ 模块不可用，进行详细检查:");
                    await DiagnoseModuleAvailability("房产面积汇总表", parameters);
                    return;
                }

                // 2. 检查数据文件查找
                Console.WriteLine("\n步骤2: 检查数据文件查找...");
                await TestDataFileSearchForEstate(generator, parameters);

                // 3. 尝试生成模块
                Console.WriteLine("\n步骤3: 尝试生成模块...");
                var progressMessages = new List<string>();
                Action<int, string> progressCallback = (progress, message) =>
                {
                    progressMessages.Add($"[{progress}%] {message}");
                    Console.WriteLine($"  {message}");
                };

                bool result = await generator.GenerateAsync(parameters, progressCallback);
                Console.WriteLine($"生成结果: {(result ? "✓ 成功" : "❌ 失败")}");

                if (!result)
                {
                    Console.WriteLine("\n❌ 生成失败，错误信息:");
                    foreach (var msg in progressMessages)
                    {
                        if (msg.Contains("[ERROR]") || msg.Contains("[WARNING]"))
                        {
                            Console.WriteLine($"  {msg}");
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ 诊断房产面积汇总表模块时出错: {ex.Message}");
                Console.WriteLine($"详细错误: {ex}");
            }
        }

        /// <summary>
        /// 诊断模块可用性问题
        /// </summary>
        private static async Task DiagnoseModuleAvailability(string moduleName, Dictionary<string, object> parameters)
        {
            string dataDirectory = parameters["DataFolder"]?.ToString();
            string outputDirectory = parameters["OutputDir"]?.ToString();

            // 检查数据目录
            Console.WriteLine($"  检查数据目录: {dataDirectory}");
            if (string.IsNullOrEmpty(dataDirectory))
            {
                Console.WriteLine("    ❌ 数据目录为空");
                return;
            }
            if (!Directory.Exists(dataDirectory))
            {
                Console.WriteLine("    ❌ 数据目录不存在");
                return;
            }
            Console.WriteLine("    ✓ 数据目录存在");

            // 检查输出目录
            Console.WriteLine($"  检查输出目录: {outputDirectory}");
            if (string.IsNullOrEmpty(outputDirectory))
            {
                Console.WriteLine("    ❌ 输出目录为空");
                return;
            }
            Console.WriteLine("    ✓ 输出目录配置正确");

            // 检查模板文件
            string templatePath = "";
            if (moduleName == "楼栋基本信息")
            {
                templatePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "报告模板", "06_楼栋基本信息", "楼栋基本信息.docx");
            }
            else if (moduleName == "房产面积汇总表")
            {
                templatePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "报告模板", "11_房产面积汇总表", "房产面积汇总表.docx");
            }

            Console.WriteLine($"  检查模板文件: {templatePath}");
            if (!File.Exists(templatePath))
            {
                Console.WriteLine("    ❌ 模板文件不存在");
                return;
            }
            Console.WriteLine("    ✓ 模板文件存在");

            // 检查数据文件
            Console.WriteLine("  检查数据文件:");
            await CheckDataFiles(dataDirectory);
        }

        /// <summary>
        /// 检查数据文件
        /// </summary>
        private static async Task CheckDataFiles(string dataDirectory)
        {
            try
            {
                // 检查标准Excel文件
                var excelFiles = Directory.GetFiles(dataDirectory, "*.xls*", SearchOption.AllDirectories);
                Console.WriteLine($"    找到 {excelFiles.Length} 个Excel文件");

                bool foundStandardExcel = false;
                foreach (string filePath in excelFiles)
                {
                    try
                    {
                        if (await ValidateExcelFileStructure(filePath))
                        {
                            Console.WriteLine($"    ✓ 找到标准Excel文件: {Path.GetFileName(filePath)}");
                            foundStandardExcel = true;
                            break;
                        }
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"    ⚠ 验证文件失败 {Path.GetFileName(filePath)}: {ex.Message}");
                    }
                }

                if (!foundStandardExcel)
                {
                    Console.WriteLine("    ❌ 未找到标准Excel文件（包含4个指定工作表）");
                }

                // 检查CGB文件
                var cgbFiles = Directory.GetFiles(dataDirectory, "*.xls*", SearchOption.AllDirectories)
                    .Where(file => Path.GetFileName(file).ToLower().Contains("cgb"))
                    .ToArray();

                if (cgbFiles.Any())
                {
                    Console.WriteLine($"    ✓ 找到CGB文件: {string.Join(", ", cgbFiles.Select(Path.GetFileName))}");
                }
                else
                {
                    Console.WriteLine("    ❌ 未找到CGB文件");
                }

                // 检查7z文件
                var archiveFiles = Directory.GetFiles(dataDirectory, "*.7z", SearchOption.AllDirectories)
                    .Where(file => Path.GetFileName(file).Contains("成果包"))
                    .ToArray();

                if (archiveFiles.Any())
                {
                    Console.WriteLine($"    ✓ 找到成果包.7z文件: {string.Join(", ", archiveFiles.Select(Path.GetFileName))}");
                }
                else
                {
                    Console.WriteLine("    ❌ 未找到成果包.7z文件");
                }

                if (!foundStandardExcel && !cgbFiles.Any() && !archiveFiles.Any())
                {
                    Console.WriteLine("    ❌ 未找到任何可用的数据文件");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"    ❌ 检查数据文件时出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 验证Excel文件是否包含4个指定的工作表
        /// </summary>
        private static async Task<bool> ValidateExcelFileStructure(string filePath)
        {
            var requiredSheets = new HashSet<string>
            {
                "房屋建筑面积总表",
                "共有建筑面积的分摊",
                "房产分户面积统计表",
                "楼盘表信息"
            };

            try
            {
                using (var stream = new FileStream(filePath, FileMode.Open, FileAccess.Read, FileShare.ReadWrite))
                {
                    IWorkbook workbook = WorkbookFactory.Create(stream);

                    if (workbook.NumberOfSheets != 4)
                    {
                        return false;
                    }

                    var actualSheets = new HashSet<string>();
                    for (int i = 0; i < workbook.NumberOfSheets; i++)
                    {
                        actualSheets.Add(workbook.GetSheetName(i));
                    }

                    return requiredSheets.SetEquals(actualSheets);
                }
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 测试数据文件查找（楼栋基本信息）
        /// </summary>
        private static async Task TestDataFileSearch(BuildingInfoModuleGenerator generator, Dictionary<string, object> parameters)
        {
            string dataDirectory = parameters["DataFolder"]?.ToString();

            Console.WriteLine("  测试数据文件查找逻辑...");

            var progressMessages = new List<string>();
            Action<int, string> progressCallback = (progress, message) =>
            {
                progressMessages.Add($"[{progress}%] {message}");
                Console.WriteLine($"    {message}");
            };

            try
            {
                // 使用反射调用私有方法FindCGBFile
                var method = typeof(BuildingInfoModuleGenerator).GetMethod("FindCGBFile",
                    System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);

                if (method != null)
                {
                    string result = (string)method.Invoke(generator, new object[] { dataDirectory, progressCallback });

                    if (!string.IsNullOrEmpty(result))
                    {
                        Console.WriteLine($"  ✓ 找到数据文件: {Path.GetFileName(result)}");
                        Console.WriteLine($"    完整路径: {result}");
                    }
                    else
                    {
                        Console.WriteLine("  ❌ 未找到数据文件");
                    }
                }
                else
                {
                    Console.WriteLine("  ⚠ 无法测试FindCGBFile方法（反射失败）");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"  ❌ 测试数据文件查找时出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 测试数据文件查找（房产面积汇总表）
        /// </summary>
        private static async Task TestDataFileSearchForEstate(EstateAreaSummaryModuleGenerator generator, Dictionary<string, object> parameters)
        {
            string dataDirectory = parameters["DataFolder"]?.ToString();

            Console.WriteLine("  测试数据文件查找逻辑...");

            var progressMessages = new List<string>();
            Action<int, string> progressCallback = (progress, message) =>
            {
                progressMessages.Add($"[{progress}%] {message}");
                Console.WriteLine($"    {message}");
            };

            try
            {
                // 使用反射调用私有方法FindCGBFile
                var method = typeof(EstateAreaSummaryModuleGenerator).GetMethod("FindCGBFile",
                    System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);

                if (method != null)
                {
                    string result = (string)method.Invoke(generator, new object[] { dataDirectory, progressCallback });

                    if (!string.IsNullOrEmpty(result))
                    {
                        Console.WriteLine($"  ✓ 找到数据文件: {Path.GetFileName(result)}");
                        Console.WriteLine($"    完整路径: {result}");
                    }
                    else
                    {
                        Console.WriteLine("  ❌ 未找到数据文件");
                    }
                }
                else
                {
                    Console.WriteLine("  ⚠ 无法测试FindCGBFile方法（反射失败）");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"  ❌ 测试数据文件查找时出错: {ex.Message}");
            }
        }
    }
}
