@echo off
chcp 65001 >nul
echo ========================================
echo RESClient 记住密码功能验证脚本
echo ========================================
echo.

echo 1. 快速验证功能是否正常工作
echo    运行命令: RESClient.exe verify-remember-password
echo.

echo 2. 运行完整的功能测试
echo    运行命令: RESClient.exe test-remember-password
echo.

echo 3. 查看功能演示
echo    运行命令: RESClient.exe demo-remember-password
echo.

echo 4. 手动测试步骤：
echo    a) 启动 RESServer（确保认证功能启用）
echo    b) 启动 RESClient
echo    c) 在登录界面勾选"记住密码"
echo    d) 登录成功后重启应用程序
echo    e) 验证凭据是否自动填充
echo.

echo 5. 功能特点：
echo    ✓ 安全的密码加密存储（Windows DPAPI）
echo    ✓ 自动填充用户名和密码
echo    ✓ 手动登录确认（不自动提交）
echo    ✓ 便捷的清除凭据选项
echo    ✓ 只在服务器启用认证时生效
echo.

echo 按任意键退出...
pause >nul
