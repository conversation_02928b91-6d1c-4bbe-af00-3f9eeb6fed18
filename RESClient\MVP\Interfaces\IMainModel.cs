using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using RESClient.Services;

namespace RESClient.MVP.Interfaces
{
    /// <summary>
    /// 主模型接口
    /// </summary>
    public interface IMainModel : IModel
    {
        /// <summary>
        /// 错误收集器
        /// </summary>
        ErrorCollector ErrorCollector { get; }

        /// <summary>
        /// 获取报告模板列表
        /// </summary>
        /// <returns>报告模板列表</returns>
        Task<List<string>> GetTemplateListAsync();
        
        /// <summary>
        /// 生成报告
        /// </summary>
        /// <param name="parameters">报告参数</param>
        /// <param name="progressCallback">进度回调</param>
        /// <returns>生成结果</returns>
        Task<bool> GenerateReportAsync(Dictionary<string, object> parameters, Action<int, string> progressCallback);
        
        /// <summary>
        /// 获取目录结构信息
        /// </summary>
        /// <returns>目录结构信息字典</returns>
        Dictionary<string, string> GetDirectoryInfo();

        /// <summary>
        /// 测试生成房产分层测绘图
        /// </summary>
        /// <param name="progressCallback">进度回调</param>
        /// <returns>生成结果</returns>
        Task<bool> TestFloorPlanGenerationAsync(Action<int, string> progressCallback);
    }
} 