using System;
using RESClient.MVP.Interfaces;

namespace RESClient.MVP.Base
{
    /// <summary>
    /// 模型基类
    /// </summary>
    public abstract class BaseModel : IModel
    {
        /// <summary>
        /// 构造函数
        /// </summary>
        protected BaseModel()
        {
            Initialize();
        }

        /// <summary>
        /// 初始化模型
        /// </summary>
        public virtual void Initialize()
        {
            // 子类可重写此方法实现特定的初始化逻辑
        }

        /// <summary>
        /// 释放模型资源
        /// </summary>
        public virtual void Dispose()
        {
            // 子类可重写此方法实现特定的资源释放逻辑
        }

        /// <summary>
        /// 处理异常
        /// </summary>
        /// <param name="ex">异常</param>
        /// <returns>错误消息</returns>
        protected string HandleException(Exception ex)
        {
            // 记录异常日志，未来可扩展
            Console.WriteLine($"错误: {ex.Message}");
            return ex.Message;
        }
    }
} 