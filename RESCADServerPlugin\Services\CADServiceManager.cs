namespace RESCADServerPlugin.Services
{
    public static class CADServiceManager
    {
        private static CADServerService _serverService;
        private static CADDrawingService _drawingService;

        // 获取或创建服务器服务实例
        public static CADServerService GetServerService()
        {
            if (_serverService == null)
            {
                _serverService = new CADServerService();
            }
            return _serverService;
        }

        // 获取或创建绘图服务实例
        public static CADDrawingService GetDrawingService()
        {
            if (_drawingService == null)
            {
                _drawingService = new CADDrawingService();
            }
            return _drawingService;
        }
    }
} 