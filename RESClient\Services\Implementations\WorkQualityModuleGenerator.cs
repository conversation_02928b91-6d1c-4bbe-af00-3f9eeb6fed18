using RESClient.MVP.Models;
using System;
using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;

namespace RESClient.Services.Implementations
{
    /// <summary>
    /// 作业、质量检查与验收模块生成器
    /// </summary>
    public class WorkQualityModuleGenerator : IReportModuleGenerator
    {
        private readonly WorkQualityParametersModel _parametersModel;
        private readonly WorkQualityModel _workQualityModel;

        public WorkQualityModuleGenerator()
        {
            _parametersModel = new WorkQualityParametersModel();
            _workQualityModel = new WorkQualityModel();
        }

        /// <summary>
        /// 模块名称
        /// </summary>
        public string ModuleName => "作业、质量检查与验收";

        /// <summary>
        /// 生成报告模块
        /// </summary>
        /// <param name="parameters">生成参数</param>
        /// <param name="progressCallback">进度回调</param>
        /// <returns>生成结果</returns>
        public async Task<bool> GenerateAsync(Dictionary<string, object> parameters, Action<int, string> progressCallback)
        {
            try
            {
                progressCallback?.Invoke(0, "[INFO]开始生成作业、质量检查与验收模块...");

                // 获取输出目录 - 支持两种参数键名
                object outputDirObj = null;
                if (!parameters.TryGetValue("OutputDirectory", out outputDirObj) ||
                    string.IsNullOrEmpty(outputDirObj?.ToString()))
                {
                    // 尝试使用 OutputDir 参数键
                    if (!parameters.TryGetValue("OutputDir", out outputDirObj) ||
                        string.IsNullOrEmpty(outputDirObj?.ToString()))
                    {
                        progressCallback?.Invoke(0, "[ERROR]未指定输出目录 (需要 OutputDirectory 或 OutputDir 参数)");
                        return false;
                    }
                }

                string outputDirectory = outputDirObj.ToString();
                progressCallback?.Invoke(10, "[INFO]输出目录: " + outputDirectory);

                // 确保输出目录存在
                if (!Directory.Exists(outputDirectory))
                {
                    Directory.CreateDirectory(outputDirectory);
                    progressCallback?.Invoke(20, "[INFO]创建输出目录");
                }

                // 获取作业、质量检查与验收参数
                var workQualityParameters = _parametersModel.GetCurrentParameters();
                progressCallback?.Invoke(30, "[INFO]加载作业、质量检查与验收参数");

                // 验证参数
                var (isValid, errors) = workQualityParameters.Validate();
                if (!isValid)
                {
                    string errorMessage = "作业、质量检查与验收参数验证失败: " + string.Join(", ", errors);
                    progressCallback?.Invoke(30, "[ERROR]" + errorMessage);
                    return false;
                }

                progressCallback?.Invoke(50, "[INFO]参数验证通过");

                // 转换为WorkQualityModel所需的数据格式
                var workQualityData = ConvertToWorkQualityData(workQualityParameters);
                progressCallback?.Invoke(60, "[INFO]转换参数格式");

                // 生成输出文件路径
                string outputPath = Path.Combine(outputDirectory, $"{ModuleName}.docx");
                progressCallback?.Invoke(70, "[INFO]准备生成文档: " + Path.GetFileName(outputPath));

                // 调用WorkQualityModel生成作业、质量检查与验收
                bool result = await _workQualityModel.FillWorkQualityTemplateAsync(workQualityData, outputPath);

                if (result)
                {
                    progressCallback?.Invoke(100, "[SUCCESS]作业、质量检查与验收生成完成");
                    return true;
                }
                else
                {
                    progressCallback?.Invoke(100, "[ERROR]作业、质量检查与验收生成失败");
                    return false;
                }
            }
            catch (Exception ex)
            {
                progressCallback?.Invoke(100, $"[ERROR]作业、质量检查与验收生成过程中发生异常: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 转换参数为数据格式
        /// </summary>
        /// <param name="parameters">参数</param>
        /// <returns>数据格式</returns>
        private WorkQualityModel.WorkQualityData ConvertToWorkQualityData(WorkQualityParametersModel.WorkQualityParameters parameters)
        {
            return new WorkQualityModel.WorkQualityData
            {
                InternalWorkerName = parameters.InternalWorkerName,
                InternalWorkerCertificateNumber = parameters.InternalWorkerCertificateNumber,
                InternalWorkerWorkCertificate = parameters.InternalWorkerWorkCertificate,
                InternalWorkerCertificateValidity = parameters.InternalWorkerCertificateValidity,

                ExternalWorker1Name = parameters.ExternalWorker1Name,
                ExternalWorker1CertificateNumber = parameters.ExternalWorker1CertificateNumber,
                ExternalWorker1WorkCertificate = parameters.ExternalWorker1WorkCertificate,
                ExternalWorker1CertificateValidity = parameters.ExternalWorker1CertificateValidity,

                ExternalWorker2Name = parameters.ExternalWorker2Name,
                ExternalWorker2CertificateNumber = parameters.ExternalWorker2CertificateNumber,
                ExternalWorker2WorkCertificate = parameters.ExternalWorker2WorkCertificate,
                ExternalWorker2CertificateValidity = parameters.ExternalWorker2CertificateValidity,

                FirstLevelInspectorName = parameters.FirstLevelInspectorName,
                FirstLevelInspectorCertificateNumber = parameters.FirstLevelInspectorCertificateNumber,
                FirstLevelInspectorWorkCertificate = parameters.FirstLevelInspectorWorkCertificate,
                FirstLevelInspectorCertificateValidity = parameters.FirstLevelInspectorCertificateValidity,

                SecondLevelInspectorName = parameters.SecondLevelInspectorName,
                SecondLevelInspectorCertificateNumber = parameters.SecondLevelInspectorCertificateNumber,
                SecondLevelInspectorWorkCertificate = parameters.SecondLevelInspectorWorkCertificate,
                SecondLevelInspectorCertificateValidity = parameters.SecondLevelInspectorCertificateValidity,

                SignerName = parameters.SignerName,
                SignerCertificateNumber = parameters.SignerCertificateNumber,
                SignerWorkCertificate = parameters.SignerWorkCertificate,
                SignerCertificateValidity = parameters.SignerCertificateValidity,

                CalculationMethod = parameters.CalculationMethod,
                Comparator = parameters.Comparator,
                EdgeLengthMethod = parameters.EdgeLengthMethod
            };
        }

        /// <summary>
        /// 是否可用
        /// </summary>
        /// <param name="parameters">检查参数</param>
        /// <returns>是否可用</returns>
        public bool IsAvailable(Dictionary<string, object> parameters)
        {
            try
            {
                // 检查模板文件是否存在
                string templatePath = Path.Combine(
                    AppDomain.CurrentDomain.BaseDirectory,
                    Program.TemplateDirectory,
                    "04_作业、质量检查与验收",
                    "作业、质量检查与验收.docx");

                return File.Exists(templatePath);
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 获取参数模型（用于外部访问）
        /// </summary>
        /// <returns>参数模型</returns>
        public WorkQualityParametersModel GetParametersModel()
        {
            return _parametersModel;
        }
    }
}