﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using RESClient.MVP.Interfaces;
using RESClient.MVP.Models;
using RESClient.Services.Implementations;
using RESClient.Services;
using RESClient.Models;
using RESClient.Views;
using System.IO;

namespace RESClient.MVP.Views
{
    public partial class MainForm : Form, IMainView
    {
        // 模块状态类型
        public enum ModuleStatus
        {
            None,       // 无
            Ready,      // 就绪
            Processing, // 处理中
            Success,    // 成功
            Failed      // 失败
        }
        
        // 日志消息类型
        private enum LogMessageType
        {
            Normal,     // 普通消息
            Success,    // 成功消息
            Warning,    // 警告消息
            Error,      // 错误消息
            Info,       // 信息消息
            Directory,  // 目录信息
            Template,   // 模板信息
            Progress    // 进度信息
        }

        // 日志消息颜色映射
        private readonly Dictionary<LogMessageType, Color> _logColors = new Dictionary<LogMessageType, Color>
        {
            { LogMessageType.Normal, Color.White },
            { LogMessageType.Success, Color.LightGreen },
            { LogMessageType.Warning, Color.Yellow },
            { LogMessageType.Error, Color.Salmon },
            { LogMessageType.Info, Color.LightSkyBlue },
            { LogMessageType.Directory, Color.Plum },
            { LogMessageType.Template, Color.Aqua },
            { LogMessageType.Progress, Color.LightGreen }
        };

        // 模块状态颜色和文本映射
        private readonly Dictionary<ModuleStatus, Color> _moduleStatusColors = new Dictionary<ModuleStatus, Color>
        {
            { ModuleStatus.None, SystemColors.ControlText },
            { ModuleStatus.Ready, Color.DodgerBlue },
            { ModuleStatus.Processing, Color.Orange },
            { ModuleStatus.Success, Color.Green },
            { ModuleStatus.Failed, Color.Red }
        };

        private readonly Dictionary<ModuleStatus, string> _moduleStatusTexts = new Dictionary<ModuleStatus, string>
        {
            { ModuleStatus.None, "无" },
            { ModuleStatus.Ready, "就绪" },
            { ModuleStatus.Processing, "处理中" },
            { ModuleStatus.Success, "成功" },
            { ModuleStatus.Failed, "失败" }
        };

        // 存储选择的资料目录路径
        private string selectedDirectoryPath;

        // 存储模块名称到状态标签的映射
        private readonly Dictionary<string, Label> _moduleStatusLabels;

        // For checkbox state persistence
        private readonly string _settingsFilePath;
        private readonly List<CheckBox> _moduleCheckBoxes;

        // 认证相关
        private IAuthService _authService;
        private AuthenticationStatusInfo _currentAuthStatus;
        private bool _isLoginDialogShowing = false;
        private bool _isAuthenticated = false;
        private bool _authenticationRequired = true;

        // 服务器连接相关
        private IServerConnectionService _connectionService;
        private ServerConnectionInfo _currentConnectionStatus;
        private bool _isConnectionCheckInProgress;

        public MainForm()
        {
            InitializeComponent();
            generateReportButton.Click += (s, e) =>
            {
                if (CheckConnectionBeforeOperation("报告生成"))
                {
                    GenerateReportRequested?.Invoke(this, EventArgs.Empty);
                }
            };

            // 初始化连接服务
            _connectionService = new ServerConnectionService(RESServerConfigService.Instance);
            _connectionService.ConnectionStatusChanged += OnConnectionStatusChanged;

            // 初始化认证服务
            _authService = new AuthService(RESServerConfigService.Instance);
            _authService.AuthenticationStatusChanged += OnAuthenticationStatusChanged;

            // 初始化 HTTP 客户端工厂
            HttpClientFactoryInstance.Initialize(RESServerConfigService.Instance, _authService);

            // 初始化连接状态UI
            InitializeConnectionStatusUI();

            // 添加集成报告功能暂时禁用的提示
            AddLog("=== 重要通知 ===", LogMessageType.Warning);
            AddLog("集成报告功能暂时禁用，仅支持单独模块生成", LogMessageType.Warning);
            AddLog("个别模块文件将保存到输出目录中", LogMessageType.Info);
            AddLog("集成报告功能将在后续版本中恢复", LogMessageType.Info);
            AddLog("===============", LogMessageType.Warning);

            // 绑定封面设置按钮事件（button2对应封面的设置按钮）
            button2.Click += CoverSettingsButton_Click;

            // 绑定作业声明设置按钮事件（button4对应作业声明的设置按钮）
            button4.Click += WorkStatementSettingsButton_Click;

            // 绑定作业、质量检查与验收设置按钮事件（button5对应作业、质量检查与验收的设置按钮）
            button5.Click += WorkQualitySettingsButton_Click;

            // 绑定项目基本信息设置按钮事件（button6对应项目基本信息的设置按钮）
            button6.Click += ProjectInfoSettingsButton_Click;

            // 绑定经主管部门批准的相关证照设置按钮事件（button8对应经主管部门批准的相关证照的设置按钮）
            button8.Click += CertificatesSettingsButton_Click;

            // 绑定地下室人防区域说明设置按钮事件（button9对应地下室人防区域说明的设置按钮）
            button9.Click += BasementDefenseSettingsButton_Click;

            // 绑定楼栋基本信息设置按钮事件（button7对应楼栋基本信息的设置按钮）
            button7.Click += BuildingInfoSettingsButton_Click;

            // 绑定房产面积汇总表设置按钮事件（button12对应房产面积汇总表的设置按钮）
            button12.Click += EstateAreaSummarySettingsButton_Click;
            
            // 设置富文本框属性
            richTextBoxLog.Font = new Font("Consolas", 9F, FontStyle.Regular);
            richTextBoxLog.BackColor = Color.Black;
            richTextBoxLog.ForeColor = Color.LightGray;
            richTextBoxLog.ReadOnly = true;
            
            _moduleCheckBoxes = new List<CheckBox>
            {
                checkBox1, checkBox2, checkBox3, checkBox4, checkBox5, checkBox6, checkBox7,
                checkBox8, checkBox9, checkBox10, checkBox11, checkBox12, checkBox13
            };
            
            // 初始化模块状态标签映射
            _moduleStatusLabels = new Dictionary<string, Label>
            {
                { checkBox1.Text, label1 },
                { checkBox2.Text, label2 },
                { checkBox3.Text, label3 },
                { checkBox4.Text, label4 },
                { checkBox5.Text, label5 },
                { checkBox6.Text, label6 },
                { checkBox7.Text, label7 },
                { checkBox8.Text, label8 },
                { checkBox9.Text, label9 },
                { checkBox10.Text, label10 },
                { checkBox11.Text, label11 },
                { checkBox12.Text, label12 },
                { checkBox13.Text, label15 }
            };

            // 初始化所有模块状态为"无"
            foreach (var pair in _moduleStatusLabels)
            {
                UpdateModuleStatus(pair.Key, ModuleStatus.None);
            }

            // Load checkbox states
            _settingsFilePath = Path.Combine(Application.LocalUserAppDataPath, "checkbox_states.ini");
            LoadCheckboxStates();
            this.FormClosing += MainForm_FormClosing;
        }

        private void MainForm_FormClosing(object sender, FormClosingEventArgs e)
        {
            SaveCheckboxStates();
        }

        private void LoadCheckboxStates()
        {
            if (!File.Exists(_settingsFilePath))
            {
                return; // Use default checked states from designer on first run
            }

            try
            {
                var states = File.ReadAllLines(_settingsFilePath)
                    .Select(line => line.Split('='))
                    .Where(parts => parts.Length == 2)
                    .ToDictionary(parts => parts[0], parts => bool.Parse(parts[1]));

                foreach (var checkBox in _moduleCheckBoxes)
                {
                    if (states.TryGetValue(checkBox.Name, out bool isChecked))
                    {
                        checkBox.Checked = isChecked;
                    }
                }
            }
            catch (Exception ex)
            {
                AddLog($"加载复选框状态失败: {ex.Message}", LogMessageType.Error);
            }
        }

        private void SaveCheckboxStates()
        {
            try
            {
                var settings = new StringBuilder();
                foreach (var checkBox in _moduleCheckBoxes)
                {
                    settings.AppendLine($"{checkBox.Name}={checkBox.Checked}");
                }

                string directory = Path.GetDirectoryName(_settingsFilePath);
                if (!Directory.Exists(directory))
                {
                    Directory.CreateDirectory(directory);
                }

                File.WriteAllText(_settingsFilePath, settings.ToString());
            }
            catch (Exception ex)
            {
                AddLog($"保存复选框状态失败: {ex.Message}", LogMessageType.Error);
            }
        }

        /// <summary>
        /// 更新模块状态
        /// </summary>
        /// <param name="moduleName">模块名称</param>
        /// <param name="status">状态</param>
        public void UpdateModuleStatus(string moduleName, ModuleStatus status)
        {
            if (InvokeRequired)
            {
                Invoke(new Action<string, ModuleStatus>(UpdateModuleStatus), moduleName, status);
                return;
            }

            if (_moduleStatusLabels.TryGetValue(moduleName, out Label label))
            {
                label.Text = _moduleStatusTexts[status];
                label.ForeColor = _moduleStatusColors[status];

                // 添加日志
                string logMessage = $"模块 [{moduleName}] 状态: {_moduleStatusTexts[status]}";
                LogMessageType logType = LogMessageType.Normal;
                
                switch (status)
                {
                    case ModuleStatus.Success:
                        logType = LogMessageType.Success;
                        break;
                    case ModuleStatus.Failed:
                        logType = LogMessageType.Error;
                        break;
                    case ModuleStatus.Ready:
                    case ModuleStatus.Processing:
                        logType = LogMessageType.Info;
                        break;
                }
                
                AddLog(logMessage, logType);
            }
        }

        /// <summary>
        /// 更新所有模块状态
        /// </summary>
        /// <param name="status">状态</param>
        public void UpdateAllModulesStatus(ModuleStatus status)
        {
            if (InvokeRequired)
            {
                Invoke(new Action<ModuleStatus>(UpdateAllModulesStatus), status);
                return;
            }

            foreach (var pair in _moduleStatusLabels)
            {
                UpdateModuleStatus(pair.Key, status);
            }
        }

        /// <summary>
        /// 重置所有模块状态为"无"
        /// </summary>
        public void ResetAllModulesStatus()
        {
            UpdateAllModulesStatus(ModuleStatus.None);
        }

        /// <summary>
        /// 添加日志消息
        /// </summary>
        /// <param name="message">消息内容</param>
        /// <param name="type">消息类型</param>
        /// <param name="indent">缩进级别</param>
        private void AddLog(string message, LogMessageType type = LogMessageType.Normal, int indent = 0)
        {
            if (InvokeRequired)
            {
                Invoke(new Action<string, LogMessageType, int>(AddLog), message, type, indent);
                return;
            }

            // 构造缩进
            string indentation = indent > 0 ? new string(' ', indent * 4) : string.Empty;
            
            // 构造前缀
            string prefix = string.Empty;
            switch (type)
            {
                case LogMessageType.Success: prefix = "✓ "; break;
                case LogMessageType.Warning: prefix = "⚠ "; break;
                case LogMessageType.Error: prefix = "✗ "; break;
                case LogMessageType.Info: prefix = "ℹ "; break;
                case LogMessageType.Directory: prefix = "📂 "; break;
                case LogMessageType.Template: prefix = "📄 "; break;
                case LogMessageType.Progress: prefix = "► "; break;
            }

            // 构造完整信息
            string timestamp = DateTime.Now.ToString("HH:mm:ss");
            string fullMessage = $"[{timestamp}] {indentation}{prefix}{message}";

            // 添加文本并设置颜色
            richTextBoxLog.SelectionStart = richTextBoxLog.TextLength;
            richTextBoxLog.SelectionLength = 0;
            richTextBoxLog.SelectionColor = _logColors[type];
            richTextBoxLog.AppendText(fullMessage + Environment.NewLine);
            
            // 滚动到最后
            richTextBoxLog.ScrollToCaret();
        }

        /// <summary>
        /// 向日志窗口添加普通日志信息
        /// </summary>
        /// <param name="message">日志信息</param>
        private void LogInfo(string message)
        {
            AddLog(message);
        }

        private void button1_Click(object sender, EventArgs e)
        {
            // 配置OpenFileDialog实现选择目录功能
            openFileDialog1.ValidateNames = false;
            openFileDialog1.CheckFileExists = false;
            openFileDialog1.CheckPathExists = true;
            openFileDialog1.FileName = "选择此文件夹";
            openFileDialog1.Title = "请选择数据目录";

            if (openFileDialog1.ShowDialog() == DialogResult.OK)
            {
                // 从选择的文件路径获取目录路径
                selectedDirectoryPath = Path.GetDirectoryName(openFileDialog1.FileName);
                textBox2.Text = selectedDirectoryPath;
                AddLog($"已选择资料目录: {selectedDirectoryPath}", LogMessageType.Directory);

                // 更新所有模块状态为就绪
                UpdateAllModulesStatus(ModuleStatus.Ready);
            }
        }

        /// <summary>
        /// 封面设置按钮点击事件
        /// </summary>
        /// <param name="sender">事件发送者</param>
        /// <param name="e">事件参数</param>
        private void CoverSettingsButton_Click(object sender, EventArgs e)
        {
            AddLog("打开封面参数设置窗口", LogMessageType.Info);

            var result = ModuleSettingsService.OpenCoverSettings(this);

            if (result.Success)
            {
                AddLog(result.Message, LogMessageType.Success);

                if (result.IsParametersValid)
                {
                    AddLog("封面参数验证通过，模块已就绪", LogMessageType.Success);
                    UpdateModuleStatus("封面", ModuleStatus.Ready);
                }
                else
                {
                    AddLog($"封面参数验证失败: {string.Join(", ", result.ValidationErrors)}", LogMessageType.Warning);
                    UpdateModuleStatus("封面", ModuleStatus.None);
                }
            }
            else if (result.Cancelled)
            {
                AddLog(result.Message, LogMessageType.Info);
            }
            else
            {
                AddLog(result.Message, LogMessageType.Error);
                if (result.Exception != null)
                {
                    ShowError($"打开封面设置窗口时发生错误：{result.Exception.Message}");
                }
                else
                {
                    ShowError(result.Message);
                }
            }
        }

        /// <summary>
        /// 作业声明设置按钮点击事件
        /// </summary>
        /// <param name="sender">事件发送者</param>
        /// <param name="e">事件参数</param>
        private void WorkStatementSettingsButton_Click(object sender, EventArgs e)
        {
            AddLog("打开作业声明参数设置窗口", LogMessageType.Info);

            var result = ModuleSettingsService.OpenWorkStatementSettings(this);

            if (result.Success)
            {
                AddLog(result.Message, LogMessageType.Success);

                if (result.IsParametersValid)
                {
                    AddLog("作业声明参数验证通过，模块已就绪", LogMessageType.Success);
                    UpdateModuleStatus("作业声明", ModuleStatus.Ready);
                }
                else
                {
                    AddLog($"作业声明参数验证失败: {string.Join(", ", result.ValidationErrors)}", LogMessageType.Warning);
                    UpdateModuleStatus("作业声明", ModuleStatus.None);
                }
            }
            else if (result.Cancelled)
            {
                AddLog(result.Message, LogMessageType.Info);
            }
            else
            {
                AddLog(result.Message, LogMessageType.Error);
                if (result.Exception != null)
                {
                    ShowError($"打开作业声明设置窗口时发生错误：{result.Exception.Message}");
                }
                else
                {
                    ShowError(result.Message);
                }
            }
        }

        /// <summary>
        /// 作业、质量检查与验收设置按钮点击事件
        /// </summary>
        /// <param name="sender">事件发送者</param>
        /// <param name="e">事件参数</param>
        private void WorkQualitySettingsButton_Click(object sender, EventArgs e)
        {
            AddLog("打开作业、质量检查与验收参数设置窗口", LogMessageType.Info);

            var result = ModuleSettingsService.OpenWorkQualitySettings(this);

            if (result.Success)
            {
                AddLog(result.Message, LogMessageType.Success);

                if (result.IsParametersValid)
                {
                    AddLog("作业、质量检查与验收参数验证通过，模块已就绪", LogMessageType.Success);
                    UpdateModuleStatus("作业、质量检查与验收", ModuleStatus.Ready);
                }
                else
                {
                    AddLog($"作业、质量检查与验收参数验证失败: {string.Join(", ", result.ValidationErrors)}", LogMessageType.Warning);
                    UpdateModuleStatus("作业、质量检查与验收", ModuleStatus.None);
                }
            }
            else if (result.Cancelled)
            {
                AddLog(result.Message, LogMessageType.Info);
            }
            else
            {
                AddLog(result.Message, LogMessageType.Error);
                if (result.Exception != null)
                {
                    ShowError($"打开作业、质量检查与验收设置窗口时发生错误：{result.Exception.Message}");
                }
                else
                {
                    ShowError(result.Message);
                }
            }
        }

        /// <summary>
        /// 项目基本信息设置按钮点击事件
        /// </summary>
        /// <param name="sender">事件发送者</param>
        /// <param name="e">事件参数</param>
        private void ProjectInfoSettingsButton_Click(object sender, EventArgs e)
        {
            AddLog("打开项目基本信息参数设置窗口", LogMessageType.Info);

            var result = ModuleSettingsService.OpenProjectInfoSettings(this);

            if (result.Success)
            {
                AddLog(result.Message, LogMessageType.Success);

                if (result.IsParametersValid)
                {
                    AddLog("项目基本信息参数验证通过，模块已就绪", LogMessageType.Success);
                    UpdateModuleStatus("项目基本信息", ModuleStatus.Ready);
                }
                else
                {
                    AddLog($"项目基本信息参数验证失败: {string.Join(", ", result.ValidationErrors)}", LogMessageType.Warning);
                    UpdateModuleStatus("项目基本信息", ModuleStatus.None);
                }
            }
            else if (result.Cancelled)
            {
                AddLog(result.Message, LogMessageType.Info);
            }
            else
            {
                AddLog(result.Message, LogMessageType.Error);
                if (result.Exception != null)
                {
                    ShowError($"打开项目基本信息设置窗口时发生错误：{result.Exception.Message}");
                }
                else
                {
                    ShowError(result.Message);
                }
            }
        }

        /// <summary>
        /// 地下室人防区域说明设置按钮点击事件
        /// </summary>
        /// <param name="sender">事件发送者</param>
        /// <param name="e">事件参数</param>
        private void BasementDefenseSettingsButton_Click(object sender, EventArgs e)
        {
            AddLog("打开地下室人防区域说明参数设置窗口", LogMessageType.Info);

            var result = ModuleSettingsService.OpenBasementDefenseSettings(this);

            if (result.Success)
            {
                AddLog(result.Message, LogMessageType.Success);

                if (result.IsParametersValid)
                {
                    AddLog("地下室人防区域说明参数验证通过，模块已就绪", LogMessageType.Success);
                    UpdateModuleStatus("地下室人防区域说明", ModuleStatus.Ready);
                }
                else
                {
                    AddLog($"地下室人防区域说明参数验证失败: {string.Join(", ", result.ValidationErrors)}", LogMessageType.Warning);
                    UpdateModuleStatus("地下室人防区域说明", ModuleStatus.None);
                }
            }
            else if (result.Cancelled)
            {
                AddLog(result.Message, LogMessageType.Info);
            }
            else
            {
                AddLog(result.Message, LogMessageType.Error);
                if (result.Exception != null)
                {
                    ShowError($"打开地下室人防区域说明设置窗口时发生错误：{result.Exception.Message}");
                }
                else
                {
                    ShowError(result.Message);
                }
            }
        }

        /// <summary>
        /// 经主管部门批准的相关证照设置按钮点击事件
        /// </summary>
        /// <param name="sender">事件发送者</param>
        /// <param name="e">事件参数</param>
        private void CertificatesSettingsButton_Click(object sender, EventArgs e)
        {
            AddLog("打开经主管部门批准的相关证照参数设置窗口", LogMessageType.Info);

            var result = ModuleSettingsService.OpenCertificatesSettings(this);

            if (result.Success)
            {
                AddLog(result.Message, LogMessageType.Success);

                if (result.IsParametersValid)
                {
                    AddLog("经主管部门批准的相关证照参数验证通过，模块已就绪", LogMessageType.Success);
                    UpdateModuleStatus("经主管部门批准的相关证照", ModuleStatus.Ready);
                }
                else
                {
                    AddLog($"经主管部门批准的相关证照参数验证失败: {string.Join(", ", result.ValidationErrors)}", LogMessageType.Warning);
                    UpdateModuleStatus("经主管部门批准的相关证照", ModuleStatus.None);
                }
            }
            else if (result.Cancelled)
            {
                AddLog(result.Message, LogMessageType.Info);
            }
            else
            {
                AddLog(result.Message, LogMessageType.Error);
                if (result.Exception != null)
                {
                    ShowError($"打开经主管部门批准的相关证照设置窗口时发生错误：{result.Exception.Message}");
                }
                else
                {
                    ShowError(result.Message);
                }
            }
        }

        /// <summary>
        /// 楼栋基本信息设置按钮点击事件
        /// </summary>
        /// <param name="sender">事件发送者</param>
        /// <param name="e">事件参数</param>
        private void BuildingInfoSettingsButton_Click(object sender, EventArgs e)
        {
            AddLog("打开楼栋基本信息参数设置窗口", LogMessageType.Info);

            var result = ModuleSettingsService.OpenBuildingInfoSettings(this);

            if (result.Success)
            {
                AddLog(result.Message, LogMessageType.Success);

                if (result.IsParametersValid)
                {
                    AddLog("楼栋基本信息参数验证通过，模块已就绪", LogMessageType.Success);
                    UpdateModuleStatus("楼栋基本信息", ModuleStatus.Ready);
                }
                else
                {
                    AddLog($"楼栋基本信息参数验证失败: {string.Join(", ", result.ValidationErrors)}", LogMessageType.Warning);
                    UpdateModuleStatus("楼栋基本信息", ModuleStatus.None);
                }
            }
            else if (result.Cancelled)
            {
                AddLog(result.Message, LogMessageType.Info);
            }
            else
            {
                AddLog(result.Message, LogMessageType.Error);
                if (result.Exception != null)
                {
                    ShowError($"打开楼栋基本信息设置窗口时发生错误：{result.Exception.Message}");
                }
                else
                {
                    ShowError(result.Message);
                }
            }
        }

        /// <summary>
        /// 房产面积汇总表设置按钮点击事件
        /// </summary>
        /// <param name="sender">事件发送者</param>
        /// <param name="e">事件参数</param>
        private void EstateAreaSummarySettingsButton_Click(object sender, EventArgs e)
        {
            AddLog("打开房产面积汇总表参数设置窗口", LogMessageType.Info);

            var result = ModuleSettingsService.OpenEstateAreaSummarySettings(this);

            if (result.Success)
            {
                AddLog(result.Message, LogMessageType.Success);

                if (result.IsParametersValid)
                {
                    AddLog("房产面积汇总表参数验证通过，模块已就绪", LogMessageType.Success);
                    UpdateModuleStatus("房产面积汇总表", ModuleStatus.Ready);
                }
                else
                {
                    AddLog($"房产面积汇总表参数验证失败: {string.Join(", ", result.ValidationErrors)}", LogMessageType.Warning);
                    UpdateModuleStatus("房产面积汇总表", ModuleStatus.None);
                }
            }
            else if (result.Cancelled)
            {
                AddLog(result.Message, LogMessageType.Info);
            }
            else
            {
                AddLog(result.Message, LogMessageType.Error);
                if (result.Exception != null)
                {
                    ShowError($"打开房产面积汇总表设置窗口时发生错误：{result.Exception.Message}");
                }
                else
                {
                    ShowError(result.Message);
                }
            }
        }

        #region IView Implementation

        public void ShowMessage(string message)
        {
            MessageBox.Show(message, "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        public void ShowError(string message)
        {
            MessageBox.Show(message, "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            AddLog(message, LogMessageType.Error);
        }

        // 以下是旧方法，保留以兼容旧代码
        public void ShowMessage(string message, string title)
        {
            MessageBox.Show(message, title);
        }

        public void ShowError(string message, string title)
        {
            MessageBox.Show(message, title, MessageBoxButtons.OK, MessageBoxIcon.Error);
            AddLog(message, LogMessageType.Error);
        }

        #endregion

        #region IMainView Implementation

        // 实现Invoke方法来满足IMainView接口要求
        void IMainView.Invoke(Action method)
        {
            if (InvokeRequired)
            {
                base.Invoke(method);
            }
            else
            {
                method();
            }
        }

        // 废弃的事件，保留以兼容旧代码
        public event EventHandler ReportGenerateRequest;
        public event EventHandler ManageTemplatesRequest;
        public event EventHandler TestFloorPlanRequest;

        // 主要事件
        public event EventHandler GenerateReportRequested;
        public event EventHandler TestFloorPlanRequested;
        public event EventHandler ManageTemplatesRequested;

        /// <summary>
        /// 设置标题
        /// </summary>
        /// <param name="title">标题文本</param>
        public void SetTitle(string title)
        {
            if (InvokeRequired)
            {
                Invoke(new Action<string>(SetTitle), title);
                return;
            }
            this.Text = title;
        }

        /// <summary>
        /// 更新目录信息
        /// </summary>
        /// <param name="directoryInfo">目录信息字典</param>
        public void UpdateDirectoryInfo(Dictionary<string, string> directoryInfo)
        {
            if (directoryInfo != null)
            {
                AddLog("目录信息:", LogMessageType.Info);
                foreach (var item in directoryInfo)
                {
                    AddLog($"{item.Key} = {item.Value}", LogMessageType.Directory, 1);
                }
            }
        }

        /// <summary>
        /// 更新模板列表
        /// </summary>
        /// <param name="templates">模板列表</param>
        public void UpdateTemplateList(List<string> templates)
        {
            if (templates != null)
            {
                AddLog($"已加载 {templates.Count} 个模板", LogMessageType.Info);
                foreach (var template in templates)
                {
                    AddLog(template, LogMessageType.Template, 1);
                }
            }
        }

        /// <summary>
        /// 获取选中的模块
        /// </summary>
        /// <returns>选中的模块名称列表</returns>
        public List<string> GetSelectedModules()
        {
            return _moduleCheckBoxes.Where(cb => cb.Checked).Select(cb => cb.Text).ToList();
        }

        /// <summary>
        /// 获取选择的数据文件夹
        /// </summary>
        /// <returns>数据文件夹路径</returns>
        public string GetSelectedDataFolder()
        {
            return selectedDirectoryPath;
        }

        /// <summary>
        /// 显示进度条
        /// </summary>
        /// <param name="visible">是否显示</param>
        public void ShowProgressBar(bool visible)
        {
            if (InvokeRequired)
            {
                Invoke(new Action<bool>(ShowProgressBar), visible);
                return;
            }

            if (visible)
            {
                generateReportButton.Enabled = false;
                button1.Enabled = false;
                
                // 将所有选中的模块状态设置为"处理中"
                foreach (var moduleName in GetSelectedModules())
                {
                    UpdateModuleStatus(moduleName, ModuleStatus.Processing);
                }
            }
            else
            {
                // 只有在服务器连接正常时才启用按钮
                var functionsEnabled = AreFunctionsEnabled();
                generateReportButton.Enabled = functionsEnabled;
                button1.Enabled = functionsEnabled;
            }
        }

        /// <summary>
        /// 更新进度
        /// </summary>
        /// <param name="percent">百分比</param>
        /// <param name="status">状态信息</param>
        public void UpdateProgress(int percent, string status)
        {
            if (InvokeRequired)
            {
                Invoke(new Action<int, string>(UpdateProgress), percent, status);
                return;
            }

            // 默认日志类型
            LogMessageType type = LogMessageType.Progress;

            // 根据消息前缀识别日志类型和模块状态
            string message = status;
            string moduleName = null;

            if (status.StartsWith("[MODULE_SUCCESS]"))
            {
                type = LogMessageType.Success;
                var parts = status.Substring(16).Split(':');
                if (parts.Length >= 2)
                {
                    moduleName = parts[0];
                    message = parts[1];
                    UpdateModuleStatus(moduleName, ModuleStatus.Success);
                }
                else
                {
                    message = status.Substring(16);
                }
            }
            else if (status.StartsWith("[MODULE_FAILED]"))
            {
                type = LogMessageType.Error;
                var parts = status.Substring(15).Split(':');
                if (parts.Length >= 2)
                {
                    moduleName = parts[0];
                    message = parts[1];
                    UpdateModuleStatus(moduleName, ModuleStatus.Failed);
                }
                else
                {
                    message = status.Substring(15);
                }
            }
            else if (status.StartsWith("[MODULE_SKIPPED]"))
            {
                type = LogMessageType.Warning;
                var parts = status.Substring(16).Split(':');
                if (parts.Length >= 2)
                {
                    moduleName = parts[0];
                    message = parts[1];
                    UpdateModuleStatus(moduleName, ModuleStatus.None);
                }
                else
                {
                    message = status.Substring(16);
                }
            }
            else if (status.StartsWith("[INFO]"))
            {
                type = LogMessageType.Info;
                message = status.Substring(6);
            }
            else if (status.StartsWith("[SUCCESS]"))
            {
                type = LogMessageType.Success;
                message = status.Substring(9);
            }
            else if (status.StartsWith("[WARNING]"))
            {
                type = LogMessageType.Warning;
                message = status.Substring(9);
            }
            else if (status.StartsWith("[ERROR]"))
            {
                type = LogMessageType.Error;
                message = status.Substring(7);
            }
            else if (percent == 100)
            {
                // 兼容未添加前缀的旧代码，100%进度通常是成功
                type = LogMessageType.Success;
            }

            // 添加百分比到消息中
            AddLog($"{message} ({percent}%)", type);
        }

        /// <summary>
        /// 更新特定模块的处理结果
        /// </summary>
        /// <param name="moduleName">模块名称</param>
        /// <param name="success">是否成功</param>
        /// <param name="message">消息</param>
        public void UpdateModuleResult(string moduleName, bool success, string message = null)
        {
            if (success)
            {
                UpdateModuleStatus(moduleName, ModuleStatus.Success);
                if (!string.IsNullOrEmpty(message))
                {
                    AddLog($"模块 [{moduleName}] 成功: {message}", LogMessageType.Success);
                }
            }
            else
            {
                UpdateModuleStatus(moduleName, ModuleStatus.Failed);
                if (!string.IsNullOrEmpty(message))
                {
                    AddLog($"模块 [{moduleName}] 失败: {message}", LogMessageType.Error);
                }
            }
        }

        // 兼容旧方法
        public void ShowLoading(bool isLoading, string message = "")
        {
            if (isLoading)
            {
                ShowProgressBar(true);
                AddLog(message, LogMessageType.Info);
            }
            else
            {
                ShowProgressBar(false);
            }
        }

        // 兼容旧方法
        public void UpdateReportProgress(int progress, string message)
        {
            UpdateProgress(progress, message);
        }

        #endregion

        #region 认证相关方法

        /// <summary>
        /// 窗体加载时首先检查服务器连接状态
        /// </summary>
        protected override async void OnLoad(EventArgs e)
        {
            base.OnLoad(e);

            // 首先检查服务器连接状态
            await CheckServerConnection();
        }

        /// <summary>
        /// 检查认证状态
        /// </summary>
        private async Task CheckAuthenticationStatus()
        {
            try
            {
                // 先获取服务器状态，不触发事件
                var status = await _authService.GetServerStatusAsync(triggerEvent: false);

                // 只有在服务器启用认证时才打印检查日志
                if (status.IsAuthenticationEnabled)
                {
                    AddLog("正在检查服务器认证状态...", LogMessageType.Info);
                }

                HandleAuthenticationStatus(status);
            }
            catch (Exception ex)
            {
                AddLog($"检查认证状态失败：{ex.Message}", LogMessageType.Error);
            }
        }

        /// <summary>
        /// 处理认证状态变更
        /// </summary>
        private void OnAuthenticationStatusChanged(object sender, AuthenticationStatusInfo status)
        {
            if (InvokeRequired)
            {
                Invoke(new Action(() => HandleAuthenticationStatus(status)));
            }
            else
            {
                HandleAuthenticationStatus(status);
            }
        }

        /// <summary>
        /// 处理认证状态
        /// </summary>
        private void HandleAuthenticationStatus(AuthenticationStatusInfo status)
        {
            _currentAuthStatus = status;

            switch (status.Status)
            {
                case AuthenticationStatus.Disabled:
                    // 移除未启用认证时的日志输出，避免重复打印
                    // AddLog("服务器未启用认证功能，可直接使用", LogMessageType.Info);
                    _isAuthenticated = true;
                    _authenticationRequired = false;
                    EnableApplicationFunctions(true);
                    break;

                case AuthenticationStatus.RequiredButNotLoggedIn:
                    _isAuthenticated = false;
                    _authenticationRequired = true;
                    EnableApplicationFunctions(false);

                    // 只有在没有显示登录对话框时才显示
                    if (!_isLoginDialogShowing)
                    {
                        AddLog("服务器启用了用户认证，正在显示登录界面", LogMessageType.Info);
                        // 当服务器启用认证时，直接显示登录界面，不执行自动登录
                        ShowLoginDialog();
                    }
                    else
                    {
                        AddLog("登录对话框已在显示中，跳过重复显示", LogMessageType.Info);
                    }
                    break;

                case AuthenticationStatus.LoggedIn:
                    AddLog($"用户认证成功：{status.User?.Username}", LogMessageType.Success);
                    _isAuthenticated = true;
                    _authenticationRequired = true;
                    EnableApplicationFunctions(true);
                    break;

                case AuthenticationStatus.TokenExpired:
                    AddLog("登录会话已过期，正在显示重新登录界面", LogMessageType.Warning);
                    _isAuthenticated = false;
                    _authenticationRequired = true;
                    EnableApplicationFunctions(false);

                    // 只有在没有显示登录对话框时才显示
                    if (!_isLoginDialogShowing)
                    {
                        ShowLoginDialog();
                    }
                    else
                    {
                        AddLog("登录对话框已在显示中，跳过重复显示", LogMessageType.Info);
                    }
                    break;

                case AuthenticationStatus.ServerConnectionFailed:
                    AddLog($"无法连接到服务器：{status.Message}", LogMessageType.Error);
                    _isAuthenticated = false;
                    EnableApplicationFunctions(false);
                    break;

                default:
                    AddLog($"认证状态：{status.Message}", LogMessageType.Info);
                    break;
            }
        }

        /// <summary>
        /// 显示登录对话框
        /// </summary>
        private void ShowLoginDialog()
        {
            // 防止多个登录对话框同时显示
            if (_isLoginDialogShowing)
            {
                AddLog("登录对话框已在显示中，跳过重复请求", LogMessageType.Info);
                return;
            }

            try
            {
                _isLoginDialogShowing = true;
                AddLog("显示登录对话框", LogMessageType.Info);

                using (var loginForm = new LoginForm(_authService))
                {
                    var result = loginForm.ShowDialog(this);

                    if (result == DialogResult.OK)
                    {
                        AddLog("登录成功", LogMessageType.Success);
                        _isAuthenticated = true;

                        // 登录成功后，重新检查认证状态以更新UI
                        _ = Task.Run(async () =>
                        {
                            try
                            {
                                await Task.Delay(500); // 短暂延迟确保登录完成
                                if (!IsDisposed && IsHandleCreated)
                                {
                                    Invoke(new Action(async () => await CheckAuthenticationStatus()));
                                }
                            }
                            catch (Exception ex)
                            {
                                if (!IsDisposed && IsHandleCreated)
                                {
                                    Invoke(new Action(() => AddLog($"重新检查认证状态失败：{ex.Message}", LogMessageType.Error)));
                                }
                            }
                        });

                        // 启用应用程序功能
                        EnableApplicationFunctions(true);
                    }
                    else if (result == DialogResult.Ignore)
                    {
                        // 服务器未启用认证功能
                        AddLog("服务器未启用认证功能", LogMessageType.Info);
                        _isAuthenticated = true;
                        _authenticationRequired = false;
                        EnableApplicationFunctions(true);
                    }
                    else
                    {
                        AddLog("用户取消登录", LogMessageType.Warning);
                        _isAuthenticated = false;

                        // 处理登录取消的情况
                        HandleLoginCancelled();
                    }
                }
            }
            catch (Exception ex)
            {
                AddLog($"显示登录对话框失败：{ex.Message}", LogMessageType.Error);
            }
            finally
            {
                _isLoginDialogShowing = false;
            }
        }

        /// <summary>
        /// 处理登录取消的情况
        /// </summary>
        private void HandleLoginCancelled()
        {
            // 移除认证取消的日志输出，避免在禁用验证功能时打印禁用日志
            // AddLog("=== 认证被取消 ===", LogMessageType.Warning);
            // AddLog("应用程序功能已被禁用，需要认证后才能使用", LogMessageType.Warning);

            // 禁用所有业务功能
            EnableApplicationFunctions(false);

            // 显示认证状态信息 - 已禁用以避免打印禁用日志
            // ShowAuthenticationRequiredMessage();

            // 提供用户选择
            ShowLoginCancelledOptions();
        }

        /// <summary>
        /// 启用或禁用应用程序功能（基于认证状态）
        /// </summary>
        /// <param name="enabled">是否启用功能</param>
        private void EnableApplicationFunctions(bool enabled)
        {
            // 调用现有的功能控制方法，但不显示连接相关的日志
            // 因为这是认证状态变更，不是连接问题
            EnableOrDisableFunctions(enabled, false);

            // 更新认证状态显示
            UpdateAuthenticationStatusDisplay(enabled);
        }

        /// <summary>
        /// 显示认证要求消息
        /// </summary>
        private void ShowAuthenticationRequiredMessage()
        {
            AddLog("=== 认证要求 ===", LogMessageType.Info);
            AddLog("此应用程序需要用户认证才能访问以下功能：", LogMessageType.Info);
            AddLog("• 报告生成", LogMessageType.Info);
            AddLog("• 模块配置", LogMessageType.Info);
            AddLog("• 数据处理", LogMessageType.Info);
            AddLog("• 文件操作", LogMessageType.Info);
            AddLog("===============", LogMessageType.Info);
        }

        /// <summary>
        /// 显示登录取消后的选项
        /// </summary>
        private void ShowLoginCancelledOptions()
        {
            var result = MessageBox.Show(
                "登录已取消，应用程序的主要功能已被禁用。\n\n" +
                "您可以选择：\n" +
                "• 是 - 重新尝试登录\n" +
                "• 否 - 继续使用受限模式（仅基本功能）\n" +
                "• 取消 - 退出应用程序",
                "认证取消",
                MessageBoxButtons.YesNoCancel,
                MessageBoxIcon.Question,
                MessageBoxDefaultButton.Button1
            );

            switch (result)
            {
                case DialogResult.Yes:
                    AddLog("用户选择重新登录", LogMessageType.Info);
                    // 延迟一点时间再显示登录对话框，避免立即弹出
                    _ = Task.Delay(1000).ContinueWith(_ =>
                    {
                        if (!IsDisposed && IsHandleCreated)
                        {
                            Invoke(new Action(() => ShowLoginDialog()));
                        }
                    });
                    break;

                case DialogResult.No:
                    AddLog("用户选择继续使用受限模式", LogMessageType.Info);
                    ShowRestrictedModeInformation();
                    break;

                case DialogResult.Cancel:
                    AddLog("用户选择退出程序", LogMessageType.Warning);
                    var confirmResult = MessageBox.Show(
                        "确定要退出程序吗？",
                        "确认退出",
                        MessageBoxButtons.YesNo,
                        MessageBoxIcon.Question,
                        MessageBoxDefaultButton.Button2
                    );

                    if (confirmResult == DialogResult.Yes)
                    {
                        Application.Exit();
                    }
                    else
                    {
                        ShowRestrictedModeInformation();
                    }
                    break;
            }
        }

        /// <summary>
        /// 显示受限模式信息
        /// </summary>
        private void ShowRestrictedModeInformation()
        {
            AddLog("=== 受限模式 ===", LogMessageType.Info);
            AddLog("当前运行在受限模式下，可用功能：", LogMessageType.Info);
            AddLog("• 查看应用程序信息", LogMessageType.Info);
            AddLog("• 配置服务器连接", LogMessageType.Info);
            AddLog("• 查看帮助文档", LogMessageType.Info);
            AddLog("• 重新尝试登录", LogMessageType.Info);
            AddLog("===============", LogMessageType.Info);

            // 在受限模式下，可以添加一个"重新登录"按钮
            AddRestrictedModeControls();
        }

        /// <summary>
        /// 添加受限模式下的控件
        /// </summary>
        private void AddRestrictedModeControls()
        {
            // 这里可以添加一个"重新登录"按钮或其他受限模式下的控件
            // 为了简单起见，我们在日志中提供说明
            AddLog("提示：您可以随时通过菜单或快捷键重新尝试登录", LogMessageType.Info);
        }

        /// <summary>
        /// 更新认证状态显示
        /// </summary>
        /// <param name="authenticated">是否已认证</param>
        private void UpdateAuthenticationStatusDisplay(bool authenticated)
        {
            var baseTitle = "房产测绘报告生成系统";

            if (!authenticated && _authenticationRequired)
            {
                this.Text = $"{baseTitle} - [需要认证]";
                // 显示重新登录按钮
                retryLoginButton.Visible = true;
            }
            else if (authenticated)
            {
                this.Text = baseTitle;
                // 隐藏重新登录按钮
                retryLoginButton.Visible = false;
            }
            else
            {
                this.Text = $"{baseTitle} - [受限模式]";
                // 在受限模式下也显示重新登录按钮
                retryLoginButton.Visible = _authenticationRequired;
            }
        }

        /// <summary>
        /// 获取当前认证状态
        /// </summary>
        public AuthenticationStatusInfo GetCurrentAuthStatus()
        {
            return _currentAuthStatus;
        }

        /// <summary>
        /// 获取认证服务
        /// </summary>
        public IAuthService GetAuthService()
        {
            return _authService;
        }

        #endregion

        #region 服务器连接相关方法

        /// <summary>
        /// 初始化连接状态UI
        /// </summary>
        private void InitializeConnectionStatusUI()
        {
            // 绑定重试按钮事件
            retryConnectionButton.Click += RetryConnectionButton_Click;

            // 绑定重新登录按钮事件
            retryLoginButton.Click += RetryLoginButton_Click;

            // 设置初始状态
            UpdateConnectionStatusUI(new ServerConnectionInfo
            {
                Status = ServerConnectionStatus.Unknown,
                Message = "正在检查服务器连接...",
                ServerUrl = RESServerConfigService.Instance.ServerAddress
            });
        }

        /// <summary>
        /// 重试连接按钮点击事件
        /// </summary>
        private async void RetryConnectionButton_Click(object sender, EventArgs e)
        {
            if (_isConnectionCheckInProgress)
                return;

            await CheckServerConnection();
        }

        /// <summary>
        /// 重新登录按钮点击事件
        /// </summary>
        private void RetryLoginButton_Click(object sender, EventArgs e)
        {
            AddLog("用户点击重新登录", LogMessageType.Info);

            // 重置认证状态
            _isAuthenticated = false;

            // 显示登录对话框
            ShowLoginDialog();
        }

        /// <summary>
        /// 检查是否有权限执行受保护的操作
        /// </summary>
        /// <param name="operationName">操作名称</param>
        /// <returns>是否有权限</returns>
        private bool CheckAuthenticationForOperation(string operationName)
        {
            if (_authenticationRequired && !_isAuthenticated)
            {
                // 移除操作被拒绝的日志输出，避免在禁用验证功能时打印禁用日志
                // AddLog($"操作被拒绝：{operationName} - 需要用户认证", LogMessageType.Warning);

                var result = MessageBox.Show(
                    $"执行 '{operationName}' 需要用户认证。\n\n是否现在登录？",
                    "需要认证",
                    MessageBoxButtons.YesNo,
                    MessageBoxIcon.Question,
                    MessageBoxDefaultButton.Button1
                );

                if (result == DialogResult.Yes)
                {
                    ShowLoginDialog();
                }

                return false;
            }

            return true;
        }

        /// <summary>
        /// 检查服务器连接状态
        /// </summary>
        private async Task CheckServerConnection()
        {
            if (_isConnectionCheckInProgress)
                return;

            _isConnectionCheckInProgress = true;

            try
            {
                AddLog("正在检查服务器连接状态...", LogMessageType.Info);

                var connectionInfo = await _connectionService.CheckConnectionAsync(15); // 15秒超时

                // 不需要手动调用 HandleConnectionStatus，因为 ConnectionStatusChanged 事件会自动处理
                // HandleConnectionStatus(connectionInfo);

                // 如果连接成功，继续检查认证状态
                if (connectionInfo.Status == ServerConnectionStatus.Connected)
                {
                    if (AuthConfigService.Instance.EnableAutoLoginCheck)
                    {
                        await CheckAuthenticationStatus();
                    }
                    else
                    {
                        AddLog("自动登录检查已禁用", LogMessageType.Info);
                    }
                }
            }
            catch (Exception ex)
            {
                AddLog($"检查服务器连接时发生错误：{ex.Message}", LogMessageType.Error);
                // ServerConnectionService 会自动处理异常并触发 ConnectionStatusChanged 事件
                // 所以这里不需要额外的处理
            }
            finally
            {
                _isConnectionCheckInProgress = false;
            }
        }

        /// <summary>
        /// 处理连接状态变更
        /// </summary>
        private void OnConnectionStatusChanged(object sender, ServerConnectionInfo connectionInfo)
        {
            if (InvokeRequired)
            {
                Invoke(new Action(() => HandleConnectionStatus(connectionInfo)));
            }
            else
            {
                HandleConnectionStatus(connectionInfo);
            }
        }

        /// <summary>
        /// 处理连接状态
        /// </summary>
        private void HandleConnectionStatus(ServerConnectionInfo connectionInfo)
        {
            _currentConnectionStatus = connectionInfo;

            // 更新UI显示
            UpdateConnectionStatusUI(connectionInfo);

            // 根据连接状态启用/禁用功能
            var isConnected = connectionInfo.Status == ServerConnectionStatus.Connected;
            var isConnecting = connectionInfo.Status == ServerConnectionStatus.Connecting;

            // 在连接过程中不显示日志，避免误导性的禁用消息
            if (isConnecting)
            {
                EnableOrDisableFunctions(false, false); // 禁用功能但不显示日志
            }
            else
            {
                EnableOrDisableFunctions(isConnected, true); // 显示日志
            }

            // 记录日志
            LogConnectionStatus(connectionInfo);

            // 如果连接失败且是首次检查，显示错误提示窗体
            if (!isConnected && ShouldShowConnectionErrorDialog(connectionInfo))
            {
                ShowConnectionErrorDialog(connectionInfo);
            }
        }

        /// <summary>
        /// 更新连接状态UI显示
        /// </summary>
        private void UpdateConnectionStatusUI(ServerConnectionInfo connectionInfo)
        {
            var status = connectionInfo.Status;
            var color = ServerConnectionService.GetStatusColor(status);
            var description = ServerConnectionService.GetStatusDescription(status);

            // 更新状态图标颜色
            connectionStatusIcon.ForeColor = color;

            // 更新状态文本
            var statusText = $"服务器状态: {description}";
            if (connectionInfo.ResponseTime.TotalMilliseconds > 0)
            {
                statusText += $" ({connectionInfo.ResponseTime.TotalMilliseconds:F0}ms)";
            }
            connectionStatusLabel.Text = statusText;

            // 显示/隐藏重试按钮
            retryConnectionButton.Visible = ServerConnectionService.ShouldShowErrorDetails(status);

            // 更新工具提示
            var tooltip = $"服务器地址: {connectionInfo.ServerUrl}\n";
            tooltip += $"检查时间: {connectionInfo.LastCheckTime:yyyy-MM-dd HH:mm:ss}\n";
            tooltip += $"状态: {connectionInfo.Message}";

            if (!string.IsNullOrEmpty(connectionInfo.ErrorDetails))
            {
                tooltip += $"\n错误详情: {connectionInfo.ErrorDetails}";
            }

            if (!string.IsNullOrEmpty(connectionInfo.ServerVersion))
            {
                tooltip += $"\n服务器版本: {connectionInfo.ServerVersion}";
            }

            // 设置工具提示（如果有ToolTip控件的话）
            connectionStatusLabel.Tag = tooltip;
        }

        /// <summary>
        /// 记录连接状态日志
        /// </summary>
        private void LogConnectionStatus(ServerConnectionInfo connectionInfo)
        {
            LogMessageType logType;
            switch (connectionInfo.Status)
            {
                case ServerConnectionStatus.Connected:
                    logType = LogMessageType.Success;
                    break;
                case ServerConnectionStatus.Connecting:
                    logType = LogMessageType.Info;
                    break;
                case ServerConnectionStatus.Failed:
                case ServerConnectionStatus.Timeout:
                case ServerConnectionStatus.Unreachable:
                    logType = LogMessageType.Error;
                    break;
                default:
                    logType = LogMessageType.Info;
                    break;
            }

            var message = $"服务器连接状态: {connectionInfo.Message}";
            if (connectionInfo.ResponseTime.TotalMilliseconds > 0)
            {
                message += $" (响应时间: {connectionInfo.ResponseTime.TotalMilliseconds:F0}ms)";
            }

            AddLog(message, logType);

            if (!string.IsNullOrEmpty(connectionInfo.ErrorDetails))
            {
                AddLog($"错误详情: {connectionInfo.ErrorDetails}", LogMessageType.Error);
            }
        }

        /// <summary>
        /// 获取当前连接状态
        /// </summary>
        public ServerConnectionInfo GetCurrentConnectionStatus()
        {
            return _currentConnectionStatus;
        }

        /// <summary>
        /// 获取连接服务
        /// </summary>
        public IServerConnectionService GetConnectionService()
        {
            return _connectionService;
        }

        /// <summary>
        /// 启用或禁用所有业务功能
        /// </summary>
        /// <param name="enabled">是否启用功能</param>
        private void EnableOrDisableFunctions(bool enabled)
        {
            EnableOrDisableFunctions(enabled, true);
        }

        /// <summary>
        /// 启用或禁用所有业务功能
        /// </summary>
        /// <param name="enabled">是否启用功能</param>
        /// <param name="showLogs">是否显示状态日志</param>
        private void EnableOrDisableFunctions(bool enabled, bool showLogs)
        {
            try
            {
                // 检查是否需要考虑认证状态
                bool finalEnabled = enabled;
                bool isAuthenticationIssue = false;

                if (enabled && _authenticationRequired && !_isAuthenticated)
                {
                    finalEnabled = false; // 即使服务器连接正常，但未认证时也要禁用功能
                    isAuthenticationIssue = true;
                }

                // 禁用/启用所有模块复选框
                foreach (var checkBox in _moduleCheckBoxes)
                {
                    checkBox.Enabled = finalEnabled;
                }

                // 禁用/启用所有模块按钮
                var moduleButtons = new[]
                {
                    button3, button4, button5, button6, button7, button8,
                    button9, button10, button11, button12, button13, button14
                };

                foreach (var button in moduleButtons)
                {
                    button.Enabled = finalEnabled;
                }

                // 禁用/启用主要功能按钮
                generateReportButton.Enabled = finalEnabled;
                button1.Enabled = finalEnabled; // 选择文件夹按钮
                button2.Enabled = finalEnabled; // 封面设置按钮

                // 禁用/启用文件路径输入
                textBox2.Enabled = finalEnabled;

                // 显示相应的状态信息
                if (showLogs && !finalEnabled)
                {
                    if (!enabled && !isAuthenticationIssue)
                    {
                        // 只有在真正的服务器连接问题时才显示连接相关的日志
                        AddLog("=== 功能已禁用 ===", LogMessageType.Warning);
                        AddLog("无法连接到 RESServer，所有业务功能已被禁用", LogMessageType.Warning);
                        AddLog("请检查服务器连接后点击\"重试连接\"按钮", LogMessageType.Warning);
                        AddLog("==================", LogMessageType.Warning);
                    }
                    // 认证问题时不显示连接相关的禁用日志
                    // 认证相关的日志由HandleAuthenticationStatus方法处理
                }
                else if (showLogs && finalEnabled)
                {
                    // 移除重复的功能启用日志，避免多次打印
                    // AddLog("所有功能已启用", LogMessageType.Success);
                }

                // 更新界面状态提示
                UpdateFunctionStatusDisplay(finalEnabled);
            }
            catch (Exception ex)
            {
                AddLog($"更新功能状态时发生错误：{ex.Message}", LogMessageType.Error);
            }
        }

        /// <summary>
        /// 更新功能状态显示
        /// </summary>
        /// <param name="enabled">功能是否启用</param>
        private void UpdateFunctionStatusDisplay(bool enabled)
        {
            // 更新窗体标题以反映状态
            var baseTitle = "房产测绘报告生成系统";

            if (!enabled)
            {
                if (_authenticationRequired && !_isAuthenticated)
                {
                    this.Text = $"{baseTitle} - [需要认证]";
                }
                else
                {
                    this.Text = $"{baseTitle} - [服务器连接失败]";
                }
            }
            else
            {
                this.Text = baseTitle;
            }

            // 可以在这里添加其他UI状态更新逻辑
            // 例如改变背景色、添加覆盖层等
        }

        /// <summary>
        /// 检查功能是否可用
        /// </summary>
        /// <returns>功能是否可用</returns>
        public bool AreFunctionsEnabled()
        {
            return _currentConnectionStatus?.Status == ServerConnectionStatus.Connected;
        }

        /// <summary>
        /// 在执行业务操作前检查连接状态和认证状态
        /// </summary>
        /// <param name="operationName">操作名称</param>
        /// <returns>是否可以继续执行</returns>
        private bool CheckConnectionBeforeOperation(string operationName)
        {
            // 首先检查服务器连接
            if (!AreFunctionsEnabled())
            {
                var message = $"无法执行\"{operationName}\"操作：服务器连接不可用";
                AddLog(message, LogMessageType.Error);

                MessageBox.Show(
                    $"{message}\n\n请检查服务器连接状态后重试。",
                    "操作被阻止",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Warning
                );

                return false;
            }

            // 然后检查认证状态
            if (!CheckAuthenticationForOperation(operationName))
            {
                return false;
            }

            return true;
        }

        /// <summary>
        /// 判断是否应该显示连接错误对话框
        /// </summary>
        /// <param name="connectionInfo">连接信息</param>
        /// <returns>是否显示对话框</returns>
        private bool ShouldShowConnectionErrorDialog(ServerConnectionInfo connectionInfo)
        {
            // 只在严重错误时显示对话框
            return connectionInfo.Status == ServerConnectionStatus.Failed ||
                   connectionInfo.Status == ServerConnectionStatus.Timeout ||
                   connectionInfo.Status == ServerConnectionStatus.Unreachable;
        }

        /// <summary>
        /// 显示连接错误对话框
        /// </summary>
        /// <param name="connectionInfo">连接信息</param>
        private async void ShowConnectionErrorDialog(ServerConnectionInfo connectionInfo)
        {
            try
            {
                using (var errorForm = new ConnectionErrorForm(connectionInfo))
                {
                    var result = errorForm.ShowDialog(this);

                    switch (result)
                    {
                        case DialogResult.Retry:
                            AddLog("用户选择重试连接", LogMessageType.Info);
                            await CheckServerConnection();
                            break;

                        case DialogResult.Cancel:
                            AddLog("用户选择退出程序", LogMessageType.Warning);
                            var confirmResult = MessageBox.Show(
                                "确定要退出程序吗？\n\n退出后将无法使用任何功能。",
                                "确认退出",
                                MessageBoxButtons.YesNo,
                                MessageBoxIcon.Question,
                                MessageBoxDefaultButton.Button2
                            );

                            if (confirmResult == DialogResult.Yes)
                            {
                                Application.Exit();
                            }
                            break;

                        default:
                            // 用户关闭了对话框，不做任何操作
                            break;
                    }
                }
            }
            catch (Exception ex)
            {
                AddLog($"显示连接错误对话框时发生错误：{ex.Message}", LogMessageType.Error);
            }
        }

        #endregion
    }
}
