# RESCADServerPlugin - AutoCAD 插件

RESCADServerPlugin 是一个 AutoCAD 插件，提供了通过 Web API 操作 AutoCAD 绘图的能力，同时具备自定义命令功能。

## 系统架构

该插件采用了模块化的架构设计，将各个功能组件分离为不同的服务类：

### 核心组件

1. **MyServerPlugin** - 插件的主类，实现 IExtensionApplication 接口，负责插件的初始化和终止。

2. **CADServiceManager** - 服务管理器，实现了单例模式，统一管理和提供各种服务实例。

### 服务组件

1. **CADServerService** - Web API 服务类，提供 HTTP 服务器功能，处理外部的 API 请求。

2. **CADDrawingService** - 绘图服务类，封装了各种 AutoCAD 绘图操作的方法。

### 命令组件

1. **CADCommandService** - 命令服务类，实现了各种自定义 AutoCAD 命令。

## 功能特性

### Web API 功能

插件启动后会自动创建一个 HTTP 服务器，默认监听 6060 端口（如果被占用会尝试其他端口）。外部应用可通过以下 API 与 AutoCAD 进行交互：

1. **获取服务状态**
   - URL: `/api/test/info`
   - 方法: GET
   - 功能: 获取服务运行状态和时间戳

2. **绘制圆形**
   - URL: `/api/test/draw-circle`
   - 方法: GET
   - 参数: x, y, radius
   - 功能: 在指定坐标绘制指定半径的圆形

3. **绘制直线**
   - URL: `/api/test/draw-line`
   - 方法: GET
   - 参数: x1, y1, x2, y2
   - 功能: 从点(x1,y1)到点(x2,y2)绘制直线

4. **添加文字**
   - URL: `/api/test/add-text`
   - 方法: GET
   - 参数: x, y, text, height
   - 功能: 在指定坐标添加指定高度的文本

### AutoCAD 命令

插件提供了以下 AutoCAD 命令：

1. **RESTestCommand** - 测试命令，在原点(0,0,0)创建一个半径为5的圆。

2. **RESAPIStatus** - 显示 Web API 服务的运行状态和可用的 API 端点。

3. **RESDrawDemo** - 绘图演示命令，创建一个简单的房间布局示例，包括墙体、门、窗和文字标注。

## 使用方法

1. 加载插件后，Web API 服务会自动启动，可以通过 `RESAPIStatus` 命令查看服务状态。

2. 使用 `RESTestCommand` 或 `RESDrawDemo` 命令测试绘图功能。

3. 外部应用程序可以通过 HTTP 请求调用插件提供的 API。

## 开发说明

### 添加新的绘图功能

1. 在 `CADDrawingService` 中添加新的绘图方法。
2. 如需通过 API 暴露，在 `CADServerService` 的 `HandleRequest` 方法中添加相应的路由处理。
3. 如需通过命令暴露，在 `CADCommandService` 中添加相应的命令方法。

### 添加新的命令

在 `CADCommandService` 类中添加新的方法，并使用 `[CommandMethod]` 特性标记。

```csharp
[CommandMethod("RESNewCommand", CommandFlags.Modal)]
public void NewCommand()
{
    // 命令实现
}
```

### 添加新的服务

1. 创建新的服务类。
2. 在 `CADServiceManager` 中添加相应的获取方法。
3. 根据需要在其他服务或命令中引用新服务。

## 注意事项

- 插件依赖 AutoCAD 2025 .NET API。
- Web API 服务默认使用端口 6060，如被占用会尝试 6061-6065。
- 所有对 AutoCAD 文档的操作都通过事务进行，确保数据的一致性。 