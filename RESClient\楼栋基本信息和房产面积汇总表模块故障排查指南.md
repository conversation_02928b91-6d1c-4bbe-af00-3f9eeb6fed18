# 楼栋基本信息和房产面积汇总表模块故障排查指南

## 问题描述

在报告生成过程中，"楼栋基本信息"模块（BuildingInfoModuleGenerator）和"房产面积汇总表"模块（EstateAreaSummaryModuleGenerator）显示状态为"无"，表示这两个模块没有成功生成。

## 主要原因分析

### 1. IsAvailable方法返回false（最可能的原因）

**问题**: IsAvailable方法没有包含新增的标准Excel文件查找逻辑，导致模块被错误地标记为不可用。

**症状**: 
- 模块状态显示为"无"
- 在报告生成日志中看到"模块不可用，跳过生成"的消息

**解决方案**: 
已修复IsAvailable方法，现在包含以下检查顺序：
1. 标准Excel文件（包含4个指定工作表）
2. 传统CGB文件
3. 成果包.7z文件

### 2. 数据源文件问题

**可能的问题**:
- 数据目录中没有可用的Excel文件
- Excel文件格式不正确
- 文件权限问题

**检查方法**:
```
数据目录应包含以下任一类型的文件：
1. 标准Excel文件：包含4个工作表（房屋建筑面积总表、共有建筑面积的分摊、房产分户面积统计表、楼盘表信息）
2. CGB.xls文件：文件名包含"cgb"的Excel文件
3. 成果包.7z文件：包含CGB.xls的压缩包
```

### 3. 模板文件缺失

**检查路径**:
- 楼栋基本信息: `报告模板/06_楼栋基本信息/楼栋基本信息.docx`
- 房产面积汇总表: `报告模板/11_房产面积汇总表/房产面积汇总表.docx`

### 4. 输出目录权限问题

**检查项目**:
- 输出目录是否存在
- 是否有写入权限
- 磁盘空间是否充足

## 详细故障排查步骤

### 步骤1: 使用诊断工具

运行 `ModuleDiagnosticTool.cs` 进行自动诊断：

```csharp
// 编译并运行诊断工具
dotnet run --project ModuleDiagnosticTool.cs
```

诊断工具会检查：
- 模块可用性
- 数据文件查找
- 模板文件存在性
- 生成过程测试

### 步骤2: 手动检查数据文件

1. **检查数据目录**:
   ```
   确认数据目录路径正确
   检查目录是否存在
   验证目录访问权限
   ```

2. **检查Excel文件**:
   ```
   列出所有.xls和.xlsx文件
   验证文件是否可以正常打开
   检查工作表名称和数量
   ```

3. **验证标准Excel文件**:
   ```
   工作表数量: 必须恰好4个
   工作表名称: 
   - 房屋建筑面积总表
   - 共有建筑面积的分摊
   - 房产分户面积统计表
   - 楼盘表信息
   ```

### 步骤3: 检查模板文件

1. **验证模板文件路径**:
   ```
   楼栋基本信息: [应用程序目录]/报告模板/06_楼栋基本信息/楼栋基本信息.docx
   房产面积汇总表: [应用程序目录]/报告模板/11_房产面积汇总表/房产面积汇总表.docx
   ```

2. **检查模板文件**:
   ```
   文件是否存在
   文件是否损坏
   文件权限是否正确
   ```

### 步骤4: 验证参数传递

检查传递给模块的参数：
```csharp
parameters["DataFolder"] = "数据目录路径";
parameters["OutputDir"] = "输出目录路径";
```

### 步骤5: 查看详细日志

在报告生成过程中，注意以下日志信息：
- `[MODULE_SKIPPED]模块名:模块不可用，跳过生成`
- `[ERROR]未找到CGB.xls文件`
- `[INFO]查找包含标准工作表的Excel文件...`
- `[INFO]找到包含标准工作表的Excel文件`

## 常见错误和解决方案

### 错误1: "模块不可用，跳过生成"

**原因**: IsAvailable方法返回false
**解决**: 
1. 确保数据目录包含有效的Excel文件
2. 验证模板文件存在
3. 检查参数传递是否正确

### 错误2: "未找到CGB.xls文件"

**原因**: 所有查找方式都失败
**解决**:
1. 检查Excel文件是否符合标准格式
2. 确认CGB文件命名正确
3. 验证7z压缩包内容

### 错误3: "模板文件不存在"

**原因**: 模板文件路径错误或文件缺失
**解决**:
1. 检查报告模板目录结构
2. 确认模板文件名称正确
3. 重新部署模板文件

### 错误4: "Excel文件格式错误"

**原因**: Excel文件损坏或格式不支持
**解决**:
1. 使用Excel重新保存文件
2. 检查文件编码
3. 验证工作表结构

## 预防措施

### 1. 数据文件标准化

建议使用标准Excel文件格式：
- 包含4个指定工作表
- 工作表名称完全匹配
- 数据格式规范

### 2. 定期检查模板文件

确保模板文件：
- 完整性
- 可访问性
- 版本一致性

### 3. 环境验证

在部署前验证：
- 目录权限
- 文件路径
- 依赖库

## 联系支持

如果按照以上步骤仍无法解决问题，请提供：
1. 诊断工具的完整输出
2. 数据目录的文件列表
3. 详细的错误日志
4. 系统环境信息

## 更新日志

- 2024-07-14: 修复IsAvailable方法，添加标准Excel文件查找逻辑
- 2024-07-14: 创建诊断工具和故障排查指南
