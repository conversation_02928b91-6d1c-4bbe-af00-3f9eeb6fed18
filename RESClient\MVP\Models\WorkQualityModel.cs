using System;
using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;
using NPOI.XWPF.UserModel;
using RESClient.MVP.Base;

namespace RESClient.MVP.Models
{
    /// <summary>
    /// 作业、质量检查与验收模型，用于处理模板的填充
    /// </summary>
    public class WorkQualityModel : BaseModel
    {
        /// <summary>
        /// 作业、质量检查与验收信息数据结构
        /// </summary>
        public class WorkQualityData
        {
            // 内业作业人信息
            public string InternalWorkerName { get; set; }
            public string InternalWorkerCertificateNumber { get; set; }
            public string InternalWorkerWorkCertificate { get; set; }
            public string InternalWorkerCertificateValidity { get; set; }

            // 外业作业人1信息
            public string ExternalWorker1Name { get; set; }
            public string ExternalWorker1CertificateNumber { get; set; }
            public string ExternalWorker1WorkCertificate { get; set; }
            public string ExternalWorker1CertificateValidity { get; set; }

            // 外业作业人2信息
            public string ExternalWorker2Name { get; set; }
            public string ExternalWorker2CertificateNumber { get; set; }
            public string ExternalWorker2WorkCertificate { get; set; }
            public string ExternalWorker2CertificateValidity { get; set; }

            // 一级检查人信息
            public string FirstLevelInspectorName { get; set; }
            public string FirstLevelInspectorCertificateNumber { get; set; }
            public string FirstLevelInspectorWorkCertificate { get; set; }
            public string FirstLevelInspectorCertificateValidity { get; set; }

            // 二级检查人信息
            public string SecondLevelInspectorName { get; set; }
            public string SecondLevelInspectorCertificateNumber { get; set; }
            public string SecondLevelInspectorWorkCertificate { get; set; }
            public string SecondLevelInspectorCertificateValidity { get; set; }

            // 签发人信息
            public string SignerName { get; set; }
            public string SignerCertificateNumber { get; set; }
            public string SignerWorkCertificate { get; set; }
            public string SignerCertificateValidity { get; set; }

            // 作业相关信息
            public string CalculationMethod { get; set; }
            public string Comparator { get; set; }
            public string EdgeLengthMethod { get; set; }
        }

        /// <summary>
        /// 生成测试用的作业、质量检查与验收数据
        /// </summary>
        /// <returns>测试数据</returns>
        public WorkQualityData GenerateTestWorkQualityData()
        {
            return new WorkQualityData
            {
                InternalWorkerName = "贾羽",
                InternalWorkerCertificateNumber = "176800300350263Z",
                InternalWorkerWorkCertificate = "5100014821",
                InternalWorkerCertificateValidity = "2022年5月20日至2025年5月19日",

                ExternalWorker1Name = "贾羽",
                ExternalWorker1CertificateNumber = "176800300350263Z",
                ExternalWorker1WorkCertificate = "5100014821",
                ExternalWorker1CertificateValidity = "2022年5月20日至2025年5月19日",

                ExternalWorker2Name = "杨林",
                ExternalWorker2CertificateNumber = "176800300350263S",
                ExternalWorker2WorkCertificate = "5100014822",
                ExternalWorker2CertificateValidity = "2022年5月20日至2025年5月19日",

                FirstLevelInspectorName = "陈小波",
                FirstLevelInspectorCertificateNumber = "186800300350045G",
                FirstLevelInspectorWorkCertificate = "5100014823",
                FirstLevelInspectorCertificateValidity = "2022年5月20日至2025年5月19日",

                SecondLevelInspectorName = "熊春蓉",
                SecondLevelInspectorCertificateNumber = "146800300350122Z",
                SecondLevelInspectorWorkCertificate = "5100102910",
                SecondLevelInspectorCertificateValidity = "2022年5月20日至2025年5月19日",

                SignerName = "陈鸿阳",
                SignerCertificateNumber = "066800300350042Z",
                SignerWorkCertificate = "5100016815",
                SignerCertificateValidity = "2022年12月14日至2025年12月13日",

                CalculationMethod = "几何图形解析法",
                Comparator = "小于",
                EdgeLengthMethod = "设计边长"
            };
        }

        /// <summary>
        /// 填充作业、质量检查与验收模板
        /// </summary>
        /// <param name="data">数据</param>
        /// <param name="outputPath">输出路径</param>
        /// <returns>是否成功</returns>
        public async Task<bool> FillWorkQualityTemplateAsync(WorkQualityData data, string outputPath)
        {
            try
            {
                return await Task.Run(() =>
                {
                    // 获取模板路径
                    string templatePath = Path.Combine(
                        AppDomain.CurrentDomain.BaseDirectory,
                        Program.TemplateDirectory,
                        "04_作业、质量检查与验收",
                        "作业、质量检查与验收.docx");

                    if (!File.Exists(templatePath))
                    {
                        throw new FileNotFoundException("作业、质量检查与验收模板文件不存在", templatePath);
                    }

                    // 确保输出目录存在
                    string outputDir = Path.GetDirectoryName(outputPath);
                    Directory.CreateDirectory(outputDir);

                    // 创建模板的临时副本
                    string tempFilePath = Path.Combine(
                        Path.GetTempPath(),
                        $"temp_work_quality_{DateTime.Now.Ticks}.docx");

                    // 复制模板到临时文件
                    File.Copy(templatePath, tempFilePath, true);

                    // 创建变量映射
                    var variableMapping = new Dictionary<string, string>
                    {
                        // 内业作业人
                        { "${内业姓名}", data.InternalWorkerName ?? "" },
                        { "${内业作业人职业资格证书号}", data.InternalWorkerCertificateNumber ?? "" },
                        { "${内业作业证号}", data.InternalWorkerWorkCertificate ?? "" },
                        { "${内业作业证有效期}", data.InternalWorkerCertificateValidity ?? "" },
                        
                        // 外业作业人1
                        { "${外业1姓名}", data.ExternalWorker1Name ?? "" },
                        { "${外业作业人1职业资格证书号}", data.ExternalWorker1CertificateNumber ?? "" },
                        { "${外业1作业证号}", data.ExternalWorker1WorkCertificate ?? "" },
                        { "${外业1作业证有效期}", data.ExternalWorker1CertificateValidity ?? "" },
                        
                        // 外业作业人2
                        { "${外业2姓名}", data.ExternalWorker2Name ?? "" },
                        { "${外业作业人2职业资格证书号}", data.ExternalWorker2CertificateNumber ?? "" },
                        { "${外业2作业证号}", data.ExternalWorker2WorkCertificate ?? "" },
                        { "${外业2作业证有效期}", data.ExternalWorker2CertificateValidity ?? "" },
                        
                        // 一级检查人
                        { "${一级检查人姓名}", data.FirstLevelInspectorName ?? "" },
                        { "${一级检查人职业资格证书号}", data.FirstLevelInspectorCertificateNumber ?? "" },
                        { "${一级检查人作业证号}", data.FirstLevelInspectorWorkCertificate ?? "" },
                        { "${一级检查人作业证有效期}", data.FirstLevelInspectorCertificateValidity ?? "" },
                        
                        // 二级检查人
                        { "${二级检查人姓名}", data.SecondLevelInspectorName ?? "" },
                        { "${二级检查人职业资格证书号}", data.SecondLevelInspectorCertificateNumber ?? "" },
                        { "${二级检查人作业证号}", data.SecondLevelInspectorWorkCertificate ?? "" },
                        { "${二级检查人作业证有效期}", data.SecondLevelInspectorCertificateValidity ?? "" },
                        
                        // 签发人
                        { "${签发人姓名}", data.SignerName ?? "" },
                        { "${签发人职业资格证书号}", data.SignerCertificateNumber ?? "" },
                        { "${签发人作业证号}", data.SignerWorkCertificate ?? "" },
                        { "${签发人作业证有效期}", data.SignerCertificateValidity ?? "" },
                        
                        // 作业信息
                        { "${算法}", data.CalculationMethod ?? "" },
                        { "${比较器}", data.Comparator ?? "" },
                        { "${边长采用}", data.EdgeLengthMethod ?? "" }
                    };

                    // 使用NPOI打开临时Word文档
                    XWPFDocument doc;
                    using (FileStream fs = new FileStream(tempFilePath, FileMode.Open, FileAccess.Read))
                    {
                        doc = new XWPFDocument(fs);
                    }

                    // 替换所有文本中的占位符
                    foreach (var paragraph in doc.Paragraphs)
                    {
                        ReplaceVariablesInParagraph(paragraph, variableMapping);
                    }

                    // 表格中的占位符替换
                    foreach (var table in doc.Tables)
                    {
                        foreach (var row in table.Rows)
                        {
                            foreach (var cell in row.GetTableCells())
                            {
                                foreach (var paragraph in cell.Paragraphs)
                                {
                                    ReplaceVariablesInParagraph(paragraph, variableMapping);
                                }
                            }
                        }
                    }

                    // 保存填充后的文档到指定输出路径
                    using (FileStream fs = new FileStream(outputPath, FileMode.Create, FileAccess.Write))
                    {
                        doc.Write(fs);
                    }

                    doc.Close();

                    // 清理临时文件
                    try
                    {
                        if (File.Exists(tempFilePath))
                        {
                            File.Delete(tempFilePath);
                        }
                    }
                    catch
                    {
                        // 忽略临时文件清理错误
                    }

                    return true;
                });
            }
            catch (Exception ex)
            {
                HandleException(ex);
                return false;
            }
        }

        /// <summary>
        /// 替换段落中的所有变量
        /// </summary>
        /// <param name="paragraph">段落</param>
        /// <param name="variableMapping">变量映射字典</param>
        private void ReplaceVariablesInParagraph(XWPFParagraph paragraph, Dictionary<string, string> variableMapping)
        {
            // 替换所有变量
            foreach (var variable in variableMapping)
            {
                if (paragraph.Text.Contains(variable.Key))
                {
                    ReplaceTextInParagraph(paragraph, variable.Key, variable.Value);
                }
            }
        }

        /// <summary>
        /// 替换段落中的文本，保留原始格式
        /// </summary>
        private void ReplaceTextInParagraph(XWPFParagraph paragraph, string searchText, string replaceText)
        {
            // 查找包含占位符的运行
            for (int i = 0; i < paragraph.Runs.Count; i++)
            {
                var run = paragraph.Runs[i];
                string runText = run.Text;

                if (runText != null && runText.Contains(searchText))
                {
                    // 保留格式，仅替换文本
                    run.SetText(runText.Replace(searchText, replaceText), 0);
                    return; // 替换完成
                }

                // 处理跨多个运行的占位符情况
                if (i < paragraph.Runs.Count - 1 && runText != null)
                {
                    // 查找是否占位符跨多个运行
                    string combinedText = runText;
                    int j = i + 1;

                    // 尝试组合多个运行的文本查找占位符
                    while (j < paragraph.Runs.Count && !combinedText.Contains(searchText))
                    {
                        string nextRunText = paragraph.Runs[j].Text ?? "";
                        combinedText += nextRunText;

                        if (combinedText.Contains(searchText))
                        {
                            // 找到了跨越多个运行的占位符
                            // 现在需要删除整个占位符并在第一个运行位置插入替换文本

                            // 在第一个运行中放入替换文本
                            int placeholderStart = combinedText.IndexOf(searchText);
                            int firstRunLength = runText.Length;

                            if (placeholderStart < firstRunLength)
                            {
                                // 占位符开始于第一个运行
                                string beforePlaceholder = runText.Substring(0, placeholderStart);
                                run.SetText(beforePlaceholder + replaceText, 0);

                                // 计算占位符末尾位置
                                int placeholderEnd = placeholderStart + searchText.Length;

                                // 如果占位符结束于当前运行之后，需要调整后面的运行
                                if (placeholderEnd > firstRunLength)
                                {
                                    int currentEnd = firstRunLength;
                                    int currentRun = i + 1;

                                    // 处理或删除跨越的运行
                                    while (currentRun <= j && currentEnd < placeholderEnd)
                                    {
                                        string currentText = paragraph.Runs[currentRun].Text ?? "";
                                        int currentLength = currentText.Length;

                                        if (currentEnd + currentLength > placeholderEnd)
                                        {
                                            // 这个运行包含占位符结束后的文本
                                            string afterPlaceholder = currentText.Substring(
                                                placeholderEnd - currentEnd);
                                            paragraph.Runs[currentRun].SetText(afterPlaceholder, 0);
                                            break;
                                        }
                                        else
                                        {
                                            // 这个运行完全在占位符内，清空它
                                            paragraph.Runs[currentRun].SetText("", 0);
                                        }

                                        currentEnd += currentLength;
                                        currentRun++;
                                    }
                                }

                                return; // 替换完成
                            }
                        }

                        j++;
                    }
                }
            }
        }
    }
}
