using System;
using System.Windows.Forms;
using RESClient.MVP.Models;
using RESClient.MVP.Views;

namespace RESClient
{
    /// <summary>
    /// 测试作业声明参数输入窗体
    /// </summary>
    public static class TestWorkStatementParameterForm
    {
        /// <summary>
        /// 测试作业声明参数输入窗体的显示和功能
        /// </summary>
        public static void TestParameterInputForm()
        {
            try
            {
                Application.EnableVisualStyles();
                Application.SetCompatibleTextRenderingDefault(false);

                Console.WriteLine("开始测试作业声明参数输入窗体...");

                // 创建参数模型
                var parametersModel = new WorkStatementParametersModel();
                var currentParams = parametersModel.GetCurrentParameters();

                Console.WriteLine("当前参数默认值:");
                Console.WriteLine($"  测绘机构: {currentParams.SurveyInstitution}");
                Console.WriteLine($"  证书编号: {currentParams.CertificateNumber}");
                Console.WriteLine($"  证书等级: {currentParams.CertificateLevel}");
                Console.WriteLine($"  法人代表: {currentParams.LegalRepresentative}");
                Console.WriteLine($"  技术负责人: {currentParams.TechnicalManager}");
                Console.WriteLine($"  公司地址: {currentParams.CompanyAddress}");
                Console.WriteLine($"  电话: {currentParams.Phone}");
                Console.WriteLine($"  传真: {currentParams.Fax}");

                // 创建并显示参数输入窗体
                using (var form = new WorkStatementParameterInputForm(currentParams))
                {
                    Console.WriteLine("显示作业声明参数输入窗体...");
                    var result = form.ShowDialog();
                    
                    Console.WriteLine($"窗体结果: {result}");
                    
                    if (result == DialogResult.OK)
                    {
                        Console.WriteLine("用户确认了参数设置");
                        Console.WriteLine("更新后的参数:");
                        Console.WriteLine($"  委托方: {form.Parameters.ClientName}");
                        Console.WriteLine($"  项目地址: {form.Parameters.ProjectAddress}");
                        Console.WriteLine($"  测绘机构: {form.Parameters.SurveyInstitution}");
                        Console.WriteLine($"  证书编号: {form.Parameters.CertificateNumber}");
                        Console.WriteLine($"  法人代表: {form.Parameters.LegalRepresentative}");
                        Console.WriteLine($"  技术负责人: {form.Parameters.TechnicalManager}");
                    }
                    else
                    {
                        Console.WriteLine("用户取消了参数设置");
                    }
                }

                Console.WriteLine("作业声明参数输入窗体测试完成");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"测试作业声明参数输入窗体时发生错误: {ex.Message}");
                Console.WriteLine($"详细错误: {ex}");
            }
        }

        /// <summary>
        /// 测试参数验证功能
        /// </summary>
        public static void TestParameterValidation()
        {
            try
            {
                Console.WriteLine("开始测试作业声明参数验证...");

                var parametersModel = new WorkStatementParametersModel();
                var parameters = parametersModel.GetCurrentParameters();

                // 测试空参数验证
                parameters.ClientName = "";
                parameters.ProjectAddress = "";
                
                var validationResult = parameters.Validate();
                Console.WriteLine($"空参数验证结果: {validationResult.IsValid}");
                
                if (!validationResult.IsValid)
                {
                    Console.WriteLine("验证错误:");
                    foreach (var error in validationResult.Errors)
                    {
                        Console.WriteLine($"  - {error}");
                    }
                }

                // 测试有效参数
                parameters.ClientName = "测试委托方";
                parameters.ProjectAddress = "测试项目地址";
                
                validationResult = parameters.Validate();
                Console.WriteLine($"有效参数验证结果: {validationResult.IsValid}");

                Console.WriteLine("作业声明参数验证测试完成");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"测试参数验证时发生错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 主测试方法
        /// </summary>
        public static void RunAllTests()
        {
            Console.WriteLine("=== 作业声明参数输入窗体测试 ===");
            TestParameterValidation();
            Console.WriteLine();
            TestParameterInputForm();
            Console.WriteLine("=== 测试完成 ===");
        }
    }
}
