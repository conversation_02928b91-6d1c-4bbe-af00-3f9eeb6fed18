using System;
using Autodesk.AutoCAD.ApplicationServices;
using Autodesk.AutoCAD.EditorInput;
using RESCADServerPlugin.Services;

namespace RESCADServerPlugin.Commands.Implementations
{
    /// <summary>
    /// 状态命令实现类
    /// </summary>
    public class StatusCommands : CommandBase
    {
        /// <summary>
        /// 执行API状态命令 - 显示Web API服务状态和可用命令
        /// </summary>
        public void ExecuteAPIStatus()
        {
            Document doc = GetActiveDocument();
            if (doc == null) return;

            Editor ed = GetEditor();

            var serverService = CADServiceManager.GetServerService();
            var (isRunning, currentPort) = serverService.GetServerStatus();
            
            // 显示命令信息
            WriteMessage("\n可用命令:");
            WriteMessage("\n - RESTestCommand: 测试命令 - 在原点创建一个简单圆形");
            WriteMessage("\n - RESDrawDemo: 绘图演示 - 创建一个简单房间布局示例");
            WriteMessage("\n - RESAPIStatus: 显示此帮助信息");
            WriteMessage("\n - RESGetXData: 获取实体的扩展属性数据并显示所有键值对");
            WriteMessage("\n - RESGetXrecord: 获取实体的Xrecord扩展记录");
            
            // 显示API信息
            WriteMessage("\n");
            if (isRunning)
            {
                WriteMessage($"\nWeb API 服务正在运行: http://localhost:{currentPort}");
            }
            else
            {
                WriteMessage("\nWeb API 服务未运行");
            }
        }
    }
} 