<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gemini 文件分析</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', 'Segoe UI', sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        h1 {
            color: #333;
            text-align: center;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input[type="file"] {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        textarea {
            width: 100%;
            min-height: 100px;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        button {
            background-color: #4CAF50;
            color: white;
            padding: 10px 15px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover {
            background-color: #45a049;
        }
        #result {
            margin-top: 20px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background-color: #f9f9f9;
            min-height: 100px;
            white-space: pre-wrap;
        }
        .loader {
            border: 5px solid #f3f3f3;
            border-top: 5px solid #3498db;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            animation: spin 2s linear infinite;
            margin: 20px auto;
            display: none;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .back-link {
            display: inline-block;
            margin-bottom: 20px;
            color: #4CAF50;
            text-decoration: none;
        }
        .back-link:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <a href="/" class="back-link">← 返回首页</a>
    
    <h1>Gemini 文件分析工具</h1>
    
    <div class="form-group">
        <label for="files">上传文件（可多选）：</label>
        <input type="file" id="files" name="files" multiple>
    </div>
    
    <div class="form-group">
        <label for="prompt">分析提示（可选）：</label>
        <textarea id="prompt" name="prompt" placeholder="请输入分析提示，例如：分析这些文件并提供详细摘要"></textarea>
    </div>
    
    <button id="submitBtn">上传并分析</button>
    
    <div class="loader" id="loader"></div>
    
    <div id="result"></div>

    <script>
        document.getElementById('submitBtn').addEventListener('click', async () => {
            const files = document.getElementById('files').files;
            if (files.length === 0) {
                alert('请至少选择一个文件');
                return;
            }

            const prompt = document.getElementById('prompt').value;
            const formData = new FormData();
            
            for (let i = 0; i < files.length; i++) {
                formData.append('Files', files[i]);
            }
            
            if (prompt) {
                formData.append('Prompt', prompt);
            }

            const resultDiv = document.getElementById('result');
            const loader = document.getElementById('loader');
            const submitBtn = document.getElementById('submitBtn');
            
            resultDiv.textContent = '';
            loader.style.display = 'block';
            submitBtn.disabled = true;
            
            try {
                const response = await fetch('/api/GeminiAnalysis/analyze', {
                    method: 'POST',
                    body: formData
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    resultDiv.textContent = data.analysis;
                } else {
                    resultDiv.textContent = `错误: ${data.message || '未知错误'}`;
                }
            } catch (error) {
                resultDiv.textContent = `请求错误: ${error.message}`;
            } finally {
                loader.style.display = 'none';
                submitBtn.disabled = false;
            }
        });
    </script>
</body>
</html> 