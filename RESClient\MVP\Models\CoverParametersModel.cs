using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.IO;
using Newtonsoft.Json;
using RESClient.MVP.Base;
using RESClient.Utils;

namespace RESClient.MVP.Models
{
    /// <summary>
    /// 封面参数数据模型
    /// </summary>
    public class CoverParametersModel : BaseModel
    {
        /// <summary>
        /// 封面参数数据结构
        /// </summary>
        public class CoverParameters
        {
            [DisplayName("测绘编号")]
            [Description("测绘项目的唯一编号")]
            public string SurveyNumber { get; set; } = "";

            [DisplayName("项目编号")]
            [Description("项目的唯一编号")]
            public string ProjectNumber { get; set; } = "";

            [DisplayName("项目名称")]
            [Description("项目的完整名称")]
            public string ProjectName { get; set; } = "";

            [DisplayName("项目地址")]
            [Description("项目所在地址")]
            public string ProjectAddress { get; set; } = "";

            [DisplayName("建设单位")]
            [Description("项目建设单位名称")]
            public string ConstructionUnit { get; set; } = "";

            [DisplayName("测绘楼栋")]
            [Description("需要测绘的楼栋信息")]
            public string SurveyBuilding { get; set; } = "";

            [DisplayName("测绘公司")]
            [Description("执行测绘的公司名称")]
            public string SurveyCompany { get; set; } = "";

            [DisplayName("测绘资格证书号")]
            [Description("测绘公司的资格证书号")]
            public string QualificationNumber { get; set; } = "";

            [DisplayName("日期")]
            [Description("报告生成日期")]
            public DateTime ReportDate { get; set; } = DateTime.Now;

            /// <summary>
            /// 获取所有变量的映射字典
            /// </summary>
            /// <returns>变量名到值的映射</returns>
            public Dictionary<string, string> GetVariableMapping()
            {
                return new Dictionary<string, string>
                {
                    { "${测绘编号}", SurveyNumber },
                    { "${项目编号}", ProjectNumber },
                    { "${项目名称}", ProjectName },
                    { "${项目地址}", ProjectAddress },
                    { "${建设单位}", ConstructionUnit },
                    { "${测绘楼栋}", SurveyBuilding },
                    { "${测绘公司}", SurveyCompany },
                    { "${测绘资格证书号}", QualificationNumber },
                    { "${日期}", ChineseDateFormatter.ToChineseUppercase(ReportDate) }
                };
            }

            /// <summary>
            /// 验证参数完整性
            /// </summary>
            /// <returns>验证结果和错误信息</returns>
            public (bool IsValid, List<string> Errors) Validate()
            {
                var errors = new List<string>();

                if (string.IsNullOrWhiteSpace(SurveyNumber))
                    errors.Add("测绘编号不能为空");

                if (string.IsNullOrWhiteSpace(ProjectNumber))
                    errors.Add("项目编号不能为空");

                if (string.IsNullOrWhiteSpace(ProjectName))
                    errors.Add("项目名称不能为空");

                if (string.IsNullOrWhiteSpace(ProjectAddress))
                    errors.Add("项目地址不能为空");

                if (string.IsNullOrWhiteSpace(ConstructionUnit))
                    errors.Add("建设单位不能为空");

                if (string.IsNullOrWhiteSpace(SurveyCompany))
                    errors.Add("测绘公司不能为空");

                if (string.IsNullOrWhiteSpace(QualificationNumber))
                    errors.Add("测绘资格证书号不能为空");

                return (errors.Count == 0, errors);
            }
        }

        private readonly string _configFilePath;
        private CoverParameters _currentParameters;

        public CoverParametersModel()
        {
            // 配置文件保存在应用程序数据目录
            string appDataPath = Path.Combine(
                Environment.GetFolderPath(Environment.SpecialFolder.LocalApplicationData),
                "RESClient");
            
            if (!Directory.Exists(appDataPath))
            {
                Directory.CreateDirectory(appDataPath);
            }

            _configFilePath = Path.Combine(appDataPath, "cover_parameters.json");
            _currentParameters = LoadParameters();
        }

        /// <summary>
        /// 获取当前参数
        /// </summary>
        /// <returns>当前封面参数</returns>
        public CoverParameters GetCurrentParameters()
        {
            return _currentParameters ?? new CoverParameters();
        }

        /// <summary>
        /// 保存参数
        /// </summary>
        /// <param name="parameters">要保存的参数</param>
        /// <returns>是否保存成功</returns>
        public bool SaveParameters(CoverParameters parameters)
        {
            try
            {
                var settings = new JsonSerializerSettings
                {
                    Formatting = Formatting.Indented
                };

                string json = JsonConvert.SerializeObject(parameters, settings);
                File.WriteAllText(_configFilePath, json, System.Text.Encoding.UTF8);

                _currentParameters = parameters;
                return true;
            }
            catch (Exception ex)
            {
                HandleException(ex);
                return false;
            }
        }

        /// <summary>
        /// 加载参数
        /// </summary>
        /// <returns>加载的参数，如果失败则返回默认参数</returns>
        private CoverParameters LoadParameters()
        {
            try
            {
                if (!File.Exists(_configFilePath))
                {
                    return CreateDefaultParameters();
                }

                string json = File.ReadAllText(_configFilePath, System.Text.Encoding.UTF8);
                var parameters = JsonConvert.DeserializeObject<CoverParameters>(json);
                
                return parameters ?? CreateDefaultParameters();
            }
            catch (Exception ex)
            {
                HandleException(ex);
                return CreateDefaultParameters();
            }
        }

        /// <summary>
        /// 创建默认参数
        /// </summary>
        /// <returns>默认参数</returns>
        private CoverParameters CreateDefaultParameters()
        {
            return new CoverParameters
            {
                SurveyCompany = "四川省川建勘察设计院有限公司",
                QualificationNumber = "甲测资字：51100923",
                ReportDate = DateTime.Now
            };
        }

        /// <summary>
        /// 重置为默认参数
        /// </summary>
        /// <returns>是否重置成功</returns>
        public bool ResetToDefault()
        {
            var defaultParameters = CreateDefaultParameters();
            return SaveParameters(defaultParameters);
        }

        /// <summary>
        /// 检查配置文件是否存在
        /// </summary>
        /// <returns>配置文件是否存在</returns>
        public bool ConfigFileExists()
        {
            return File.Exists(_configFilePath);
        }

        /// <summary>
        /// 获取配置文件路径
        /// </summary>
        /// <returns>配置文件路径</returns>
        public string GetConfigFilePath()
        {
            return _configFilePath;
        }
    }
}
