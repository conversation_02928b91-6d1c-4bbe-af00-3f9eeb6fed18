using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Windows.Forms;
using RESClient.Services;
using RESClient.MVP.Views;

namespace RESClient
{
    /// <summary>
    /// 测试错误报告系统
    /// </summary>
    public class TestErrorReporting
    {
        /// <summary>
        /// 测试错误收集和显示功能
        /// </summary>
        public static async Task TestErrorCollectionAndDisplay()
        {
            Console.WriteLine("=== 测试错误报告系统 ===");
            
            try
            {
                // 1. 测试错误收集器
                Console.WriteLine("\n1. 测试错误收集器...");
                TestErrorCollector();
                
                // 2. 测试错误摘要对话框
                Console.WriteLine("\n2. 测试错误摘要对话框...");
                await TestErrorSummaryDialog();
                
                // 3. 测试进度消息解析
                Console.WriteLine("\n3. 测试进度消息解析...");
                TestProgressMessageParsing();
                
                // 4. 测试错误统计
                Console.WriteLine("\n4. 测试错误统计...");
                TestErrorStatistics();
                
                Console.WriteLine("\n✓ 所有错误报告系统测试完成");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"\n✗ 测试过程中发生错误: {ex.Message}");
                Console.WriteLine($"详细信息: {ex.StackTrace}");
            }
        }

        /// <summary>
        /// 测试错误收集器基本功能
        /// </summary>
        private static void TestErrorCollector()
        {
            try
            {
                Console.WriteLine("   创建错误收集器实例...");
                var errorCollector = new ErrorCollector();
                Console.WriteLine("   ✓ 错误收集器创建成功");

                // 测试添加不同类型的错误
                Console.WriteLine("   添加测试错误...");
                
                errorCollector.AddError(ErrorType.MissingFile, ErrorSeverity.Error, 
                    "封面", "模板文件不存在", 
                    details: "找不到封面模板文件 cover_template.docx",
                    suggestedSolution: "请检查模板目录中是否包含封面模板文件");

                errorCollector.AddError(ErrorType.DataValidation, ErrorSeverity.Warning, 
                    "项目基本信息", "项目名称为空", 
                    suggestedSolution: "请在参数设置中填写项目名称");

                errorCollector.AddError(ErrorType.DocumentGeneration, ErrorSeverity.Error, 
                    "楼栋基本信息", "Excel数据读取失败",
                    details: "无法读取CGB.xls文件中的楼栋信息",
                    suggestedSolution: "请检查Excel文件格式和内容是否正确");

                errorCollector.AddError(ErrorType.SystemException, ErrorSeverity.Critical, 
                    "系统", "内存不足",
                    details: "OutOfMemoryException: 处理大型文档时内存不足",
                    suggestedSolution: "请关闭其他应用程序或增加系统内存");

                Console.WriteLine($"   ✓ 成功添加 {errorCollector.GetErrorCount()} 个测试错误");

                // 测试错误查询
                Console.WriteLine("   测试错误查询功能...");
                var allErrors = errorCollector.GetAllErrors();
                var criticalErrors = errorCollector.GetErrorsBySeverity(ErrorSeverity.Critical);
                var moduleErrors = errorCollector.GetErrorsByModule("封面");
                
                Console.WriteLine($"   - 总错误数: {allErrors.Count}");
                Console.WriteLine($"   - 严重错误数: {criticalErrors.Count}");
                Console.WriteLine($"   - 封面模块错误数: {moduleErrors.Count}");
                Console.WriteLine($"   - 是否有严重错误: {errorCollector.HasCriticalErrors()}");

                Console.WriteLine("   ✓ 错误收集器基本功能测试通过");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"   ✗ 错误收集器测试失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 测试错误摘要对话框
        /// </summary>
        private static async Task TestErrorSummaryDialog()
        {
            try
            {
                Console.WriteLine("   创建测试错误数据...");
                var errors = CreateTestErrors();
                Console.WriteLine($"   ✓ 创建了 {errors.Count} 个测试错误");

                Console.WriteLine("   测试错误摘要对话框创建...");
                using (var dialog = new ErrorSummaryDialog(errors))
                {
                    Console.WriteLine("   ✓ 错误摘要对话框创建成功");
                    
                    // 注意：在控制台应用中不能直接显示对话框
                    // 这里只测试对话框的创建和基本功能
                    Console.WriteLine("   ✓ 错误摘要对话框基本功能测试通过");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"   ✗ 错误摘要对话框测试失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 测试进度消息解析
        /// </summary>
        private static void TestProgressMessageParsing()
        {
            try
            {
                Console.WriteLine("   测试进度消息解析...");
                var errorCollector = new ErrorCollector();

                // 测试不同格式的进度消息
                var testMessages = new[]
                {
                    "[MODULE_FAILED]封面:模板文件不存在",
                    "[ERROR]资源文件验证失败: 找不到CGB.xls文件",
                    "[WARNING]用户参数加载失败，使用默认值",
                    "[MODULE_SUCCESS]目录:模块生成成功",
                    "[INFO]开始生成报告模块..."
                };

                foreach (var message in testMessages)
                {
                    errorCollector.ParseAndAddErrorFromProgressMessage(message, "测试模块");
                }

                var parsedErrors = errorCollector.GetAllErrors();
                Console.WriteLine($"   ✓ 成功解析 {parsedErrors.Count} 个错误消息");

                // 验证解析结果
                foreach (var error in parsedErrors)
                {
                    Console.WriteLine($"   - {error.Type}: {error.Message} (严重程度: {error.Severity})");
                }

                Console.WriteLine("   ✓ 进度消息解析测试通过");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"   ✗ 进度消息解析测试失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 测试错误统计功能
        /// </summary>
        private static void TestErrorStatistics()
        {
            try
            {
                Console.WriteLine("   测试错误统计功能...");
                var errorCollector = new ErrorCollector();
                var testErrors = CreateTestErrors();

                foreach (var error in testErrors)
                {
                    errorCollector.AddError(error);
                }

                // 测试统计功能
                var typeStats = errorCollector.GetErrorStatistics();
                var severityStats = errorCollector.GetSeverityStatistics();

                Console.WriteLine("   错误类型统计:");
                foreach (var stat in typeStats)
                {
                    Console.WriteLine($"   - {stat.Key}: {stat.Value} 个");
                }

                Console.WriteLine("   严重程度统计:");
                foreach (var stat in severityStats)
                {
                    Console.WriteLine($"   - {stat.Key}: {stat.Value} 个");
                }

                Console.WriteLine("   ✓ 错误统计功能测试通过");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"   ✗ 错误统计功能测试失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 创建测试错误数据
        /// </summary>
        private static List<ErrorInfo> CreateTestErrors()
        {
            return new List<ErrorInfo>
            {
                new ErrorInfo
                {
                    Type = ErrorType.MissingFile,
                    Severity = ErrorSeverity.Error,
                    ModuleName = "封面",
                    Message = "模板文件不存在",
                    Details = "找不到封面模板文件 cover_template.docx",
                    SuggestedSolution = "请检查模板目录中是否包含封面模板文件"
                },
                new ErrorInfo
                {
                    Type = ErrorType.DataValidation,
                    Severity = ErrorSeverity.Warning,
                    ModuleName = "项目基本信息",
                    Message = "项目名称为空",
                    SuggestedSolution = "请在参数设置中填写项目名称"
                },
                new ErrorInfo
                {
                    Type = ErrorType.DocumentGeneration,
                    Severity = ErrorSeverity.Error,
                    ModuleName = "楼栋基本信息",
                    Message = "Excel数据读取失败",
                    Details = "无法读取CGB.xls文件中的楼栋信息",
                    SuggestedSolution = "请检查Excel文件格式和内容是否正确"
                },
                new ErrorInfo
                {
                    Type = ErrorType.TemplateProcessing,
                    Severity = ErrorSeverity.Error,
                    ModuleName = "房产面积汇总表",
                    Message = "模板表格结构不匹配",
                    Details = "模板中缺少必需的表格列",
                    SuggestedSolution = "请使用正确版本的模板文件"
                },
                new ErrorInfo
                {
                    Type = ErrorType.SystemException,
                    Severity = ErrorSeverity.Critical,
                    ModuleName = "系统",
                    Message = "内存不足",
                    Details = "OutOfMemoryException: 处理大型文档时内存不足",
                    SuggestedSolution = "请关闭其他应用程序或增加系统内存",
                    Exception = new OutOfMemoryException("处理大型文档时内存不足")
                }
            };
        }

        /// <summary>
        /// 演示错误摘要对话框（需要在Windows Forms应用中调用）
        /// </summary>
        public static void DemonstrateErrorSummaryDialog()
        {
            try
            {
                var errors = CreateTestErrors();
                using (var dialog = new ErrorSummaryDialog(errors))
                {
                    dialog.ShowDialog();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"演示错误摘要对话框时出错: {ex.Message}", "错误", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }
}
