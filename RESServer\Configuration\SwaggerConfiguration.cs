using Microsoft.OpenApi.Models;
using Swashbuckle.AspNetCore.SwaggerGen;

namespace RESServer.Configuration
{
    /// <summary>
    /// Swagger 文件上传操作过滤器
    /// </summary>
    public class FileUploadOperationFilter : IOperationFilter
    {
        /// <summary>
        /// 应用文件上传参数配置
        /// </summary>
        public void Apply(OpenApiOperation operation, OperationFilterContext context)
        {
            var fileUploadMethods = context.MethodInfo.GetParameters()
                .Where(p => p.ParameterType == typeof(IFormFile) || 
                            p.ParameterType == typeof(IFormFileCollection) || 
                            (p.ParameterType.IsGenericType && 
                             p.ParameterType.GetGenericTypeDefinition() == typeof(List<>) && 
                             p.ParameterType.GetGenericArguments()[0] == typeof(IFormFile)))
                .Select(p => p.Name)
                .ToList();

            if (fileUploadMethods.Any())
            {
                operation.RequestBody = new OpenApiRequestBody
                {
                    Content = new Dictionary<string, OpenApiMediaType>
                    {
                        ["multipart/form-data"] = new OpenApiMediaType
                        {
                            Schema = new OpenApiSchema
                            {
                                Type = "object",
                                Properties = fileUploadMethods.ToDictionary(
                                    name => name,
                                    name => new OpenApiSchema
                                    {
                                        Type = "string",
                                        Format = "binary"
                                    }
                                ),
                                Required = new HashSet<string>(fileUploadMethods)
                            }
                        }
                    }
                };
            }
        }
    }
} 