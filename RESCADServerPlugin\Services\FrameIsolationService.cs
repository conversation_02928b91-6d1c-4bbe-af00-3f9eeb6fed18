using Autodesk.AutoCAD.ApplicationServices;
using Autodesk.AutoCAD.DatabaseServices;
using System;
using System.Collections.Concurrent;
using System.Diagnostics;
using System.IO;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace RESCADServerPlugin.Services
{
    public class FrameIsolationService
    {
        private readonly string _tempDirectory;
        private readonly ConcurrentQueue<ProcessingRequest> _processingQueue = new ConcurrentQueue<ProcessingRequest>();
        private readonly SemaphoreSlim _processingLock = new SemaphoreSlim(1, 1);
        private bool _isProcessing = false;
        private readonly string _logFilePath;
        
        // Singleton instance
        private static FrameIsolationService _instance;
        private static readonly object _lock = new object();
        
        public static FrameIsolationService Instance
        {
            get
            {
                if (_instance == null)
                {
                    lock (_lock)
                    {
                        if (_instance == null)
                        {
                            _instance = new FrameIsolationService();
                        }
                    }
                }
                return _instance;
            }
        }
        
        // Private constructor to enforce singleton pattern
        private FrameIsolationService()
        {
            // Create a temporary directory for processing files
            string pluginDir = Path.GetDirectoryName(System.Reflection.Assembly.GetExecutingAssembly().Location);
            _tempDirectory = Path.Combine(pluginDir, "TempFrameIsolation");
            
            if (!Directory.Exists(_tempDirectory))
            {
                Directory.CreateDirectory(_tempDirectory);
            }
            
            // 创建日志文件路径
            _logFilePath = Path.Combine(pluginDir, "FrameIsolationService.log");
            LogToFile("FrameIsolationService 初始化", true); // 第二个参数表示是否清空现有日志
            
            // Start the processing thread
            Task.Run(() => ProcessQueueAsync());
        }
        
        /// <summary>
        /// 记录日志到文件
        /// </summary>
        /// <param name="message">日志消息</param>
        /// <param name="clearLog">是否清空现有日志</param>
        private void LogToFile(string message, bool clearLog = false)
        {
            try
            {
                string logMessage = $"[{DateTime.Now:yyyy-MM-dd HH:mm:ss.fff}] {message}";
                
                // 创建包含调用栈信息的更详细日志
                var stackTrace = new StackTrace(true);
                string callerInfo = "";
                
                if (stackTrace.FrameCount > 1)
                {
                    var frame = stackTrace.GetFrame(1); // 获取调用者信息
                    var method = frame.GetMethod();
                    callerInfo = $" [{method.DeclaringType}.{method.Name}]";
                }
                
                logMessage += callerInfo;
                
                // 将日志写入文件
                using (StreamWriter writer = new StreamWriter(_logFilePath, !clearLog, Encoding.UTF8))
                {
                    writer.WriteLine(logMessage);
                }
                
                // 同时输出到调试窗口
                System.Diagnostics.Debug.WriteLine(logMessage);
                
                // 如果有活动文档，也输出到命令行
                Document doc = Application.DocumentManager.MdiActiveDocument;
                if (doc != null)
                {
                    doc.Editor.WriteMessage($"\n{logMessage}");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"记录日志时出错: {ex.Message}");
            }
        }
        
        // Request processing class to hold request information
        private class ProcessingRequest
        {
            public string FilePath { get; set; }
            public TaskCompletionSource<string> CompletionSource { get; set; }
        }
        
        /// <summary>
        /// Processes a DWG file, isolates the print frame and exports it with drawing number as filename
        /// </summary>
        /// <param name="uploadedFilePath">Path to the uploaded DWG file</param>
        /// <returns>Path to the exported DWG file with the isolated frame</returns>
        public Task<string> ProcessDwgFile(string uploadedFilePath)
        {
            Document doc = Application.DocumentManager.MdiActiveDocument;
            if (doc != null)
            {
                doc.Editor.WriteMessage($"\n添加处理请求到队列: {Path.GetFileName(uploadedFilePath)}");
            }
            
            // Create a completion source for this request
            var completionSource = new TaskCompletionSource<string>();
            
            // Add to the processing queue
            _processingQueue.Enqueue(new ProcessingRequest
            {
                FilePath = uploadedFilePath,
                CompletionSource = completionSource
            });
            
            // Trigger processing if not already running
            TriggerProcessing();
            
            return completionSource.Task;
        }
        
        /// <summary>
        /// Triggers the queue processing if it's not already running
        /// </summary>
        private void TriggerProcessing()
        {
            if (!_isProcessing)
            {
                lock (_lock)
                {
                    if (!_isProcessing)
                    {
                        _isProcessing = true;
                        Task.Run(() => ProcessQueueAsync());
                    }
                }
            }
        }
        
        /// <summary>
        /// Processes the queue of requests
        /// </summary>
        private async Task ProcessQueueAsync()
        {
            while (_processingQueue.TryDequeue(out ProcessingRequest request))
            {
                Document doc = Application.DocumentManager.MdiActiveDocument;
                if (doc != null)
                {
                    doc.Editor.WriteMessage($"\n开始处理文件: {Path.GetFileName(request.FilePath)}");
                    doc.Editor.WriteMessage($"\n当前队列中剩余任务: {_processingQueue.Count}");
                }
                
                try
                {
                    // Ensure only one file is processed at a time
                    await _processingLock.WaitAsync();
                    
                    try
                    {
                        // Process the file
                        string result = await ProcessDwgFileInternal(request.FilePath);
                        request.CompletionSource.SetResult(result);
                    }
                    catch (Exception ex)
                    {
                        request.CompletionSource.SetException(ex);
                    }
                    finally
                    {
                        _processingLock.Release();
                    }
                }
                catch (Exception ex)
                {
                    request.CompletionSource.SetException(ex);
                    
                    if (doc != null)
                    {
                        doc.Editor.WriteMessage($"\n处理队列任务时出错: {ex.Message}");
                    }
                }
            }
            
            // No more items in the queue
            _isProcessing = false;
        }
        
        /// <summary>
        /// Internal method to process a DWG file
        /// </summary>
        private Task<string> ProcessDwgFileInternal(string uploadedFilePath)
        {
            var tcs = new TaskCompletionSource<string>();
            
            // Set up a 5-minute timeout for the entire operation.
            var cts = new CancellationTokenSource(TimeSpan.FromMinutes(5));
            cts.Token.Register(() => {
                // This will be called if the CancellationToken is cancelled (i.e., on timeout).
                // We attempt to set an exception on the TaskCompletionSource. 
                // TrySetException returns false if the task is already completed.
                tcs.TrySetException(new TimeoutException("等待 'RESSAVEPRINTDRAWINGS' 命令完成超时 (5 分钟)。"));
            });

            // The core logic must run in the AutoCAD main thread context.
            Application.DocumentManager.ExecuteInApplicationContext(state => {
                Document doc = null;
                CommandEventHandler commandEndedHandler = null;

                commandEndedHandler = (s, e) => {
                    // Check if the correct command has ended.
                    if (e.GlobalCommandName.Equals("RESSAVEPRINTDRAWINGS", StringComparison.OrdinalIgnoreCase))
                    {
                        var eventDoc = s as Document;
                        if (eventDoc == null)
                        {
                            tcs.TrySetException(new InvalidOperationException("CommandEnded 事件未能在有效的文档对象上触发。"));
                            cts.Dispose(); // Clean up the CancellationTokenSource
                            return;
                        }

                        // Unsubscribe from the event to prevent memory leaks.
                        eventDoc.CommandEnded -= commandEndedHandler;
                        LogToFile($"命令 'RESSAVEPRINTDRAWINGS' 已完成。");
                        
                        try 
                        {
                            LogToFile("命令成功完成。准备保存文档。");
                            string resultPath = eventDoc.Name;
                            // The command modified the document, now we save it.
                            eventDoc.Database.SaveAs(resultPath, true, DwgVersion.Current, eventDoc.Database.SecurityParameters);
                            LogToFile($"文档已保存: {resultPath}");

                            // Close the document without saving changes again.
                            if (!eventDoc.IsDisposed)
                            {
                                eventDoc.CloseAndDiscard();
                                LogToFile($"文档已关闭: {uploadedFilePath}");
                            }
                            
                            // Try to set the result. This will do nothing if the task was already cancelled by the timeout.
                            tcs.TrySetResult(resultPath);
                        }
                        catch (Exception ex)
                        {
                            LogToFile($"在命令完成后的处理中出错: {ex.Message}\n{ex.StackTrace}");
                            tcs.TrySetException(ex);
                        }
                        finally
                        {
                            cts.Dispose(); // Clean up the CancellationTokenSource
                        }
                    }
                };

                try
                {
                    LogToFile($"准备打开文件并执行命令: {uploadedFilePath}");

                    // Open the document with write access
                    doc = Application.DocumentManager.Open(uploadedFilePath, false);
                    Application.DocumentManager.MdiActiveDocument = doc;
                    
                    LogToFile($"文档已打开并激活: {doc.Name}");
                    
                    // Subscribe to the event.
                    doc.CommandEnded += commandEndedHandler;

                    // Send the command to execute. The space at the end acts as pressing Enter.
                    // This is non-blocking. The method will return, and the work will be finished by the event handler.
                    LogToFile("发送 'RESSAVEPRINTDRAWINGS' 命令...");
                    doc.SendStringToExecute("RESSavePrintDrawings ", true, false, true);
                }
                catch (Exception ex)
                {
                    LogToFile($"在主线程准备执行命令时出错: {ex.Message}\n{ex.StackTrace}");
                    // If an error occurs here, we must clean up and complete the task.
                    if (doc != null)
                    {
                        doc.CommandEnded -= commandEndedHandler;
                        if (!doc.IsDisposed)
                        {
                            doc.CloseAndDiscard();
                        }
                    }
                    tcs.TrySetException(ex);
                    cts.Dispose(); // Clean up the CancellationTokenSource
                }
            }, null);

            return tcs.Task;
        }
        
        /// <summary>
        /// Gets the current queue status
        /// </summary>
        public int GetQueueLength()
        {
            return _processingQueue.Count;
        }
        
        /// <summary>
        /// Cleans up temporary files older than a specified time
        /// </summary>
        public void CleanupTempFiles(TimeSpan maxAge)
        {
            try
            {
                if (!Directory.Exists(_tempDirectory))
                {
                    return;
                }
                
                foreach (string dir in Directory.GetDirectories(_tempDirectory))
                {
                    DirectoryInfo dirInfo = new DirectoryInfo(dir);
                    if (DateTime.Now - dirInfo.CreationTime > maxAge)
                    {
                        try
                        {
                            Directory.Delete(dir, true);
                        }
                        catch (Exception)
                        {
                            // Ignore errors when cleaning up
                        }
                    }
                }
            }
            catch (Exception)
            {
                // Ignore errors when cleaning up
            }
        }
    }
} 