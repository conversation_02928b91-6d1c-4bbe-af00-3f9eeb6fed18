using System;
using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;
using RESClient.MVP.Models;

namespace RESClient.Services.Implementations
{
    /// <summary>
    /// 经主管部门批准的相关证照模块生成器
    /// </summary>
    public class CertificatesModuleGenerator : IReportModuleGenerator
    {
        private readonly CertificatesParametersModel _parametersModel;
        private readonly CertificatesModel _certificatesModel;

        /// <summary>
        /// 模块名称
        /// </summary>
        public string ModuleName => "经主管部门批准的相关证照";

        /// <summary>
        /// 构造函数
        /// </summary>
        public CertificatesModuleGenerator()
        {
            _parametersModel = new CertificatesParametersModel();
            _certificatesModel = new CertificatesModel();
        }

        /// <summary>
        /// 生成报告模块
        /// </summary>
        /// <param name="parameters">生成参数</param>
        /// <param name="progressCallback">进度回调</param>
        /// <returns>生成结果</returns>
        public async Task<bool> GenerateAsync(Dictionary<string, object> parameters, Action<int, string> progressCallback)
        {
            try
            {
                progressCallback?.Invoke(0, "[INFO]开始生成经主管部门批准的相关证照模块...");

                // 获取输出目录 - 支持两种参数键名
                object outputDirObj = null;
                if (!parameters.TryGetValue("OutputDirectory", out outputDirObj) ||
                    string.IsNullOrEmpty(outputDirObj?.ToString()))
                {
                    // 尝试使用 OutputDir 参数键
                    if (!parameters.TryGetValue("OutputDir", out outputDirObj) ||
                        string.IsNullOrEmpty(outputDirObj?.ToString()))
                    {
                        progressCallback?.Invoke(0, "[ERROR]未指定输出目录 (需要 OutputDirectory 或 OutputDir 参数)");
                        return false;
                    }
                }

                string outputDirectory = outputDirObj.ToString();
                progressCallback?.Invoke(10, "[INFO]输出目录: " + outputDirectory);

                // 获取参数或使用当前保存的参数
                var certificatesParameters = GetParametersFromDictionary(parameters) ?? _parametersModel.GetCurrentParameters();
                progressCallback?.Invoke(30, "[INFO]获取参数完成");

                // 验证参数
                var validationResult = certificatesParameters.Validate();
                if (!validationResult.IsValid)
                {
                    string errorMsg = "[ERROR]参数验证失败: " + string.Join(", ", validationResult.Errors);
                    progressCallback?.Invoke(0, errorMsg);
                    return false;
                }

                progressCallback?.Invoke(50, "[INFO]参数验证通过");

                // 转换为CertificatesModel所需的数据格式
                var certificatesData = ConvertToCertificatesData(certificatesParameters);
                progressCallback?.Invoke(60, "[INFO]转换参数格式");

                // 生成输出文件路径
                string outputPath = Path.Combine(outputDirectory, $"{ModuleName}.docx");
                progressCallback?.Invoke(70, "[INFO]准备生成文档: " + Path.GetFileName(outputPath));

                // 调用CertificatesModel生成经主管部门批准的相关证照
                bool result = await _certificatesModel.FillCertificatesTemplateAsync(certificatesData, outputPath);

                if (result)
                {
                    progressCallback?.Invoke(100, "[SUCCESS]经主管部门批准的相关证照生成完成");
                    return true;
                }
                else
                {
                    progressCallback?.Invoke(100, "[ERROR]经主管部门批准的相关证照生成失败");
                    return false;
                }
            }
            catch (Exception ex)
            {
                progressCallback?.Invoke(100, $"[ERROR]经主管部门批准的相关证照生成过程中发生异常: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 转换参数格式
        /// </summary>
        /// <param name="parameters">经主管部门批准的相关证照参数</param>
        /// <returns>CertificatesModel所需的数据格式</returns>
        private CertificatesModel.CertificatesData ConvertToCertificatesData(CertificatesParametersModel.CertificatesParameters parameters)
        {
            return new CertificatesModel.CertificatesData
            {
                PlanningPermit = parameters.PlanningPermit,
                PlanningLayout = parameters.PlanningLayout,
                ConstructionPermit = parameters.ConstructionPermit,
                ReviewReportIssuer = parameters.ReviewReportIssuer,
                ProjectNumber = parameters.ProjectNumber,
                PoliceStationAddressProof = parameters.PoliceStationAddressProof,
                BuildingNameRegistrationNotice = parameters.BuildingNameRegistrationNotice
            };
        }

        /// <summary>
        /// 从参数字典中获取参数
        /// </summary>
        /// <param name="parameters">参数字典</param>
        /// <returns>参数对象，如果无法获取则返回null</returns>
        private CertificatesParametersModel.CertificatesParameters GetParametersFromDictionary(Dictionary<string, object> parameters)
        {
            try
            {
                var certificatesParams = new CertificatesParametersModel.CertificatesParameters();

                if (parameters.TryGetValue("PlanningPermit", out var planningPermit))
                    certificatesParams.PlanningPermit = planningPermit?.ToString() ?? "/";

                if (parameters.TryGetValue("PlanningLayout", out var planningLayout))
                    certificatesParams.PlanningLayout = planningLayout?.ToString() ?? "/";

                if (parameters.TryGetValue("ConstructionPermit", out var constructionPermit))
                    certificatesParams.ConstructionPermit = constructionPermit?.ToString() ?? "/";

                if (parameters.TryGetValue("ReviewReportIssuer", out var reviewReportIssuer))
                    certificatesParams.ReviewReportIssuer = reviewReportIssuer?.ToString() ?? "/";

                if (parameters.TryGetValue("ProjectNumber", out var projectNumber))
                    certificatesParams.ProjectNumber = projectNumber?.ToString() ?? "/";

                if (parameters.TryGetValue("PoliceStationAddressProof", out var policeStationAddressProof))
                    certificatesParams.PoliceStationAddressProof = policeStationAddressProof?.ToString() ?? "/";

                if (parameters.TryGetValue("BuildingNameRegistrationNotice", out var buildingNameRegistrationNotice))
                    certificatesParams.BuildingNameRegistrationNotice = buildingNameRegistrationNotice?.ToString() ?? "/";

                return certificatesParams;
            }
            catch
            {
                return null;
            }
        }

        /// <summary>
        /// 是否可用
        /// </summary>
        /// <param name="parameters">检查参数</param>
        /// <returns>是否可用</returns>
        public bool IsAvailable(Dictionary<string, object> parameters)
        {
            try
            {
                // 检查模板文件是否存在
                string templatePath = Path.Combine(
                    AppDomain.CurrentDomain.BaseDirectory,
                    Program.TemplateDirectory,
                    "07_经主管部门批准的相关证照",
                    "经主管部门批准的相关证照.docx");

                return File.Exists(templatePath);
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 获取参数模型（用于外部访问）
        /// </summary>
        /// <returns>参数模型</returns>
        public CertificatesParametersModel GetParametersModel()
        {
            return _parametersModel;
        }
    }
}