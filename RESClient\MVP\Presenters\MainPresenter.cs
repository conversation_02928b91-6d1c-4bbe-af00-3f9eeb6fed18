using System;
using System.Collections.Generic;
using System.Linq;
using System.IO;
using System.Threading.Tasks;
using System.Windows.Forms;
using RESClient.MVP.Interfaces;
using RESClient.MVP.Models;
using RESClient.MVP.Views;
using RESClient.Services;

namespace RESClient.MVP.Presenters
{
    /// <summary>
    /// 主窗体表示器
    /// </summary>
    public class MainPresenter
    {
        private readonly IMainView _view;
        private readonly IMainModel _model;

        /// <summary>
        /// 获取模型实例（用于参数输入表单访问错误收集器）
        /// </summary>
        internal MainModel Model => _model as MainModel;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="view">视图</param>
        /// <param name="model">模型</param>
        public MainPresenter(IMainView view, IMainModel model)
        {
            _view = view ?? throw new ArgumentNullException(nameof(view));
            _model = model ?? throw new ArgumentNullException(nameof(model));

            // 注册事件
            _view.GenerateReportRequested += OnGenerateReportRequested;
            _view.TestFloorPlanRequested += OnTestFloorPlanRequested;
            _view.ManageTemplatesRequested += OnManageTemplatesRequested;

            // 初始化视图
            InitializeViewAsync();
        }

        /// <summary>
        /// 初始化视图
        /// </summary>
        private async void InitializeViewAsync()
        {
            try
            {
                // 获取目录结构信息
                var directoryInfo = _model.GetDirectoryInfo();
                _view.UpdateDirectoryInfo(directoryInfo);

                // 获取模板列表
                var templates = await _model.GetTemplateListAsync();
                _view.UpdateTemplateList(templates);
            }
            catch (Exception ex)
            {
                _view.ShowError($"初始化失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 生成报告请求处理
        /// </summary>
        /// <param name="sender">事件发送者</param>
        /// <param name="e">事件参数</param>
        private async void OnGenerateReportRequested(object sender, EventArgs e)
        {
            try
            {
                // 显示进度条
                _view.ShowProgressBar(true);

                // 获取用户选中的模块
                var selectedModules = _view.GetSelectedModules();
                if (selectedModules.Count == 0)
                {
                    _view.ShowError("请至少选择一个要生成的模块");
                    _view.ShowProgressBar(false);
                    return;
                }

                // 获取用户选择的数据文件夹
                var dataFolder = _view.GetSelectedDataFolder();
                if (string.IsNullOrEmpty(dataFolder))
                {
                    _view.ShowError("请选择资料文件夹");
                    _view.ShowProgressBar(false);
                    return;
                }

                // 准备参数
                var parameters = new Dictionary<string, object>
                {
                    ["SelectedModules"] = selectedModules,
                    ["DataFolder"] = dataFolder
                };

                // 调用模型生成报告
                bool result = await _model.GenerateReportAsync(parameters, UpdateProgressStatus);

                // 更新状态并显示错误摘要
                if (result)
                {
                    // 检查是否有错误或警告
                    if (_model.ErrorCollector.HasErrors())
                    {
                        var errors = _model.ErrorCollector.GetAllErrors();
                        var hasActualErrors = errors.Any(error =>
                            error.Severity == ErrorSeverity.Error ||
                            error.Severity == ErrorSeverity.Critical);
                        var hasWarningsOnly = errors.All(error =>
                            error.Severity == ErrorSeverity.Warning ||
                            error.Severity == ErrorSeverity.Info);

                        if (hasWarningsOnly)
                        {
                            // 仅有警告时，只显示成功消息，不显示错误对话框
                            // 警告信息仅记录到系统日志中
                            _view.ShowMessage("报告生成成功！已完成模块生成和文档合并，请查看输出目录。");
                        }
                        else if (hasActualErrors)
                        {
                            // 有实际错误时，显示错误摘要对话框
                            _view.ShowMessage("报告生成成功！已完成模块生成和文档合并，请查看输出目录。");
                            ShowErrorSummary();
                        }
                        else
                        {
                            _view.ShowMessage("报告生成成功！已完成模块生成和文档合并，请查看输出目录。");
                        }
                    }
                    else
                    {
                        _view.ShowMessage("报告生成成功！已完成模块生成和文档合并，请查看输出目录。");
                    }

                    OpenOutputDirectory();
                }
                else
                {
                    // 生成失败时，显示错误摘要对话框
                    ShowErrorSummary();
                }
            }
            catch (Exception ex)
            {
                // 添加异常到错误收集器
                _model.ErrorCollector.AddError(ErrorType.SystemException, ErrorSeverity.Critical,
                    "系统", $"生成报告时出错: {ex.Message}",
                    details: ex.StackTrace,
                    suggestedSolution: "请联系技术支持，提供详细的错误信息",
                    exception: ex);

                // 显示错误摘要
                ShowErrorSummary();
            }
            finally
            {
                // 隐藏进度条
                _view.ShowProgressBar(false);
            }
        }

        /// <summary>
        /// 打开输出目录
        /// </summary>
        private void OpenOutputDirectory()
        {
            try
            {
                var directoryInfo = _model.GetDirectoryInfo();
                if (directoryInfo.TryGetValue("OutputDir", out var outputDir) &&
                    directoryInfo.TryGetValue("TestUser", out var userDir))
                {
                    var finalOutputDir = Path.Combine(outputDir, userDir);
                    if (Directory.Exists(finalOutputDir))
                    {
                        System.Diagnostics.Process.Start("explorer.exe", finalOutputDir);
                    }
                    else
                    {
                        _view.ShowError($"输出目录不存在: {finalOutputDir}");
                    }
                }
                else
                {
                    _view.ShowError("无法确定输出目录。");
                }
            }
            catch (Exception ex)
            {
                _view.ShowError($"打开输出目录时出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 测试分层图请求处理
        /// </summary>
        /// <param name="sender">事件发送者</param>
        /// <param name="e">事件参数</param>
        private async void OnTestFloorPlanRequested(object sender, EventArgs e)
        {
            try
            {
                // 显示进度条
                _view.ShowProgressBar(true);

                // 调用模型进行测试
                bool result = await _model.TestFloorPlanGenerationAsync(UpdateProgressStatus);

                // 更新状态
                if (result)
                {
                    _view.ShowMessage("测试分层图生成成功");
                }
                else
                {
                    _view.ShowError("测试分层图生成失败");
                }
            }
            catch (Exception ex)
            {
                _view.ShowError($"测试分层图时出错: {ex.Message}");
            }
            finally
            {
                // 隐藏进度条
                _view.ShowProgressBar(false);
            }
        }

        /// <summary>
        /// 管理模板请求处理
        /// </summary>
        /// <param name="sender">事件发送者</param>
        /// <param name="e">事件参数</param>
        private void OnManageTemplatesRequested(object sender, EventArgs e)
        {
            try
            {
                // 获取模板目录
                var directoryInfo = _model.GetDirectoryInfo();
                if (directoryInfo.TryGetValue("TemplatesDir", out string templatesDir))
                {
                    // 打开模板目录
                    System.Diagnostics.Process.Start("explorer.exe", templatesDir);
                }
                else
                {
                    _view.ShowError("未找到模板目录");
                }
            }
            catch (Exception ex)
            {
                _view.ShowError($"打开模板目录时出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 更新进度状态
        /// </summary>
        /// <param name="percent">百分比</param>
        /// <param name="status">状态信息</param>
        private void UpdateProgressStatus(int percent, string status)
        {
            // 在UI线程更新进度
            if (_view.InvokeRequired)
            {
                _view.Invoke(new Action(() =>
                {
                    _view.UpdateProgress(percent, status);
                }));
            }
            else
            {
                _view.UpdateProgress(percent, status);
            }
        }

        /// <summary>
        /// 显示错误摘要对话框
        /// </summary>
        private void ShowErrorSummary()
        {
            try
            {
                var errors = _model.ErrorCollector.GetAllErrors();

                // 在UI线程显示错误摘要
                if (_view.InvokeRequired)
                {
                    _view.Invoke(new Action(() =>
                    {
                        using (var errorDialog = new ErrorSummaryDialog(errors))
                        {
                            errorDialog.ShowDialog();
                        }
                    }));
                }
                else
                {
                    using (var errorDialog = new ErrorSummaryDialog(errors))
                    {
                        errorDialog.ShowDialog();
                    }
                }
            }
            catch (Exception ex)
            {
                // 如果显示错误摘要失败，回退到简单的错误消息
                _view.ShowError($"显示错误摘要时出错: {ex.Message}");
            }
        }
    }
}