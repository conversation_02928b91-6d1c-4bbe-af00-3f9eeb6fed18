# RESServer API Endpoints

## Drawing Processing Endpoints

### 1. Process Drawing (Split Print Drawings)
**Endpoint:** `POST /api/DrawingProcessing/process`

**Description:** Uploads and processes a DWG file using the `RESSavePrintDrawings` command to split print drawings.

**Request:**
- Content-Type: `multipart/form-data`
- Parameter: `file` (DWG file, max 100MB)

**Response:**
```json
{
  "success": true,
  "message": "DWG 文件已上传，正在处理中",
  "sessionId": "unique-session-id",
  "fileType": null,
  "fileCount": 0
}
```

### 2. Generate Floor Plans (NEW)
**Endpoint:** `POST /api/DrawingProcessing/generate-floor-plans`

**Description:** Uploads and processes a DWG file using the `RES_GENERATE_FLOOR_PLANS` command to generate floor plans.

**Request:**
- Content-Type: `multipart/form-data`
- Parameter: `file` (DWG file, max 100MB)

**Response:**
```json
{
  "success": true,
  "message": "DWG 文件已上传，正在生成平面图",
  "sessionId": "unique-session-id",
  "fileType": null,
  "fileCount": 0
}
```

### 3. Get Session Status
**Endpoint:** `GET /api/DrawingProcessing/status/{sessionId}`

**Description:** Gets the processing status for a specific session.

**Response:**
```json
{
  "sessionId": "session-id",
  "percentComplete": 0,
  "currentOperation": "正在处理中...",
  "isCompleted": false,
  "hasError": false,
  "errorMessage": null,
  "lastUpdated": "2024-01-01T00:00:00Z",
  "resultFilePath": "",
  "fileName": null,
  "fileSize": 0,
  "fileCount": 5,
  "dwgFileCount": 3,
  "gifFileCount": 2
}
```

### 4. Download Results
**Endpoint:** `GET /api/DrawingProcessing/download/{sessionId}`

**Description:** Downloads the processing results as a ZIP file.

**Response:** ZIP file containing the processed drawings.

### 5. Get Session Files
**Endpoint:** `GET /api/DrawingProcessing/files/{sessionId}`

**Description:** Gets the list of files in the processing result directory.

**Response:**
```json
[
  {
    "fileName": "drawing1.dwg",
    "filePath": null,
    "fileSize": 1024,
    "lastModified": "2024-01-01T00:00:00Z",
    "isDirectory": false,
    "isInProcessingDir": true
  }
]
```

## Key Differences Between Endpoints

### Process Drawing vs Generate Floor Plans

| Feature | Process Drawing | Generate Floor Plans |
|---------|----------------|---------------------|
| CAD Command | `RESSavePrintDrawings` | `RES_GENERATE_FLOOR_PLANS` |
| Purpose | Split print drawings | Generate floor plans |
| Script File | Uses shared script | Uses session-specific script |
| Output | Split drawing files | Floor plan files |

## Testing with Swagger

1. Start the RESServer with administrator privileges
2. Navigate to `https://localhost:7xxx/swagger` (replace with actual port)
3. Find the "DrawingProcessing" section
4. Test the new `POST /api/DrawingProcessing/generate-floor-plans` endpoint
5. Upload a DWG file and get a session ID
6. Use the session ID with other endpoints to monitor progress and download results

## Error Handling

Both endpoints handle the same error scenarios:
- Missing file (400 Bad Request)
- Invalid file type (400 Bad Request)
- Processing errors (500 Internal Server Error)
- Timeout errors (500 Internal Server Error)

## Session Management

Both endpoints use the same session management system:
- Unique session IDs generated for each request
- Background processing with completion markers
- Shared status monitoring and file download endpoints
