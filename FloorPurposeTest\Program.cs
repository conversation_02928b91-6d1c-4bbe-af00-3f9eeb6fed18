﻿using System;
using System.Collections.Generic;
using System.Linq;

namespace FloorPurposeTest
{
    /// <summary>
    /// Test program to verify the new floor purpose formatting logic
    /// </summary>
    class Program
    {
        static void Main(string[] args)
        {
            Console.WriteLine("Testing Floor Purpose Formatting Logic");
            Console.WriteLine("=====================================");

            // Test Case 1: Mixed floors with different purposes
            var testCase1 = new Dictionary<int, HashSet<string>>
            {
                { 1, new HashSet<string> { "架空部分(公共活动空间)" } },
                { 2, new HashSet<string> { "公寓" } },
                { 3, new HashSet<string> { "住宅", "公寓" } },
                { 4, new HashSet<string> { "住宅" } },
                { 5, new HashSet<string> { "住宅" } },
                { 6, new HashSet<string> { "住宅" } },
                { 7, new HashSet<string> { "住宅" } },
                { 8, new HashSet<string> { "住宅" } },
                { 9, new HashSet<string> { "住宅" } },
                { 10, new HashSet<string> { "住宅" } },
                { 11, new HashSet<string> { "住宅" } },
                { 12, new HashSet<string> { "住宅" } },
                { 13, new HashSet<string> { "住宅" } },
                { 14, new HashSet<string> { "住宅" } },
                { 15, new HashSet<string> { "住宅" } }
            };

            Console.WriteLine("Test Case 1 - Mixed floors:");
            string result1 = GetDesignPurposeText(testCase1);
            Console.WriteLine(result1);
            Console.WriteLine();

            // Test Case 2: Basement floors with multiple purposes
            var testCase2 = new Dictionary<int, HashSet<string>>
            {
                { -1, new HashSet<string> { "车位", "充电桩车位", "人防车位", "无障碍车位", "人防物管用房停车位", "人防无障碍车位", "人防", "走道(部分人防区域)", "非机动车库(部分人防区域)", "非机动车库", "物管用房(戊类库房)(部分人防区域)", "物管用房(戊类库房)", "消防控制室", "报警阀间(人防区域)", "报警阀间", "配电房(人防区域)", "配电房", "柴油发电机房", "弱电进线间", "蓄水池", "消防水泵房", "消防水池", "生活水箱间及生活水泵房", "雨水处理机房" } }
            };

            Console.WriteLine("Test Case 2 - Basement with multiple purposes:");
            string result2 = GetDesignPurposeText(testCase2);
            Console.WriteLine(result2);
            Console.WriteLine();

            // Test Case 3: Simple consecutive floors with same purpose
            var testCase3 = new Dictionary<int, HashSet<string>>
            {
                { 1, new HashSet<string> { "商业" } },
                { 2, new HashSet<string> { "商业" } },
                { 3, new HashSet<string> { "商业" } }
            };

            Console.WriteLine("Test Case 3 - Consecutive floors with same purpose:");
            string result3 = GetDesignPurposeText(testCase3);
            Console.WriteLine(result3);
            Console.WriteLine();

            Console.WriteLine("Press any key to exit...");
            Console.ReadKey();
        }

        /// <summary>
        /// 楼层范围数据类
        /// </summary>
        private class FloorRange
        {
            public int StartFloor { get; set; }
            public int EndFloor { get; set; }
            public string Purposes { get; set; }
        }

        /// <summary>
        /// 获取设计用途显示文本
        /// </summary>
        public static string GetDesignPurposeText(Dictionary<int, HashSet<string>> floorPurposes)
        {
            if (floorPurposes.Count == 0) return "\\";

            var result = new List<string>();

            // 按楼层排序处理
            var sortedFloors = floorPurposes.OrderBy(kvp => kvp.Key).ToList();

            // 新的逻辑：按楼层分组，每个楼层列出所有用途
            // 首先创建楼层到用途组合的映射
            var floorToPurposeGroups = new Dictionary<int, string>();

            foreach (var floorData in sortedFloors)
            {
                int floor = floorData.Key;
                var purposes = floorData.Value.OrderBy(p => p).ToList(); // 按用途排序以保持一致性

                // 将该楼层的所有用途用逗号连接
                string purposeText = string.Join("、", purposes);
                floorToPurposeGroups[floor] = purposeText;
            }

            // 现在尝试合并具有相同用途组合的连续楼层
            var mergedRanges = MergeConsecutiveFloorsWithSamePurposes(floorToPurposeGroups);

            // 生成最终的文本
            foreach (var range in mergedRanges)
            {
                string floorText;
                if (range.StartFloor == range.EndFloor)
                {
                    floorText = $"{range.StartFloor}层为{range.Purposes}";
                }
                else
                {
                    floorText = $"{range.StartFloor}-{range.EndFloor}层为{range.Purposes}";
                }
                result.Add(floorText);
            }

            return string.Join("；\n", result) + "；";
        }

        /// <summary>
        /// 合并具有相同用途组合的连续楼层
        /// </summary>
        private static List<FloorRange> MergeConsecutiveFloorsWithSamePurposes(Dictionary<int, string> floorToPurposeGroups)
        {
            var result = new List<FloorRange>();
            if (floorToPurposeGroups.Count == 0) return result;

            var sortedFloors = floorToPurposeGroups.OrderBy(kvp => kvp.Key).ToList();

            int currentStart = sortedFloors[0].Key;
            int currentEnd = sortedFloors[0].Key;
            string currentPurposes = sortedFloors[0].Value;

            for (int i = 1; i < sortedFloors.Count; i++)
            {
                int floor = sortedFloors[i].Key;
                string purposes = sortedFloors[i].Value;

                // 检查是否可以合并：楼层连续且用途相同
                if (floor == currentEnd + 1 && purposes == currentPurposes)
                {
                    // 扩展当前范围
                    currentEnd = floor;
                }
                else
                {
                    // 保存当前范围并开始新范围
                    result.Add(new FloorRange
                    {
                        StartFloor = currentStart,
                        EndFloor = currentEnd,
                        Purposes = currentPurposes
                    });

                    currentStart = floor;
                    currentEnd = floor;
                    currentPurposes = purposes;
                }
            }

            // 添加最后一个范围
            result.Add(new FloorRange
            {
                StartFloor = currentStart,
                EndFloor = currentEnd,
                Purposes = currentPurposes
            });

            return result;
        }
    }
}
