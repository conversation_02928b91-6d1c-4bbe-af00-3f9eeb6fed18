using System;
using System.Threading.Tasks;
using System.Windows.Forms;
using RESClient.Services;
using RESClient.Views;
using RESClient.Models;

namespace RESClient
{
    /// <summary>
    /// 测试登录窗体无限循环修复
    /// </summary>
    public class TestLoginFormInfiniteLoopFix
    {
        private static int _loginDialogCount = 0;
        private static int _authStatusEventCount = 0;
        
        /// <summary>
        /// 测试登录窗体不会出现无限循环
        /// </summary>
        public static async Task TestNoInfiniteLoop()
        {
            Console.WriteLine("=== 测试登录窗体无限循环修复 ===");
            
            try
            {
                // 重置计数器
                _loginDialogCount = 0;
                _authStatusEventCount = 0;
                
                // 初始化服务
                var configService = RESServerConfigService.Instance;
                var authService = new AuthService(configService);
                
                // 监听认证状态变更事件
                authService.AuthenticationStatusChanged += OnAuthenticationStatusChanged;
                
                Console.WriteLine("1. 测试LoginForm创建和服务器状态检查...");
                
                // 创建LoginForm但不显示，测试服务器状态检查
                using (var loginForm = new LoginForm(authService))
                {
                    Console.WriteLine("   ✅ LoginForm创建成功");
                    
                    // 等待一段时间让异步操作完成
                    await Task.Delay(2000);
                    
                    Console.WriteLine($"   认证状态事件触发次数: {_authStatusEventCount}");
                    
                    if (_authStatusEventCount <= 2) // 允许最多2次（初始检查 + 可能的重试）
                    {
                        Console.WriteLine("   ✅ 认证状态事件触发次数正常，未出现无限循环");
                    }
                    else
                    {
                        Console.WriteLine("   ❌ 认证状态事件触发次数过多，可能存在循环问题");
                    }
                }
                
                Console.WriteLine("\n2. 测试多次快速调用GetServerStatusAsync...");
                
                // 重置计数器
                _authStatusEventCount = 0;
                
                // 快速连续调用多次
                var tasks = new Task[5];
                for (int i = 0; i < 5; i++)
                {
                    tasks[i] = authService.GetServerStatusAsync();
                }
                
                await Task.WhenAll(tasks);
                
                Console.WriteLine($"   认证状态事件触发次数: {_authStatusEventCount}");
                
                if (_authStatusEventCount <= 10) // 允许一定的重复，但不应该无限
                {
                    Console.WriteLine("   ✅ 多次调用未导致无限循环");
                }
                else
                {
                    Console.WriteLine("   ❌ 多次调用可能导致过多事件触发");
                }
                
                Console.WriteLine("\n3. 测试不触发事件的版本...");
                
                // 重置计数器
                _authStatusEventCount = 0;
                
                // 使用不触发事件的版本
                for (int i = 0; i < 5; i++)
                {
                    await authService.GetServerStatusAsync(triggerEvent: false);
                }
                
                Console.WriteLine($"   认证状态事件触发次数: {_authStatusEventCount}");
                
                if (_authStatusEventCount == 0)
                {
                    Console.WriteLine("   ✅ 不触发事件的版本工作正常");
                }
                else
                {
                    Console.WriteLine("   ❌ 不触发事件的版本仍然触发了事件");
                }
                
                // 清理事件订阅
                authService.AuthenticationStatusChanged -= OnAuthenticationStatusChanged;
                
                Console.WriteLine("\n=== 测试完成 ===");
                
            }
            catch (Exception ex)
            {
                Console.WriteLine($"   ❌ 测试失败: {ex.Message}");
                Console.WriteLine($"   详细信息: {ex.StackTrace}");
            }
        }
        
        /// <summary>
        /// 模拟MainForm的登录对话框显示逻辑
        /// </summary>
        public static void TestMainFormLoginDialogLogic()
        {
            Console.WriteLine("\n=== 测试MainForm登录对话框逻辑 ===");
            
            try
            {
                // 模拟多次快速调用ShowLoginDialog的情况
                bool isLoginDialogShowing = false;
                
                for (int i = 0; i < 5; i++)
                {
                    Console.WriteLine($"第{i + 1}次调用ShowLoginDialog...");
                    
                    if (isLoginDialogShowing)
                    {
                        Console.WriteLine("   登录对话框已在显示中，跳过重复请求");
                        continue;
                    }
                    
                    isLoginDialogShowing = true;
                    _loginDialogCount++;
                    
                    Console.WriteLine($"   显示登录对话框 (总计: {_loginDialogCount})");
                    
                    // 模拟对话框显示和关闭
                    Task.Delay(100).ContinueWith(_ => {
                        isLoginDialogShowing = false;
                        Console.WriteLine("   登录对话框关闭");
                    });
                }
                
                Console.WriteLine($"\n总共显示了 {_loginDialogCount} 个登录对话框");
                
                if (_loginDialogCount == 1)
                {
                    Console.WriteLine("✅ 防重复显示逻辑工作正常");
                }
                else
                {
                    Console.WriteLine("❌ 防重复显示逻辑可能存在问题");
                }
                
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ 测试失败: {ex.Message}");
            }
        }
        
        /// <summary>
        /// 认证状态变更事件处理器
        /// </summary>
        private static void OnAuthenticationStatusChanged(object sender, AuthenticationStatusInfo status)
        {
            _authStatusEventCount++;
            Console.WriteLine($"   [事件 #{_authStatusEventCount}] 认证状态变更: {status.Status} - {status.Message}");
            
            // 检测可能的无限循环
            if (_authStatusEventCount > 20)
            {
                Console.WriteLine("   ⚠️  警告：认证状态事件触发次数过多，可能存在无限循环！");
            }
        }
        
        /// <summary>
        /// 运行所有测试
        /// </summary>
        public static async Task RunAllTests()
        {
            await TestNoInfiniteLoop();
            TestMainFormLoginDialogLogic();
            
            Console.WriteLine("\n=== 所有测试完成 ===");
            Console.WriteLine("\n修复要点总结:");
            Console.WriteLine("1. ✅ LoginForm只在OnShown事件中检查一次服务器状态");
            Console.WriteLine("2. ✅ MainForm使用标志防止多个登录对话框同时显示");
            Console.WriteLine("3. ✅ AuthService提供不触发事件的GetServerStatusAsync版本");
            Console.WriteLine("4. ✅ 改进了线程安全和异常处理");
            Console.WriteLine("5. ✅ 添加了防护逻辑避免认证状态循环触发");
        }
    }
}
