<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DWG 打印图纸分割工具 (带进度显示)</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        
        .upload-area {
            border: 2px dashed #ddd;
            border-radius: 10px;
            padding: 40px;
            text-align: center;
            margin-bottom: 20px;
            transition: border-color 0.3s;
        }
        
        .upload-area:hover {
            border-color: #007bff;
        }
        
        .upload-area.dragover {
            border-color: #007bff;
            background-color: #f8f9fa;
        }
        
        input[type="file"] {
            display: none;
        }
        
        .upload-btn {
            background-color: #007bff;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
        }
        
        .upload-btn:hover {
            background-color: #0056b3;
        }
        
        .upload-btn:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        
        .progress-container {
            display: none;
            margin: 20px 0;
        }
        
        .progress-bar {
            width: 100%;
            height: 20px;
            background-color: #e9ecef;
            border-radius: 10px;
            overflow: hidden;
            margin-bottom: 10px;
        }
        
        .progress-fill {
            height: 100%;
            background-color: #28a745;
            width: 0%;
            transition: width 0.3s ease;
        }
        
        .progress-text {
            text-align: center;
            color: #666;
            font-size: 14px;
        }
        
        .status-text {
            margin: 10px 0;
            padding: 10px;
            border-radius: 5px;
            font-weight: bold;
        }
        
        .status-info {
            background-color: #d1ecf1;
            color: #0c5460;
        }
        
        .status-success {
            background-color: #d4edda;
            color: #155724;
        }
        
        .status-error {
            background-color: #f8d7da;
            color: #721c24;
        }
        
        .file-info {
            margin: 10px 0;
            padding: 10px;
            background-color: #f8f9fa;
            border-radius: 5px;
            font-size: 14px;
        }
        
        .processed-files {
            margin-top: 20px;
        }
        
        .processed-files h3 {
            color: #333;
            margin-bottom: 10px;
        }
        
        .file-list {
            list-style-type: none;
            padding: 0;
        }
        
        .file-list li {
            padding: 5px 10px;
            margin: 2px 0;
            background-color: #e9ecef;
            border-radius: 3px;
            font-size: 14px;
        }
        
        .download-btn {
            background-color: #28a745;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 0;
            display: none;
        }
        
        .download-btn:hover {
            background-color: #218838;
        }
        
        .queue-status {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            display: none;
        }
        
        .session-info {
            background-color: #e2e3e5;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>DWG 打印图纸分割工具</h1>
        <p style="text-align: center; color: #666;">上传 DWG 文件，自动识别并分割打印图纸为独立的 DWG 文件</p>
        
        <div class="queue-status" id="queueStatus">
            <strong>队列状态:</strong> <span id="queueInfo"></span>
        </div>
        
        <div class="upload-area" id="uploadArea">
            <p>点击选择文件或拖拽 DWG 文件到此区域</p>
            <input type="file" id="fileInput" accept=".dwg" />
            <button class="upload-btn" onclick="document.getElementById('fileInput').click()">选择文件</button>
        </div>
        
        <div class="file-info" id="fileInfo" style="display: none;"></div>
        
        <button class="upload-btn" id="submitBtn" style="display: none;" disabled>开始处理</button>
        
        <div class="session-info" id="sessionInfo" style="display: none;"></div>
        
        <div class="progress-container" id="progressContainer">
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>
            <div class="progress-text" id="progressText">0%</div>
            <div class="status-text" id="statusText"></div>
        </div>
        
        <div class="processed-files" id="processedFiles" style="display: none;">
            <h3>已处理的文件:</h3>
            <ul class="file-list" id="fileList"></ul>
        </div>
        
        <button class="download-btn" id="downloadBtn">下载分割后的文件</button>
        
        <div id="errorMessage" class="status-text status-error" style="display: none;"></div>
    </div>

    <script>
        // DOM 元素
        const fileInput = document.getElementById('fileInput');
        const uploadArea = document.getElementById('uploadArea');
        const fileInfo = document.getElementById('fileInfo');
        const submitBtn = document.getElementById('submitBtn');
        const progressContainer = document.getElementById('progressContainer');
        const progressFill = document.getElementById('progressFill');
        const progressText = document.getElementById('progressText');
        const statusText = document.getElementById('statusText');
        const errorMessage = document.getElementById('errorMessage');
        const queueStatus = document.getElementById('queueStatus');
        const queueInfo = document.getElementById('queueInfo');
        const sessionInfo = document.getElementById('sessionInfo');
        const processedFiles = document.getElementById('processedFiles');
        const fileList = document.getElementById('fileList');
        const downloadBtn = document.getElementById('downloadBtn');
        
        // 获取当前服务器端口
        const serverPort = window.location.port || '6060';
        const apiUrl = `http://localhost:${serverPort}/api/print-drawings/process-with-progress`;
        const progressUrl = `http://localhost:${serverPort}/api/print-drawings/progress`;
        const queueStatusUrl = `http://localhost:${serverPort}/api/print-drawings/queue-status`;
        
        // 状态变量
        let currentSessionId = null;
        let progressInterval = null;
        let selectedFile = null;
        
        // 检查队列状态
        checkQueueStatus();
        setInterval(checkQueueStatus, 10000);
        
        // 文件选择处理
        fileInput.addEventListener('change', handleFileSelect);
        uploadArea.addEventListener('dragover', handleDragOver);
        uploadArea.addEventListener('drop', handleDrop);
        uploadArea.addEventListener('dragleave', handleDragLeave);
        submitBtn.addEventListener('click', startProcessing);
        downloadBtn.addEventListener('click', downloadResult);
        
        function handleFileSelect(event) {
            const file = event.target.files[0];
            if (file) {
                selectedFile = file;
                showFileInfo(file);
            }
        }
        
        function handleDragOver(event) {
            event.preventDefault();
            uploadArea.classList.add('dragover');
        }
        
        function handleDrop(event) {
            event.preventDefault();
            uploadArea.classList.remove('dragover');
            
            const files = event.dataTransfer.files;
            if (files.length > 0) {
                const file = files[0];
                if (file.name.toLowerCase().endsWith('.dwg')) {
                    selectedFile = file;
                    fileInput.files = files;
                    showFileInfo(file);
                } else {
                    showError('请选择 DWG 文件');
                }
            }
        }
        
        function handleDragLeave(event) {
            uploadArea.classList.remove('dragover');
        }
        
        function showFileInfo(file) {
            fileInfo.style.display = 'block';
            fileInfo.innerHTML = `
                <strong>选择的文件:</strong> ${file.name}<br>
                <strong>文件大小:</strong> ${(file.size / 1024 / 1024).toFixed(2)} MB<br>
                <strong>文件类型:</strong> ${file.type || 'application/octet-stream'}
            `;
            submitBtn.style.display = 'inline-block';
            submitBtn.disabled = false;
            errorMessage.style.display = 'none';
        }
        
        function checkQueueStatus() {
            fetch(queueStatusUrl)
                .then(response => response.json())
                .then(data => {
                    if (data.activeProcessing > 0) {
                        queueStatus.style.display = 'block';
                        queueInfo.textContent = `当前有 ${data.activeProcessing} 个文件正在处理中 (最大并发: ${data.maxConcurrency})`;
                    } else {
                        queueStatus.style.display = 'none';
                    }
                })
                .catch(error => {
                    console.error('Error checking queue status:', error);
                });
        }
        
        async function startProcessing() {
            if (!selectedFile) {
                showError('请先选择一个 DWG 文件');
                return;
            }
            
            // 重置状态
            errorMessage.style.display = 'none';
            submitBtn.disabled = true;
            progressContainer.style.display = 'block';
            processedFiles.style.display = 'none';
            downloadBtn.style.display = 'none';
            
            updateProgress(0, '准备上传文件...');
            
            try {
                // 创建 FormData
                const formData = new FormData();
                formData.append('dwgFile', selectedFile);
                
                // 发送请求
                const response = await fetch(apiUrl, {
                    method: 'POST',
                    body: formData
                });
                
                const result = await response.json();
                
                if (result.success) {
                    currentSessionId = result.sessionId;
                    sessionInfo.style.display = 'block';
                    sessionInfo.textContent = `会话ID: ${currentSessionId}`;
                    
                    updateProgress(5, '文件上传成功，开始处理...');
                    
                    // 开始轮询进度
                    startProgressPolling();
                } else {
                    throw new Error(result.message || '处理失败');
                }
            } catch (error) {
                showError(`处理失败: ${error.message}`);
                resetUI();
            }
        }
        
        function startProgressPolling() {
            if (progressInterval) {
                clearInterval(progressInterval);
            }
            
            progressInterval = setInterval(checkProgress, 1000);
        }
        
        function stopProgressPolling() {
            if (progressInterval) {
                clearInterval(progressInterval);
                progressInterval = null;
            }
        }
        
        async function checkProgress() {
            if (!currentSessionId) return;
            
            try {
                const response = await fetch(`${progressUrl}?sessionId=${currentSessionId}`);
                const data = await response.json();
                
                if (data.success) {
                    const progress = data;
                    updateProgress(progress.percentComplete, progress.currentOperation, progress.currentFile);
                    
                    // 更新已处理文件列表
                    if (progress.processedFiles && progress.processedFiles.length > 0) {
                        updateProcessedFiles(progress.processedFiles);
                    }
                    
                    // 检查是否完成
                    if (progress.isCompleted) {
                        stopProgressPolling();
                        
                        if (progress.hasError) {
                            showError(`处理失败: ${progress.errorMessage}`);
                            resetUI();
                        } else {
                            updateProgress(100, '处理完成！');
                            statusText.className = 'status-text status-success';
                            downloadBtn.style.display = 'inline-block';
                        }
                    }
                } else {
                    console.warn('Progress check failed:', data.message);
                }
            } catch (error) {
                console.error('Error checking progress:', error);
            }
        }
        
        function updateProgress(percent, operation, currentFile) {
            progressFill.style.width = `${percent}%`;
            progressText.textContent = `${percent}%`;
            
            let statusMessage = operation;
            if (currentFile) {
                statusMessage += ` - ${currentFile}`;
            }
            
            statusText.textContent = statusMessage;
            statusText.className = 'status-text status-info';
        }
        
        function updateProcessedFiles(files) {
            if (files.length > 0) {
                processedFiles.style.display = 'block';
                fileList.innerHTML = '';
                
                files.forEach(file => {
                    const li = document.createElement('li');
                    li.textContent = file;
                    fileList.appendChild(li);
                });
            }
        }
        
        function downloadResult() {
            if (currentSessionId) {
                // 这里需要实现下载逻辑
                // 可能需要一个专门的下载端点
                window.open(`http://localhost:${serverPort}/api/print-drawings/download?sessionId=${currentSessionId}`);
            }
        }
        
        function showError(message) {
            errorMessage.textContent = message;
            errorMessage.style.display = 'block';
            statusText.className = 'status-text status-error';
        }
        
        function resetUI() {
            submitBtn.disabled = false;
            progressContainer.style.display = 'none';
            stopProgressPolling();
            currentSessionId = null;
            sessionInfo.style.display = 'none';
        }
    </script>
</body>
</html>
