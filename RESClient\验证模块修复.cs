using System;
using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;
using RESClient.Services.Implementations;

namespace RESClient
{
    /// <summary>
    /// 验证楼栋基本信息和房产面积汇总表模块修复效果
    /// </summary>
    public class VerifyModuleFix
    {
        public static async Task Main(string[] args)
        {
            Console.WriteLine("=== 验证模块修复效果 ===");
            Console.WriteLine("检查楼栋基本信息和房产面积汇总表模块的IsAvailable方法是否正确工作");
            Console.WriteLine();

            // 使用实际的数据目录路径
            string dataDirectory = @"C:\Users\<USER>\source\repos\XSENTRYCAD\实测";
            string outputDirectory = @"C:\Users\<USER>\source\repos\XSENTRYCAD\实测\Output";

            Console.WriteLine($"数据目录: {dataDirectory}");
            Console.WriteLine($"输出目录: {outputDirectory}");
            Console.WriteLine();

            var parameters = new Dictionary<string, object>
            {
                ["DataFolder"] = dataDirectory,
                ["OutputDir"] = outputDirectory
            };

            await TestBuildingInfoModule(parameters);
            Console.WriteLine();
            await TestEstateAreaSummaryModule(parameters);
            Console.WriteLine();
            await TestHouseholdAreaStatisticsModule(parameters);

            Console.WriteLine("\n=== 验证完成 ===");
            Console.WriteLine("如果所有模块都显示'可用'，说明修复成功。");
            Console.WriteLine("如果仍显示'不可用'，请运行ModuleDiagnosticTool进行详细诊断。");
            Console.WriteLine("\n按任意键退出...");
            Console.ReadKey();
        }

        /// <summary>
        /// 测试楼栋基本信息模块
        /// </summary>
        private static async Task TestBuildingInfoModule(Dictionary<string, object> parameters)
        {
            Console.WriteLine("1. 测试楼栋基本信息模块");
            Console.WriteLine("----------------------------------------");

            try
            {
                var generator = new BuildingInfoModuleGenerator();
                
                Console.WriteLine("检查模块可用性...");
                bool isAvailable = generator.IsAvailable(parameters);
                
                Console.WriteLine($"模块名称: {generator.ModuleName}");
                Console.WriteLine($"模块可用性: {(isAvailable ? "✓ 可用" : "❌ 不可用")}");
                
                if (isAvailable)
                {
                    Console.WriteLine("✓ 修复成功！模块现在可以正常使用。");
                }
                else
                {
                    Console.WriteLine("❌ 模块仍然不可用，需要进一步检查。");
                    await DiagnoseAvailabilityIssue("楼栋基本信息", parameters);
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ 测试楼栋基本信息模块时出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 测试房产面积汇总表模块
        /// </summary>
        private static async Task TestEstateAreaSummaryModule(Dictionary<string, object> parameters)
        {
            Console.WriteLine("2. 测试房产面积汇总表模块");
            Console.WriteLine("----------------------------------------");

            try
            {
                var generator = new EstateAreaSummaryModuleGenerator();
                
                Console.WriteLine("检查模块可用性...");
                bool isAvailable = generator.IsAvailable(parameters);
                
                Console.WriteLine($"模块名称: {generator.ModuleName}");
                Console.WriteLine($"模块可用性: {(isAvailable ? "✓ 可用" : "❌ 不可用")}");
                
                if (isAvailable)
                {
                    Console.WriteLine("✓ 修复成功！模块现在可以正常使用。");
                }
                else
                {
                    Console.WriteLine("❌ 模块仍然不可用，需要进一步检查。");
                    await DiagnoseAvailabilityIssue("房产面积汇总表", parameters);
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ 测试房产面积汇总表模块时出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 测试房产分户面积统计表模块
        /// </summary>
        private static async Task TestHouseholdAreaStatisticsModule(Dictionary<string, object> parameters)
        {
            Console.WriteLine("3. 测试房产分户面积统计表模块");
            Console.WriteLine("----------------------------------------");

            try
            {
                var generator = new HouseholdAreaStatisticsModuleGenerator();
                
                Console.WriteLine("检查模块可用性...");
                bool isAvailable = generator.IsAvailable(parameters);
                
                Console.WriteLine($"模块名称: {generator.ModuleName}");
                Console.WriteLine($"模块可用性: {(isAvailable ? "✓ 可用" : "❌ 不可用")}");
                
                if (isAvailable)
                {
                    Console.WriteLine("✓ 修复成功！模块现在可以正常使用。");
                }
                else
                {
                    Console.WriteLine("❌ 模块仍然不可用，需要进一步检查。");
                    await DiagnoseAvailabilityIssue("房产分户面积统计表", parameters);
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ 测试房产分户面积统计表模块时出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 诊断可用性问题
        /// </summary>
        private static async Task DiagnoseAvailabilityIssue(string moduleName, Dictionary<string, object> parameters)
        {
            string dataDirectory = parameters["DataFolder"]?.ToString();
            string outputDirectory = parameters["OutputDir"]?.ToString();

            Console.WriteLine("  进行快速诊断:");

            // 检查数据目录
            if (string.IsNullOrEmpty(dataDirectory) || !Directory.Exists(dataDirectory))
            {
                Console.WriteLine("  ❌ 数据目录不存在或为空");
                return;
            }
            Console.WriteLine("  ✓ 数据目录存在");

            // 检查输出目录参数
            if (string.IsNullOrEmpty(outputDirectory))
            {
                Console.WriteLine("  ❌ 输出目录参数为空");
                return;
            }
            Console.WriteLine("  ✓ 输出目录参数正确");

            // 检查模板文件
            string templatePath = "";
            if (moduleName == "楼栋基本信息")
            {
                templatePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "报告模板", "06_楼栋基本信息", "楼栋基本信息.docx");
            }
            else if (moduleName == "房产面积汇总表")
            {
                templatePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "报告模板", "11_房产面积汇总表", "房产面积汇总表.docx");
            }
            else if (moduleName == "房产分户面积统计表")
            {
                templatePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "报告模板", "12_房产分户面积统计表", "房产分户面积统计表.docx");
            }

            if (!File.Exists(templatePath))
            {
                Console.WriteLine($"  ❌ 模板文件不存在: {templatePath}");
                return;
            }
            Console.WriteLine("  ✓ 模板文件存在");

            // 检查数据文件
            Console.WriteLine("  检查数据文件:");
            
            // 检查Excel文件
            var excelFiles = Directory.GetFiles(dataDirectory, "*.xls*", SearchOption.AllDirectories);
            Console.WriteLine($"    找到 {excelFiles.Length} 个Excel文件");

            if (excelFiles.Length == 0)
            {
                Console.WriteLine("    ❌ 未找到任何Excel文件");
                return;
            }

            // 检查CGB文件
            bool foundCGB = false;
            foreach (var file in excelFiles)
            {
                if (Path.GetFileName(file).ToLower().Contains("cgb"))
                {
                    Console.WriteLine($"    ✓ 找到CGB文件: {Path.GetFileName(file)}");
                    foundCGB = true;
                    break;
                }
            }

            // 检查7z文件
            var archiveFiles = Directory.GetFiles(dataDirectory, "*.7z", SearchOption.AllDirectories);
            bool foundArchive = false;
            foreach (var file in archiveFiles)
            {
                if (Path.GetFileName(file).Contains("成果包"))
                {
                    Console.WriteLine($"    ✓ 找到成果包.7z文件: {Path.GetFileName(file)}");
                    foundArchive = true;
                    break;
                }
            }

            if (!foundCGB && !foundArchive)
            {
                Console.WriteLine("    ❌ 未找到CGB文件或成果包.7z文件");
                Console.WriteLine("    建议: 请确保数据目录包含正确的数据文件");
            }
        }
    }
}
