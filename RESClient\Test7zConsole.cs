using System;
using System.IO;
using SevenZip;

namespace RESClient
{
    /// <summary>
    /// 独立的7z.dll测试控制台程序
    /// </summary>
    class Test7zConsole
    {
        static void Main(string[] args)
        {
            Console.WriteLine("=== 7z.dll 测试程序 ===");
            Console.WriteLine();
            
            try
            {
                // 设置7z.dll路径
                string appDirectory = AppDomain.CurrentDomain.BaseDirectory;
                string sevenZipLibraryPath = Path.Combine(appDirectory, "tool", "7z", "7z.dll");
                
                Console.WriteLine($"应用程序目录: {appDirectory}");
                Console.WriteLine($"7z.dll路径: {sevenZipLibraryPath}");
                Console.WriteLine();
                
                if (!File.Exists(sevenZipLibraryPath))
                {
                    Console.WriteLine("❌ 错误: 7z.dll文件不存在！");
                    Console.WriteLine("请确保7z.dll文件位于tool/7z/目录下");
                    Console.ReadKey();
                    return;
                }
                
                Console.WriteLine("✓ 7z.dll文件存在");
                
                // 获取文件信息
                FileInfo fileInfo = new FileInfo(sevenZipLibraryPath);
                Console.WriteLine($"文件大小: {fileInfo.Length:N0} 字节");
                Console.WriteLine($"修改时间: {fileInfo.LastWriteTime}");
                Console.WriteLine();
                
                // 初始化SevenZipSharp库
                Console.WriteLine("正在初始化SevenZipSharp库...");
                SevenZipBase.SetLibraryPath(sevenZipLibraryPath);
                Console.WriteLine("✓ SevenZipSharp库初始化成功");
                
                // 测试库是否可以正常工作
                Console.WriteLine();
                Console.WriteLine("正在测试库功能...");
                
                try
                {
                    var features = SevenZipBase.CurrentLibraryFeatures;
                    Console.WriteLine($"✓ 7z库功能特性: {features}");
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"⚠ 无法获取库功能特性: {ex.Message}");
                }
                
                Console.WriteLine();
                Console.WriteLine("🎉 7z.dll测试成功！库可以正常使用。");
                Console.WriteLine();
                Console.WriteLine("现在可以尝试运行房产面积汇总表生成功能。");
                
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ 7z.dll测试失败: {ex.Message}");
                Console.WriteLine();
                Console.WriteLine("详细错误信息:");
                Console.WriteLine(ex.ToString());
                
                if (ex.Message.Contains("failed to load library"))
                {
                    Console.WriteLine();
                    Console.WriteLine("可能的解决方案:");
                    Console.WriteLine("1. 确保使用正确版本的7z.dll（x64或x86）");
                    Console.WriteLine("2. 确保安装了Visual C++ Redistributable");
                    Console.WriteLine("3. 检查7z.dll文件是否损坏");
                }
            }
            
            Console.WriteLine();
            Console.WriteLine("按任意键退出...");
            Console.ReadKey();
        }
    }
}
