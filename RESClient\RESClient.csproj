﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{3C1F9D61-62D4-4E64-B721-BD5DAA012727}</ProjectGuid>
    <OutputType>WinExe</OutputType>
    <RootNamespace>RESClient</RootNamespace>
    <AssemblyName>RESClient</AssemblyName>
    <TargetFrameworkVersion>v4.8</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <AutoGenerateBindingRedirects>true</AutoGenerateBindingRedirects>
    <Deterministic>true</Deterministic>
    <NuGetPackageImportStamp>
    </NuGetPackageImportStamp>
    <PublishUrl>publish\</PublishUrl>
    <Install>true</Install>
    <InstallFrom>Disk</InstallFrom>
    <UpdateEnabled>false</UpdateEnabled>
    <UpdateMode>Foreground</UpdateMode>
    <UpdateInterval>7</UpdateInterval>
    <UpdateIntervalUnits>Days</UpdateIntervalUnits>
    <UpdatePeriodically>false</UpdatePeriodically>
    <UpdateRequired>false</UpdateRequired>
    <MapFileExtensions>true</MapFileExtensions>
    <ApplicationRevision>0</ApplicationRevision>
    <ApplicationVersion>1.0.0.%2a</ApplicationVersion>
    <IsWebBootstrapper>false</IsWebBootstrapper>
    <UseApplicationTrust>false</UseApplicationTrust>
    <BootstrapperEnabled>true</BootstrapperEnabled>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="BouncyCastle.Cryptography, Version=*******, Culture=neutral, PublicKeyToken=072edcf4a5328938, processorArchitecture=MSIL">
      <HintPath>..\packages\BouncyCastle.Cryptography.2.3.1\lib\net461\BouncyCastle.Cryptography.dll</HintPath>
    </Reference>
    <Reference Include="Enums.NET, Version=4.0.0.0, Culture=neutral, PublicKeyToken=7ea1c1650d506225, processorArchitecture=MSIL">
      <HintPath>..\packages\Enums.NET.4.0.1\lib\net45\Enums.NET.dll</HintPath>
    </Reference>
    <Reference Include="ExtendedNumerics.BigDecimal, Version=2025.1001.2.129, Culture=neutral, PublicKeyToken=65f1315a45ad8949, processorArchitecture=MSIL">
      <HintPath>..\packages\ExtendedNumerics.BigDecimal.2025.1001.2.129\lib\net48\ExtendedNumerics.BigDecimal.dll</HintPath>
    </Reference>
    <Reference Include="ICSharpCode.SharpZipLib, Version=********, Culture=neutral, PublicKeyToken=1b03e6acf1164f73, processorArchitecture=MSIL">
      <HintPath>..\packages\SharpZipLib.1.4.2\lib\netstandard2.0\ICSharpCode.SharpZipLib.dll</HintPath>
    </Reference>
    <Reference Include="MathNet.Numerics, Version=*******, Culture=neutral, PublicKeyToken=cd8b63ad3d691a37, processorArchitecture=MSIL">
      <HintPath>..\packages\MathNet.Numerics.Signed.5.0.0\lib\net48\MathNet.Numerics.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Bcl.AsyncInterfaces, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Bcl.AsyncInterfaces.9.0.7\lib\net462\Microsoft.Bcl.AsyncInterfaces.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.IO.RecyclableMemoryStream, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.IO.RecyclableMemoryStream.3.0.0\lib\netstandard2.0\Microsoft.IO.RecyclableMemoryStream.dll</HintPath>
    </Reference>
    <Reference Include="Newtonsoft.Json, Version=********, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed, processorArchitecture=MSIL">
      <HintPath>..\packages\Newtonsoft.Json.13.0.3\lib\net45\Newtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference Include="NPOI.Core, Version=*******, Culture=neutral, PublicKeyToken=0df73ec7942b34e1, processorArchitecture=MSIL">
      <HintPath>..\packages\NPOI.2.7.3\lib\net472\NPOI.Core.dll</HintPath>
    </Reference>
    <Reference Include="NPOI.OOXML, Version=*******, Culture=neutral, PublicKeyToken=0df73ec7942b34e1, processorArchitecture=MSIL">
      <HintPath>..\packages\NPOI.2.7.3\lib\net472\NPOI.OOXML.dll</HintPath>
    </Reference>
    <Reference Include="NPOI.OpenXml4Net, Version=*******, Culture=neutral, PublicKeyToken=0df73ec7942b34e1, processorArchitecture=MSIL">
      <HintPath>..\packages\NPOI.2.7.3\lib\net472\NPOI.OpenXml4Net.dll</HintPath>
    </Reference>
    <Reference Include="NPOI.OpenXmlFormats, Version=*******, Culture=neutral, PublicKeyToken=0df73ec7942b34e1, processorArchitecture=MSIL">
      <HintPath>..\packages\NPOI.2.7.3\lib\net472\NPOI.OpenXmlFormats.dll</HintPath>
    </Reference>
    <Reference Include="SevenZipSharp, Version=********, Culture=neutral, PublicKeyToken=c8ff6ba0184838bb, processorArchitecture=MSIL">
      <HintPath>..\packages\Squid-Box.SevenZipSharp.Lite.********\lib\net472\SevenZipSharp.dll</HintPath>
    </Reference>
    <Reference Include="SixLabors.Fonts, Version=*******, Culture=neutral, PublicKeyToken=d998eea7b14cab13, processorArchitecture=MSIL">
      <HintPath>..\packages\SixLabors.Fonts.1.0.1\lib\netstandard2.0\SixLabors.Fonts.dll</HintPath>
    </Reference>
    <Reference Include="SixLabors.ImageSharp, Version=*******, Culture=neutral, PublicKeyToken=d998eea7b14cab13, processorArchitecture=MSIL">
      <HintPath>..\packages\SixLabors.ImageSharp.2.1.10\lib\net472\SixLabors.ImageSharp.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Buffers, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Buffers.4.5.1\lib\net461\System.Buffers.dll</HintPath>
    </Reference>
    <Reference Include="System.ComponentModel.DataAnnotations" />
    <Reference Include="System.Configuration" />
    <Reference Include="System.Core" />
    <Reference Include="System.IO.Pipelines, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.IO.Pipelines.9.0.7\lib\net462\System.IO.Pipelines.dll</HintPath>
    </Reference>
    <Reference Include="System.Memory, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Memory.4.5.5\lib\net461\System.Memory.dll</HintPath>
    </Reference>
    <Reference Include="System.Numerics" />
    <Reference Include="System.Numerics.Vectors, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Numerics.Vectors.4.5.0\lib\net46\System.Numerics.Vectors.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.CompilerServices.Unsafe, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Runtime.CompilerServices.Unsafe.6.0.0\lib\net461\System.Runtime.CompilerServices.Unsafe.dll</HintPath>
    </Reference>
    <Reference Include="System.Security" />
    <Reference Include="System.Security.Cryptography.Pkcs, Version=8.0.0.1, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Security.Cryptography.Pkcs.8.0.1\lib\net462\System.Security.Cryptography.Pkcs.dll</HintPath>
    </Reference>
    <Reference Include="System.Security.Cryptography.Xml, Version=8.0.0.2, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Security.Cryptography.Xml.8.0.2\lib\net462\System.Security.Cryptography.Xml.dll</HintPath>
    </Reference>
    <Reference Include="System.Text.Encoding.CodePages, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Text.Encoding.CodePages.5.0.0\lib\net461\System.Text.Encoding.CodePages.dll</HintPath>
    </Reference>
    <Reference Include="System.Text.Encodings.Web, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Text.Encodings.Web.9.0.7\lib\net462\System.Text.Encodings.Web.dll</HintPath>
    </Reference>
    <Reference Include="System.Text.Json, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Text.Json.9.0.7\lib\net462\System.Text.Json.dll</HintPath>
    </Reference>
    <Reference Include="System.Threading.Tasks.Extensions, Version=4.2.0.1, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Threading.Tasks.Extensions.4.5.4\lib\net461\System.Threading.Tasks.Extensions.dll</HintPath>
    </Reference>
    <Reference Include="System.ValueTuple, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.ValueTuple.4.5.0\lib\net47\System.ValueTuple.dll</HintPath>
    </Reference>
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Deployment" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Net.Http" />
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="MVP\Base\BaseModel.cs" />
    <Compile Include="MVP\Base\BasePresenter.cs" />
    <Compile Include="MVP\Base\BaseView.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="MVP\Interfaces\IMainModel.cs" />
    <Compile Include="MVP\Interfaces\IMainView.cs" />
    <Compile Include="MVP\Interfaces\IModel.cs" />
    <Compile Include="MVP\Interfaces\IPresenter.cs" />
    <Compile Include="MVP\Interfaces\IView.cs" />
    <Compile Include="MVP\Models\BasementDefenseModel.cs" />
    <Compile Include="MVP\Models\BasementDefenseParametersModel.cs" />
    <Compile Include="MVP\Models\BuildingInfoParametersModel.cs" />
    <Compile Include="MVP\Models\CertificatesModel.cs" />
    <Compile Include="MVP\Models\CertificatesParametersModel.cs" />
    <Compile Include="MVP\Models\CoverModel.cs" />
    <Compile Include="MVP\Models\CoverParametersModel.cs" />
    <Compile Include="MVP\Models\EstateAreaSummaryParametersModel.cs" />
    <Compile Include="MVP\Models\FloorPlanModel.cs" />
    <Compile Include="MVP\Models\MainModel.cs" />
    <Compile Include="MVP\Models\ProjectInfoModel.cs" />
    <Compile Include="MVP\Models\ProjectInfoParametersModel.cs" />
    <Compile Include="MVP\Models\WorkQualityModel.cs" />
    <Compile Include="MVP\Models\WorkQualityParametersModel.cs" />
    <Compile Include="MVP\Models\WorkStatementModel.cs" />
    <Compile Include="MVP\Models\WorkStatementParametersModel.cs" />
    <Compile Include="MVP\Presenters\MainPresenter.cs" />
    <Compile Include="Models\AuthModels.cs" />
    <Compile Include="Services\AuthService.cs" />
    <Compile Include="Services\AuthConfigService.cs" />
    <Compile Include="Services\HttpClientFactory.cs" />
    <Compile Include="Services\ServerConnectionService.cs" />
    <Compile Include="Views\LoginForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Views\LoginForm.Designer.cs">
      <DependentUpon>LoginForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Views\ConnectionErrorForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="TestConnectionService.cs" />
    <Compile Include="MVP\Views\BuildingInfoParameterInputForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="MVP\Views\CertificatesParameterInputForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="MVP\Views\ErrorSummaryDialog.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="MVP\Views\ErrorSummaryDialog.Designer.cs">
      <DependentUpon>ErrorSummaryDialog.cs</DependentUpon>
    </Compile>
    <Compile Include="MVP\Views\EstateAreaSummaryParameterInputForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="MVP\Views\MainForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="MVP\Views\MainForm.Designer.cs">
      <DependentUpon>MainForm.cs</DependentUpon>
    </Compile>
    <Compile Include="MVP\Views\BasementDefenseParameterInputForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="MVP\Views\ParameterInputForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="MVP\Views\ParameterInputForm.Designer.cs">
      <DependentUpon>ParameterInputForm.cs</DependentUpon>
    </Compile>
    <Compile Include="MVP\Views\ParameterInputFormStyleConfig.cs" />
    <Compile Include="MVP\Views\ProjectInfoParameterInputForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="MVP\Views\WorkQualityParameterInputForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="MVP\Views\WorkQualityParameterInputForm.Designer.cs">
      <DependentUpon>WorkQualityParameterInputForm.cs</DependentUpon>
    </Compile>
    <Compile Include="MVP\Views\WorkStatementParameterInputForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Program.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="Services\ModuleNumberingService.cs" />
    <Compile Include="SimpleTest.cs" />
    <Compile Include="TestErrorReporting.cs" />
    <Compile Include="TestErrorReportingConsole.cs" />
    <Compile Include="TestErrorHandlingImprovements.cs" />
    <Compile Include="TestIntegratedReportManagement.cs" />
    <Compile Include="TestDocumentMergingImprovements.cs" />
    <Compile Include="TestDocumentMergingComprehensive.cs" />
    <Compile Include="QuickErrorTest.cs" />
    <Compile Include="TestCoverParameters.cs" />
    <Compile Include="TestParameterValidation.cs" />
    <Compile Include="TestOutputDirectoryFix.cs" />
    <Compile Include="TestReportGeneration.cs" />
    <Compile Include="TestTableOfContentsModule.cs" />
    <Compile Include="TestWorkStatementModule.cs" />
    <Compile Include="TestWorkStatementParameterForm.cs" />
    <Compile Include="TestWorkQualityModule.cs" />
    <Compile Include="TestMainFormIntegration.cs" />
    <Compile Include="TestConsole.cs" />
    <Compile Include="TestNewModules.cs" />
    <Compile Include="TestEstateAreaSummaryIntegration.cs" />
    <Compile Include="TestArchiveExtraction.cs" />
    <Compile Include="Test7zDll.cs" />
    <Compile Include="TestLoginFormFix.cs" />
    <Compile Include="TestLoginFormInfiniteLoopFix.cs" />
    <Compile Include="TestLoginCancellationBehavior.cs" />
    <Compile Include="TestAuthenticationSystem.cs" />
    <Compile Include="TestRememberPasswordFeature.cs" />
    <Compile Include="DemoRememberPasswordFeature.cs" />
    <Compile Include="QuickVerifyRememberPassword.cs" />
    <Compile Include="TestAuthenticationLogMessages.cs" />
    <Compile Include="Utils\ChineseDateFormatter.cs" />
    <EmbeddedResource Include="MVP\Base\BaseView.resx">
      <DependentUpon>BaseView.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="MVP\Views\MainForm.resx">
      <DependentUpon>MainForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Views\LoginForm.resx">
      <DependentUpon>LoginForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Properties\Resources.resx">
      <Generator>ResXFileCodeGenerator</Generator>
      <LastGenOutput>Resources.Designer.cs</LastGenOutput>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <Compile Include="Properties\Resources.Designer.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Resources.resx</DependentUpon>
    </Compile>
    <Compile Include="Services\ArchiveExtractionService.cs" />
    <Compile Include="Services\IReportModuleGenerator.cs" />
    <Compile Include="Services\RESServerConfigService.cs" />
    <Compile Include="Services\Implementations\BasementDefenseModuleGenerator.cs" />
    <Compile Include="Services\Implementations\BuildingImageModuleGenerator.cs" />
    <Compile Include="Services\Implementations\BuildingInfoModuleGenerator.cs" />
    <Compile Include="Services\Implementations\CertificatesModuleGenerator.cs" />
    <Compile Include="Services\Implementations\CoverModuleGenerator.cs" />
    <Compile Include="Services\Implementations\EstateAreaSummaryModuleGenerator.cs" />
    <Compile Include="Services\Implementations\FloorPlanModuleGenerator.cs" />
    <Compile Include="Services\Implementations\HouseholdAreaStatisticsModuleGenerator.cs" />
    <Compile Include="Services\Implementations\ProjectDistributionMapModuleGenerator.cs" />
    <Compile Include="Services\Implementations\ProjectInfoModuleGenerator.cs" />
    <Compile Include="Services\Implementations\TableOfContentsModuleGenerator.cs" />
    <Compile Include="Services\Implementations\WorkQualityModuleGenerator.cs" />
    <Compile Include="Services\Implementations\WorkStatementModuleGenerator.cs" />
    <Compile Include="Services\DocumentMergingService.cs" />
    <Compile Include="Services\ErrorCollector.cs" />
    <Compile Include="Services\ModuleSettingsService.cs" />
    <Compile Include="Services\ReportGeneratorService.cs" />
    <None Include="packages.config" />
    <None Include="Properties\Settings.settings">
      <Generator>SettingsSingleFileGenerator</Generator>
      <LastGenOutput>Settings.Designer.cs</LastGenOutput>
    </None>
    <Compile Include="Properties\Settings.Designer.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Settings.settings</DependentUpon>
      <DesignTimeSharedInput>True</DesignTimeSharedInput>
    </Compile>
    <None Include="tool\CADC\Arabic.lan">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="tool\CADC\default.opt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="tool\CADC\English.lan">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="tool\CADC\fonts\acad.ctb">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="tool\CADC\fonts\acad.stb">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="tool\CADC\fonts\amgdt.shx">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="tool\CADC\fonts\Autodesk-Color.stb">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="tool\CADC\fonts\Autodesk-MONO.stb">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="tool\CADC\fonts\complex.shx">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="tool\CADC\fonts\dim.shx">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="tool\CADC\fonts\DWF Virtual Pens.ctb">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="tool\CADC\fonts\Fill Patterns.ctb">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="tool\CADC\fonts\gbeitc.shx">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="tool\CADC\fonts\gbenor.shx">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="tool\CADC\fonts\gothice.shx">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="tool\CADC\fonts\gothicg.shx">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="tool\CADC\fonts\gothici.shx">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="tool\CADC\fonts\Grayscale.ctb">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="tool\CADC\fonts\greekc.shx">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="tool\CADC\fonts\greeks.shx">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="tool\CADC\fonts\hzdx.shx">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="tool\CADC\fonts\isocp.shx">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="tool\CADC\fonts\isocp2.shx">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="tool\CADC\fonts\isocp3.shx">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="tool\CADC\fonts\isoct.shx">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="tool\CADC\fonts\isoct2.shx">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="tool\CADC\fonts\isoct3.shx">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="tool\CADC\fonts\italic.shx">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="tool\CADC\fonts\italicc.shx">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="tool\CADC\fonts\italict.shx">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="tool\CADC\fonts\monochrome.ctb">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="tool\CADC\fonts\monochrome.stb">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="tool\CADC\fonts\monotxt.shx">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="tool\CADC\fonts\romanc.shx">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="tool\CADC\fonts\romand.shx">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="tool\CADC\fonts\romans.shx">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="tool\CADC\fonts\romant.shx">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="tool\CADC\fonts\Screening 100%25.ctb">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="tool\CADC\fonts\Screening 25%25.ctb">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="tool\CADC\fonts\Screening 50%25.ctb">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="tool\CADC\fonts\Screening 75%25.ctb">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="tool\CADC\fonts\scriptc.shx">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="tool\CADC\fonts\scripts.shx">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="tool\CADC\fonts\simplex.shx">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="tool\CADC\fonts\syastro.shx">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="tool\CADC\fonts\symap.shx">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="tool\CADC\fonts\symath.shx">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="tool\CADC\fonts\symeteo.shx">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="tool\CADC\fonts\symusic.shx">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="tool\CADC\fonts\txt.shx">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="tool\CADC\German.lan">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="tool\CADC\Hungarian.lan">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="tool\CADC\Italian.lan">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="tool\CADC\Korean.lan">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="tool\CADC\Polish.lan">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="tool\CADC\Slovenian.lan">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="tool\CADC\TChinese.lan">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="报告模板\01_封面\封面.docx">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="报告模板\02_目录\目录.docx">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="报告模板\03_作业声明\作业声明.docx">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="报告模板\04_作业、质量检查与验收\作业、质量检查与验收.docx">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="报告模板\05_项目基本信息\项目基本信息.docx">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="报告模板\06_楼栋基本信息\楼栋基本信息.docx">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="报告模板\07_经主管部门批准的相关证照\经主管部门批准的相关证照.docx">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="报告模板\08_地下室人防区域说明\地下室人防区域说明.docx">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="报告模板\09_项目丘地及测绘房屋分布图\项目住地及测绘房屋分布图.docx">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="报告模板\10_建筑物现状影像图\建筑物现状影像图.docx">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="报告模板\11_房产面积汇总表\房产面积汇总表.docx">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="报告模板\12_房产分户面积统计表\房产分户面积统计表-地下室.docx">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="报告模板\12_房产分户面积统计表\房产分户面积统计表.docx">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="报告模板\13_房产分层测绘图\房产分层测绘图.docx">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
  </ItemGroup>
  <ItemGroup>
    <None Include="App.config" />
  </ItemGroup>
  <ItemGroup>
    <WCFMetadata Include="Connected Services\" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="tool\CADC\ACAD.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="tool\CADC\gsapp.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="tool\CADC\gsimage.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="tool\CADC\gsio.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="tool\CADC\gspdf.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="tool\CADC\gsui.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="tool\CADC\gzip.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="tool\CADC\history.txt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="tool\CADC\iow.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="tool\CADC\zip32.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
  </ItemGroup>
  <ItemGroup>
    <BootstrapperPackage Include=".NETFramework,Version=v4.8">
      <Visible>False</Visible>
      <ProductName>Microsoft .NET Framework 4.8 %28x86 和 x64%29</ProductName>
      <Install>true</Install>
    </BootstrapperPackage>
    <BootstrapperPackage Include="Microsoft.Net.Framework.3.5.SP1">
      <Visible>False</Visible>
      <ProductName>.NET Framework 3.5 SP1</ProductName>
      <Install>false</Install>
    </BootstrapperPackage>
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <Import Project="..\packages\7z.Libs.25.00\build\net\7z.Libs.targets" Condition="Exists('..\packages\7z.Libs.25.00\build\net\7z.Libs.targets')" />
  <Target Name="EnsureNuGetPackageBuildImports" BeforeTargets="PrepareForBuild">
    <PropertyGroup>
      <ErrorText>这台计算机上缺少此项目引用的 NuGet 程序包。使用“NuGet 程序包还原”可下载这些程序包。有关更多信息，请参见 http://go.microsoft.com/fwlink/?LinkID=322105。缺少的文件是 {0}。</ErrorText>
    </PropertyGroup>
    <Error Condition="!Exists('..\packages\7z.Libs.25.00\build\net\7z.Libs.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\7z.Libs.25.00\build\net\7z.Libs.targets'))" />
  </Target>
</Project>