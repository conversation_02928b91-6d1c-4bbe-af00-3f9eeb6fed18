# RESClient 记住密码功能使用指南

## 快速开始

### 1. 功能概述
RESClient现在支持"记住密码"功能，当RESServer启用认证时，用户可以选择保存登录凭据以便下次自动填充。

### 2. 基本使用步骤

#### 第一次使用
1. 启动RESClient应用程序
2. 当服务器启用认证时，会显示登录界面
3. 输入用户名和密码
4. **勾选"记住密码"复选框**
5. 点击"登录"按钮

#### 后续使用
1. 启动RESClient应用程序
2. 登录界面会**自动填充**保存的用户名和密码
3. "记住密码"复选框会自动勾选
4. **仍需手动点击"登录"按钮**确认

## 详细功能说明

### 🔐 安全特性

#### 密码加密存储
- 使用Windows数据保护API (DPAPI)
- 只有当前Windows用户可以解密
- 密码不以明文形式存储在本地

#### 存储位置
```
%LOCALAPPDATA%\RESClient\remembered_credentials.json
```

#### 安全级别
- **用户级别保护**：只有当前用户可以访问
- **系统级别加密**：使用操作系统提供的加密功能
- **自动清理**：应用程序卸载时可以手动清理

### 🖥️ 用户界面

#### 新增控件
1. **"记住密码"复选框**
   - 位置：密码输入框下方
   - 功能：控制是否保存登录凭据

2. **"清除"按钮**
   - 显示条件：取消勾选"记住密码"时
   - 功能：删除已保存的登录凭据

#### 界面行为
- 有保存凭据时：自动填充用户名和密码
- 无保存凭据时：显示默认用户名"admin"
- 清除凭据后：恢复到默认状态

### ⚙️ 配置要求

#### 服务器端配置
确保RESServer的`appsettings.json`中启用认证：
```json
{
  "Auth": {
    "EnableAuthentication": true
  }
}
```

#### 客户端配置
在RESClient的`App.config`中：
```xml
<appSettings>
  <add key="RememberLoginState" value="true" />
  <add key="EnableAutoLoginCheck" value="true" />
</appSettings>
```

## 高级使用

### 🧹 清除保存的凭据

#### 方法一：通过界面
1. 在登录界面取消勾选"记住密码"
2. 点击出现的"清除"按钮
3. 在确认对话框中选择"是"

#### 方法二：通过代码
```csharp
var authConfig = AuthConfigService.Instance;
authConfig.ClearRememberedCredentials();
```

#### 方法三：手动删除文件
删除文件：`%LOCALAPPDATA%\RESClient\remembered_credentials.json`

### 🔧 故障排除

#### 常见问题

**Q: 密码没有自动填充**
A: 检查以下项目：
- 确保之前勾选了"记住密码"
- 确保服务器启用了认证功能
- 检查凭据文件是否存在

**Q: 提示"清除凭据失败"**
A: 可能的原因：
- 文件被其他程序占用
- 没有足够的文件系统权限
- 凭据文件已损坏

**Q: 密码解密失败**
A: 可能的原因：
- Windows用户账户发生变化
- 系统重装或迁移
- 凭据文件损坏

#### 解决方案
1. **重新保存凭据**：清除现有凭据后重新登录并保存
2. **检查权限**：确保对LocalApplicationData目录有读写权限
3. **重启应用**：有时重启应用程序可以解决临时问题

### 📊 测试和验证

#### 快速验证功能
```bash
# 运行快速验证
RESClient.exe verify-remember-password

# 运行完整测试
RESClient.exe test-remember-password

# 运行功能演示
RESClient.exe demo-remember-password
```

#### 手动测试步骤
1. 清除任何现有凭据
2. 登录并勾选"记住密码"
3. 关闭应用程序
4. 重新启动应用程序
5. 验证凭据是否自动填充
6. 测试清除功能

## 安全建议

### 🛡️ 使用建议

#### 适合使用的场景
- 个人专用计算机
- 安全的办公环境
- 需要频繁登录的情况

#### 不建议使用的场景
- 共享计算机
- 公共场所的计算机
- 高安全要求的环境

#### 最佳实践
1. **定期清理**：定期清除不需要的保存凭据
2. **账户安全**：确保Windows用户账户使用强密码
3. **及时更新**：密码变更后及时更新保存的凭据
4. **监控使用**：注意异常的登录行为

### 🔒 安全限制

#### 技术限制
- 只能在Windows系统上使用
- 依赖于Windows用户账户安全
- 不支持跨用户账户访问

#### 功能限制
- 只保存最近一次的登录凭据
- 不支持多账户凭据管理
- 不支持凭据的云同步

## 开发者信息

### 🔧 技术实现

#### 核心类
- `AuthConfigService`：凭据管理服务
- `LoginForm`：登录界面
- `AuthService`：认证服务

#### 关键方法
```csharp
// 保存凭据
SaveRememberedCredentials(username, password, remember)

// 加载凭据
LoadRememberedCredentials()

// 清除凭据
ClearRememberedCredentials()
```

#### 依赖项
- .NET Framework 4.8
- Windows DPAPI
- Newtonsoft.Json

### 📝 版本历史

#### v1.0.0 (2025-07-13)
- 初始实现记住密码功能
- 添加安全的密码存储
- 实现用户界面集成
- 添加完整的测试套件

## 支持和反馈

### 📞 获取帮助
如果遇到问题或需要帮助：
1. 查看本使用指南
2. 运行内置的验证工具
3. 检查日志文件
4. 联系技术支持

### 🐛 报告问题
报告问题时请提供：
- 详细的错误描述
- 重现步骤
- 系统环境信息
- 相关的日志信息

### 💡 功能建议
欢迎提供功能改进建议：
- 多用户凭据管理
- 凭据自动过期
- 生物识别集成
- 云同步支持
