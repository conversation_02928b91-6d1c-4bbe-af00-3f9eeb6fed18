using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.IO;
using System.Linq;
using NPOI.XWPF.UserModel;
using NPOI.OpenXmlFormats.Dml;
using NPOI.OpenXmlFormats.Dml.WordProcessing;
using NPOI.OpenXmlFormats.Wordprocessing;
using NPOI.Util;
using System.Drawing;
using System.Drawing.Imaging;

namespace RESClient.Services.Implementations
{
    /// <summary>
    /// 建筑物现状影像图模块生成器
    /// </summary>
    public class BuildingImageModuleGenerator : IReportModuleGenerator
    {
        private const string PHOTO_FOLDER_NAME = "照片";
        private const string TEMPLATE_FILE_NAME = "建筑物现状影像图.docx";

        /// <summary>
        /// 模块名称
        /// </summary>
        public string ModuleName => "建筑物现状影像图";

        /// <summary>
        /// 生成报告模块
        /// </summary>
        /// <param name="parameters">生成参数</param>
        /// <param name="progressCallback">进度回调</param>
        /// <returns>生成结果</returns>
        public async Task<bool> GenerateAsync(Dictionary<string, object> parameters, Action<int, string> progressCallback)
        {
            try
            {
                // 记录开始进度
                progressCallback?.Invoke(0, $"[INFO]开始生成{ModuleName}...");
            
                // 获取必要参数
                if (!parameters.TryGetValue("DataFolder", out object resourceDirObj) || 
                    !(resourceDirObj is string resourceDirectory))
                {
                    progressCallback?.Invoke(0, $"[ERROR]未提供资源目录路径");
                    return false;
                }

                if (!parameters.TryGetValue("OutputDirectory", out object outputDirObj) || 
                    !(outputDirObj is string outputDirectory))
                {
                    // 尝试使用OutputDir参数
                    if (!parameters.TryGetValue("OutputDir", out outputDirObj) || 
                        !(outputDirObj is string outputDir))
                    {
                        progressCallback?.Invoke(0, $"[ERROR]未提供输出目录路径");
                        return false;
                    }
                    outputDirectory = outputDir;
                }

                progressCallback?.Invoke(5, $"[INFO]资源目录: {resourceDirectory}");
                
                // 查找照片文件夹，可能在根目录或子目录中
                string photosDirectory = FindPhotosDirectory(resourceDirectory);
                
                if (string.IsNullOrEmpty(photosDirectory))
                {
                    progressCallback?.Invoke(0, $"[ERROR]在资源目录中未找到照片文件夹");
                    return false;
                }
                
                progressCallback?.Invoke(10, $"[INFO]找到照片文件夹: {photosDirectory}");

                // 获取模板文件路径
                string templateDirectory = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "报告模板");
                string templateFilePath = Path.Combine(templateDirectory, "10_建筑物现状影像图", TEMPLATE_FILE_NAME);
                
                if (!File.Exists(templateFilePath))
                {
                    progressCallback?.Invoke(0, $"[ERROR]模板文件不存在: {templateFilePath}");
                    return false;
                }
                
                progressCallback?.Invoke(15, $"[INFO]找到模板文件: {templateFilePath}");

                // 确保输出目录存在
                Directory.CreateDirectory(outputDirectory);
                string outputFilePath = Path.Combine(outputDirectory, TEMPLATE_FILE_NAME);

                // 复制模板文件到输出目录
                File.Copy(templateFilePath, outputFilePath, true);

                // 获取建筑物文件夹（按栋号命名的文件夹）
                string[] buildingDirectories = Directory.GetDirectories(photosDirectory);
                int totalBuildings = buildingDirectories.Length;
                
                progressCallback?.Invoke(20, $"[INFO]找到{totalBuildings}个建筑物文件夹");
                
                if (totalBuildings == 0)
                {
                    progressCallback?.Invoke(0, $"[ERROR]照片文件夹内没有建筑物文件夹");
                    return false;
                }

                foreach (string dir in buildingDirectories)
                {
                    progressCallback?.Invoke(20, $"[DEBUG]建筑物文件夹: {Path.GetFileName(dir)}");
                }

                // 获取所有建筑物的所有图片
                List<ImageInfo> allImages = new List<ImageInfo>();
                
                foreach (string buildingDir in buildingDirectories)
                {
                    string buildingName = Path.GetFileName(buildingDir); // 栋号（文件夹名称）
                    
                    // Format the building name according to the rules.
                    if (buildingName.Contains("#"))
                    {
                        buildingName = buildingName.Replace("#", "栋");
                    }
                    else if (int.TryParse(buildingName, out _))
                    {
                        buildingName += "栋";
                    }
                    
                    string[] imageFiles = Directory.GetFiles(buildingDir)
                        .Where(file => IsImageFile(file))
                        .ToArray();
                        
                    foreach (string imageFile in imageFiles)
                    {
                        string fileName = Path.GetFileNameWithoutExtension(imageFile);
                        DateTime creationTime = File.GetCreationTime(imageFile);
                        string shootingTime = creationTime.ToString("yyyy年MM月dd日");
                        
                        allImages.Add(new ImageInfo 
                        { 
                            BuildingName = buildingName,
                            ImagePath = imageFile,
                            FileName = fileName,
                            ShootingTime = shootingTime
                        });
                    }
                }
                
                progressCallback?.Invoke(25, $"[INFO]共找到 {allImages.Count} 张图片");
                
                if (allImages.Count == 0)
                {
                    progressCallback?.Invoke(0, $"[ERROR]未找到任何图片");
                    return false;
                }
                
                // 创建文档对象
                using (FileStream fs = new FileStream(outputFilePath, FileMode.Open, FileAccess.ReadWrite))
                {
                    XWPFDocument doc = new XWPFDocument(fs);
                    
                    // Vertically center the content on each page for this document section.
                    if (doc.Document.body != null)
                    {
                        CT_SectPr sectPr = doc.Document.body.sectPr;
                        if (sectPr == null)
                        {
                            sectPr = doc.Document.body.AddNewSectPr();
                        }
                        
                        CT_VerticalJc vAlign = sectPr.vAlign;
                        if (vAlign == null)
                        {
                            vAlign = new CT_VerticalJc();
                            sectPr.vAlign = vAlign;
                        }
                        vAlign.val = ST_VerticalJc.center;
                    }
                    
                    if (doc.Tables.Count < 2)
                    {
                        progressCallback?.Invoke(0, $"[ERROR]模板文件格式不正确，没有找到至少2个表格");
                        return false;
                    }
                    
                    // 创建模板表格的深层副本以备后用
                    XWPFTable originalTemplateTable = doc.Tables[1];
                    XWPFDocument tempDocForCopy = new XWPFDocument();
                    XWPFTable deepCopiedTemplateTable = tempDocForCopy.CreateTable();
                    CopyTableStructure(originalTemplateTable, deepCopiedTemplateTable);

                    // 处理每张图片
                    int totalImages = allImages.Count;
                    for (int i = 0; i < totalImages; i++)
                    {
                        ImageInfo imageInfo = allImages[i];
                        XWPFTable tableToUse;
                        
                        // 前两张图片使用现有表格，第三张及以后需要复制模板表格
                        if (i < 2)
                        {
                            tableToUse = doc.Tables[i];
                        }
                        else
                        {
                            // A better way to create a page break:
                            // Add a new paragraph and set its "Page Break Before" property to true.
                            // This avoids issues with spurious blank pages that can be caused by
                            // inserting a hard page break character after a table.
                            XWPFParagraph newPagePara = doc.CreateParagraph();
                            newPagePara.IsPageBreak = true;
                            
                            // Create the new table following this paragraph
                            tableToUse = doc.CreateTable();
                            CopyTableStructure(deepCopiedTemplateTable, tableToUse);
                        }
                        
                        // 替换占位符
                        ReplaceTablePlaceholders(tableToUse, imageInfo.BuildingName, imageInfo.ShootingTime, imageInfo.FileName);
                        
                        // 插入图片（在第三行第一列）
                        if (tableToUse.Rows.Count >= 3 && tableToUse.Rows[2].GetTableCells().Count >= 1)
                        {
                            XWPFTableCell cell = tableToUse.Rows[2].GetCell(0);
                            InsertImageToCell(doc, cell, imageInfo.ImagePath);
                        }
                        
                        // 更新进度
                        int progress = (int)((i + 1) * 100.0 / totalImages);
                        progressCallback?.Invoke(progress, $"[INFO]正在处理图片 {i + 1}/{totalImages} - {imageInfo.BuildingName}/{imageInfo.FileName}");
                    }
                    
                    // 保存文档
                    using (FileStream outStream = new FileStream(outputFilePath, FileMode.Create))
                    {
                        doc.Write(outStream);
                    }
                }
            
                // 记录完成进度
                progressCallback?.Invoke(100, $"[SUCCESS]{ModuleName}生成完成");
                return true;
            }
            catch (Exception ex)
            {
                progressCallback?.Invoke(0, $"[ERROR]{ModuleName}生成失败：{ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 图片信息类
        /// </summary>
        private class ImageInfo
        {
            public string BuildingName { get; set; }
            public string ImagePath { get; set; }
            public string FileName { get; set; }
            public string ShootingTime { get; set; }
        }

        /// <summary>
        /// 查找照片文件夹
        /// </summary>
        /// <param name="rootDirectory">根目录路径</param>
        /// <returns>照片文件夹路径，如果未找到则返回null</returns>
        private string FindPhotosDirectory(string rootDirectory)
        {
            // 首先检查根目录下是否直接有照片文件夹
            string directPath = Path.Combine(rootDirectory, PHOTO_FOLDER_NAME);
            if (Directory.Exists(directPath))
            {
                return directPath;
            }

            // 如果没有，检查根目录下的一级子目录
            foreach (string subDir in Directory.GetDirectories(rootDirectory))
            {
                string candidatePath = Path.Combine(subDir, PHOTO_FOLDER_NAME);
                if (Directory.Exists(candidatePath))
                {
                    return candidatePath;
                }
                
                // 检查文件夹名称是否为"照片"（不区分大小写）
                if (Path.GetFileName(subDir).Equals(PHOTO_FOLDER_NAME, StringComparison.OrdinalIgnoreCase))
                {
                    return subDir;
                }
            }

            return null;
        }

        /// <summary>
        /// 判断文件是否为图片
        /// </summary>
        private bool IsImageFile(string filePath)
        {
            string extension = Path.GetExtension(filePath).ToLower();
            return extension == ".jpg" || extension == ".jpeg" || extension == ".png" || 
                   extension == ".bmp" || extension == ".gif";
        }

        /// <summary>
        /// 复制表格结构
        /// </summary>
        private void CopyTableStructure(XWPFTable source, XWPFTable target)
        {
            // 复制表格级属性
            if (source.GetCTTbl().tblPr != null)
            {
                if (target.GetCTTbl().tblPr == null) target.GetCTTbl().AddNewTblPr();
                target.GetCTTbl().tblPr = (NPOI.OpenXmlFormats.Wordprocessing.CT_TblPr)source.GetCTTbl().tblPr.Copy();
            }

            // 复制表格网格
            if (source.GetCTTbl().tblGrid != null)
            {
                if (target.GetCTTbl().tblGrid == null) target.GetCTTbl().AddNewTblGrid();
                target.GetCTTbl().tblGrid = (NPOI.OpenXmlFormats.Wordprocessing.CT_TblGrid)source.GetCTTbl().tblGrid.Copy();
            }

            // 复制行
            for (int i = 0; i < source.Rows.Count; i++)
            {
                XWPFTableRow sourceRow = source.Rows[i];
                XWPFTableRow targetRow = target.CreateRow();

                // 复制行属性
                if (sourceRow.GetCTRow().trPr != null)
                {
                    targetRow.GetCTRow().AddNewTrPr();
                    targetRow.GetCTRow().trPr = (NPOI.OpenXmlFormats.Wordprocessing.CT_TrPr)sourceRow.GetCTRow().trPr.Copy();
                }

                // Ensure rows don't break across pages
                if (targetRow.GetCTRow().trPr == null) targetRow.GetCTRow().AddNewTrPr();
                CT_OnOff cantSplit = targetRow.GetCTRow().trPr.AddNewCantSplit();
                cantSplit.val = true;

                // 复制单元格
                for (int j = 0; j < sourceRow.GetTableCells().Count; j++)
                {
                    XWPFTableCell sourceCell = sourceRow.GetTableCells()[j];
                    XWPFTableCell targetCell = (j == 0) ? targetRow.GetCell(0) : targetRow.AddNewTableCell();

                    // 复制单元格属性
                    if (sourceCell.GetCTTc().tcPr != null)
                    {
                        if (targetCell.GetCTTc().tcPr == null)
                        {
                             targetCell.GetCTTc().AddNewTcPr();
                        }
                        targetCell.GetCTTc().tcPr = (NPOI.OpenXmlFormats.Wordprocessing.CT_TcPr)sourceCell.GetCTTc().tcPr.Copy();
                    }
            
                    // 新单元格有一个默认的空段落，在复制前应清除它
                    for (int p = targetCell.Paragraphs.Count - 1; p >= 0; p--)
                    {
                        targetCell.RemoveParagraph(p);
                    }

                    // 从源单元格复制段落
                    foreach (var paragraph in sourceCell.Paragraphs)
                    {
                        XWPFParagraph newPara = targetCell.AddParagraph();
                        CopyParagraph(paragraph, newPara);
                    }
                }
            }
    
            // 目标表格创建时带有一个空的默认行，将其删除
            if (target.Rows.Count > source.Rows.Count)
            {
                target.RemoveRow(0);
            }
        }

        private void CopyParagraph(XWPFParagraph source, XWPFParagraph target)
        {
            // 复制段落属性
            if (source.GetCTP().pPr != null)
            {
                if (target.GetCTP().pPr == null) target.GetCTP().AddNewPPr();
                target.GetCTP().pPr = (NPOI.OpenXmlFormats.Wordprocessing.CT_PPr)source.GetCTP().pPr.Copy();
            }

            // 复制运行块
            foreach (var sourceRun in source.Runs)
            {
                XWPFRun targetRun = target.CreateRun();
                CopyRun(sourceRun, targetRun);
            }
        }

        private void CopyRun(XWPFRun source, XWPFRun target)
        {
            // 复制运行块属性
            if (source.GetCTR().rPr != null)
            {
                if (target.GetCTR().rPr == null) target.GetCTR().AddNewRPr();
                target.GetCTR().rPr = (NPOI.OpenXmlFormats.Wordprocessing.CT_RPr)source.GetCTR().rPr.Copy();
            }
    
            // 复制文本
            target.SetText(source.Text, 0);
        }

        /// <summary>
        /// 替换表格中的占位符
        /// </summary>
        private void ReplaceTablePlaceholders(XWPFTable table, string dirname, string shootingTime, string filename)
        {
            foreach (XWPFTableRow row in table.Rows)
            {
                foreach (XWPFTableCell cell in row.GetTableCells())
                {
                    foreach (XWPFParagraph paragraph in cell.Paragraphs)
                    {
                        string text = paragraph.Text;
                        // A more robust check to see if a placeholder is likely present.
                        if (text.Contains("${") && text.Contains("}"))
                        {
                            // Preserve the first run's properties to re-apply them later.
                            // This helps maintain styling.
                            XWPFRun templateRun = null;
                            if (paragraph.Runs.Count > 0)
                            {
                                templateRun = paragraph.Runs[0];
                            }
                            
                            // Perform replacements for all placeholders.
                            // Handles both cases for Shootingtime as a safeguard.
                            string newText = text.Replace("${dirname}", dirname)
                                               .Replace("${Shootingtime}", shootingTime)
                                               .Replace("${shoottime}", shootingTime) 
                                               .Replace("${filename}", filename);

                            // Only rewrite the paragraph if a replacement actually occurred.
                            if (newText != text)
                            {
                                // Remove all existing runs from the paragraph. This is necessary to handle
                                // cases where a placeholder is split across multiple runs.
                                for (int i = paragraph.Runs.Count - 1; i >= 0; i--)
                                {
                                    paragraph.RemoveRun(i);
                                }

                                // Add a new run with the updated text and apply the original styling.
                                XWPFRun newRun = paragraph.CreateRun();
                                newRun.SetText(newText);

                                if (templateRun != null)
                                {
                                    newRun.FontFamily = templateRun.FontFamily;
                                    newRun.FontSize = templateRun.FontSize;
                                    newRun.IsBold = templateRun.IsBold;
                                    newRun.IsItalic = templateRun.IsItalic;
                                    newRun.IsStrikeThrough = templateRun.IsStrikeThrough;
                                }
                            }
                        }
                    }
                }
            }
        }

        /// <summary>
        /// 在单元格中插入图片
        /// </summary>
        private void InsertImageToCell(XWPFDocument doc, XWPFTableCell cell, string imagePath)
        {
            // 清除单元格中的现有内容
            for (int i = cell.Paragraphs.Count - 1; i >= 0; i--)
            {
                cell.RemoveParagraph(i);
            }

            // 添加一个新段落用于放置图片
            XWPFParagraph paragraph = cell.AddParagraph();
            paragraph.Alignment = ParagraphAlignment.CENTER;

            // Get available width in the cell in Twips (twentieths of a point)
            long availableWidthTwips = 0;
            if (cell.GetCTTc().tcPr?.tcW != null && ulong.TryParse(cell.GetCTTc().tcPr.tcW.w, out ulong cellWidth))
            {
                availableWidthTwips = (long)cellWidth;

                // Subtract margins (if any)
                if (cell.GetCTTc().tcPr.tcMar != null)
                {
                    if (cell.GetCTTc().tcPr.tcMar.left != null && ulong.TryParse(cell.GetCTTc().tcPr.tcMar.left.w, out ulong leftMargin))
                    {
                        availableWidthTwips -= (long)leftMargin;
                    }
                    if (cell.GetCTTc().tcPr.tcMar.right != null && ulong.TryParse(cell.GetCTTc().tcPr.tcMar.right.w, out ulong rightMargin))
                    {
                        availableWidthTwips -= (long)rightMargin;
                    }
                }
            }

            // If width couldn't be determined, use a default based on standard A4 page width
            if (availableWidthTwips <= 0)
            {
                // Approx 6.5 inches in twips for content area on A4 paper with 1" margins
                availableWidthTwips = (long)(6.5 * 1440); 
            }

            // Convert available width to EMU (English Metric Units)
            // 1 inch = 1440 twips
            // 1 inch = 914400 EMUs
            // 1 twip = 914400 / 1440 = 635 EMUs
            long maxWidthEmu = availableWidthTwips * 635;


            using (FileStream imageStream = new FileStream(imagePath, FileMode.Open, FileAccess.Read))
            using (Image img = Image.FromStream(imageStream, true, true))
            {
                // Convert image dimensions from pixels to EMU using image DPI
                long imgWidthEmu = (long)((double)img.Width / img.HorizontalResolution * 914400);
                long imgHeightEmu = (long)((double)img.Height / img.VerticalResolution * 914400);

                // Calculate scaled dimensions to fit width while maintaining aspect ratio
                double scaleRatio = (double)maxWidthEmu / imgWidthEmu;
                long finalWidthEmu = maxWidthEmu;
                long finalHeightEmu = (long)(imgHeightEmu * scaleRatio);
                
                // Don't let the image be taller than the page.
                // A4 height is 11.69 inches. Let's cap at ~8 inches to be safe with margins and headers.
                long maxHeightEmu = (long)(8.0 * 914400);
                if (finalHeightEmu > maxHeightEmu)
                {
                    scaleRatio = (double)maxHeightEmu / imgHeightEmu;
                    finalHeightEmu = maxHeightEmu;
                    finalWidthEmu = (long)(imgWidthEmu * scaleRatio);
                }

                imageStream.Position = 0;

                // 在段落中插入图片
                XWPFRun run = paragraph.CreateRun();
                run.AddPicture(imageStream, GetPictureType(imagePath), 
                               Path.GetFileName(imagePath), (int)finalWidthEmu, (int)finalHeightEmu);
            }
        }

        /// <summary>
        /// 根据文件扩展名确定图片类型
        /// </summary>
        private int GetPictureType(string filePath)
        {
            string extension = Path.GetExtension(filePath).ToLower();
            if (extension == ".jpg" || extension == ".jpeg")
                return (int)PictureType.JPEG;
            else if (extension == ".png")
                return (int)PictureType.PNG;
            else if (extension == ".gif")
                return (int)PictureType.GIF;
            else if (extension == ".bmp")
                return (int)PictureType.BMP;
            else
                return (int)PictureType.JPEG; // 默认使用JPEG
        }

        /// <summary>
        /// 是否可用
        /// </summary>
        /// <param name="parameters">检查参数</param>
        /// <returns>是否可用</returns>
        public bool IsAvailable(Dictionary<string, object> parameters)
        {
            try
            {
                if (parameters == null)
                {
                    Console.WriteLine($"[DEBUG]{ModuleName}: parameters是null");
                    return false;
                }

                if (!parameters.TryGetValue("DataFolder", out object resourceDirObj) || 
                    !(resourceDirObj is string resourceDirectory))
                {
                    Console.WriteLine($"[DEBUG]{ModuleName}: DataFolder参数不可用");
                    return false;
                }
                
                string templateDirectory = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "报告模板");
                string templateFilePath = Path.Combine(templateDirectory, "10_建筑物现状影像图", TEMPLATE_FILE_NAME);

                if (!File.Exists(templateFilePath))
                {
                    Console.WriteLine($"[DEBUG]{ModuleName}: 模板文件不存在 - {templateFilePath}");
                    return false;
                }

                // 查找照片文件夹
                string photosDirectory = FindPhotosDirectory(resourceDirectory);
                
                if (string.IsNullOrEmpty(photosDirectory))
                {
                    Console.WriteLine($"[DEBUG]{ModuleName}: 未找到照片文件夹");
                    return false;
                }
                
                string[] buildingDirectories = Directory.GetDirectories(photosDirectory);
                
                if (buildingDirectories.Length == 0)
                {
                    Console.WriteLine($"[DEBUG]{ModuleName}: 照片文件夹内没有建筑物文件夹");
                    return false;
                }
                
                // 检查至少有一个建筑物文件夹包含图片
                bool hasAnyImages = false;
                foreach (string buildingDir in buildingDirectories)
                {
                    string[] imageFiles = Directory.GetFiles(buildingDir)
                        .Where(file => IsImageFile(file))
                        .ToArray();
                        
                    if (imageFiles.Length > 0)
                    {
                        hasAnyImages = true;
                        break;
                    }
                }
                
                if (!hasAnyImages)
                {
                    Console.WriteLine($"[DEBUG]{ModuleName}: 所有建筑物文件夹中都没有图片");
                    return false;
                }

                Console.WriteLine($"[DEBUG]{ModuleName}: 模块可用，照片文件夹: {photosDirectory}，包含{buildingDirectories.Length}个建筑物文件夹");
                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[DEBUG]{ModuleName}: 检查可用性时发生异常 - {ex.Message}");
                return false;
            }
        }
    }
} 