using System;
using System.Windows.Forms;
using RESClient.MVP.Models;
using RESClient.MVP.Views;
using RESClient.Services;

namespace RESClient
{
    /// <summary>
    /// 测试封面参数功能的辅助类
    /// </summary>
    public class TestCoverParameters
    {
        /// <summary>
        /// 运行完整的封面参数功能测试
        /// </summary>
        public static void RunFullTest()
        {
            try
            {
                Console.WriteLine("开始测试封面参数功能...");

                // 测试1: 创建参数模型
                Console.WriteLine("1. 测试参数模型创建...");
                var parametersModel = new CoverParametersModel();
                var currentParams = parametersModel.GetCurrentParameters();
                Console.WriteLine($"   当前参数: 测绘公司={currentParams.SurveyCompany}");

                // 测试2: 保存和加载参数
                Console.WriteLine("2. 测试参数保存和加载...");
                currentParams.ProjectName = "测试项目";
                currentParams.ProjectAddress = "测试地址";
                bool saveResult = parametersModel.SaveParameters(currentParams);
                Console.WriteLine($"   保存结果: {saveResult}");

                // 测试3: 验证参数
                Console.WriteLine("3. 测试参数验证...");
                var validationResult = currentParams.Validate();
                Console.WriteLine($"   验证结果: {validationResult.IsValid}");
                if (!validationResult.IsValid)
                {
                    Console.WriteLine($"   验证错误: {string.Join(", ", validationResult.Errors)}");
                }

                // 测试4: 显示参数输入窗体
                Console.WriteLine("4. 测试参数输入窗体...");
                using (var form = new ParameterInputForm(currentParams))
                {
                    Console.WriteLine("   参数输入窗体创建成功");
                    // 不显示窗体，只测试创建
                }

                // 测试5: 测试模块设置服务
                Console.WriteLine("5. 测试模块设置服务...");
                var statusInfo = ModuleSettingsService.GetCoverModuleStatus();
                Console.WriteLine($"   模块状态: {statusInfo.GetStatusDescription()}");

                Console.WriteLine("所有测试完成！");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"测试过程中发生错误: {ex.Message}");
                Console.WriteLine($"详细信息: {ex}");
            }
        }

        /// <summary>
        /// 测试参数输入窗体的显示
        /// </summary>
        public static void TestParameterInputForm()
        {
            try
            {
                Application.EnableVisualStyles();
                Application.SetCompatibleTextRenderingDefault(false);

                var parametersModel = new CoverParametersModel();
                var currentParams = parametersModel.GetCurrentParameters();

                using (var form = new ParameterInputForm(currentParams))
                {
                    var result = form.ShowDialog();
                    Console.WriteLine($"窗体结果: {result}");
                    if (result == DialogResult.OK)
                    {
                        Console.WriteLine("用户确认了参数设置");
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"测试窗体时发生错误: {ex.Message}");
            }
        }
    }
}
