<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DWG 文件处理</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 20px;
        }
        h1 {
            color: #333;
            margin-top: 0;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input[type="file"] {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        button {
            background-color: #4CAF50;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover {
            background-color: #45a049;
        }
        button:disabled {
            background-color: #cccccc;
            cursor: not-allowed;
        }
        #status {
            margin-top: 20px;
            padding: 15px;
            border-radius: 4px;
            background-color: #f8f9fa;
            border-left: 4px solid #4CAF50;
            display: none;
        }
        #files {
            margin-top: 20px;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 10px;
            max-height: 300px;
            overflow-y: auto;
        }
        .file-item {
            padding: 8px;
            border-bottom: 1px solid #eee;
            display: flex;
            justify-content: space-between;
        }
        .file-item:last-child {
            border-bottom: none;
        }
        .directory-label {
            background-color: #e9ecef;
            border-radius: 4px;
            padding: 2px 6px;
            margin-left: 8px;
            font-size: 12px;
        }
        .processing-label {
            background-color: #cfe2ff;
            border-radius: 4px;
            padding: 2px 6px;
            margin-left: 8px;
            font-size: 12px;
        }
        .progress-container {
            margin-top: 20px;
            background-color: #e9ecef;
            border-radius: 4px;
            height: 20px;
            overflow: hidden;
        }
        .progress-bar {
            height: 100%;
            background-color: #4CAF50;
            width: 0%;
            transition: width 0.3s ease;
        }
        .result-links {
            margin-top: 20px;
            display: none;
        }
        .result-links a {
            display: inline-block;
            margin-right: 10px;
            text-decoration: none;
            color: #007bff;
        }
        .result-links a:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>DWG 文件处理</h1>
        
        <div class="form-group">
            <label for="file">选择 DWG 文件：</label>
            <input type="file" id="file" accept=".dwg" />
        </div>
        
        <button id="upload" onclick="uploadFile()">上传并处理</button>
        
        <div id="status"></div>
        
        <div class="progress-container" style="display: none;">
            <div class="progress-bar"></div>
        </div>
        
        <div id="files" style="display: none;">
            <h3>处理文件列表</h3>
            <div id="file-list"></div>
        </div>
        
        <div class="result-links">
            <a href="#" id="download-link" target="_blank">下载结果</a>
            <a href="#" id="details-link" target="_blank">查看详细信息</a>
        </div>
    </div>

    <script>
        let sessionId = null;
        let statusCheckInterval = null;
        let fileCheckInterval = null;
        
        async function uploadFile() {
            const fileInput = document.getElementById('file');
            const statusDiv = document.getElementById('status');
            const uploadButton = document.getElementById('upload');
            const progressContainer = document.querySelector('.progress-container');
            const progressBar = document.querySelector('.progress-bar');
            
            // 检查是否选择了文件
            if (!fileInput.files.length) {
                alert('请选择 DWG 文件');
                return;
            }
            
            const file = fileInput.files[0];
            if (!file.name.toLowerCase().endsWith('.dwg')) {
                alert('请选择 DWG 文件');
                return;
            }
            
            // 禁用上传按钮，防止重复提交
            uploadButton.disabled = true;
            
            // 显示状态
            statusDiv.style.display = 'block';
            statusDiv.textContent = '正在上传文件...';
            progressContainer.style.display = 'block';
            progressBar.style.width = '10%';
            
            // 准备表单数据
            const formData = new FormData();
            formData.append('file', file);
            
            try {
                // 发送上传请求
                const response = await fetch('/api/DrawingProcessing/process', {
                    method: 'POST',
                    body: formData
                });
                
                if (!response.ok) {
                    throw new Error(`上传失败: ${response.statusText}`);
                }
                
                const result = await response.json();
                sessionId = result.sessionId;
                
                // 更新状态
                statusDiv.textContent = `文件已上传，正在处理中... 会话 ID: ${sessionId}`;
                progressBar.style.width = '30%';
                
                // 显示文件列表区域
                document.getElementById('files').style.display = 'block';
                
                // 开始定期检查状态
                startStatusChecking();
                
                // 开始定期检查文件列表
                startFileChecking();
                
                // 更新下载链接
                updateResultLinks();
            } catch (error) {
                console.error('上传错误:', error);
                statusDiv.textContent = `错误: ${error.message}`;
                uploadButton.disabled = false;
            }
        }
        
        function startStatusChecking() {
            // 清除之前的间隔
            if (statusCheckInterval) {
                clearInterval(statusCheckInterval);
            }
            
            // 每 5 秒检查一次状态
            statusCheckInterval = setInterval(checkStatus, 5000);
            
            // 立即检查一次
            checkStatus();
        }
        
        async function checkStatus() {
            if (!sessionId) return;
            
            try {
                const response = await fetch(`/api/DrawingProcessing/status/${sessionId}`);
                
                if (!response.ok) {
                    if (response.status === 404) {
                        // 会话不存在，停止检查
                        clearInterval(statusCheckInterval);
                        document.getElementById('status').textContent = '找不到处理会话，可能已过期或被删除';
                        return;
                    }
                    throw new Error(`获取状态失败: ${response.statusText}`);
                }
                
                const status = await response.json();
                const statusDiv = document.getElementById('status');
                const progressBar = document.querySelector('.progress-bar');
                
                // 更新状态
                statusDiv.textContent = `状态: ${status.currentOperation} (${status.percentComplete}%)`;
                progressBar.style.width = `${status.percentComplete}%`;
                
                // 如果已完成，停止检查
                if (status.isCompleted) {
                    clearInterval(statusCheckInterval);
                    statusDiv.textContent = '处理完成！';
                    document.getElementById('upload').disabled = false;
                    
                    // 显示结果链接
                    document.querySelector('.result-links').style.display = 'block';
                }
                
                // 如果有错误，显示错误信息
                if (status.hasError) {
                    statusDiv.textContent = `错误: ${status.errorMessage}`;
                    clearInterval(statusCheckInterval);
                    document.getElementById('upload').disabled = false;
                }
            } catch (error) {
                console.error('检查状态错误:', error);
            }
        }
        
        function startFileChecking() {
            // 清除之前的间隔
            if (fileCheckInterval) {
                clearInterval(fileCheckInterval);
            }
            
            // 每 5 秒检查一次文件列表
            fileCheckInterval = setInterval(checkFiles, 5000);
            
            // 立即检查一次
            checkFiles();
        }
        
        async function checkFiles() {
            if (!sessionId) return;
            
            try {
                const response = await fetch(`/api/DrawingProcessing/files/${sessionId}`);
                
                if (!response.ok) {
                    if (response.status === 404) {
                        // 会话不存在，停止检查
                        clearInterval(fileCheckInterval);
                        return;
                    }
                    throw new Error(`获取文件列表失败: ${response.statusText}`);
                }
                
                const result = await response.json();
                const fileListDiv = document.getElementById('file-list');
                
                // 清空当前列表
                fileListDiv.innerHTML = '';
                
                // 添加文件
                if (result.files && result.files.length) {
                    result.files.forEach(file => {
                        const fileItem = document.createElement('div');
                        fileItem.className = 'file-item';
                        
                        const nameSpan = document.createElement('span');
                        nameSpan.textContent = file.fileName;
                        
                        // 如果是目录，添加标签
                        if (file.isDirectory) {
                            const dirLabel = document.createElement('span');
                            dirLabel.className = 'directory-label';
                            dirLabel.textContent = '目录';
                            nameSpan.appendChild(dirLabel);
                        }
                        
                        // 如果在处理目录中，添加标签
                        if (file.isInProcessingDir) {
                            const procLabel = document.createElement('span');
                            procLabel.className = 'processing-label';
                            procLabel.textContent = '处理目录';
                            nameSpan.appendChild(procLabel);
                        }
                        
                        const sizeSpan = document.createElement('span');
                        if (!file.isDirectory) {
                            sizeSpan.textContent = formatFileSize(file.fileSize);
                        }
                        
                        fileItem.appendChild(nameSpan);
                        fileItem.appendChild(sizeSpan);
                        fileListDiv.appendChild(fileItem);
                    });
                } else {
                    fileListDiv.innerHTML = '<p>暂无文件</p>';
                }
            } catch (error) {
                console.error('检查文件列表错误:', error);
            }
        }
        
        function updateResultLinks() {
            if (!sessionId) return;
            
            const downloadLink = document.getElementById('download-link');
            downloadLink.href = `/api/DrawingProcessing/download/${sessionId}`;
            
            const detailsLink = document.getElementById('details-link');
            detailsLink.href = `/api/DrawingProcessing/status/${sessionId}`;
        }
        
        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }
    </script>
</body>
</html> 