using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Linq;
using System.Reflection;
using System.Windows.Forms;
using RESClient.MVP.Models;
using RESClient.MVP.Presenters;

namespace RESClient.MVP.Views
{
    /// <summary>
    /// 地下室人防区域说明参数输入窗体
    /// </summary>
    public partial class BasementDefenseParameterInputForm : Form
    {
        private BasementDefenseParametersModel.BasementDefenseParameters _parameters;
        private Dictionary<string, Control> _inputControls;
        private TableLayoutPanel _mainPanel;
        private Button _okButton;
        private Button _cancelButton;
        private Button _resetButton;

        /// <summary>
        /// 是否已确认
        /// </summary>
        public bool IsConfirmed { get; private set; } = false;

        /// <summary>
        /// 参数对象
        /// </summary>
        public BasementDefenseParametersModel.BasementDefenseParameters Parameters => _parameters;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="parameters">地下室人防区域说明参数</param>
        public BasementDefenseParameterInputForm(BasementDefenseParametersModel.BasementDefenseParameters parameters)
        {
            _parameters = parameters ?? new BasementDefenseParametersModel.BasementDefenseParameters();
            _inputControls = new Dictionary<string, Control>();

            InitializeComponent();
            InitializeCustomControls();
            CreateDynamicControls();
            LoadParameterValues();
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();
            this.ResumeLayout(false);
        }

        private void InitializeCustomControls()
        {
            // 窗体设置
            this.Text = "地下室人防区域说明参数设置";
            this.Size = new Size(620, 400);  // 适当增加高度以容纳多行文本框
            this.MinimumSize = new Size(520, 350);
            this.MaximumSize = new Size(800, 500);
            this.StartPosition = FormStartPosition.CenterParent;
            this.FormBorderStyle = FormBorderStyle.Sizable;
            this.MaximizeBox = true;
            this.MinimizeBox = false;
            this.ShowInTaskbar = false;

            // 创建主容器面板
            var mainContainer = new Panel
            {
                Dock = DockStyle.Fill,
                Padding = new Padding(15, 15, 15, 60)
            };

            // 创建可滚动的主面板 - 与封面参数输入窗体保持一致
            _mainPanel = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                AutoScroll = true,
                AutoSize = true,
                AutoSizeMode = AutoSizeMode.GrowAndShrink,
                ColumnCount = 2,
                Padding = new Padding(5),
                CellBorderStyle = TableLayoutPanelCellBorderStyle.None
            };

            // 设置列样式 - 与封面参数输入窗体保持一致
            _mainPanel.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 140));  // 标签列
            _mainPanel.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 100));   // 输入列

            mainContainer.Controls.Add(_mainPanel);
            this.Controls.Add(mainContainer);
        }

        private void CreateDynamicControls()
        {
            var properties = typeof(BasementDefenseParametersModel.BasementDefenseParameters).GetProperties()
                .Where(p => p.CanRead && p.CanWrite)
                .ToArray();

            _mainPanel.RowCount = properties.Length + 1; // +1 for buttons

            int rowIndex = 0;
            foreach (var property in properties)
            {
                CreateControlForProperty(property, rowIndex);
                rowIndex++;
            }

            // 创建按钮面板
            CreateButtonPanel(rowIndex);
        }

        private void CreateControlForProperty(PropertyInfo property, int rowIndex)
        {
            // 获取显示名称
            var displayNameAttr = property.GetCustomAttribute<DisplayNameAttribute>();
            string displayName = displayNameAttr?.DisplayName ?? property.Name;

            // 获取描述
            var descriptionAttr = property.GetCustomAttribute<DescriptionAttribute>();
            string description = descriptionAttr?.Description ?? "";

            // 设置行样式
            int rowHeight = GetRowHeight(property);
            _mainPanel.RowStyles.Add(new RowStyle(SizeType.Absolute, rowHeight));

            // 创建标签容器
            var labelContainer = new Panel
            {
                Dock = DockStyle.Fill,
                Margin = new Padding(3, 8, 3, 8),
                Padding = new Padding(5, 0, 10, 0)
            };

            // 创建标签
            var label = new Label
            {
                Text = displayName + ":",  // 添加冒号后缀
                Dock = DockStyle.Fill,
                TextAlign = ContentAlignment.MiddleRight,  // 右对齐
                Font = new Font(this.Font.FontFamily, this.Font.Size, FontStyle.Regular),
                ForeColor = SystemColors.ControlText
            };

            labelContainer.Controls.Add(label);

            // 创建输入控件容器
            var inputContainer = new Panel
            {
                Dock = DockStyle.Fill,
                Margin = new Padding(3, 8, 3, 8),
                Padding = new Padding(0, 0, 5, 0)
            };

            // 创建输入控件
            var inputControl = CreateInputControl(property);
            inputControl.Dock = DockStyle.Top;
            inputContainer.Controls.Add(inputControl);

            // 添加工具提示
            if (!string.IsNullOrEmpty(description))
            {
                var toolTip = new ToolTip();
                toolTip.SetToolTip(label, description);
                toolTip.SetToolTip(inputControl, description);
            }

            // 添加到主面板
            _mainPanel.Controls.Add(labelContainer, 0, rowIndex);
            _mainPanel.Controls.Add(inputContainer, 1, rowIndex);

            // 保存控件引用
            _inputControls[property.Name] = inputControl;
        }

        private int GetRowHeight(PropertyInfo property)
        {
            // "说明" 字段需要更高的行高用于多行文本框
            if (property.PropertyType == typeof(string) && property.Name == "Description")
            {
                return 80;
            }

            return 40;
        }

        private Control CreateInputControl(PropertyInfo property)
        {
            if (property.PropertyType == typeof(DateTime))
            {
                var dateTimePicker = new DateTimePicker
                {
                    Format = DateTimePickerFormat.Long,
                    ShowUpDown = false,
                    Font = new Font(this.Font.FontFamily, this.Font.Size, FontStyle.Regular),
                    Height = 25,
                    Width = 200,  // 设置固定宽度以确保与封面参数对话框一致
                    MinimumSize = new Size(200, 25),  // 确保最小尺寸一致性
                    Anchor = AnchorStyles.Left | AnchorStyles.Top  // 左对齐，避免拉伸
                };
                return dateTimePicker;
            }
            else if (property.PropertyType == typeof(string))
            {
                // "说明" 字段使用多行文本框，其他字段使用单行文本框
                bool isMultiline = property.Name == "Description";

                var textBox = new TextBox
                {
                    Font = new Font(this.Font.FontFamily, this.Font.Size, FontStyle.Regular),
                    Multiline = isMultiline,
                    ScrollBars = isMultiline ? ScrollBars.Vertical : ScrollBars.None,
                    Height = isMultiline ? 60 : 25,
                    TextAlign = HorizontalAlignment.Left,
                    Anchor = AnchorStyles.Left | AnchorStyles.Top | AnchorStyles.Right
                };

                if (isMultiline)
                {
                    textBox.AcceptsReturn = true;
                    textBox.WordWrap = true;
                }

                return textBox;
            }

            // 默认返回文本框
            return new TextBox
            {
                Font = new Font(this.Font.FontFamily, this.Font.Size, FontStyle.Regular),
                Height = 25,
                TextAlign = HorizontalAlignment.Left,
                Anchor = AnchorStyles.Left | AnchorStyles.Top | AnchorStyles.Right
            };
        }

        private void CreateButtonPanel(int rowIndex)
        {
            // 创建固定在底部的按钮面板 - 与作业声明参数输入窗体保持一致
            var buttonContainer = new Panel
            {
                Height = 50,
                Dock = DockStyle.Bottom,
                BackColor = SystemColors.Control,
                Padding = new Padding(15, 10, 15, 10)
            };

            var buttonPanel = new FlowLayoutPanel
            {
                Dock = DockStyle.Fill,
                FlowDirection = FlowDirection.RightToLeft,
                WrapContents = false,
                AutoSize = true
            };

            // 取消按钮
            _cancelButton = new Button
            {
                Text = "取消",
                Size = new Size(80, 32),
                DialogResult = DialogResult.Cancel,
                Margin = new Padding(5, 0, 0, 0),
                UseVisualStyleBackColor = true,
                Font = new Font(this.Font.FontFamily, this.Font.Size, FontStyle.Regular)
            };
            _cancelButton.Click += CancelButton_Click;

            // 确定按钮
            _okButton = new Button
            {
                Text = "确定",
                Size = new Size(80, 32),
                Margin = new Padding(5, 0, 0, 0),
                UseVisualStyleBackColor = true,
                Font = new Font(this.Font.FontFamily, this.Font.Size, FontStyle.Bold)
            };
            _okButton.Click += OkButton_Click;

            // 重置按钮
            _resetButton = new Button
            {
                Text = "重置",
                Size = new Size(80, 32),
                Margin = new Padding(5, 0, 0, 0),
                UseVisualStyleBackColor = true,
                Font = new Font(this.Font.FontFamily, this.Font.Size, FontStyle.Regular)
            };
            _resetButton.Click += ResetButton_Click;

            // 按钮添加顺序：取消、确定、重置（从右到左）
            buttonPanel.Controls.Add(_cancelButton);
            buttonPanel.Controls.Add(_okButton);
            buttonPanel.Controls.Add(_resetButton);

            buttonContainer.Controls.Add(buttonPanel);

            // 将按钮容器添加到窗体而不是TableLayoutPanel
            this.Controls.Add(buttonContainer);

            // 设置默认按钮
            this.CancelButton = _cancelButton;
        }

        private void LoadParameterValues()
        {
            var properties = typeof(BasementDefenseParametersModel.BasementDefenseParameters).GetProperties()
                .Where(p => p.CanRead && p.CanWrite);

            foreach (var property in properties)
            {
                if (_inputControls.TryGetValue(property.Name, out Control control))
                {
                    var value = property.GetValue(_parameters);
                    SetControlValue(control, value);
                }
            }
        }

        private void SetControlValue(Control control, object value)
        {
            switch (control)
            {
                case TextBox textBox:
                    textBox.Text = value?.ToString() ?? "";
                    break;
                case DateTimePicker dateTimePicker:
                    if (value is DateTime dateTime)
                        dateTimePicker.Value = dateTime;
                    break;
            }
        }

        private void SaveParameterValues()
        {
            var properties = typeof(BasementDefenseParametersModel.BasementDefenseParameters).GetProperties()
                .Where(p => p.CanRead && p.CanWrite);

            foreach (var property in properties)
            {
                if (_inputControls.TryGetValue(property.Name, out Control control))
                {
                    var value = GetControlValue(control, property.PropertyType);
                    property.SetValue(_parameters, value);
                }
            }
        }

        private object GetControlValue(Control control, Type targetType)
        {
            switch (control)
            {
                case TextBox textBox:
                    // 如果文本框为空，自动填充反斜杠占位符并记录警告
                    string textValue = textBox.Text?.Trim() ?? "";
                    if (string.IsNullOrEmpty(textValue))
                    {
                        textValue = "\\";
                        // 记录参数为空的警告到错误收集器
                        LogEmptyParameterWarning(control.Name, "地下室人防区域说明");
                    }
                    return textValue;
                case DateTimePicker dateTimePicker:
                    return dateTimePicker.Value;
                default:
                    return null;
            }
        }

        /// <summary>
        /// 记录空参数警告
        /// </summary>
        /// <param name="parameterName">参数名称</param>
        /// <param name="moduleName">模块名称</param>
        private void LogEmptyParameterWarning(string parameterName, string moduleName)
        {
            try
            {
                // 获取主窗体的错误收集器
                var mainForm = Application.OpenForms.OfType<Form>()
                    .FirstOrDefault(f => f.GetType().Name == "MainForm");

                if (mainForm != null)
                {
                    // 通过反射获取MainPresenter和ErrorCollector
                    var presenterField = mainForm.GetType().GetField("_presenter",
                        System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);

                    if (presenterField?.GetValue(mainForm) is MainPresenter presenter)
                    {
                        var modelProperty = presenter.GetType().GetProperty("Model",
                            System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);

                        if (modelProperty?.GetValue(presenter) is MainModel model)
                        {
                            model.ErrorCollector.AddError(
                                Services.ErrorType.ParameterConfiguration,
                                Services.ErrorSeverity.Warning,
                                moduleName,
                                $"参数 '{GetParameterDisplayName(parameterName)}' 未填写",
                                details: $"用户未填写参数 '{parameterName}'，已自动使用占位符 '\\'",
                                suggestedSolution: "建议在参数设置中填写完整的参数信息以获得更好的报告质量");
                        }
                    }
                }
            }
            catch (Exception)
            {
                // 忽略警告记录失败，不影响主要功能
            }
        }

        /// <summary>
        /// 获取参数显示名称
        /// </summary>
        /// <param name="parameterName">参数名称</param>
        /// <returns>显示名称</returns>
        private string GetParameterDisplayName(string parameterName)
        {
            // 通过反射获取DisplayName特性
            try
            {
                var property = typeof(BasementDefenseParametersModel.BasementDefenseParameters).GetProperty(parameterName);
                var displayNameAttr = property?.GetCustomAttribute<System.ComponentModel.DisplayNameAttribute>();
                return displayNameAttr?.DisplayName ?? parameterName;
            }
            catch
            {
                return parameterName;
            }
        }

        private void OkButton_Click(object sender, EventArgs e)
        {
            try
            {
                // 保存当前输入的参数值
                SaveParameterValues();

                // 验证参数
                var validationResult = _parameters.Validate();
                if (!validationResult.IsValid)
                {
                    // 构建详细的错误信息
                    string errorMessage = "参数验证失败，请检查以下问题：\n\n";
                    for (int i = 0; i < validationResult.Errors.Count; i++)
                    {
                        errorMessage += $"{i + 1}. {validationResult.Errors[i]}\n";
                    }
                    errorMessage += "\n请修正上述问题后重试。";

                    // 显示错误提示，用户点击确定后返回到输入窗体继续编辑
                    MessageBox.Show(errorMessage, "参数验证失败",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);

                    // 验证失败时不关闭窗体，让用户继续编辑
                    return;
                }

                // 验证成功，设置确认标志并关闭窗体
                IsConfirmed = true;
                this.DialogResult = DialogResult.OK;
                this.Close();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"保存参数时发生错误: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void CancelButton_Click(object sender, EventArgs e)
        {
            IsConfirmed = false;
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }

        private void ResetButton_Click(object sender, EventArgs e)
        {
            try
            {
                // 重置为默认参数
                _parameters = new BasementDefenseParametersModel.BasementDefenseParameters();
                LoadParameterValues();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"重置参数时发生错误: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }
}
