using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using System.Drawing;
using System.Drawing.Imaging;
using NPOI.XWPF.UserModel;
using System.Diagnostics;

namespace RESClient.Services.Implementations
{
    /// <summary>
    /// 项目住地及测绘房屋分布图模块生成器
    /// </summary>
    public class ProjectDistributionMapModuleGenerator : IReportModuleGenerator
    {
        /// <summary>
        /// 模块名称
        /// </summary>
        public string ModuleName => "项目丘地及测绘房屋分布图";

        /// <summary>
        /// 生成报告模块
        /// </summary>
        /// <param name="parameters">生成参数</param>
        /// <param name="progressCallback">进度回调</param>
        /// <returns>生成结果</returns>
        public async Task<bool> GenerateAsync(Dictionary<string, object> parameters, Action<int, string> progressCallback)
        {
            try
            {
                // 记录开始进度
                progressCallback?.Invoke(0, $"[INFO]开始生成{ModuleName}...");

                // 1. 获取选择的数据路径
                if (!parameters.TryGetValue("DataFolder", out object dataDirectoryObj) || dataDirectoryObj == null)
                {
                    progressCallback?.Invoke(0, "[ERROR]未找到数据目录");
                    return false;
                }

                string dataDirectory = dataDirectoryObj.ToString();
                if (!Directory.Exists(dataDirectory))
                {
                    progressCallback?.Invoke(0, $"[ERROR]数据目录不存在: {dataDirectory}");
                    return false;
                }

                // 2. 获取输出目录
                if (!parameters.TryGetValue("OutputDir", out object outputDirectoryObj) || outputDirectoryObj == null)
                {
                    progressCallback?.Invoke(0, "[ERROR]未找到输出目录");
                    return false;
                }

                string outputDirectory = outputDirectoryObj.ToString();
                if (!Directory.Exists(outputDirectory))
                {
                    Directory.CreateDirectory(outputDirectory);
                }

                progressCallback?.Invoke(10, "[INFO]查找丘地和产权DWG文件...");

                // 3. 查找包含"丘地"和"产权"的DWG文件
                var dwgFiles = FindDwgFiles(dataDirectory);
                
                if (dwgFiles == null || dwgFiles.Length == 0)
                {
                    progressCallback?.Invoke(10, "[ERROR]未找到包含丘地和产权的DWG文件");
                    return false;
                }

                progressCallback?.Invoke(20, $"[INFO]找到{dwgFiles.Length}个丘地和产权DWG文件");
                foreach (var file in dwgFiles)
                {
                    progressCallback?.Invoke(20, $"[DEBUG]找到文件: {Path.GetFileName(file)}");
                }

                // 4. 创建临时输出目录
                string tempOutputDir = Path.Combine(Path.GetTempPath(), $"DistributionMap_{DateTime.Now.Ticks}");
                Directory.CreateDirectory(tempOutputDir);

                progressCallback?.Invoke(30, "[INFO]转换DWG文件到图片...");

                // 5. 转换DWG文件为图片
                var imageFiles = await ConvertDwgToImages(dwgFiles, tempOutputDir, progressCallback);
                if (imageFiles == null || imageFiles.Length == 0)
                {
                    progressCallback?.Invoke(40, "[ERROR]转换DWG文件失败");
                    return false;
                }

                progressCallback?.Invoke(50, $"[INFO]成功转换{imageFiles.Length}个DWG文件到图片");

                // 6. 获取模板路径
                string templateDirectory = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "报告模板");
                string templatePath = Path.Combine(templateDirectory, "09_项目丘地及测绘房屋分布图", "项目住地及测绘房屋分布图.docx");
                
                if (!File.Exists(templatePath))
                {
                    progressCallback?.Invoke(60, $"[ERROR]模板文件不存在: {templatePath}");
                    return false;
                }

                progressCallback?.Invoke(70, "[INFO]读取模板文件...");

                // 7. 创建输出文件路径
                string outputPath = Path.Combine(outputDirectory, $"{ModuleName}.docx");

                // 8. 处理图片并生成Word文档
                bool result = await Task.Run(() => GenerateWordDocument(templatePath, outputPath, imageFiles, progressCallback));

                // 9. 清理临时文件
                try
                {
                    if (Directory.Exists(tempOutputDir))
                    {
                        Directory.Delete(tempOutputDir, true);
                    }
                }
                catch (Exception ex)
                {
                    progressCallback?.Invoke(95, $"[WARNING]清理临时文件失败: {ex.Message}");
                    // 继续执行, 不影响结果
                }

                progressCallback?.Invoke(100, result ? $"[SUCCESS]{ModuleName}生成完成" : $"[ERROR]{ModuleName}生成失败");
                return result;
            }
            catch (Exception ex)
            {
                progressCallback?.Invoke(100, $"[ERROR]{ModuleName}生成失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 查找包含"丘地"和"产权"的DWG文件
        /// </summary>
        /// <param name="directoryPath">目录路径</param>
        /// <returns>文件路径数组</returns>
        private string[] FindDwgFiles(string directoryPath)
        {
            try
            {
                // 递归搜索所有子目录中的DWG文件
                return Directory.GetFiles(directoryPath, "*.dwg", SearchOption.AllDirectories)
                    .Where(file => {
                        // 文件名需要同时包含"丘地"和"产权"
                        string fileName = Path.GetFileNameWithoutExtension(file).ToLower();
                        return (fileName.Contains("丘地") && fileName.Contains("产权"));
                    })
                    .Take(1) // 只读取第一个文件
                    .ToArray();
            }
            catch (Exception)
            {
                return new string[0];
            }
        }

        /// <summary>
        /// 转换DWG文件为图片
        /// </summary>
        /// <param name="dwgFiles">DWG文件路径数组</param>
        /// <param name="outputDir">输出目录</param>
        /// <param name="progressCallback">进度回调</param>
        /// <returns>生成的图片文件路径数组</returns>
        private async Task<string[]> ConvertDwgToImages(string[] dwgFiles, string outputDir, Action<int, string> progressCallback)
        {
            return await Task.Run(() =>
            {
                string toolDir = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "tool", "CADC");
                string dllPath = Path.Combine(toolDir, "ACAD.dll");
                string exePath = Path.Combine(toolDir, "ACAD.exe");
                bool exeCreated = false;

                try
                {
                    if (dwgFiles == null || dwgFiles.Length == 0)
                    {
                        return new string[0];
                    }

                    // 工具路径
                    if (!Directory.Exists(toolDir))
                    {
                        progressCallback?.Invoke(35, $"[ERROR]工具目录不存在: {toolDir}");
                        return new string[0];
                    }

                    // 检查是否需要更名
                    if (File.Exists(dllPath) && !File.Exists(exePath))
                    {
                        progressCallback?.Invoke(35, "[INFO]准备转换DLL为EXE...");
                        try
                        {
                            File.Copy(dllPath, exePath);
                            exeCreated = true;
                        }
                        catch (Exception ex)
                        {
                            progressCallback?.Invoke(35, $"[ERROR]转换DLL为EXE失败: {ex.Message}");
                            return new string[0];
                        }
                    }
                    else if (!File.Exists(exePath))
                    {
                        progressCallback?.Invoke(35, $"[ERROR]未找到转换工具: {exePath}");
                        return new string[0];
                    }

                    progressCallback?.Invoke(36, "[INFO]开始转换DWG文件...");

                    // 创建命令行参数，使用固定尺寸
                    int dpi = 300;
                    string baseArguments = $"/r /e /p 1 /l convert.log /f 3 /d \"{outputDir}\" /w {210}mm /h {297}mm /res {dpi} /key 15KJNKUPIY5D7WL9NSLSYZHCG3SGWMBOX2JYDWBEFPX8A9ZC7GJXMW09MOMCS";
                    
                    // 添加所有DWG文件到一个命令中
                    string arguments = baseArguments;
                    foreach (string dwgFile in dwgFiles)
                    {
                        arguments += $" \"{dwgFile}\"";
                    }

                    // 启动转换进程
                    ProcessStartInfo psi = new ProcessStartInfo(exePath, arguments);
                    psi.CreateNoWindow = true;
                    psi.UseShellExecute = false;
                    psi.RedirectStandardOutput = true;
                    psi.RedirectStandardError = true;
                    
                    progressCallback?.Invoke(37, "[INFO]执行转换命令...");
                    
                    using (Process process = Process.Start(psi))
                    {
                        string output = process.StandardOutput.ReadToEnd();
                        string error = process.StandardError.ReadToEnd();
                        process.WaitForExit();
                        
                        if (process.ExitCode != 0)
                        {
                            progressCallback?.Invoke(38, $"[ERROR]转换DWG文件失败。错误代码: {process.ExitCode}, 错误信息: {error}");
                            return new string[0];
                        }
                        else
                        {
                            progressCallback?.Invoke(38, "[INFO]DWG文件转换成功");
                        }
                    }

                    // 查找生成的GIF文件
                    progressCallback?.Invoke(39, "[INFO]查找转换后的图片文件...");
                    string[] imageFiles = Directory.GetFiles(outputDir, "*.gif", SearchOption.TopDirectoryOnly);
                    
                    return imageFiles;
                }
                catch (Exception ex)
                {
                    progressCallback?.Invoke(38, $"[ERROR]转换过程中发生错误: {ex.Message}");
                    return new string[0];
                }
                finally
                {
                    // 清理EXE
                    if (exeCreated && File.Exists(exePath))
                    {
                        try { File.Delete(exePath); } catch { /* best effort */ }
                    }
                }
            });
        }

        /// <summary>
        /// 生成Word文档并插入图片
        /// </summary>
        /// <param name="templatePath">模板路径</param>
        /// <param name="outputPath">输出路径</param>
        /// <param name="imageFiles">图片文件路径数组</param>
        /// <param name="progressCallback">进度回调</param>
        /// <returns>是否成功</returns>
        private bool GenerateWordDocument(string templatePath, string outputPath, string[] imageFiles, Action<int, string> progressCallback)
        {
            // 创建临时文件
            string tempFilePath = Path.Combine(
                Path.GetTempPath(),
                $"temp_distmap_{DateTime.Now.Ticks}.docx");
            
            try
            {
                // 复制模板到临时文件
                File.Copy(templatePath, tempFilePath, true);
                
                progressCallback?.Invoke(75, "[INFO]复制模板完成，准备处理图片...");

                // 打开文档
                XWPFDocument doc;
                using (FileStream fs = new FileStream(tempFilePath, FileMode.Open, FileAccess.ReadWrite))
                {
                    doc = new XWPFDocument(fs);
                }

                // 获取模板中的占位图片尺寸信息 (in EMUs)
                Tuple<long, long> templateImageEmuSize = null;
                bool foundPlaceholder = false;

                // 查找模板中的占位图片尺寸
                foreach (var element in doc.BodyElements.OfType<XWPFParagraph>())
                {
                    foreach (var run in element.Runs)
                    {
                        foreach (var picture in run.GetEmbeddedPictures())
                        {
                            var ctPicture = picture.GetCTPicture();
                            if (ctPicture?.spPr?.xfrm?.ext != null)
                            {
                                long widthEmu = ctPicture.spPr.xfrm.ext.cx;
                                long heightEmu = ctPicture.spPr.xfrm.ext.cy;

                                if (widthEmu > 0 && heightEmu > 0) // 确保尺寸有效
                                {
                                    templateImageEmuSize = Tuple.Create(widthEmu, heightEmu);
                                    foundPlaceholder = true;
                                    progressCallback?.Invoke(80, $"[INFO]找到占位图片，尺寸: {widthEmu}x{heightEmu} EMU");
                                    break;
                                }
                            }
                        }
                        if (foundPlaceholder) break;
                    }
                    if (foundPlaceholder) break;
                }

                if (!foundPlaceholder)
                {
                    progressCallback?.Invoke(80, "[ERROR]无法在模板中找到有效的占位图片尺寸");
                    return false;
                }

                // 存储所有包含图片的段落，以便后续替换
                var paragraphsWithImages = new List<XWPFParagraph>();
                foreach (var element in doc.BodyElements.OfType<XWPFParagraph>())
                {
                    foreach (var run in element.Runs)
                    {
                        if (run.GetEmbeddedPictures().Count > 0)
                        {
                            paragraphsWithImages.Add(element);
                            break;
                        }
                    }
                }

                progressCallback?.Invoke(85, "[INFO]删除模板中的占位图片...");

                // 删除包含图片的段落
                foreach (var paragraph in paragraphsWithImages)
                {
                    doc.RemoveBodyElement(doc.GetPosOfParagraph(paragraph));
                }

                // 添加新图片
                progressCallback?.Invoke(90, "[INFO]添加图片到文档...");

                for (int i = 0; i < imageFiles.Length; i++)
                {
                    string imagePath = imageFiles[i];
                    XWPFParagraph paragraph = doc.CreateParagraph();
                    paragraph.Alignment = ParagraphAlignment.CENTER; // 居中对齐
                    XWPFRun run = paragraph.CreateRun();

                    using (FileStream imageStream = new FileStream(imagePath, FileMode.Open, FileAccess.Read))
                    {
                        int pictureFormat = GetPictureTypeForNPOI(Path.GetExtension(imagePath));
                        
                        using (Image img = Image.FromStream(imageStream, false, false)) // 保持流打开，暂不验证图像数据
                        {
                            SizeF originalDpi = new SizeF(img.HorizontalResolution, img.VerticalResolution);
                            if (originalDpi.Width == 0) originalDpi.Width = 96f; // 默认DPI
                            if (originalDpi.Height == 0) originalDpi.Height = 96f;

                            Tuple<long, long> finalEmuDimensions = CalculateScaledEmuDimensions(
                                img.Size, 
                                originalDpi, 
                                templateImageEmuSize);

                            // 重置流位置以便AddPicture使用
                            imageStream.Position = 0;
                            run.AddPicture(imageStream, 
                                          pictureFormat, 
                                          Path.GetFileName(imagePath), 
                                          (int)finalEmuDimensions.Item1,
                                          (int)finalEmuDimensions.Item2);

                            progressCallback?.Invoke(90 + (i * 5 / Math.Max(1, imageFiles.Length)), 
                                $"[INFO]添加图片 {i+1}/{imageFiles.Length}: {Path.GetFileName(imagePath)}");
                        }
                    }

                    // 只在多张图片的情况下添加分页符
                    if (imageFiles.Length > 1 && i < imageFiles.Length - 1)
                    {
                        // 每张图片后添加分页符，除了最后一张
                        doc.CreateParagraph().CreateRun().AddBreak(BreakType.PAGE);
                    }
                }

                progressCallback?.Invoke(95, "[INFO]保存文档...");

                // 保存填充后的文档到指定输出路径
                using (FileStream fs = new FileStream(outputPath, FileMode.Create, FileAccess.Write))
                {
                    doc.Write(fs);
                }

                return true;
            }
            catch (Exception ex)
            {
                progressCallback?.Invoke(95, $"[ERROR]生成文档失败: {ex.Message}");
                return false;
            }
            finally
            {
                // 删除临时文件
                try
                {
                    if (File.Exists(tempFilePath))
                    {
                        File.Delete(tempFilePath);
                    }
                }
                catch 
                { 
                    // 临时文件删除失败不影响主流程 
                }
            }
        }

        /// <summary>
        /// 计算按比例缩放的EMU尺寸，以使图像适合目标EMU尺寸
        /// </summary>
        /// <param name="originalImagePixelSize">原始图像的像素尺寸</param>
        /// <param name="originalImageDpi">原始图像的DPI</param>
        /// <param name="targetPlaceholderEmuSize">目标占位符EMU尺寸</param>
        /// <returns>包含缩放后宽度和高度（以EMU为单位）的Tuple</returns>
        private Tuple<long, long> CalculateScaledEmuDimensions(Size originalImagePixelSize, SizeF originalImageDpi, Tuple<long, long> targetPlaceholderEmuSize)
        {
            if (originalImagePixelSize.Width <= 0 || originalImagePixelSize.Height <= 0 ||
                originalImageDpi.Width <= 0 || originalImageDpi.Height <= 0 ||
                targetPlaceholderEmuSize.Item1 <= 0 || targetPlaceholderEmuSize.Item2 <= 0)
            {
                // 返回一个小的默认值，避免除零/零大小
                if (targetPlaceholderEmuSize.Item1 <= 0 || targetPlaceholderEmuSize.Item2 <= 0)
                    return Tuple.Create((long)((double)originalImagePixelSize.Width / originalImageDpi.Width * 914400.0),
                                      (long)((double)originalImagePixelSize.Height / originalImageDpi.Height * 914400.0)); // 回退到自然大小
                // 如果原始图像参数不好，则回退到小尺寸
                return Tuple.Create(100000L, 100000L);
            }

            // 计算原始图像的"自然"EMU尺寸
            double naturalWidthEmu = ((double)originalImagePixelSize.Width / originalImageDpi.Width) * 914400.0;
            double naturalHeightEmu = ((double)originalImagePixelSize.Height / originalImageDpi.Height) * 914400.0;

            if (naturalWidthEmu <= 0 || naturalHeightEmu <= 0)
            {
                 return Tuple.Create(targetPlaceholderEmuSize.Item1, targetPlaceholderEmuSize.Item2); // 如果自然尺寸为零，则回退到目标尺寸
            }

            // 计算缩放比例以适应目标占位符
            double widthRatio = (double)targetPlaceholderEmuSize.Item1 / naturalWidthEmu;
            double heightRatio = (double)targetPlaceholderEmuSize.Item2 / naturalHeightEmu;

            // 使用较小的比例，确保图像按比例适应
            double scalingFactor = Math.Min(widthRatio, heightRatio);

            long finalWidthEmu = (long)(naturalWidthEmu * scalingFactor);
            long finalHeightEmu = (long)(naturalHeightEmu * scalingFactor);
            
            // 如果缩放结果太小，确保最小尺寸
            if (finalWidthEmu < 12700) finalWidthEmu = 12700; // 1点
            if (finalHeightEmu < 12700) finalHeightEmu = 12700;

            return Tuple.Create(finalWidthEmu, finalHeightEmu);
        }

        /// <summary>
        /// 根据文件扩展名获取NPOI的图片类型
        /// </summary>
        private int GetPictureTypeForNPOI(string extension)
        {
            switch (extension.ToLower())
            {
                case ".emf": return (int)PictureType.EMF;
                case ".wmf": return (int)PictureType.WMF;
                case ".pict": return (int)PictureType.PICT;
                case ".jpeg":
                case ".jpg": return (int)PictureType.JPEG;
                case ".png": return (int)PictureType.PNG;
                case ".dib": return (int)PictureType.DIB;
                case ".gif": return (int)PictureType.PNG; // NPOI通常将GIF转换为PNG进行插入
                case ".tiff": return (int)PictureType.TIFF;
                case ".eps": return (int)PictureType.EPS;
                case ".bmp": return (int)PictureType.BMP;
                case ".wpg": return (int)PictureType.WPG;
                default:
                    // 未知或不支持的扩展名，默认使用PNG
                    return (int)PictureType.PNG;
            }
        }

        /// <summary>
        /// 是否可用
        /// </summary>
        /// <param name="parameters">检查参数</param>
        /// <returns>是否可用</returns>
        public bool IsAvailable(Dictionary<string, object> parameters)
        {
            if (parameters == null)
                return false;

            // 检查必要参数
            if (!parameters.TryGetValue("DataFolder", out object dataDirectoryObj) || 
                !parameters.TryGetValue("OutputDir", out object outputDirectoryObj))
                return false;

            string dataDirectory = dataDirectoryObj?.ToString();
            
            // 检查数据目录
            if (string.IsNullOrEmpty(dataDirectory) || !Directory.Exists(dataDirectory))
                return false;
            
            // 检查模板文件
            string templatePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "报告模板", "09_项目丘地及测绘房屋分布图", "项目住地及测绘房屋分布图.docx");
            if (!File.Exists(templatePath))
                return false;
            
            // 检查转换工具
            string toolDir = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "tool", "CADC");
            string dllPath = Path.Combine(toolDir, "ACAD.dll");
            string exePath = Path.Combine(toolDir, "ACAD.exe");
            if (!File.Exists(dllPath) && !File.Exists(exePath))
                return false;
            
            return true;
        }
    }
}