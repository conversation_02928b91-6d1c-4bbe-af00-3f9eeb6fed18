using System;
using System.Threading.Tasks;
using System.Windows.Forms;
using RESClient.Services;
using RESClient.Views;

namespace RESClient
{
    /// <summary>
    /// 测试修复后的登录窗体
    /// </summary>
    public class TestLoginFormFix
    {
        /// <summary>
        /// 测试登录窗体的创建和显示
        /// </summary>
        public static void TestLoginFormCreation()
        {
            Console.WriteLine("=== 测试修复后的登录窗体 ===");
            
            try
            {
                // 初始化服务
                var configService = RESServerConfigService.Instance;
                var authService = new AuthService(configService);
                
                Console.WriteLine("1. 创建登录窗体...");
                
                // 测试窗体创建
                using (var loginForm = new LoginForm(authService))
                {
                    Console.WriteLine("   ✅ 登录窗体创建成功");
                    Console.WriteLine($"   窗体标题: {loginForm.Text}");
                    Console.WriteLine($"   窗体大小: {loginForm.Size}");
                    Console.WriteLine($"   窗体位置: {loginForm.StartPosition}");
                    
                    // 测试窗体属性
                    if (loginForm.FormBorderStyle == FormBorderStyle.FixedDialog)
                    {
                        Console.WriteLine("   ✅ 窗体边框样式正确");
                    }
                    
                    if (!loginForm.MaximizeBox && !loginForm.MinimizeBox)
                    {
                        Console.WriteLine("   ✅ 窗体按钮设置正确");
                    }
                    
                    Console.WriteLine("   💡 窗体创建和属性设置均正常");
                }
                
                Console.WriteLine("\n2. 测试窗体显示（可选）...");
                Console.WriteLine("   如需测试窗体显示，请运行 'TestLoginFormFix.ShowLoginFormTest()'");
                
            }
            catch (Exception ex)
            {
                Console.WriteLine($"   ❌ 测试失败: {ex.Message}");
                Console.WriteLine($"   详细信息: {ex.StackTrace}");
            }
            
            Console.WriteLine("\n=== 测试完成 ===");
        }
        
        /// <summary>
        /// 显示登录窗体进行UI测试
        /// </summary>
        public static void ShowLoginFormTest()
        {
            Console.WriteLine("=== 显示登录窗体进行UI测试 ===");
            
            try
            {
                Application.EnableVisualStyles();
                Application.SetCompatibleTextRenderingDefault(false);
                
                var configService = RESServerConfigService.Instance;
                var authService = new AuthService(configService);
                
                using (var loginForm = new LoginForm(authService))
                {
                    Console.WriteLine("正在显示登录窗体...");
                    var result = loginForm.ShowDialog();
                    Console.WriteLine($"窗体关闭，结果: {result}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"显示窗体时发生错误: {ex.Message}");
                MessageBox.Show($"显示窗体时发生错误: {ex.Message}", "错误", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
        
        /// <summary>
        /// 运行完整的登录窗体测试
        /// </summary>
        public static void RunAllTests()
        {
            TestLoginFormCreation();

            Console.WriteLine("\n是否要显示登录窗体进行UI测试？(y/n)");
            var key = Console.ReadKey();
            if (key.KeyChar == 'y' || key.KeyChar == 'Y')
            {
                Console.WriteLine();
                ShowLoginFormTest();
            }
        }
    }
}
