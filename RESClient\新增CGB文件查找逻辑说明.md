# 新增CGB文件查找逻辑说明

## 概述

在现有的CGB.xls文件查找逻辑基础上，新增了一个高优先级的查找方案，用于查找包含标准工作表结构的Excel文件。

## 新增查找逻辑

### 查找优先级（从高到低）

1. **标准Excel文件查找**（新增，最高优先级）
   - 查找包含4个指定工作表的Excel文件
   - 工作表名称必须完全匹配

2. **直接CGB文件查找**（现有逻辑）
   - 查找文件名包含"cgb"的Excel文件

3. **7z压缩包提取查找**（现有逻辑）
   - 从"成果包.7z"中提取并查找CGB文件

### 标准工作表要求

新的查找逻辑要求Excel文件必须同时满足以下条件：

1. **工作表数量**：恰好包含4个工作表
2. **工作表名称**：必须完全匹配以下4个名称：
   - "房屋建筑面积总表"
   - "共有建筑面积的分摊"
   - "房产分户面积统计表"
   - "楼盘表信息"

## 修改的文件

### 1. BuildingInfoModuleGenerator.cs
位置：`RESClient/Services/Implementations/BuildingInfoModuleGenerator.cs`

**新增方法：**
- `FindStandardExcelFile()` - 查找包含标准工作表的Excel文件
- `ValidateExcelFileStructure()` - 验证Excel文件结构

**修改方法：**
- `FindCGBFile()` - 添加新的查找步骤

### 2. EstateAreaSummaryModuleGenerator.cs
位置：`RESClient/Services/Implementations/EstateAreaSummaryModuleGenerator.cs`

**新增方法：**
- `FindStandardExcelFile()` - 查找包含标准工作表的Excel文件
- `ValidateExcelFileStructure()` - 验证Excel文件结构

**修改方法：**
- `FindCGBFile()` - 添加新的查找步骤

### 3. HouseholdAreaStatisticsModuleGenerator.cs
位置：`RESClient/Services/Implementations/HouseholdAreaStatisticsModuleGenerator.cs`

**新增方法：**
- `FindStandardExcelFile()` - 查找包含标准工作表的Excel文件
- `ValidateExcelFileStructure()` - 验证Excel文件结构

**修改方法：**
- `FindCGBFile()` - 添加新的查找步骤

## 查找流程

### 新的查找流程

```
开始查找
    ↓
1. 查找标准Excel文件
    ├─ 遍历所有Excel文件
    ├─ 验证工作表数量（必须为4个）
    ├─ 验证工作表名称（必须完全匹配）
    └─ 找到 → 返回文件路径
    ↓ 未找到
2. 查找CGB文件
    ├─ 搜索文件名包含"cgb"的Excel文件
    └─ 找到 → 返回文件路径
    ↓ 未找到
3. 从7z压缩包提取
    ├─ 查找"成果包.7z"文件
    ├─ 提取压缩包到临时目录
    ├─ 在提取目录中查找CGB文件
    └─ 找到 → 返回文件路径
    ↓ 未找到
返回null（查找失败）
```

### 进度反馈

新的查找逻辑提供详细的进度反馈：

- `5%` - 开始查找标准Excel文件
- `7%` - 显示找到的Excel文件数量
- `8%` - 验证文件失败的警告信息
- `10%` - 未找到标准Excel文件，开始查找CGB文件
- `15%` - 开始从7z压缩包提取
- `20%` - 验证通过的成功信息
- `25%` - 找到目标文件

## 向后兼容性

新的查找逻辑完全保持向后兼容性：

1. **现有CGB文件**：如果目录中存在传统的CGB.xls文件，仍然可以被正确识别和使用
2. **7z压缩包**：现有的7z压缩包提取逻辑保持不变
3. **错误处理**：保持现有的错误处理和进度回调机制
4. **接口不变**：所有公共接口和方法签名保持不变

## 使用示例

### 测试新的查找逻辑

1. 创建测试目录：`C:\TestData`
2. 准备测试Excel文件：
   - 标准Excel文件：包含4个指定工作表的Excel文件
   - 传统CGB文件：文件名包含"cgb"的Excel文件
   - 7z压缩包：包含CGB文件的"成果包.7z"
3. 运行测试：`TestNewCGBFindLogic.cs`

### 预期行为

- **标准Excel文件存在**：优先使用标准Excel文件
- **仅有CGB文件**：使用传统CGB文件查找逻辑
- **仅有7z压缩包**：使用7z提取逻辑
- **多种文件共存**：按优先级顺序选择

## 错误处理

新的查找逻辑包含完善的错误处理：

1. **文件访问错误**：捕获并记录文件访问异常，继续查找其他文件
2. **Excel格式错误**：捕获并记录Excel文件格式异常，继续验证其他文件
3. **工作表验证失败**：记录验证失败的详细信息，继续查找
4. **回退机制**：如果新逻辑失败，自动回退到现有逻辑

## 性能考虑

1. **文件遍历优化**：只遍历Excel文件，跳过其他格式文件
2. **早期退出**：找到第一个符合条件的文件即返回
3. **异常处理**：避免单个文件错误影响整体查找过程
4. **资源管理**：正确释放Excel文件资源，避免内存泄漏

## 日志和调试

新的查找逻辑提供详细的日志信息：

- **INFO级别**：正常的查找进度和结果
- **WARNING级别**：文件验证失败或访问错误
- **ERROR级别**：严重错误或查找完全失败

这些日志信息有助于调试和问题排查。
