# RESServer/RESClient 认证系统使用说明

## 概述

本认证系统为 RESServer 和 RESClient 提供了完整的用户身份验证功能，支持可配置的认证开关，确保系统的灵活性和安全性。

## 功能特性

### 🔐 核心功能
- **JWT Token 认证**：基于 JSON Web Token 的安全认证机制
- **用户管理**：支持用户创建、更新、删除等管理功能
- **角色权限**：支持管理员和普通用户角色
- **可配置开关**：可通过配置文件启用/禁用认证功能
- **自动登录**：支持记住登录状态和自动登录检查

### 🛡️ 安全特性
- **密码加密**：使用 BCrypt 算法安全存储密码
- **Token 过期**：支持 Token 自动过期和刷新
- **中间件保护**：自动为 API 添加认证保护
- **向后兼容**：认证功能可选，不影响现有功能

## 配置说明

### RESServer 配置

在 `appsettings.json` 中配置认证相关设置：

```json
{
  "Auth": {
    "EnableAuthentication": false,  // 是否启用认证功能
    "Jwt": {
      "SecretKey": "RESServer-JWT-Secret-Key-2024-Very-Long-And-Secure-Key-For-Production-Use",
      "Issuer": "RESServer",
      "Audience": "RESClient",
      "ExpirationMinutes": 480  // Token 过期时间（分钟）
    }
  },
  "ConnectionStrings": {
    "AuthDatabase": "Data Source=auth.db"  // SQLite 数据库连接字符串
  }
}
```

### RESClient 配置

在 `App.config` 中配置认证相关设置：

```xml
<appSettings>
  <!-- 认证相关配置 -->
  <add key="EnableAutoLoginCheck" value="true" />
  <add key="RememberLoginState" value="true" />
  <add key="LoginTimeoutWarningMinutes" value="30" />
</appSettings>
```

## 使用方法

### 1. 启用认证功能

**步骤 1：修改 RESServer 配置**
```json
{
  "Auth": {
    "EnableAuthentication": true  // 设置为 true 启用认证
  }
}
```

**步骤 2：启动 RESServer**
```bash
cd RESServer
dotnet run
```

**步骤 3：启动 RESClient**
- 启动 RESClient 应用程序
- 系统会自动检测服务器认证状态
- 如果需要认证，会自动显示登录界面

### 2. 默认用户账户

系统预置了以下默认用户：

| 用户名 | 密码 | 角色 | 说明 |
|--------|------|------|------|
| admin | admin123 | Admin | 系统管理员 |
| user | user123 | User | 普通用户 |

### 3. 禁用认证功能

如果不需要认证功能，可以在 `appsettings.json` 中设置：

```json
{
  "Auth": {
    "EnableAuthentication": false  // 设置为 false 禁用认证
  }
}
```

禁用认证后，所有 API 请求无需认证即可访问。

## API 接口

### 认证相关 API

| 接口 | 方法 | 说明 | 认证要求 |
|------|------|------|----------|
| `/api/auth/status` | GET | 获取服务器认证状态 | 无 |
| `/api/auth/login` | POST | 用户登录 | 无 |
| `/api/auth/me` | GET | 获取当前用户信息 | 需要 |
| `/api/auth/validate` | GET | 验证 Token | 需要 |
| `/api/auth/change-password` | POST | 修改密码 | 需要 |

### 用户管理 API（管理员专用）

| 接口 | 方法 | 说明 | 认证要求 |
|------|------|------|----------|
| `/api/auth/users` | GET | 获取用户列表 | 管理员 |
| `/api/auth/users` | POST | 创建用户 | 管理员 |
| `/api/auth/users/{id}` | GET | 获取用户信息 | 管理员 |
| `/api/auth/users/{id}` | PUT | 更新用户信息 | 管理员 |
| `/api/auth/users/{id}` | DELETE | 删除用户 | 管理员 |

## 测试验证

### RESServer 测试

运行 RESServer 认证 API 测试：

```bash
cd RESServer
dotnet run TestAuthenticationAPI.cs
```

### RESClient 测试

运行 RESClient 认证系统测试：

```bash
cd RESClient
RESClient.exe test-auth
```

### 手动测试场景

1. **认证功能禁用测试**
   - 设置 `EnableAuthentication: false`
   - 启动服务器和客户端
   - 验证无需登录即可使用所有功能

2. **认证功能启用测试**
   - 设置 `EnableAuthentication: true`
   - 启动服务器和客户端
   - 验证需要登录才能使用功能

3. **登录功能测试**
   - 使用正确用户名密码登录
   - 使用错误用户名密码登录
   - 验证登录状态保持

4. **Token 过期测试**
   - 设置较短的过期时间
   - 等待 Token 过期
   - 验证自动提示重新登录

## 故障排除

### 常见问题

**Q: 启动 RESServer 时出现数据库错误**
A: 确保有足够的权限创建 SQLite 数据库文件，数据库会自动创建。

**Q: RESClient 无法连接到服务器**
A: 检查 `App.config` 中的服务器地址配置是否正确。

**Q: 登录后仍然提示需要认证**
A: 检查系统时间是否正确，Token 可能因为时间偏差而被认为已过期。

**Q: 忘记管理员密码**
A: 删除 `auth.db` 文件重新启动服务器，会重新创建默认用户。

### 日志查看

RESServer 会在控制台输出详细的认证相关日志，包括：
- 用户登录/登出记录
- Token 验证结果
- 认证错误信息

## 安全建议

### 生产环境配置

1. **修改默认密码**：立即修改默认用户的密码
2. **更换 JWT 密钥**：使用强随机密钥替换默认密钥
3. **设置合适的过期时间**：根据安全需求设置 Token 过期时间
4. **启用 HTTPS**：在生产环境中使用 HTTPS 协议
5. **定期备份数据库**：定期备份用户数据库

### 密钥管理

JWT 密钥应该：
- 至少 32 个字符长度
- 包含随机字符
- 定期更换
- 安全存储，不要提交到版本控制

## 版本信息

- **版本**: 1.0.0
- **更新日期**: 2025-07-13
- **兼容性**: .NET 8.0 (RESServer), .NET Framework 4.8 (RESClient)
