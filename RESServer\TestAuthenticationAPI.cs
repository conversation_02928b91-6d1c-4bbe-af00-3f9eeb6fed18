using System;
using System.Net.Http;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;

namespace RESServer
{
    /// <summary>
    /// RESServer 认证 API 测试程序
    /// </summary>
    public class TestAuthenticationAPI
    {
        private readonly string _baseUrl;
        private readonly HttpClient _httpClient;

        public TestAuthenticationAPI(string baseUrl = "http://localhost:5104")
        {
            _baseUrl = baseUrl.TrimEnd('/');
            _httpClient = new HttpClient();
        }

        /// <summary>
        /// 运行所有测试
        /// </summary>
        public async Task RunAllTests()
        {
            Console.WriteLine("=== RESServer 认证 API 测试 ===");
            Console.WriteLine($"服务器地址: {_baseUrl}");
            Console.WriteLine();

            try
            {
                // 测试 1: 检查服务器状态
                await TestServerStatus();

                // 测试 2: 测试登录 API
                var token = await TestLogin();

                // 测试 3: 测试受保护的 API
                if (!string.IsNullOrEmpty(token))
                {
                    await TestProtectedAPI(token);
                }

                // 测试 4: 测试用户管理 API
                if (!string.IsNullOrEmpty(token))
                {
                    await TestUserManagement(token);
                }

                // 测试 5: 测试无效 Token
                await TestInvalidToken();

                Console.WriteLine("\n=== 所有测试完成 ===");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"测试过程中发生错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 测试服务器状态
        /// </summary>
        private async Task TestServerStatus()
        {
            Console.WriteLine("1. 测试服务器状态 API...");
            
            try
            {
                var response = await _httpClient.GetAsync($"{_baseUrl}/api/auth/status");
                var content = await response.Content.ReadAsStringAsync();
                
                if (response.IsSuccessStatusCode)
                {
                    var statusDoc = JsonDocument.Parse(content);
                    var authEnabled = statusDoc.RootElement.GetProperty("authenticationEnabled").GetBoolean();
                    var message = statusDoc.RootElement.GetProperty("message").GetString();
                    
                    Console.WriteLine($"   状态: {response.StatusCode}");
                    Console.WriteLine($"   认证启用: {authEnabled}");
                    Console.WriteLine($"   消息: {message}");
                    Console.WriteLine("   ✅ 服务器状态 API 测试成功");
                }
                else
                {
                    Console.WriteLine($"   ❌ 服务器状态 API 测试失败: {response.StatusCode}");
                    Console.WriteLine($"   响应: {content}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"   ❌ 服务器状态 API 测试失败: {ex.Message}");
            }
            
            Console.WriteLine();
        }

        /// <summary>
        /// 测试登录 API
        /// </summary>
        private async Task<string> TestLogin()
        {
            Console.WriteLine("2. 测试登录 API...");
            
            try
            {
                // 测试有效登录
                var loginRequest = new
                {
                    username = "admin",
                    password = "admin123"
                };
                
                var json = JsonSerializer.Serialize(loginRequest);
                var content = new StringContent(json, Encoding.UTF8, "application/json");
                
                Console.WriteLine("   尝试使用 admin/admin123 登录...");
                var response = await _httpClient.PostAsync($"{_baseUrl}/api/auth/login", content);
                var responseContent = await response.Content.ReadAsStringAsync();
                
                if (response.IsSuccessStatusCode)
                {
                    var loginDoc = JsonDocument.Parse(responseContent);
                    var success = loginDoc.RootElement.GetProperty("success").GetBoolean();
                    
                    if (success)
                    {
                        var token = loginDoc.RootElement.GetProperty("token").GetString();
                        var username = loginDoc.RootElement.GetProperty("user").GetProperty("username").GetString();
                        
                        Console.WriteLine("   ✅ 登录成功");
                        Console.WriteLine($"   用户: {username}");
                        Console.WriteLine($"   Token: {token[..20]}...");
                        
                        return token;
                    }
                    else
                    {
                        Console.WriteLine($"   ❌ 登录失败: {loginDoc.RootElement.GetProperty("message").GetString()}");
                    }
                }
                else
                {
                    Console.WriteLine($"   ❌ 登录 API 请求失败: {response.StatusCode}");
                    Console.WriteLine($"   响应: {responseContent}");
                }
                
                // 测试无效登录
                Console.WriteLine("   尝试使用错误密码登录...");
                var invalidRequest = new
                {
                    username = "admin",
                    password = "wrongpassword"
                };
                
                var invalidJson = JsonSerializer.Serialize(invalidRequest);
                var invalidContent = new StringContent(invalidJson, Encoding.UTF8, "application/json");
                
                var invalidResponse = await _httpClient.PostAsync($"{_baseUrl}/api/auth/login", invalidContent);
                if (invalidResponse.StatusCode == System.Net.HttpStatusCode.Unauthorized)
                {
                    Console.WriteLine("   ✅ 错误密码登录正确被拒绝");
                }
                else
                {
                    Console.WriteLine("   ❌ 错误密码登录应该返回 401");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"   ❌ 登录 API 测试失败: {ex.Message}");
            }
            
            Console.WriteLine();
            return null;
        }

        /// <summary>
        /// 测试受保护的 API
        /// </summary>
        private async Task TestProtectedAPI(string token)
        {
            Console.WriteLine("3. 测试受保护的 API...");
            
            try
            {
                // 设置认证头
                _httpClient.DefaultRequestHeaders.Authorization = 
                    new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", token);
                
                // 测试获取当前用户信息
                var response = await _httpClient.GetAsync($"{_baseUrl}/api/auth/me");
                var content = await response.Content.ReadAsStringAsync();
                
                if (response.IsSuccessStatusCode)
                {
                    var userDoc = JsonDocument.Parse(content);
                    var username = userDoc.RootElement.GetProperty("data").GetProperty("username").GetString();
                    
                    Console.WriteLine($"   ✅ 获取用户信息成功: {username}");
                }
                else
                {
                    Console.WriteLine($"   ❌ 获取用户信息失败: {response.StatusCode}");
                    Console.WriteLine($"   响应: {content}");
                }
                
                // 测试 Token 验证
                var validateResponse = await _httpClient.GetAsync($"{_baseUrl}/api/auth/validate");
                if (validateResponse.IsSuccessStatusCode)
                {
                    Console.WriteLine("   ✅ Token 验证成功");
                }
                else
                {
                    Console.WriteLine($"   ❌ Token 验证失败: {validateResponse.StatusCode}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"   ❌ 受保护 API 测试失败: {ex.Message}");
            }
            
            Console.WriteLine();
        }

        /// <summary>
        /// 测试用户管理 API
        /// </summary>
        private async Task TestUserManagement(string token)
        {
            Console.WriteLine("4. 测试用户管理 API...");
            
            try
            {
                // 测试获取用户列表（需要管理员权限）
                var response = await _httpClient.GetAsync($"{_baseUrl}/api/auth/users");
                var content = await response.Content.ReadAsStringAsync();
                
                if (response.IsSuccessStatusCode)
                {
                    var usersDoc = JsonDocument.Parse(content);
                    var users = usersDoc.RootElement.GetProperty("data").EnumerateArray();
                    var userCount = 0;
                    
                    foreach (var user in users)
                    {
                        userCount++;
                    }
                    
                    Console.WriteLine($"   ✅ 获取用户列表成功，共 {userCount} 个用户");
                }
                else
                {
                    Console.WriteLine($"   ❌ 获取用户列表失败: {response.StatusCode}");
                    Console.WriteLine($"   响应: {content}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"   ❌ 用户管理 API 测试失败: {ex.Message}");
            }
            
            Console.WriteLine();
        }

        /// <summary>
        /// 测试无效 Token
        /// </summary>
        private async Task TestInvalidToken()
        {
            Console.WriteLine("5. 测试无效 Token...");
            
            try
            {
                // 设置无效的认证头
                _httpClient.DefaultRequestHeaders.Authorization = 
                    new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", "invalid-token");
                
                var response = await _httpClient.GetAsync($"{_baseUrl}/api/auth/me");
                
                if (response.StatusCode == System.Net.HttpStatusCode.Unauthorized)
                {
                    Console.WriteLine("   ✅ 无效 Token 正确被拒绝");
                }
                else
                {
                    Console.WriteLine($"   ❌ 无效 Token 应该返回 401，实际返回: {response.StatusCode}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"   ❌ 无效 Token 测试失败: {ex.Message}");
            }
            
            Console.WriteLine();
        }

        /// <summary>
        /// 主测试入口
        /// </summary>
        public static async Task Main(string[] args)
        {
            string serverUrl = "http://localhost:5104";
            
            if (args.Length > 0)
            {
                serverUrl = args[0];
            }
            
            var tester = new TestAuthenticationAPI(serverUrl);
            await tester.RunAllTests();
            
            Console.WriteLine("按任意键退出...");
            Console.ReadKey();
        }

        public void Dispose()
        {
            _httpClient?.Dispose();
        }
    }
}
