using System;
using System.Windows.Forms;
using RESClient.Services;
using RESClient.Views;

namespace RESClient
{
    /// <summary>
    /// 记住密码功能演示
    /// </summary>
    public static class DemoRememberPasswordFeature
    {
        /// <summary>
        /// 运行记住密码功能演示
        /// </summary>
        public static void RunDemo()
        {
            Console.WriteLine("=== RESClient 记住密码功能演示 ===");
            Console.WriteLine();
            
            try
            {
                Application.EnableVisualStyles();
                Application.SetCompatibleTextRenderingDefault(false);
                
                ShowFeatureOverview();
                DemonstratePasswordStorage();
                DemonstrateLoginFormFeatures();
                
                Console.WriteLine("✅ 演示完成！");
                Console.WriteLine();
                Console.WriteLine("功能特点总结：");
                Console.WriteLine("1. 安全的密码加密存储（Windows DPAPI）");
                Console.WriteLine("2. 自动填充用户名和密码");
                Console.WriteLine("3. 手动登录确认（不自动提交）");
                Console.WriteLine("4. 便捷的清除凭据选项");
                Console.WriteLine("5. 只在服务器启用认证时生效");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ 演示失败: {ex.Message}");
            }
        }
        
        /// <summary>
        /// 显示功能概述
        /// </summary>
        private static void ShowFeatureOverview()
        {
            Console.WriteLine("📋 功能概述：");
            Console.WriteLine("当RESServer启用认证功能时，RESClient登录界面现在支持：");
            Console.WriteLine("• 记住密码选项");
            Console.WriteLine("• 安全的本地密码存储");
            Console.WriteLine("• 下次启动时自动填充凭据");
            Console.WriteLine("• 仍需手动点击登录按钮确认");
            Console.WriteLine("• 提供清除已保存凭据的选项");
            Console.WriteLine();
        }
        
        /// <summary>
        /// 演示密码存储功能
        /// </summary>
        private static void DemonstratePasswordStorage()
        {
            Console.WriteLine("🔐 演示密码存储功能：");
            
            var authConfig = AuthConfigService.Instance;
            
            // 清除任何现有的凭据
            authConfig.ClearRememberedCredentials();
            Console.WriteLine("1. 清除现有凭据");
            
            // 保存测试凭据
            Console.WriteLine("2. 保存测试凭据（用户名: demo_user, 密码: demo_pass_123）");
            authConfig.SaveRememberedCredentials("demo_user", "demo_pass_123", true);
            
            // 验证保存
            var credentials = authConfig.LoadRememberedCredentials();
            if (credentials.HasValue)
            {
                Console.WriteLine($"   ✅ 凭据保存成功");
                Console.WriteLine($"   用户名: {credentials.Value.Username}");
                Console.WriteLine($"   密码: {new string('*', credentials.Value.Password.Length)}");
            }
            else
            {
                Console.WriteLine("   ❌ 凭据保存失败");
            }
            
            // 演示加密
            Console.WriteLine("3. 密码使用Windows DPAPI加密存储");
            Console.WriteLine("   只有当前Windows用户可以解密");
            Console.WriteLine("   存储位置: %LOCALAPPDATA%\\RESClient\\remembered_credentials.json");
            
            Console.WriteLine();
        }
        
        /// <summary>
        /// 演示登录窗体功能
        /// </summary>
        private static void DemonstrateLoginFormFeatures()
        {
            Console.WriteLine("🖥️ 演示登录窗体功能：");
            Console.WriteLine();
            
            try
            {
                var configService = RESServerConfigService.Instance;
                var authService = new AuthService(configService);
                
                Console.WriteLine("即将显示登录窗体，请注意以下新功能：");
                Console.WriteLine();
                Console.WriteLine("📌 新增的UI元素：");
                Console.WriteLine("• '记住密码' 复选框（在密码框下方）");
                Console.WriteLine("• '清除' 按钮（在取消勾选记住密码时显示）");
                Console.WriteLine();
                Console.WriteLine("📌 功能测试步骤：");
                Console.WriteLine("1. 用户名和密码应该已经自动填充");
                Console.WriteLine("2. '记住密码' 复选框应该已经勾选");
                Console.WriteLine("3. 取消勾选 '记住密码' 会显示 '清除' 按钮");
                Console.WriteLine("4. 点击 '清除' 按钮可以删除保存的凭据");
                Console.WriteLine("5. 即使有保存的凭据，仍需手动点击 '登录' 按钮");
                Console.WriteLine();
                Console.WriteLine("按任意键显示登录窗体...");
                Console.ReadKey();
                
                using (var loginForm = new LoginForm(authService))
                {
                    var result = loginForm.ShowDialog();
                    Console.WriteLine($"登录窗体关闭，结果: {result}");
                }
                
                // 检查凭据状态
                var authConfig = AuthConfigService.Instance;
                var finalCredentials = authConfig.LoadRememberedCredentials();
                
                if (finalCredentials.HasValue)
                {
                    Console.WriteLine("✅ 凭据仍然保存");
                }
                else
                {
                    Console.WriteLine("🗑️ 凭据已被清除");
                }
                
                // 清理演示数据
                authConfig.ClearRememberedCredentials();
                Console.WriteLine("🧹 演示数据已清理");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ 登录窗体演示失败: {ex.Message}");
            }
            
            Console.WriteLine();
        }
        
        /// <summary>
        /// 演示安全特性
        /// </summary>
        public static void DemonstrateSecurityFeatures()
        {
            Console.WriteLine("=== 安全特性演示 ===");
            Console.WriteLine();
            
            var authConfig = AuthConfigService.Instance;
            
            Console.WriteLine("🔒 密码加密演示：");
            
            // 测试不同类型的密码
            string[] testPasswords = {
                "simple123",
                "Complex@Password!2024",
                "中文密码测试",
                "!@#$%^&*()_+-=[]{}|;:,.<>?",
                ""
            };
            
            foreach (var password in testPasswords)
            {
                Console.WriteLine($"测试密码: '{password}'");
                
                // 保存
                authConfig.SaveRememberedCredentials("testuser", password, true);
                
                // 加载
                var credentials = authConfig.LoadRememberedCredentials();
                
                if (credentials.HasValue && credentials.Value.Password == password)
                {
                    Console.WriteLine("   ✅ 加密/解密成功");
                }
                else
                {
                    Console.WriteLine("   ❌ 加密/解密失败");
                }
            }
            
            // 清理
            authConfig.ClearRememberedCredentials();
            
            Console.WriteLine();
            Console.WriteLine("🛡️ 安全特性：");
            Console.WriteLine("• 使用Windows DPAPI（数据保护API）");
            Console.WriteLine("• 只有当前用户可以解密");
            Console.WriteLine("• 密码不以明文存储");
            Console.WriteLine("• 完善的错误处理");
            Console.WriteLine("• 安全的文件存储位置");
            Console.WriteLine();
        }
    }
}
