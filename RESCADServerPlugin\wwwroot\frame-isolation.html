<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CAD 图框隔离工具</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background-color: #fff;
            padding: 20px;
            border-radius: 5px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        h1 {
            color: #333;
            margin-top: 0;
            border-bottom: 1px solid #eee;
            padding-bottom: 10px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input[type="file"] {
            display: block;
            margin-bottom: 10px;
        }
        button {
            background-color: #4CAF50;
            color: white;
            padding: 10px 15px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover {
            background-color: #45a049;
        }
        button:disabled {
            background-color: #cccccc;
            cursor: not-allowed;
        }
        .status {
            margin-top: 20px;
            padding: 10px;
            border-radius: 4px;
        }
        .status.info {
            background-color: #d9edf7;
            color: #31708f;
        }
        .status.success {
            background-color: #dff0d8;
            color: #3c763d;
        }
        .status.error {
            background-color: #f2dede;
            color: #a94442;
        }
        .progress-container {
            margin-top: 20px;
            display: none;
        }
        .progress-bar {
            width: 100%;
            background-color: #e0e0e0;
            padding: 3px;
            border-radius: 3px;
            box-shadow: inset 0 1px 3px rgba(0, 0, 0, .2);
        }
        .progress-bar-fill {
            height: 20px;
            border-radius: 3px;
            background-color: #4CAF50;
            transition: width 0.5s ease;
            width: 0;
        }
        .navbar {
            margin-bottom: 20px;
        }
        .navbar a {
            margin-right: 10px;
            color: #337ab7;
            text-decoration: none;
        }
        .navbar a:hover {
            text-decoration: underline;
        }
        .download-link {
            display: inline-block;
            margin-top: 10px;
            padding: 8px 12px;
            background-color: #337ab7;
            color: white;
            text-decoration: none;
            border-radius: 4px;
        }
        .download-link:hover {
            background-color: #286090;
        }
        #debug-info {
            margin-top: 20px;
            padding: 10px;
            background-color: #f8f8f8;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
            display: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="navbar">
            <a href="index.html">首页</a>
        </div>
        
        <h1>CAD 图框隔离工具</h1>
        <p>上传 DWG 文件，识别图框，然后隔离图框内部内容并导出以图号命名的 DWG 文件。</p>
        
        <div class="form-group">
            <label for="dwgFile">选择 DWG 文件：</label>
            <input type="file" id="dwgFile" accept=".dwg">
            <button id="uploadBtn" onclick="uploadAndProcess()">上传并处理</button>
        </div>
        
        <div id="statusContainer" class="status info" style="display: none;"></div>
        
        <div id="progressContainer" class="progress-container">
            <p>上传进度：<span id="progressText">0%</span></p>
            <div class="progress-bar">
                <div id="progressBar" class="progress-bar-fill"></div>
            </div>
        </div>
        
        <div id="resultContainer" style="display: none; margin-top: 20px;">
            <h3>处理结果：</h3>
            <div id="resultContent"></div>
        </div>
        
        <div id="debug-info"></div>
    </div>
    
    <script>
        let queueCheckInterval;
        
        async function uploadAndProcess() {
            const fileInput = document.getElementById('dwgFile');
            const uploadBtn = document.getElementById('uploadBtn');
            const statusContainer = document.getElementById('statusContainer');
            const progressContainer = document.getElementById('progressContainer');
            const progressBar = document.getElementById('progressBar');
            const progressText = document.getElementById('progressText');
            const resultContainer = document.getElementById('resultContainer');
            const resultContent = document.getElementById('resultContent');
            const debugInfo = document.getElementById('debug-info');
            
            if (!fileInput.files || fileInput.files.length === 0) {
                showStatus('请选择一个 DWG 文件', 'error');
                return;
            }
            
            const file = fileInput.files[0];
            const formData = new FormData();
            formData.append('file', file);
            
            uploadBtn.disabled = true;
            showStatus('正在上传文件...', 'info');
            progressContainer.style.display = 'block';
            resultContainer.style.display = 'none';
            
            try {
                // 使用XMLHttpRequest来跟踪上传进度
                const xhr = new XMLHttpRequest();
                
                // 创建一个Promise来处理XHR请求
                const uploadPromise = new Promise((resolve, reject) => {
                    xhr.open('POST', '/api/frame-isolation/process');
                    
                    xhr.upload.addEventListener('progress', (event) => {
                        if (event.lengthComputable) {
                            const percentComplete = Math.round((event.loaded / event.total) * 100);
                            progressBar.style.width = percentComplete + '%';
                            progressText.textContent = percentComplete + '%';
                            showStatus(`正在上传文件... ${percentComplete}%`, 'info');
                        }
                    });
                    
                    xhr.onload = function() {
                        if (xhr.status >= 200 && xhr.status < 300) {
                            resolve(xhr);
                        } else {
                            reject({
                                status: xhr.status,
                                statusText: xhr.statusText,
                                response: xhr.response
                            });
                        }
                    };
                    
                    xhr.onerror = function() {
                        reject({
                            status: xhr.status,
                            statusText: xhr.statusText,
                            response: xhr.response
                        });
                    };
                    
                    xhr.responseType = 'arraybuffer';
                    xhr.send(formData);
                });
                
                // 等待上传完成
                showStatus('文件已上传，正在处理...', 'info');
                const response = await uploadPromise;
                
                // 检查响应类型
                const contentType = response.getResponseHeader('content-type');
                
                // 显示调试信息
                debugInfo.textContent = `响应状态: ${response.status}\n`;
                debugInfo.textContent += `响应类型: ${contentType}\n`;
                debugInfo.textContent += `Content-Disposition: ${response.getResponseHeader('content-disposition')}\n`;
                debugInfo.textContent += `响应大小: ${response.response ? response.response.byteLength : 0} 字节\n`;
                
                if (contentType && contentType.includes('application/json')) {
                    // 是JSON响应，表示出错
                    const textDecoder = new TextDecoder();
                    const jsonText = textDecoder.decode(response.response);
                    debugInfo.textContent += `JSON响应: ${jsonText}\n`;
                    
                    try {
                        const jsonResponse = JSON.parse(jsonText);
                        showStatus(`处理失败: ${jsonResponse.message || '未知错误'}`, 'error');
                    } catch (e) {
                        showStatus(`处理失败: 无法解析错误信息`, 'error');
                    }
                } else {
                    // 是文件响应
                    const blob = new Blob([response.response], {
                        type: contentType || 'application/octet-stream'
                    });
                    
                    const filename = getFilenameFromResponse(response) || 'frame-isolation.dwg';
                    
                    const url = window.URL.createObjectURL(blob);
                    resultContent.innerHTML = `
                        <p>文件处理成功！</p>
                        <a href="${url}" download="${filename}" class="download-link">下载处理后的图框 DWG</a>
                    `;
                    resultContainer.style.display = 'block';
                    showStatus('文件处理成功', 'success');
                }
                
                // 显示调试信息（开发时使用）
                // debugInfo.style.display = 'block';
            } catch (error) {
                showStatus(`处理失败: ${error.message || '未知错误'}`, 'error');
                console.error('上传处理错误:', error);
                
                // 显示更详细的错误信息
                debugInfo.textContent += `错误: ${JSON.stringify(error, null, 2)}\n`;
                // debugInfo.style.display = 'block';
            } finally {
                uploadBtn.disabled = false;
                progressContainer.style.display = 'none';
                progressBar.style.width = '0%';
                stopQueueCheck();
            }
        }
        
        function getFilenameFromResponse(xhr) {
            // 从Content-Disposition头提取文件名
            const contentDisposition = xhr.getResponseHeader('content-disposition');
            if (!contentDisposition) return 'frame-isolation.dwg';
            
            const matches = /filename="(.+?)"/.exec(contentDisposition);
            return matches && matches.length > 1 ? matches[1] : 'frame-isolation.dwg';
        }
        
        function showStatus(message, type) {
            const statusContainer = document.getElementById('statusContainer');
            statusContainer.textContent = message;
            statusContainer.className = `status ${type}`;
            statusContainer.style.display = 'block';
        }
        
        function startQueueCheck() {
            stopQueueCheck();
            queueCheckInterval = setInterval(checkQueueStatus, 2000);
        }
        
        function stopQueueCheck() {
            if (queueCheckInterval) {
                clearInterval(queueCheckInterval);
                queueCheckInterval = null;
            }
        }
        
        async function checkQueueStatus() {
            try {
                const response = await fetch('/api/frame-isolation/queue-status');
                if (response.ok) {
                    const data = await response.json();
                    if (data.queueLength > 0) {
                        showStatus(`队列中还有 ${data.queueLength} 个任务正在等待处理...`, 'info');
                    }
                }
            } catch (error) {
                console.error('检查队列状态失败:', error);
            }
        }
    </script>
</body>
</html> 