using Autodesk.AutoCAD.ApplicationServices;
using Autodesk.AutoCAD.DatabaseServices;
using Autodesk.AutoCAD.EditorInput;
using Autodesk.AutoCAD.Geometry;
using RESCADServerPlugin.Configuration;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.IO.Compression;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading;
using System.Threading.Tasks;

namespace RESCADServerPlugin.Services
{
    /// <summary>
    /// Progress information for DWG file processing
    /// </summary>
    public class ProgressInfo
    {
        public string SessionId { get; set; } = string.Empty;
        public int PercentComplete { get; set; }
        public string CurrentOperation { get; set; } = string.Empty;
        public string? CurrentFile { get; set; }
        public int ProcessedCount { get; set; }
        public int TotalCount { get; set; }
        public bool IsCompleted { get; set; }
        public bool HasError { get; set; }
        public string? ErrorMessage { get; set; }
        public DateTime LastUpdated { get; set; }
        public List<string> ProcessedFiles { get; set; }

        public ProgressInfo()
        {
            ProcessedFiles = new List<string>();
            LastUpdated = DateTime.Now;
        }
    }

    /// <summary>
    /// Interface for progress reporting
    /// </summary>
    public interface IProgressReporter
    {
        void ReportProgress(string sessionId, int percentComplete, string operation, string? currentFile = null);
        void ReportError(string sessionId, string errorMessage);
        void ReportCompletion(string sessionId, List<string> processedFiles);
        ProgressInfo? GetProgress(string sessionId);
        void CleanupOldProgress(TimeSpan maxAge);
    }

    /// <summary>
    /// Progress reporter implementation
    /// </summary>
    public class ProgressReporter : IProgressReporter
    {
        private readonly ConcurrentDictionary<string, ProgressInfo> _progressData;
        private readonly object _lockObject = new object();

        public ProgressReporter()
        {
            _progressData = new ConcurrentDictionary<string, ProgressInfo>();
        }

        public void ReportProgress(string sessionId, int percentComplete, string operation, string? currentFile = null)
        {
            lock (_lockObject)
            {
                var progress = _progressData.GetOrAdd(sessionId, _ => new ProgressInfo { SessionId = sessionId });
                progress.PercentComplete = percentComplete;
                progress.CurrentOperation = operation;
                progress.CurrentFile = currentFile;
                progress.LastUpdated = DateTime.Now;

                if (!string.IsNullOrEmpty(currentFile) && !progress.ProcessedFiles.Contains(currentFile))
                {
                    progress.ProcessedFiles.Add(currentFile);
                    progress.ProcessedCount = progress.ProcessedFiles.Count;
                }
            }
        }

        public void ReportError(string sessionId, string errorMessage)
        {
            lock (_lockObject)
            {
                var progress = _progressData.GetOrAdd(sessionId, _ => new ProgressInfo { SessionId = sessionId });
                progress.HasError = true;
                progress.ErrorMessage = errorMessage;
                progress.IsCompleted = true;
                progress.LastUpdated = DateTime.Now;
            }
        }

        public void ReportCompletion(string sessionId, List<string> processedFiles)
        {
            lock (_lockObject)
            {
                var progress = _progressData.GetOrAdd(sessionId, _ => new ProgressInfo { SessionId = sessionId });
                progress.IsCompleted = true;
                progress.PercentComplete = 100;
                progress.CurrentOperation = "已完成";
                progress.ProcessedFiles = processedFiles ?? new List<string>();
                progress.ProcessedCount = progress.ProcessedFiles.Count;
                progress.TotalCount = progress.ProcessedFiles.Count;
                progress.LastUpdated = DateTime.Now;
            }
        }

        public ProgressInfo? GetProgress(string sessionId)
        {
            return _progressData.TryGetValue(sessionId, out var progress) ? progress : null;
        }

        public void CleanupOldProgress(TimeSpan maxAge)
        {
            var cutoffTime = DateTime.Now - maxAge;
            var keysToRemove = _progressData
                .Where(kvp => kvp.Value.LastUpdated < cutoffTime)
                .Select(kvp => kvp.Key)
                .ToList();

            foreach (var key in keysToRemove)
            {
                _progressData.TryRemove(key, out _);
            }
        }
    }

    public class PrintDrawingApiService
    {
        private readonly string _tempDirectory;
        private readonly SemaphoreSlim _processingLock;
        private readonly string _logFilePath;
        private readonly int _maxConcurrency;
        private readonly object _logLock = new object(); // Thread-safe logging
        private readonly IProgressReporter _progressReporter;
        
        // Singleton instance
        private static PrintDrawingApiService _instance;
        private static readonly object _lock = new object();
        
        public static PrintDrawingApiService Instance
        {
            get
            {
                if (_instance == null)
                {
                    lock (_lock)
                    {
                        if (_instance == null)
                        {
                            _instance = new PrintDrawingApiService();
                        }
                    }
                }
                return _instance;
            }
        }
        
        // Private constructor to enforce singleton pattern
        private PrintDrawingApiService()
        {
            // Set maximum concurrency based on processor count, with a minimum of 2 and maximum of 8
            _maxConcurrency = Math.Max(2, Math.Min(Environment.ProcessorCount, 8));
            _processingLock = new SemaphoreSlim(_maxConcurrency, _maxConcurrency);
            _progressReporter = new ProgressReporter();

            // Create a temporary directory for processing files
            string pluginDir = Path.GetDirectoryName(System.Reflection.Assembly.GetExecutingAssembly().Location) ??
                              Path.GetTempPath();
            _tempDirectory = Path.Combine(pluginDir, "TempDwgFiles");

            if (!Directory.Exists(_tempDirectory))
            {
                Directory.CreateDirectory(_tempDirectory);
            }

            // 创建日志文件路径
            _logFilePath = Path.Combine(pluginDir, "PrintDrawingApiService.log");
            LogToFile($"PrintDrawingApiService 初始化，最大并发数: {_maxConcurrency}", true); // 第二个参数表示是否清空现有日志
        }
        
        /// <summary>
        /// 记录日志到文件 (线程安全)
        /// </summary>
        /// <param name="message">日志消息</param>
        /// <param name="clearLog">是否清空现有日志</param>
        private void LogToFile(string message, bool clearLog = false)
        {
            try
            {
                // Add thread ID for parallel processing diagnostics
                int threadId = Thread.CurrentThread.ManagedThreadId;
                string logMessage = $"[{DateTime.Now:yyyy-MM-dd HH:mm:ss.fff}] [Thread-{threadId}] {message}";

                // 创建包含调用栈信息的更详细日志
                var stackTrace = new StackTrace(true);
                string callerInfo = "";

                if (stackTrace.FrameCount > 1)
                {
                    var frame = stackTrace.GetFrame(1); // 获取调用者信息
                    var method = frame?.GetMethod();
                    if (method != null)
                    {
                        callerInfo = $" [{method.DeclaringType?.Name}.{method.Name}]";
                    }
                }

                logMessage += callerInfo;

                // Thread-safe file writing
                lock (_logLock)
                {
                    using (StreamWriter writer = new StreamWriter(_logFilePath, !clearLog, Encoding.UTF8))
                    {
                        writer.WriteLine(logMessage);
                    }
                }

                // 同时输出到调试窗口
                System.Diagnostics.Debug.WriteLine(logMessage);

                // 如果有活动文档，也输出到命令行 (AutoCAD UI calls should be thread-safe)
                /* try
                {
                    Document doc = Application.DocumentManager.MdiActiveDocument;
                    if (doc != null)
                    {
                        doc.Editor.WriteMessage($"\n{logMessage}");
                    }
                }
                catch
                {
                    // Ignore AutoCAD UI errors in parallel processing
                } */
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"记录日志时出错: {ex.Message}");
            }
        }
        
        /// <summary>
        /// Processes an uploaded DWG file and extracts all print drawings, packaging them as DWG files
        /// </summary>
        /// <param name="uploadedFilePath">Path to the uploaded DWG file</param>
        /// <returns>Path to the ZIP file containing all split DWG files</returns>
        public Task<string> ProcessDwgFile(string uploadedFilePath)
        {
            /* Document doc = Application.DocumentManager.MdiActiveDocument;
            if (doc != null)
            {
                doc.Editor.WriteMessage($"\n开始并行处理文件: {Path.GetFileName(uploadedFilePath)}");
            } */

            // Start processing immediately in parallel
            return Task.Run(async () =>
            {
                // Acquire semaphore to limit concurrency
                await _processingLock.WaitAsync();

                try
                {
                    LogToFile($"开始处理文件 (并发槽位已获取): {uploadedFilePath}");
                    return await ProcessDwgFileInternal(uploadedFilePath);
                }
                finally
                {
                    _processingLock.Release();
                    LogToFile($"处理完成，释放并发槽位: {Path.GetFileName(uploadedFilePath)}");
                }
            });
        }

        /// <summary>
        /// Processes an uploaded DWG file with progress reporting
        /// </summary>
        /// <param name="uploadedFilePath">Path to the uploaded DWG file</param>
        /// <param name="sessionId">Session ID for progress tracking</param>
        /// <returns>Path to the ZIP file containing all split DWG files</returns>
        public Task<string> ProcessDwgFileWithProgress(string uploadedFilePath, string sessionId)
        {
            /* Document doc = Application.DocumentManager.MdiActiveDocument;
            if (doc != null)
            {
                doc.Editor.WriteMessage($"\n开始并行处理文件: {Path.GetFileName(uploadedFilePath)}");
            } */

            // Start processing immediately in parallel
            return Task.Run(async () =>
            {
                // Acquire semaphore to limit concurrency
                await _processingLock.WaitAsync();

                try
                {
                    LogToFile($"开始处理文件 (并发槽位已获取): {uploadedFilePath}");
                    _progressReporter.ReportProgress(sessionId, 0, "开始处理文件");
                    return await ProcessDwgFileInternal(uploadedFilePath, sessionId);
                }
                finally
                {
                    _processingLock.Release();
                    LogToFile($"处理完成，释放并发槽位: {Path.GetFileName(uploadedFilePath)}");
                }
            });
        }

        
        /// <summary>
        /// Internal method to process a DWG file
        /// </summary>
        private async Task<string> ProcessDwgFileInternal(string uploadedFilePath, string? sessionId = null)
        {
            LogToFile($"开始处理文件: {uploadedFilePath}");
            // Create unique session ID for this processing request if not provided
            if (string.IsNullOrEmpty(sessionId))
            {
                sessionId = Guid.NewGuid().ToString("N");
            }
            string sessionDir = Path.Combine(_tempDirectory, sessionId);
            string outputDir = Path.Combine(sessionDir, "output");
            string dwgOutputDir = Path.Combine(sessionDir, "dwg_output");
            
            try
            {
                // Create directories
                Directory.CreateDirectory(sessionDir);
                Directory.CreateDirectory(outputDir);
                Directory.CreateDirectory(dwgOutputDir);
                LogToFile($"创建会话目录: {sessionDir}");
                _progressReporter.ReportProgress(sessionId, 10, "创建工作目录");

                // Copy uploaded file to session directory
                string targetDwgPath = Path.Combine(sessionDir, Path.GetFileName(uploadedFilePath));
                File.Copy(uploadedFilePath, targetDwgPath);
                LogToFile($"复制文件到会话目录: {targetDwgPath}");
                _progressReporter.ReportProgress(sessionId, 20, "复制文件到工作目录");

                // Process the DWG file to extract print drawings
                LogToFile("开始提取打印图纸");
                _progressReporter.ReportProgress(sessionId, 30, "开始分析DWG文件");
                List<string> extractedDwgFiles = await ExtractPrintDrawingsFromDwg(targetDwgPath, dwgOutputDir, sessionId);

                if (extractedDwgFiles.Count == 0)
                {
                    LogToFile("未找到有效的打印图纸");
                    _progressReporter.ReportError(sessionId, "未找到有效的打印图纸");
                    throw new Exception("No valid print drawings found in the uploaded DWG file");
                }

                LogToFile($"提取了 {extractedDwgFiles.Count} 个打印图纸: {string.Join(", ", extractedDwgFiles)}");
                _progressReporter.ReportProgress(sessionId, 90, "打包分割后的DWG文件");

                // Create a ZIP file containing all split DWG files
                string zipFilePath = Path.Combine(sessionDir, "print_drawings.zip");
                LogToFile($"创建ZIP文件: {zipFilePath}");
                ZipDirectory(dwgOutputDir, zipFilePath);

                LogToFile($"处理完成: {zipFilePath}");
                _progressReporter.ReportCompletion(sessionId, extractedDwgFiles.Select(f => Path.GetFileName(f) ?? "").Where(f => !string.IsNullOrEmpty(f)).ToList());
                return zipFilePath;
            }
            catch (Exception ex)
            {
                // Log the error
                LogToFile($"处理文件时出错: {ex.Message}\n{ex.StackTrace}");
                /* Document doc = Application.DocumentManager.MdiActiveDocument;
                if (doc != null)
                {
                    doc.Editor.WriteMessage($"\nError processing DWG file: {ex.Message}");
                } */
                
                // Clean up session directory in case of error
                try
                {
                    if (Directory.Exists(sessionDir))
                    {
                        Directory.Delete(sessionDir, true);
                    }
                }
                catch (Exception cleanupEx) 
                { 
                    LogToFile($"清理会话目录失败: {cleanupEx.Message}");
                }
                
                throw; // Re-throw to caller
            }
        }
        
        /// <summary>
        /// Extracts print drawings from a DWG file
        /// </summary>
        private Task<List<string>> ExtractPrintDrawingsFromDwg(string dwgFilePath, string outputDir, string? sessionId = null)
        {
            var tcs = new TaskCompletionSource<List<string>>();
            LogToFile($"准备在AutoCAD主线程上下文中提取打印图纸: {dwgFilePath}");

            try
            {
                Application.DocumentManager.ExecuteInApplicationContext(callback =>
                {
                    LogToFile($"进入AutoCAD主线程上下文执行 ExtractPrintDrawingsFromDwgCore: {dwgFilePath}");
                    List<string> result = ExtractPrintDrawingsFromDwgCore(dwgFilePath, outputDir, sessionId);
                    tcs.SetResult(result);
                    LogToFile("ExtractPrintDrawingsFromDwgCore 执行完毕并设置结果");
                }, null);
            }
            catch (Exception ex)
            {
                LogToFile($"调度到主线程时出错 (ExtractPrintDrawingsFromDwg): {ex.Message}\n{ex.StackTrace}");
                tcs.SetException(ex);
            }
            return tcs.Task;
        }

        // 将原ExtractPrintDrawingsFromDwg的同步核心逻辑移到这里
        private List<string> ExtractPrintDrawingsFromDwgCore(string dwgFilePath, string outputDir, string? sessionId = null)
        {
            LogToFile($"ExtractPrintDrawingsFromDwgCore 开始执行: {dwgFilePath}");
            List<string> extractedFiles = new List<string>();
            Database? sourceDb = null;
            bool sourceDbReadSuccessfully = false;

            object oldProxyNotice = Application.GetSystemVariable("PROXYNOTICE");
            object oldXrefNotify = Application.GetSystemVariable("XREFNOTIFY");
            LogToFile($"保存系统变量: PROXYNOTICE={oldProxyNotice}, XREFNOTIFY={oldXrefNotify}");

            try
            {
                Application.SetSystemVariable("PROXYNOTICE", 0); 
                Application.SetSystemVariable("XREFNOTIFY", 0);  
                LogToFile("设置系统变量: PROXYNOTICE=0, XREFNOTIFY=0");

                LogToFile("尝试使用 Database.ReadDwgFile 直接读取源DWG文件...");
                sourceDb = new Database(false, true); 
                sourceDb.ReadDwgFile(dwgFilePath, FileShare.Read, true, ""); 
                sourceDbReadSuccessfully = true;
                LogToFile("源Database.ReadDwgFile 成功");
                
                // 直接在sourceDb上执行操作，不创建临时文档
                LogToFile("开始在源数据库上处理...");

                using (Transaction tr = sourceDb.TransactionManager.StartTransaction())
                {
                    LogToFile("源数据库事务已开始");
                    if (!string.IsNullOrEmpty(sessionId))
                    {
                        _progressReporter.ReportProgress(sessionId, 35, "分析DWG文件结构");
                    }

                    // 获取模型空间
                    BlockTable bt = (BlockTable)tr.GetObject(sourceDb.BlockTableId, OpenMode.ForRead);
                    BlockTableRecord btr = (BlockTableRecord)tr.GetObject(bt[BlockTableRecord.ModelSpace], OpenMode.ForRead);

                    // 存储所有可能的图框和文本
                    List<Tuple<ObjectId, Polyline>> potentialFrames = new List<Tuple<ObjectId, Polyline>>();
                    List<DBText> allDbTexts = new List<DBText>();
                    List<MText> allMTexts = new List<MText>();

                    LogToFile("开始遍历源数据库实体...");
                    if (!string.IsNullOrEmpty(sessionId))
                    {
                        _progressReporter.ReportProgress(sessionId, 40, "扫描图框和文本实体");
                    }
                    int entityCount = 0;
                    foreach (ObjectId objId in btr)
                    {
                        entityCount++;
                        Entity ent = tr.GetObject(objId, OpenMode.ForRead) as Entity;
                        if (ent == null) continue;
                        
                        if (ent is Polyline pl)
                        {
                            if (pl.Closed)
                            {
                                potentialFrames.Add(new Tuple<ObjectId, Polyline>(pl.ObjectId, pl.Clone() as Polyline));
                            }
                        }
                        else if (ent is DBText dbText)
                        {
                            allDbTexts.Add(dbText.Clone() as DBText);
                        }
                        else if (ent is MText mText)
                        {
                            allMTexts.Add(mText.Clone() as MText);
                        }
                    }
                    LogToFile($"实体遍历完成，总计处理 {entityCount} 个实体");
                    LogToFile($"找到 {potentialFrames.Count} 个闭合多段线, {allDbTexts.Count} 个单行文字, {allMTexts.Count} 个多行文字");
                    
                    if (!potentialFrames.Any())
                    {
                        LogToFile("没有找到闭合多段线框架");
                        LogToFile("在源数据库中没有找到有效的打印图框");
                        tr.Abort();
                        return extractedFiles;
                    }
                    
                    LogToFile($"找到 {potentialFrames.Count} 个潜在的闭合多段线框架。");
                    if (!string.IsNullOrEmpty(sessionId))
                    {
                        _progressReporter.ReportProgress(sessionId, 50, $"找到 {potentialFrames.Count} 个潜在图框，开始分析");
                    }

                    List<ObjectId> allFramesProcessed = new List<ObjectId>();
                    int frameIndex = 0;

                    foreach (var frameTuple in potentialFrames.OrderByDescending(t => t.Item2.Area))
                    {
                        frameIndex++;
                        ObjectId originalFrameId = frameTuple.Item1;
                        Polyline framePolyline = frameTuple.Item2;

                        if (allFramesProcessed.Contains(originalFrameId)) continue;

                        if (!string.IsNullOrEmpty(sessionId))
                        {
                            int progressPercent = 50 + (frameIndex * 30 / potentialFrames.Count);
                            _progressReporter.ReportProgress(sessionId, progressPercent, $"处理图框 {frameIndex}/{potentialFrames.Count}");
                        }
                        
                        List<DBText> dbTextsInFrame = allDbTexts.Where(t => IsPointInsidePolyline(t.Position, framePolyline)).ToList();
                        List<MText> mTextsInFrame = allMTexts.Where(mt => IsPointInsidePolyline(mt.Location, framePolyline)).ToList();
                        
                        int countScaleText = dbTextsInFrame.Count(t => t.TextString.Trim().Replace(" ", "").Contains("比例")) +
                                             mTextsInFrame.Count(mt => mt.Text.Trim().Replace(" ", "").Contains("比例"));
                        int countDrawingNumLabel = dbTextsInFrame.Count(t => t.TextString.Trim().Replace(" ", "").Contains("图号")) +
                                                   mTextsInFrame.Count(mt => mt.Text.Trim().Replace(" ", "").Contains("图号"));
                        LogToFile($"图框检查: 比例标记数={countScaleText}, 图号标记数={countDrawingNumLabel}");
                        
                        List<string> drawingNumberValuesFound = new List<string>();
                        drawingNumberValuesFound.AddRange(dbTextsInFrame
                            .Select(t => t.TextString.Trim().Replace(" ", ""))
                            .Where(text => Regex.IsMatch(text, @"^\d+([/\\]\d+)?$|^[\d]+$")));
                        drawingNumberValuesFound.AddRange(mTextsInFrame
                            .Select(mt => mt.Text.Trim().Replace(" ", ""))
                            .Where(text => Regex.IsMatch(text, @"^\d+([/\\]\d+)?$|^[\d]+$")));
                        List<string> distinctDrawingNumberValues = drawingNumberValuesFound.Distinct().ToList();
                        LogToFile($"找到的图号值: {string.Join(", ", distinctDrawingNumberValues)}");
                        
                        if (countScaleText == 1 && countDrawingNumLabel == 1 && distinctDrawingNumberValues.Count > 0)
                        {
                            string rawDrawingNumber = distinctDrawingNumberValues.First();
                            LogToFile($"识别到有效打印图纸，图号: '{rawDrawingNumber}'");

                            if (!string.IsNullOrEmpty(sessionId))
                            {
                                _progressReporter.ReportProgress(sessionId, 50 + (frameIndex * 30 / potentialFrames.Count),
                                    $"处理图纸: {rawDrawingNumber}", rawDrawingNumber);
                            }

                            string fileName = rawDrawingNumber.Replace('/', '-').Replace('\\', '-');
                            fileName = string.Join("_", fileName.Split(Path.GetInvalidFileNameChars()));
                            fileName = Regex.Replace(fileName, @"[^\w\.-]", "_");
                            string outputFilePath = Path.Combine(outputDir, $"{fileName}.dwg");
                            LogToFile($"输出文件路径: {outputFilePath}");
                            
                            // 准备选择条件
                            LogToFile("手动收集与图框相交或内部的实体...");
                            ObjectIdCollection idsToWblock = new ObjectIdCollection();
                            idsToWblock.Add(originalFrameId); // 确保图框本身被包含

                            Extents3d frameExtents = new Extents3d();
                            try 
                            {
                                frameExtents = framePolyline.GeometricExtents;
                            }
                            catch(System.Exception)
                            {
                                // Fallback for polylines that might throw exception on GeometricExtents
                                for(int i = 0; i < framePolyline.NumberOfVertices; i++)
                                {
                                    frameExtents.AddPoint(framePolyline.GetPoint3dAt(i));
                                }
                            }
                            
                            foreach (ObjectId objId in btr)
                            {
                                if (objId == originalFrameId) continue;

                                Entity ent = tr.GetObject(objId, OpenMode.ForRead) as Entity;
                                if (ent == null) continue;
                                
                                // 如果实体是另一个潜在的图框多段线，则跳过它
                                // 这可以防止将外部或同级的图框包含在导出内容中
                                if (ent is Polyline && potentialFrames.Any(f => f.Item1 == objId))
                                {
                                    LogToFile($"跳过另一个潜在图框: {objId}");
                                    continue;
                                }

                                bool shouldInclude = false;
                                
                                if (ent is BlockReference blockRef)
                                {
                                    // 新的块参照检测逻辑：检查块的实际内容是否与图框相交或在图框内
                                    if (IsBlockContentIntersectingPolyline(blockRef, framePolyline, tr))
                                    {
                                        shouldInclude = true;
                                        LogToFile($"块参照 ({objId}) 基于内容与图框相交，包含在选择中");
                                    }
                                }
                                else
                                {
                                    try
                                    {
                                        // 尝试使用几何边界进行检查 (交叉选择)
                                        Extents3d entExtents = ent.GeometricExtents;
                                        
                                        if (entExtents.MinPoint.X <= frameExtents.MaxPoint.X &&
                                            entExtents.MaxPoint.X >= frameExtents.MinPoint.X &&
                                            entExtents.MinPoint.Y <= frameExtents.MaxPoint.Y &&
                                            entExtents.MaxPoint.Y >= frameExtents.MinPoint.Y)
                                        {
                                            shouldInclude = true;
                                        }
                                    }
                                    catch (System.Exception ex)
                                    {
                                        // 为无法获取几何边界的非块参照实体提供备用检查
                                        LogToFile($"无法获取实体 ({objId}) (类型: {ent.GetRXClass().Name}) 的边界，尝试备用检查方法。错误: {ex.Message}");
                                        
                                        if (ent is DBText dbText)
                                        {
                                            // 对于DBText，检查文本位置
                                            if (IsPointInsidePolyline(dbText.Position, framePolyline) ||
                                                IsPointNearPolyline(dbText.Position, framePolyline, 5.0)) 
                                            {
                                                shouldInclude = true;
                                                LogToFile($"文本 ({objId}) 基于位置包含在选择中");
                                            }
                                        }
                                        else if (ent is MText mText)
                                        {
                                            // 对于MText，检查文本位置
                                            if (IsPointInsidePolyline(mText.Location, framePolyline) ||
                                                IsPointNearPolyline(mText.Location, framePolyline, 5.0))
                                            {
                                                shouldInclude = true;
                                                LogToFile($"多行文本 ({objId}) 基于位置包含在选择中");
                                            }
                                        }
                                    }
                                }
                                
                                if (shouldInclude)
                                {
                                    idsToWblock.Add(objId);
                                }
                            }
                            
                            LogToFile($"收集了 {idsToWblock.Count} 个位于图框内或与之相交的实体");
                            
                            // 添加块参照的嵌套依赖项
                            if (idsToWblock.Count > 0)
                            {
                                AddNestedBlockDependencies(sourceDb, idsToWblock, tr);
                                LogToFile($"添加嵌套依赖项后，选择集包含 {idsToWblock.Count} 个实体");
                            }
                            
                            if (idsToWblock.Count > 1) // 确保除了图框还有其他实体
                            {
                                Database newExtractedDb = null;
                                try
                                {
                                    // 使用返回新数据库对象的Wblock重载。这通常比Wblock到预先存在的数据库更稳定，
                                    // 特别是从侧数据库操作时。
                                    newExtractedDb = sourceDb.Wblock(idsToWblock, Point3d.Origin);

                                    LogToFile("检查是否为横向图框");
                                    bool needRotation = IsHorizontalFrame(sourceDb, framePolyline, dbTextsInFrame, mTextsInFrame, tr);
                                    LogToFile($"是否需要旋转: {needRotation}");
                                    
                                    if (needRotation)
                                    {
                                        LogToFile("实体已写入新数据库，准备旋转...");
                                        RotateEntitiesInDatabase(newExtractedDb, framePolyline);
                                        LogToFile("实体旋转完成");
                                    }
                                    
                                    LogToFile("开始设置所有文字样式为宋体");
                                    using (Transaction extractedDbTr = newExtractedDb.TransactionManager.StartTransaction())
                                    {
                                        ObjectIdCollection entitiesInNewDb = new ObjectIdCollection();
                                        BlockTable newBt = (BlockTable)extractedDbTr.GetObject(newExtractedDb.BlockTableId, OpenMode.ForRead);
                                        BlockTableRecord newMs = (BlockTableRecord)extractedDbTr.GetObject(newBt[BlockTableRecord.ModelSpace], OpenMode.ForRead);
                                        foreach(ObjectId id in newMs)
                                        {
                                            entitiesInNewDb.Add(id);
                                        }
                                        if(entitiesInNewDb.Count > 0) 
                                        {
                                            SetAllTextStyleToSimSun(newExtractedDb, entitiesInNewDb, extractedDbTr); 
                                        }
                                        extractedDbTr.Commit();
                                    }
                                    CopyDrawingSettings(sourceDb, newExtractedDb); 
                                    newExtractedDb.SaveAs(outputFilePath, DwgVersion.Current);
                                    LogToFile($"已保存提取的图框: {outputFilePath}");
                                    allFramesProcessed.Add(originalFrameId);
                                    extractedFiles.Add(outputFilePath);
                                }
                                catch (System.Exception ex)
                                {
                                    LogToFile($"保存提取的图框 {outputFilePath} 时出错: {ex.Message}\n{ex.StackTrace}");
                                }
                                finally
                                {
                                    if (newExtractedDb != null)
                                    {
                                        newExtractedDb.Dispose();
                                    }
                                }
                            }
                            else
                            {
                                LogToFile($"图框内未找到实体，跳过图号: {rawDrawingNumber}");
                            }
                        }
                        else
                        {
                            LogToFile($"图框不符合打印图纸标准: 比例={countScaleText}, 图号标签={countDrawingNumLabel}, 图号值数量={distinctDrawingNumberValues.Count}");
                        }
                    }
                    tr.Commit();
                    LogToFile($"打印图纸提取完成，共生成 {extractedFiles.Count} 个DWG文件");
                    return extractedFiles;
                }
            }
            catch (Exception ex)
            {
                LogToFile($"ExtractPrintDrawingsFromDwgCore 执行时出错: {ex.Message}\n{ex.StackTrace}");
                System.Diagnostics.Debug.WriteLine($"ExtractPrintDrawingsFromDwgCore 执行时出错: {ex.Message}");
                throw; 
            }
            finally
            {
                LogToFile("ExtractPrintDrawingsFromDwgCore 清理阶段");
                Application.SetSystemVariable("PROXYNOTICE", oldProxyNotice);
                Application.SetSystemVariable("XREFNOTIFY", oldXrefNotify);
                LogToFile("恢复系统变量: PROXYNOTICE, XREFNOTIFY");
                
                if (sourceDb != null && !sourceDb.IsDisposed && sourceDbReadSuccessfully)
                {
                     LogToFile("单独销毁源Database对象");
                     sourceDb.Dispose();
                }
                LogToFile("ExtractPrintDrawingsFromDwgCore 执行完毕");
            }
        }

        // 新增一个辅助方法用于在数据库中旋转实体
        private void RotateEntitiesInDatabase(Database dbToRotate, Polyline framePolylineForCenter)
        {
            LogToFile("开始在数据库中旋转实体...");
            using (Transaction tr = dbToRotate.TransactionManager.StartTransaction())
            {
                BlockTable bt = tr.GetObject(dbToRotate.BlockTableId, OpenMode.ForRead) as BlockTable;
                BlockTableRecord btr = tr.GetObject(bt[BlockTableRecord.ModelSpace], OpenMode.ForWrite) as BlockTableRecord;

                // 计算旋转中心点 (基于原始图框的克隆，其坐标在当前模型空间中)
                Point3d rotMinPoint = new Point3d(double.MaxValue, double.MaxValue, 0);
                Point3d rotMaxPoint = new Point3d(double.MinValue, double.MinValue, 0);
                for (int i = 0; i < framePolylineForCenter.NumberOfVertices; i++)
                {
                    Point3d pt = framePolylineForCenter.GetPoint3dAt(i);
                    rotMinPoint = new Point3d(Math.Min(rotMinPoint.X, pt.X), Math.Min(rotMinPoint.Y, pt.Y), 0);
                    rotMaxPoint = new Point3d(Math.Max(rotMaxPoint.X, pt.X), Math.Max(rotMaxPoint.Y, pt.Y), 0);
                }
                Point3d centerPoint = new Point3d((rotMinPoint.X + rotMaxPoint.X) / 2, (rotMinPoint.Y + rotMaxPoint.Y) / 2, 0);
                LogToFile($"计算得到的旋转中心点: {centerPoint}");

                Matrix3d rotMatrix = Matrix3d.Rotation(-Math.PI / 2, Vector3d.ZAxis, centerPoint);
                
                foreach (ObjectId id in btr)
                {
                    Entity ent = tr.GetObject(id, OpenMode.ForWrite) as Entity;
                    if (ent != null)
                    {
                        ent.TransformBy(rotMatrix);
                    }
                }
                tr.Commit();
                LogToFile("数据库实体旋转并提交事务完成");
            }
        }
        

        




        /// <summary>
        /// Creates a ZIP file from a directory
        /// </summary>
        private void ZipDirectory(string sourceDir, string zipFilePath)
        {
            if (File.Exists(zipFilePath))
            {
                File.Delete(zipFilePath);
            }
            
            ZipFile.CreateFromDirectory(sourceDir, zipFilePath);
        }
        
        /// <summary>
        /// Cleans up temporary files older than a specified time
        /// </summary>
        public void CleanupTempFiles(TimeSpan maxAge)
        {
            try
            {
                if (!Directory.Exists(_tempDirectory))
                {
                    return;
                }
                
                foreach (string dir in Directory.GetDirectories(_tempDirectory))
                {
                    DirectoryInfo dirInfo = new DirectoryInfo(dir);
                    if (DateTime.Now - dirInfo.CreationTime > maxAge)
                    {
                        try
                        {
                            Directory.Delete(dir, true);
                        }
                        catch (Exception)
                        {
                            // Ignore errors when cleaning up
                        }
                    }
                }
            }
            catch (Exception)
            {
                // Ignore errors when cleaning up
            }
        }
        
        /// <summary>
        /// Gets the current number of active processing tasks
        /// </summary>
        public int GetActiveProcessingCount()
        {
            return _maxConcurrency - _processingLock.CurrentCount;
        }

        /// <summary>
        /// Gets the maximum number of concurrent processing tasks allowed
        /// </summary>
        public int GetMaxConcurrency()
        {
            return _maxConcurrency;
        }

        /// <summary>
        /// Gets the progress reporter instance
        /// </summary>
        public IProgressReporter GetProgressReporter()
        {
            return _progressReporter;
        }

        /// <summary>
        /// Cleans up old progress data
        /// </summary>
        public void CleanupOldProgressData(TimeSpan maxAge)
        {
            _progressReporter.CleanupOldProgress(maxAge);
        }
        
        #region Helper Methods
        
        // Robust Point in Polyline Check (Crossing Number Algorithm)
        private bool IsPointInsidePolyline(Point3d testPoint, Polyline poly)
        {
            if (!poly.Closed) return false;
            if (poly.NumberOfVertices < 3) return false;

            int crossings = 0;
            for (int i = 0; i < poly.NumberOfVertices; i++)
            {
                Point2d p1 = poly.GetPoint2dAt(i);
                Point2d p2 = poly.GetPoint2dAt((i + 1) % poly.NumberOfVertices);

                if (((p1.Y <= testPoint.Y && testPoint.Y < p2.Y) ||
                     (p2.Y <= testPoint.Y && testPoint.Y < p1.Y)) &&
                    (testPoint.X < (p2.X - p1.X) * (testPoint.Y - p1.Y) / (p2.Y - p1.Y) + p1.X))
                {
                    crossings++;
                }
            }
            return (crossings % 2) == 1;
        }
        
        private bool AreAllVerticesInsidePolyline(Polyline innerPoly, Polyline outerPoly)
        {
            for (int i = 0; i < innerPoly.NumberOfVertices; i++)
            {
                if (!IsPointInsidePolyline(innerPoly.GetPoint3dAt(i), outerPoly))
                {
                    return false;
                }
            }
            return true;
        }
        
        // 新增：判断块参照内容是否与多段线相交
        private bool IsBlockContentIntersectingPolyline(BlockReference blockRef, Polyline framePolyline, Transaction tr)
        {
            // Get block definition
            if (blockRef.BlockTableRecord.IsNull)
            {
                LogToFile($"Block reference {blockRef.ObjectId} has a null BlockTableRecord, skipping.");
                return false;
            }

            var blockDef = tr.GetObject(blockRef.BlockTableRecord, OpenMode.ForRead) as BlockTableRecord;
            if (blockDef == null) return false;

            // Get transformation matrix of the block reference
            var transform = blockRef.BlockTransform;

            // Get frame's extents once, as it's expensive
            Extents3d frameExtents;
            try
            {
                frameExtents = framePolyline.GeometricExtents;
            }
            catch
            {
                frameExtents = new Extents3d();
                for (int i = 0; i < framePolyline.NumberOfVertices; i++)
                {
                    frameExtents.AddPoint(framePolyline.GetPoint3dAt(i));
                }
            }

            // Iterate through entities in the block definition
            foreach (ObjectId entId in blockDef)
            {
                var entInBlock = tr.GetObject(entId, OpenMode.ForRead) as Entity;
                if (entInBlock == null || !entInBlock.Visible) continue;
                
                // Skip AttributeDefinition entities as they don't have geometry and cause eInvalidExtents exceptions
                if (entInBlock is AttributeDefinition)
                {
                    continue;
                }
                
                Entity transformedEnt = null;
                try
                {
                    transformedEnt = entInBlock.GetTransformedCopy(transform);

                    // AABB check of the transformed entity against the frame
                    var transformedExtents = transformedEnt.GeometricExtents;
                    if (transformedExtents.MinPoint.X <= frameExtents.MaxPoint.X &&
                        transformedExtents.MaxPoint.X >= frameExtents.MinPoint.X &&
                        transformedExtents.MinPoint.Y <= frameExtents.MaxPoint.Y &&
                        transformedExtents.MaxPoint.Y >= frameExtents.MinPoint.Y)
                    {
                        // Bounding boxes intersect. This is a good enough approximation for "crossing selection".
                        return true;
                    }
                }
                catch (System.Exception ex)
                {
                    LogToFile($"Error checking transformed entity {entInBlock.GetType().Name}: {ex.Message}");
                }
                finally
                {
                    if (transformedEnt != null && !transformedEnt.IsDisposed)
                    {
                        transformedEnt.Dispose();
                    }
                }
            }
            
            return false;
        }

        // 判断是否为横向图框(检查文本旋转角度)
        private bool IsHorizontalFrame(Database db, Polyline framePolyline, List<DBText> dbTextsInFrame, List<MText> mTextsInFrame, Transaction tr)
        {
            try
            {
                // 查找"比例"和"图号"文本
                var scaleTexts = dbTextsInFrame.FindAll(t => t.TextString.Trim().Replace(" ", "").Contains("比例"));
                var drawingNumTexts = dbTextsInFrame.FindAll(t => t.TextString.Trim().Replace(" ", "").Contains("图号"));
                
                // 如果找到了这些文本，检查它们的旋转角度
                foreach (var text in scaleTexts)
                {
                    // 检查旋转值是否接近90度（以角度为单位）
                    if (Math.Abs(text.Rotation * 180 / Math.PI - 90) < 5)
                    {
                        return true;
                    }
                }
                
                foreach (var text in drawingNumTexts)
                {
                    // 检查旋转值是否接近90度（以角度为单位）
                    if (Math.Abs(text.Rotation * 180 / Math.PI - 90) < 5)
                    {
                        return true;
                    }
                }
                
                // 也检查多行文本
                foreach (var mtext in mTextsInFrame)
                {
                    string content = mtext.Text.Trim().Replace(" ", "");
                    if (content.Contains("比例") || content.Contains("图号"))
                    {
                        // 检查旋转值是否接近90度（以角度为单位）
                        if (Math.Abs(mtext.Rotation * 180 / Math.PI - 90) < 5)
                        {
                            return true;
                        }
                    }
                }
                
                // 没有找到横向的文本
                return false;
            }
            catch (System.Exception)
            {
                return false; // 出错时默认为非横向
            }
        }
        
        // 添加新方法处理字体和文本样式复制
        private void CopyDrawingSettings(Database sourceDb, Database targetDb)
        {
            using (Transaction sourceTr = sourceDb.TransactionManager.StartTransaction())
            using (Transaction targetTr = targetDb.TransactionManager.StartTransaction())
            {
                try
                {
                    // 处理文本样式表以确保中文字体正确复制
                    TextStyleTable sourceTextStyleTable = sourceTr.GetObject(sourceDb.TextStyleTableId, OpenMode.ForRead) as TextStyleTable;
                    TextStyleTable targetTextStyleTable = targetTr.GetObject(targetDb.TextStyleTableId, OpenMode.ForWrite) as TextStyleTable;

                    foreach (ObjectId styleId in sourceTextStyleTable)
                    {
                        TextStyleTableRecord sourceStyle = sourceTr.GetObject(styleId, OpenMode.ForRead) as TextStyleTableRecord;
                        if (!targetTextStyleTable.Has(sourceStyle.Name))
                        {
                            continue; // 如果目标没有这个样式，跳过(Wblock应该已经复制)
                        }

                        // 获取目标中同名样式进行更新
                        if (targetTextStyleTable.Has(sourceStyle.Name))
                        {
                            ObjectId targetStyleId = targetTextStyleTable[sourceStyle.Name];
                            TextStyleTableRecord targetStyle = targetTr.GetObject(targetStyleId, OpenMode.ForWrite) as TextStyleTableRecord;
                            
                            // 确保字体名称正确复制
                            if (string.IsNullOrEmpty(targetStyle.FileName) && !string.IsNullOrEmpty(sourceStyle.FileName))
                            {
                                targetStyle.FileName = sourceStyle.FileName;
                            }

                            // 复制字体相关设置
                            targetStyle.Font = sourceStyle.Font;
                            
                            // 特别处理中文大字体文件 (SHX字体常用于中文)
                            if (string.IsNullOrEmpty(targetStyle.BigFontFileName) && !string.IsNullOrEmpty(sourceStyle.BigFontFileName))
                            {
                                targetStyle.BigFontFileName = sourceStyle.BigFontFileName;
                            }
                        }
                    }
                    
                    // 提交修改
                    targetTr.Commit();
                }
                catch (System.Exception)
                {
                    targetTr.Abort();
                }
            }
        }
        
        // 将所有文本和块参照属性的文字样式设置为宋体
        private void SetAllTextStyleToSimSun(Database db, ObjectIdCollection entityIds, Transaction tr)
        {
            try
            {
                // 首先确保宋体文字样式存在
                string simSunStyleName = "宋体";
                TextStyleTable textStyleTable = tr.GetObject(db.TextStyleTableId, OpenMode.ForRead) as TextStyleTable;
                
                ObjectId simSunStyleId;
                if (!textStyleTable.Has(simSunStyleName))
                {
                    // 如果宋体样式不存在，尝试创建它
                    textStyleTable.UpgradeOpen();
                    TextStyleTableRecord simSunStyle = new TextStyleTableRecord();
                    simSunStyle.Name = simSunStyleName;
                    simSunStyle.FileName = "simsun.ttf"; // 宋体字体文件
                    textStyleTable.Add(simSunStyle);
                    tr.AddNewlyCreatedDBObject(simSunStyle, true);
                    simSunStyleId = simSunStyle.ObjectId;
                }
                else
                {
                    simSunStyleId = textStyleTable[simSunStyleName];
                }
                
                // 遍历所有选中的实体
                foreach (ObjectId id in entityIds)
                {
                    Entity ent = tr.GetObject(id, OpenMode.ForRead) as Entity;
                    if (ent == null) continue;
                    
                    // 处理文本对象
                    if (ent is DBText)
                    {
                        DBText text = ent as DBText;
                        if (text.TextStyleId != simSunStyleId)
                        {
                            text.UpgradeOpen();
                            text.TextStyleId = simSunStyleId;
                        }
                    }
                    else if (ent is MText)
                    {
                        MText mtext = ent as MText;
                        if (mtext.TextStyleId != simSunStyleId)
                        {
                            mtext.UpgradeOpen();
                            mtext.TextStyleId = simSunStyleId;
                        }
                    }
                    // 处理块参照
                    else if (ent is BlockReference)
                    {
                        BlockReference blockRef = ent as BlockReference;
                        
                        // 检查块参照是否有属性
                        AttributeCollection attColl = blockRef.AttributeCollection;
                        if (attColl.Count > 0)
                        {
                            // 遍历所有属性
                            foreach (ObjectId attId in attColl)
                            {
                                AttributeReference attRef = tr.GetObject(attId, OpenMode.ForWrite) as AttributeReference;
                                if (attRef != null && attRef.TextStyleId != simSunStyleId)
                                {
                                    attRef.TextStyleId = simSunStyleId;
                                }
                            }
                        }
                    }
                    // 处理所有类型的标注(包括对齐标注)
                    else if (ent is Dimension)
                    {
                        Dimension dim = ent as Dimension;
                        if (dim.TextStyleId != simSunStyleId)
                        {
                            dim.UpgradeOpen();
                            dim.TextStyleId = simSunStyleId;
                        }
                    }
                }
            }
            catch (System.Exception)
            {
                // Ignore errors when setting text style
            }
        }
        
        // 新增方法：判断点是否靠近多段线
        private bool IsPointNearPolyline(Point3d testPoint, Polyline poly, double tolerance)
        {
            if (poly.NumberOfVertices < 2) return false;

            for (int i = 0; i < poly.NumberOfVertices; i++)
            {
                Point3d p1 = poly.GetPoint3dAt(i);
                Point3d p2 = poly.GetPoint3dAt((i + 1) % poly.NumberOfVertices);
                
                // 计算点到线段的距离
                double dist = PointToLineSegmentDistance(testPoint, p1, p2);
                if (dist <= tolerance)
                {
                    return true;
                }
            }
            return false;
        }

        // 计算点到线段的距离
        private double PointToLineSegmentDistance(Point3d point, Point3d lineStart, Point3d lineEnd)
        {
            Vector3d v = lineEnd - lineStart;
            Vector3d w = point - lineStart;

            double c1 = w.DotProduct(v);
            if (c1 <= 0)
                return point.DistanceTo(lineStart);

            double c2 = v.DotProduct(v);
            if (c2 <= c1)
                return point.DistanceTo(lineEnd);

            double b = c1 / c2;
            Point3d pb = lineStart + b * v;
            return point.DistanceTo(pb);
        }
        
        // 添加处理嵌套块的辅助方法
        private void AddNestedBlockDependencies(Database db, ObjectIdCollection idsCollection, Transaction tr)
        {
            try
            {
                HashSet<ObjectId> processedBlockRefs = new HashSet<ObjectId>();
                Dictionary<ObjectId, bool> blockDefsToProcess = new Dictionary<ObjectId, bool>();
                
                // 第1步：找出所有选择的块参照及其块定义
                foreach (ObjectId id in idsCollection)
                {
                    Entity ent = tr.GetObject(id, OpenMode.ForRead) as Entity;
                    if (ent is BlockReference blockRef)
                    {
                        processedBlockRefs.Add(blockRef.ObjectId);
                        if (!blockDefsToProcess.ContainsKey(blockRef.BlockTableRecord))
                        {
                            blockDefsToProcess.Add(blockRef.BlockTableRecord, false);
                        }
                    }
                }
                
                                 // 第2步：处理所有块定义，找出其内部可能的嵌套块
                 BlockTable blockTable = (BlockTable)tr.GetObject(db.BlockTableId, OpenMode.ForRead);
                foreach (ObjectId blockDefId in blockDefsToProcess.Keys.ToList())
                {
                    if (blockDefsToProcess[blockDefId]) continue;
                    
                    try
                    {
                        // 添加块定义到集合
                        if (!idsCollection.Contains(blockDefId))
                        {
                            idsCollection.Add(blockDefId);
                        }
                        
                        // 获取块定义
                        BlockTableRecord blockDef = tr.GetObject(blockDefId, OpenMode.ForRead) as BlockTableRecord;
                        if (blockDef != null)
                        {
                            // 处理块定义中的所有实体
                            foreach (ObjectId entId in blockDef)
                            {
                                // 添加块定义中的所有实体
                                if (!idsCollection.Contains(entId))
                                {
                                    idsCollection.Add(entId);
                                }
                                
                                // 如果实体是块参照，递归处理
                                Entity nestedEnt = tr.GetObject(entId, OpenMode.ForRead) as Entity;
                                if (nestedEnt is BlockReference nestedBlockRef && !processedBlockRefs.Contains(nestedBlockRef.ObjectId))
                                {
                                    // 添加嵌套块参照
                                    processedBlockRefs.Add(nestedBlockRef.ObjectId);
                                    
                                    // 添加嵌套块的属性
                                    AttributeCollection attColl = nestedBlockRef.AttributeCollection;
                                    foreach (ObjectId attId in attColl)
                                    {
                                        if (!idsCollection.Contains(attId))
                                        {
                                            idsCollection.Add(attId);
                                        }
                                    }
                                    
                                    // 处理嵌套块的块定义
                                    if (!blockDefsToProcess.ContainsKey(nestedBlockRef.BlockTableRecord))
                                    {
                                        blockDefsToProcess.Add(nestedBlockRef.BlockTableRecord, false);
                                    }
                                }
                            }
                        }
                        
                        blockDefsToProcess[blockDefId] = true;
                    }
                    catch (Exception ex)
                    {
                        LogToFile($"处理块定义 {blockDefId} 时出错: {ex.Message}");
                    }
                }
                
                // 第3步：再次遍历所有未处理的块定义，直到所有都处理完
                bool allProcessed;
                do
                {
                    allProcessed = true;
                    foreach (ObjectId blockDefId in blockDefsToProcess.Keys.ToList())
                    {
                        if (!blockDefsToProcess[blockDefId])
                        {
                            allProcessed = false;
                            try
                            {
                                // 重复前面的处理过程
                                if (!idsCollection.Contains(blockDefId))
                                {
                                    idsCollection.Add(blockDefId);
                                }
                                
                                BlockTableRecord blockDef = tr.GetObject(blockDefId, OpenMode.ForRead) as BlockTableRecord;
                                if (blockDef != null)
                                {
                                    foreach (ObjectId entId in blockDef)
                                    {
                                        if (!idsCollection.Contains(entId))
                                        {
                                            idsCollection.Add(entId);
                                        }
                                        
                                        Entity nestedEnt = tr.GetObject(entId, OpenMode.ForRead) as Entity;
                                        if (nestedEnt is BlockReference nestedBlockRef && !processedBlockRefs.Contains(nestedBlockRef.ObjectId))
                                        {
                                            processedBlockRefs.Add(nestedBlockRef.ObjectId);
                                            
                                            AttributeCollection attColl = nestedBlockRef.AttributeCollection;
                                            foreach (ObjectId attId in attColl)
                                            {
                                                if (!idsCollection.Contains(attId))
                                                {
                                                    idsCollection.Add(attId);
                                                }
                                            }
                                            
                                            if (!blockDefsToProcess.ContainsKey(nestedBlockRef.BlockTableRecord))
                                            {
                                                blockDefsToProcess.Add(nestedBlockRef.BlockTableRecord, false);
                                            }
                                        }
                                    }
                                }
                                
                                blockDefsToProcess[blockDefId] = true;
                            }
                            catch (Exception ex)
                            {
                                LogToFile($"再次处理块定义 {blockDefId} 时出错: {ex.Message}");
                                blockDefsToProcess[blockDefId] = true; // 标记为已处理，避免死循环
                            }
                        }
                    }
                } while (!allProcessed);
            }
            catch (Exception ex)
            {
                LogToFile($"添加嵌套块依赖项时出错: {ex.Message}");
            }
        }
        
        #endregion
    }
} 