using System;
using System.Collections.Generic;
using System.IO;
using System.Windows.Forms;
using RESClient.MVP.Models;
using RESClient.MVP.Views;
using RESClient.Services.Implementations;

namespace RESClient.Services
{
    /// <summary>
    /// 模块设置服务，用于管理各个模块的参数设置
    /// </summary>
    public class ModuleSettingsService
    {
        /// <summary>
        /// 打开封面模块设置
        /// </summary>
        /// <param name="parentForm">父窗体</param>
        /// <returns>设置结果</returns>
        public static ModuleSettingsResult OpenCoverSettings(Form parentForm)
        {
            try
            {
                // 创建封面模块生成器实例以获取参数模型
                var coverGenerator = new CoverModuleGenerator();
                var parametersModel = coverGenerator.GetParametersModel();
                
                // 获取当前参数
                var currentParameters = parametersModel.GetCurrentParameters();
                
                // 创建并显示参数输入窗体
                using (var parameterForm = new ParameterInputForm(currentParameters))
                {
                    parameterForm.StartPosition = FormStartPosition.CenterParent;
                    
                    if (parameterForm.ShowDialog(parentForm) == DialogResult.OK && parameterForm.IsConfirmed)
                    {
                        // 保存参数
                        bool saveResult = parametersModel.SaveParameters(parameterForm.Parameters);
                        
                        if (saveResult)
                        {
                            // 验证参数完整性
                            var validationResult = parameterForm.Parameters.Validate();
                            
                            return new ModuleSettingsResult
                            {
                                Success = true,
                                IsParametersValid = validationResult.IsValid,
                                ValidationErrors = validationResult.Errors,
                                Message = "封面参数保存成功"
                            };
                        }
                        else
                        {
                            return new ModuleSettingsResult
                            {
                                Success = false,
                                Message = "封面参数保存失败"
                            };
                        }
                    }
                    else
                    {
                        return new ModuleSettingsResult
                        {
                            Success = false,
                            Cancelled = true,
                            Message = "用户取消了封面参数设置"
                        };
                    }
                }
            }
            catch (Exception ex)
            {
                return new ModuleSettingsResult
                {
                    Success = false,
                    Message = $"打开封面设置窗口时发生错误: {ex.Message}",
                    Exception = ex
                };
            }
        }

        /// <summary>
        /// 打开作业声明模块设置
        /// </summary>
        /// <param name="parentForm">父窗体</param>
        /// <returns>设置结果</returns>
        public static ModuleSettingsResult OpenWorkStatementSettings(Form parentForm)
        {
            try
            {
                // 创建作业声明模块生成器实例以获取参数模型
                var workStatementGenerator = new WorkStatementModuleGenerator();
                var parametersModel = workStatementGenerator.GetParametersModel();

                // 获取当前参数
                var currentParameters = parametersModel.GetCurrentParameters();

                // 创建并显示参数输入窗体
                using (var parameterForm = new WorkStatementParameterInputForm(currentParameters))
                {
                    parameterForm.StartPosition = FormStartPosition.CenterParent;

                    if (parameterForm.ShowDialog(parentForm) == DialogResult.OK && parameterForm.IsConfirmed)
                    {
                        // 保存参数
                        bool saveResult = parametersModel.SaveParameters(parameterForm.Parameters);

                        if (saveResult)
                        {
                            // 验证参数完整性
                            var validationResult = parameterForm.Parameters.Validate();

                            return new ModuleSettingsResult
                            {
                                Success = true,
                                IsParametersValid = validationResult.IsValid,
                                ValidationErrors = validationResult.Errors,
                                Message = "作业声明参数保存成功"
                            };
                        }
                        else
                        {
                            return new ModuleSettingsResult
                            {
                                Success = false,
                                Message = "作业声明参数保存失败"
                            };
                        }
                    }
                    else
                    {
                        return new ModuleSettingsResult
                        {
                            Success = false,
                            Cancelled = true,
                            Message = "用户取消了作业声明参数设置"
                        };
                    }
                }
            }
            catch (Exception ex)
            {
                return new ModuleSettingsResult
                {
                    Success = false,
                    Message = $"打开作业声明设置窗口时发生错误: {ex.Message}",
                    Exception = ex
                };
            }
        }

        /// <summary>
        /// 获取封面模块的当前状态
        /// </summary>
        /// <returns>模块状态信息</returns>
        public static ModuleStatusInfo GetCoverModuleStatus()
        {
            try
            {
                var coverGenerator = new CoverModuleGenerator();
                var parametersModel = coverGenerator.GetParametersModel();
                var currentParameters = parametersModel.GetCurrentParameters();
                
                var validationResult = currentParameters.Validate();
                
                return new ModuleStatusInfo
                {
                    ModuleName = "封面",
                    IsConfigured = parametersModel.ConfigFileExists(),
                    IsValid = validationResult.IsValid,
                    ValidationErrors = validationResult.Errors,
                    IsAvailable = coverGenerator.IsAvailable(new Dictionary<string, object>())
                };
            }
            catch (Exception ex)
            {
                return new ModuleStatusInfo
                {
                    ModuleName = "封面",
                    IsConfigured = false,
                    IsValid = false,
                    IsAvailable = false,
                    ValidationErrors = new List<string> { $"获取模块状态时发生错误: {ex.Message}" }
                };
            }
        }

        /// <summary>
        /// 获取作业声明模块的当前状态
        /// </summary>
        /// <returns>模块状态信息</returns>
        public static ModuleStatusInfo GetWorkStatementModuleStatus()
        {
            try
            {
                var workStatementGenerator = new WorkStatementModuleGenerator();
                var parametersModel = workStatementGenerator.GetParametersModel();
                var currentParameters = parametersModel.GetCurrentParameters();

                var validationResult = currentParameters.Validate();

                return new ModuleStatusInfo
                {
                    ModuleName = "作业声明",
                    IsConfigured = parametersModel.ConfigFileExists(),
                    IsValid = validationResult.IsValid,
                    ValidationErrors = validationResult.Errors,
                    IsAvailable = workStatementGenerator.IsAvailable(new Dictionary<string, object>())
                };
            }
            catch (Exception ex)
            {
                return new ModuleStatusInfo
                {
                    ModuleName = "作业声明",
                    IsConfigured = false,
                    IsValid = false,
                    IsAvailable = false,
                    ValidationErrors = new List<string> { $"获取模块状态时发生错误: {ex.Message}" }
                };
            }
        }

        /// <summary>
        /// 打开作业、质量检查与验收模块设置
        /// </summary>
        /// <param name="parentForm">父窗体</param>
        /// <returns>设置结果</returns>
        public static ModuleSettingsResult OpenWorkQualitySettings(Form parentForm)
        {
            try
            {
                // 创建作业、质量检查与验收模块生成器实例以获取参数模型
                var workQualityGenerator = new WorkQualityModuleGenerator();
                var parametersModel = workQualityGenerator.GetParametersModel();

                // 获取当前参数
                var currentParameters = parametersModel.GetCurrentParameters();

                // 创建并显示参数输入窗体
                using (var parameterForm = new WorkQualityParameterInputForm(currentParameters))
                {
                    parameterForm.StartPosition = FormStartPosition.CenterParent;

                    if (parameterForm.ShowDialog(parentForm) == DialogResult.OK && parameterForm.IsConfirmed)
                    {
                        // 保存参数
                        bool saveResult = parametersModel.UpdateParameters(parameterForm.Parameters);

                        if (saveResult)
                        {
                            // 验证参数完整性
                            var validationResult = parameterForm.Parameters.Validate();

                            return new ModuleSettingsResult
                            {
                                Success = true,
                                IsParametersValid = validationResult.IsValid,
                                ValidationErrors = validationResult.Errors,
                                Message = "作业、质量检查与验收参数保存成功"
                            };
                        }
                        else
                        {
                            return new ModuleSettingsResult
                            {
                                Success = false,
                                Message = "作业、质量检查与验收参数保存失败"
                            };
                        }
                    }
                    else
                    {
                        return new ModuleSettingsResult
                        {
                            Success = false,
                            Cancelled = true,
                            Message = "用户取消了作业、质量检查与验收参数设置"
                        };
                    }
                }
            }
            catch (Exception ex)
            {
                return new ModuleSettingsResult
                {
                    Success = false,
                    Message = $"打开作业、质量检查与验收设置窗口时发生错误: {ex.Message}",
                    Exception = ex
                };
            }
        }

        /// <summary>
        /// 打开项目基本信息模块设置
        /// </summary>
        /// <param name="parentForm">父窗体</param>
        /// <returns>设置结果</returns>
        public static ModuleSettingsResult OpenProjectInfoSettings(Form parentForm)
        {
            try
            {
                // 创建项目基本信息模块生成器实例以获取参数模型
                var projectInfoGenerator = new ProjectInfoModuleGenerator();
                var parametersModel = projectInfoGenerator.GetParametersModel();

                // 获取当前参数
                var currentParameters = parametersModel.GetCurrentParameters();

                // 创建并显示参数输入窗体
                using (var parameterForm = new ProjectInfoParameterInputForm(currentParameters))
                {
                    parameterForm.StartPosition = FormStartPosition.CenterParent;

                    if (parameterForm.ShowDialog(parentForm) == DialogResult.OK && parameterForm.IsConfirmed)
                    {
                        // 保存参数
                        bool saveResult = parametersModel.SaveParameters(parameterForm.Parameters);

                        if (saveResult)
                        {
                            // 验证参数完整性
                            var validationResult = parameterForm.Parameters.Validate();

                            return new ModuleSettingsResult
                            {
                                Success = true,
                                IsParametersValid = validationResult.IsValid,
                                ValidationErrors = validationResult.Errors,
                                Message = "项目基本信息参数保存成功"
                            };
                        }
                        else
                        {
                            return new ModuleSettingsResult
                            {
                                Success = false,
                                Message = "项目基本信息参数保存失败"
                            };
                        }
                    }
                    else
                    {
                        return new ModuleSettingsResult
                        {
                            Success = false,
                            Cancelled = true,
                            Message = "用户取消了项目基本信息参数设置"
                        };
                    }
                }
            }
            catch (Exception ex)
            {
                return new ModuleSettingsResult
                {
                    Success = false,
                    Message = $"打开项目基本信息设置窗口时发生错误: {ex.Message}",
                    Exception = ex
                };
            }
        }

        /// <summary>
        /// 获取项目基本信息模块的当前状态
        /// </summary>
        /// <returns>模块状态信息</returns>
        public static ModuleStatusInfo GetProjectInfoModuleStatus()
        {
            try
            {
                var projectInfoGenerator = new ProjectInfoModuleGenerator();
                var parametersModel = projectInfoGenerator.GetParametersModel();
                var currentParameters = parametersModel.GetCurrentParameters();

                var validationResult = currentParameters.Validate();
                bool isAvailable = projectInfoGenerator.IsAvailable(new Dictionary<string, object>());

                return new ModuleStatusInfo
                {
                    ModuleName = "项目基本信息",
                    IsConfigured = true,
                    IsValid = validationResult.IsValid,
                    IsAvailable = isAvailable,
                    ValidationErrors = validationResult.Errors
                };
            }
            catch (Exception ex)
            {
                return new ModuleStatusInfo
                {
                    ModuleName = "项目基本信息",
                    IsConfigured = false,
                    IsValid = false,
                    IsAvailable = false,
                    ValidationErrors = new List<string> { $"获取模块状态时发生错误: {ex.Message}" }
                };
            }
        }

        /// <summary>
        /// 打开地下室人防区域说明模块设置
        /// </summary>
        /// <param name="parentForm">父窗体</param>
        /// <returns>设置结果</returns>
        public static ModuleSettingsResult OpenBasementDefenseSettings(Form parentForm)
        {
            try
            {
                // 创建地下室人防区域说明模块生成器实例以获取参数模型
                var basementDefenseGenerator = new BasementDefenseModuleGenerator();
                var parametersModel = basementDefenseGenerator.GetParametersModel();

                // 获取当前参数
                var currentParameters = parametersModel.GetCurrentParameters();

                // 创建并显示参数输入窗体
                using (var parameterForm = new BasementDefenseParameterInputForm(currentParameters))
                {
                    parameterForm.StartPosition = FormStartPosition.CenterParent;

                    if (parameterForm.ShowDialog(parentForm) == DialogResult.OK && parameterForm.IsConfirmed)
                    {
                        // 保存参数
                        bool saveResult = parametersModel.SaveParameters(parameterForm.Parameters);

                        if (saveResult)
                        {
                            // 验证参数完整性
                            var validationResult = parameterForm.Parameters.Validate();

                            return new ModuleSettingsResult
                            {
                                Success = true,
                                IsParametersValid = validationResult.IsValid,
                                ValidationErrors = validationResult.Errors,
                                Message = "地下室人防区域说明参数保存成功"
                            };
                        }
                        else
                        {
                            return new ModuleSettingsResult
                            {
                                Success = false,
                                Message = "地下室人防区域说明参数保存失败"
                            };
                        }
                    }
                    else
                    {
                        return new ModuleSettingsResult
                        {
                            Success = false,
                            Cancelled = true,
                            Message = "用户取消了地下室人防区域说明参数设置"
                        };
                    }
                }
            }
            catch (Exception ex)
            {
                return new ModuleSettingsResult
                {
                    Success = false,
                    Message = $"打开地下室人防区域说明设置窗口时发生错误: {ex.Message}",
                    Exception = ex
                };
            }
        }

        /// <summary>
        /// 获取地下室人防区域说明模块的当前状态
        /// </summary>
        /// <returns>模块状态信息</returns>
        public static ModuleStatusInfo GetBasementDefenseModuleStatus()
        {
            try
            {
                var basementDefenseGenerator = new BasementDefenseModuleGenerator();
                var parametersModel = basementDefenseGenerator.GetParametersModel();
                var currentParameters = parametersModel.GetCurrentParameters();

                var validationResult = currentParameters.Validate();
                bool isAvailable = basementDefenseGenerator.IsAvailable(new Dictionary<string, object>());

                return new ModuleStatusInfo
                {
                    ModuleName = "地下室人防区域说明",
                    IsConfigured = true,
                    IsValid = validationResult.IsValid,
                    IsAvailable = isAvailable,
                    ValidationErrors = validationResult.Errors
                };
            }
            catch (Exception ex)
            {
                return new ModuleStatusInfo
                {
                    ModuleName = "地下室人防区域说明",
                    IsConfigured = false,
                    IsValid = false,
                    IsAvailable = false,
                    ValidationErrors = new List<string> { $"获取模块状态时发生错误: {ex.Message}" }
                };
            }
        }

        /// <summary>
        /// 获取作业、质量检查与验收模块的当前状态
        /// </summary>
        /// <returns>模块状态信息</returns>
        public static ModuleStatusInfo GetWorkQualityModuleStatus()
        {
            try
            {
                var workQualityGenerator = new WorkQualityModuleGenerator();
                var parametersModel = workQualityGenerator.GetParametersModel();
                var currentParameters = parametersModel.GetCurrentParameters();

                var validationResult = currentParameters.Validate();

                return new ModuleStatusInfo
                {
                    ModuleName = "作业、质量检查与验收",
                    IsConfigured = File.Exists(Path.Combine(
                        Environment.GetFolderPath(Environment.SpecialFolder.LocalApplicationData),
                        "RESClient", "work_quality_parameters.json")),
                    IsValid = validationResult.IsValid,
                    ValidationErrors = validationResult.Errors,
                    IsAvailable = workQualityGenerator.IsAvailable(new Dictionary<string, object>())
                };
            }
            catch (Exception ex)
            {
                return new ModuleStatusInfo
                {
                    ModuleName = "作业、质量检查与验收",
                    IsConfigured = false,
                    IsValid = false,
                    IsAvailable = false,
                    ValidationErrors = new List<string> { $"获取模块状态时发生错误: {ex.Message}" }
                };
            }
        }

        /// <summary>
        /// 打开经主管部门批准的相关证照模块设置
        /// </summary>
        /// <param name="parentForm">父窗体</param>
        /// <returns>设置结果</returns>
        public static ModuleSettingsResult OpenCertificatesSettings(Form parentForm)
        {
            try
            {
                // 创建经主管部门批准的相关证照模块生成器实例以获取参数模型
                var certificatesGenerator = new CertificatesModuleGenerator();
                var parametersModel = certificatesGenerator.GetParametersModel();

                // 获取当前参数
                var currentParameters = parametersModel.GetCurrentParameters();

                // 创建并显示参数输入窗体
                using (var parameterForm = new CertificatesParameterInputForm(currentParameters))
                {
                    parameterForm.StartPosition = FormStartPosition.CenterParent;

                    if (parameterForm.ShowDialog(parentForm) == DialogResult.OK && parameterForm.IsConfirmed)
                    {
                        // 保存参数
                        bool saveResult = parametersModel.SaveParameters(parameterForm.Parameters);

                        if (saveResult)
                        {
                            // 验证参数完整性
                            var validationResult = parameterForm.Parameters.Validate();

                            return new ModuleSettingsResult
                            {
                                Success = true,
                                IsParametersValid = validationResult.IsValid,
                                ValidationErrors = validationResult.Errors,
                                Message = validationResult.IsValid ? "经主管部门批准的相关证照参数设置成功" : "参数设置成功，但存在验证错误"
                            };
                        }
                        else
                        {
                            return new ModuleSettingsResult
                            {
                                Success = false,
                                Message = "保存经主管部门批准的相关证照参数失败"
                            };
                        }
                    }
                    else
                    {
                        return new ModuleSettingsResult
                        {
                            Success = false,
                            Message = "用户取消了经主管部门批准的相关证照参数设置"
                        };
                    }
                }
            }
            catch (Exception ex)
            {
                return new ModuleSettingsResult
                {
                    Success = false,
                    Message = $"打开经主管部门批准的相关证照设置时发生错误: {ex.Message}"
                };
            }
        }

        /// <summary>
        /// 获取经主管部门批准的相关证照模块的当前状态
        /// </summary>
        /// <returns>模块状态信息</returns>
        public static ModuleStatusInfo GetCertificatesModuleStatus()
        {
            try
            {
                var certificatesGenerator = new CertificatesModuleGenerator();
                var parametersModel = certificatesGenerator.GetParametersModel();
                var currentParameters = parametersModel.GetCurrentParameters();

                // 检查模板文件是否存在
                string templatePath = Path.Combine(
                    AppDomain.CurrentDomain.BaseDirectory,
                    "报告模板",
                    "07_经主管部门批准的相关证照",
                    "经主管部门批准的相关证照.docx");

                bool templateExists = File.Exists(templatePath);

                // 验证参数
                var validationResult = currentParameters.Validate();

                return new ModuleStatusInfo
                {
                    ModuleName = "经主管部门批准的相关证照",
                    IsTemplateAvailable = templateExists,
                    IsParametersValid = validationResult.IsValid,
                    ValidationErrors = validationResult.Errors,
                    TemplatePath = templatePath,
                    IsConfigured = true,
                    IsValid = validationResult.IsValid,
                    IsAvailable = templateExists
                };
            }
            catch (Exception ex)
            {
                return new ModuleStatusInfo
                {
                    ModuleName = "经主管部门批准的相关证照",
                    IsTemplateAvailable = false,
                    IsParametersValid = false,
                    ValidationErrors = new List<string> { $"获取模块状态时发生错误: {ex.Message}" },
                    TemplatePath = "",
                    IsConfigured = false,
                    IsValid = false,
                    IsAvailable = false
                };
            }
        }

        /// <summary>
        /// 打开楼栋基本信息模块设置
        /// </summary>
        /// <param name="parentForm">父窗体</param>
        /// <returns>设置结果</returns>
        public static ModuleSettingsResult OpenBuildingInfoSettings(Form parentForm)
        {
            try
            {
                // 创建楼栋基本信息参数模型
                var parametersModel = new BuildingInfoParametersModel();

                // 获取当前参数
                var currentParameters = parametersModel.GetCurrentParameters();

                // 创建并显示参数输入窗体
                using (var parameterForm = new BuildingInfoParameterInputForm(currentParameters))
                {
                    parameterForm.StartPosition = FormStartPosition.CenterParent;

                    if (parameterForm.ShowDialog(parentForm) == DialogResult.OK && parameterForm.IsConfirmed)
                    {
                        // 保存参数
                        bool saveResult = parametersModel.SaveParameters(parameterForm.Parameters);

                        if (saveResult)
                        {
                            // 验证参数完整性
                            var validationResult = parameterForm.Parameters.Validate();

                            return new ModuleSettingsResult
                            {
                                Success = true,
                                IsParametersValid = validationResult.IsValid,
                                ValidationErrors = validationResult.Errors,
                                Message = "楼栋基本信息参数保存成功"
                            };
                        }
                        else
                        {
                            return new ModuleSettingsResult
                            {
                                Success = false,
                                Message = "楼栋基本信息参数保存失败"
                            };
                        }
                    }
                    else
                    {
                        return new ModuleSettingsResult
                        {
                            Success = false,
                            Cancelled = true,
                            Message = "用户取消了楼栋基本信息参数设置"
                        };
                    }
                }
            }
            catch (Exception ex)
            {
                return new ModuleSettingsResult
                {
                    Success = false,
                    Message = $"打开楼栋基本信息设置时发生错误: {ex.Message}"
                };
            }
        }

        /// <summary>
        /// 获取楼栋基本信息模块的当前状态
        /// </summary>
        /// <returns>模块状态信息</returns>
        public static ModuleStatusInfo GetBuildingInfoModuleStatus()
        {
            try
            {
                var parametersModel = new BuildingInfoParametersModel();
                var currentParameters = parametersModel.GetCurrentParameters();

                // 检查模板文件是否存在
                string templatePath = Path.Combine(
                    AppDomain.CurrentDomain.BaseDirectory,
                    "报告模板",
                    "06_楼栋基本信息",
                    "楼栋基本信息.docx");

                bool templateExists = File.Exists(templatePath);

                // 验证参数
                var validationResult = currentParameters.Validate();

                return new ModuleStatusInfo
                {
                    ModuleName = "楼栋基本信息",
                    IsTemplateAvailable = templateExists,
                    IsParametersValid = validationResult.IsValid,
                    ValidationErrors = validationResult.Errors,
                    TemplatePath = templatePath,
                    IsConfigured = true,
                    IsValid = validationResult.IsValid,
                    IsAvailable = templateExists && validationResult.IsValid
                };
            }
            catch (Exception ex)
            {
                return new ModuleStatusInfo
                {
                    ModuleName = "楼栋基本信息",
                    IsTemplateAvailable = false,
                    IsParametersValid = false,
                    ValidationErrors = new List<string> { ex.Message },
                    TemplatePath = "",
                    IsConfigured = false,
                    IsValid = false,
                    IsAvailable = false
                };
            }
        }

        /// <summary>
        /// 打开房产面积汇总表模块设置
        /// </summary>
        /// <param name="parentForm">父窗体</param>
        /// <returns>设置结果</returns>
        public static ModuleSettingsResult OpenEstateAreaSummarySettings(Form parentForm)
        {
            try
            {
                // 创建房产面积汇总表参数模型
                var parametersModel = new RESClient.MVP.Models.EstateAreaSummaryParametersModel();

                // 获取当前参数
                var currentParameters = parametersModel.GetCurrentParameters();

                // 创建并显示参数输入窗体
                using (var parameterForm = new RESClient.MVP.Views.EstateAreaSummaryParameterInputForm(currentParameters))
                {
                    parameterForm.StartPosition = FormStartPosition.CenterParent;

                    if (parameterForm.ShowDialog(parentForm) == DialogResult.OK && parameterForm.IsConfirmed)
                    {
                        // 保存参数
                        bool saveResult = parametersModel.SaveParameters(parameterForm.Parameters);

                        if (saveResult)
                        {
                            // 验证参数完整性
                            var validationResult = parameterForm.Parameters.Validate();

                            return new ModuleSettingsResult
                            {
                                Success = true,
                                IsParametersValid = validationResult.IsValid,
                                ValidationErrors = validationResult.Errors,
                                Message = "房产面积汇总表参数保存成功"
                            };
                        }
                        else
                        {
                            return new ModuleSettingsResult
                            {
                                Success = false,
                                Message = "房产面积汇总表参数保存失败"
                            };
                        }
                    }
                    else
                    {
                        return new ModuleSettingsResult
                        {
                            Success = false,
                            Cancelled = true,
                            Message = "用户取消了房产面积汇总表参数设置"
                        };
                    }
                }
            }
            catch (Exception ex)
            {
                return new ModuleSettingsResult
                {
                    Success = false,
                    Message = $"打开房产面积汇总表设置时发生错误: {ex.Message}"
                };
            }
        }

        /// <summary>
        /// 获取房产面积汇总表模块的当前状态
        /// </summary>
        /// <returns>模块状态信息</returns>
        public static ModuleStatusInfo GetEstateAreaSummaryModuleStatus()
        {
            try
            {
                var parametersModel = new RESClient.MVP.Models.EstateAreaSummaryParametersModel();
                var currentParameters = parametersModel.GetCurrentParameters();

                // 检查模板文件是否存在
                string templatePath = Path.Combine(
                    AppDomain.CurrentDomain.BaseDirectory,
                    "报告模板",
                    "11_房产面积汇总表",
                    "房产面积汇总表.docx");

                bool templateExists = File.Exists(templatePath);

                // 验证参数
                var validationResult = currentParameters.Validate();

                return new ModuleStatusInfo
                {
                    ModuleName = "房产面积汇总表",
                    IsTemplateAvailable = templateExists,
                    IsParametersValid = validationResult.IsValid,
                    ValidationErrors = validationResult.Errors,
                    TemplatePath = templatePath,
                    IsConfigured = true,
                    IsValid = validationResult.IsValid,
                    IsAvailable = templateExists && validationResult.IsValid
                };
            }
            catch (Exception ex)
            {
                return new ModuleStatusInfo
                {
                    ModuleName = "房产面积汇总表",
                    IsTemplateAvailable = false,
                    IsParametersValid = false,
                    ValidationErrors = new List<string> { ex.Message },
                    TemplatePath = "",
                    IsConfigured = false,
                    IsValid = false,
                    IsAvailable = false
                };
            }
        }
    }

    /// <summary>
    /// 模块设置结果
    /// </summary>
    public class ModuleSettingsResult
    {
        /// <summary>
        /// 操作是否成功
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// 是否被用户取消
        /// </summary>
        public bool Cancelled { get; set; }

        /// <summary>
        /// 参数是否有效
        /// </summary>
        public bool IsParametersValid { get; set; }

        /// <summary>
        /// 验证错误列表
        /// </summary>
        public List<string> ValidationErrors { get; set; } = new List<string>();

        /// <summary>
        /// 结果消息
        /// </summary>
        public string Message { get; set; } = "";

        /// <summary>
        /// 异常信息（如果有）
        /// </summary>
        public Exception Exception { get; set; }
    }

    /// <summary>
    /// 模块状态信息
    /// </summary>
    public class ModuleStatusInfo
    {
        /// <summary>
        /// 模块名称
        /// </summary>
        public string ModuleName { get; set; } = "";

        /// <summary>
        /// 是否已配置
        /// </summary>
        public bool IsConfigured { get; set; }

        /// <summary>
        /// 配置是否有效
        /// </summary>
        public bool IsValid { get; set; }

        /// <summary>
        /// 模块是否可用
        /// </summary>
        public bool IsAvailable { get; set; }

        /// <summary>
        /// 验证错误列表
        /// </summary>
        public List<string> ValidationErrors { get; set; } = new List<string>();

        /// <summary>
        /// 模板是否可用
        /// </summary>
        public bool IsTemplateAvailable { get; set; }

        /// <summary>
        /// 参数是否有效
        /// </summary>
        public bool IsParametersValid { get; set; }

        /// <summary>
        /// 模板文件路径
        /// </summary>
        public string TemplatePath { get; set; } = "";

        /// <summary>
        /// 获取模块状态描述
        /// </summary>
        /// <returns>状态描述</returns>
        public string GetStatusDescription()
        {
            if (!IsAvailable)
                return "不可用";

            if (!IsConfigured)
                return "未配置";

            if (!IsValid)
                return "配置无效";

            return "就绪";
        }
    }
}
