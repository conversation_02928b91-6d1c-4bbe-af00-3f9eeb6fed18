using System.Diagnostics;
using System.IO.Compression;
using System.Text;

namespace RESServer.Services
{
    /// <summary>
    /// 使用 accoreconsole.exe 处理 DWG 文件的服务
    /// </summary>
    public class AccoreConsoleService
    {
        private readonly ILogger<AccoreConsoleService> _logger;
        private readonly IConfiguration _configuration;
        private readonly string _accoreConsolePath;
        private readonly string _scriptFilePath;
        private readonly string _tempDirectory;
        
        public AccoreConsoleService(ILogger<AccoreConsoleService> logger, IConfiguration configuration)
        {
            _logger = logger;
            _configuration = configuration;

            // 创建临时目录用于保存上传的文件
            _tempDirectory = Path.Combine(Path.GetTempPath(), "AccoreConsoleService");
            if (!Directory.Exists(_tempDirectory))
            {
                Directory.CreateDirectory(_tempDirectory);
            }

            // 获取 accoreconsole.exe 路径，默认从配置中读取，如果没有则使用预设路径
            _accoreConsolePath = _configuration["AccoreConsole:Path"] ?? @"C:\Program Files\Autodesk\AutoCAD 2025\accoreconsole.exe";

            // 使用固定的脚本文件路径
            _scriptFilePath = _configuration["AccoreConsole:ScriptPath"] ?? @"C:\Users\<USER>\source\repos\XSENTRYCAD\run_command.scr";

            // 确保 accoreconsole.exe 存在
            if (!File.Exists(_accoreConsolePath))
            {
                _logger.LogWarning("accoreconsole.exe 不存在于配置的路径: {Path}", _accoreConsolePath);
            }
            else
            {
                _logger.LogInformation("找到 accoreconsole.exe: {Path}", _accoreConsolePath);
            }

            // 确保脚本文件存在，如果不存在则创建
            if (!File.Exists(_scriptFilePath))
            {
                _logger.LogInformation("脚本文件不存在，正在创建: {Path}", _scriptFilePath);
                try
                {
                    // 创建固定的脚本内容
                    string scriptContent = "NETLOAD \"RESCADServerPlugin.dll\"\n" +
                                          "RESSavePrintDrawings\n" +
                                          "QUIT\n" +
                                          "N";

                    // 创建目录（如果不存在）
                    Directory.CreateDirectory(Path.GetDirectoryName(_scriptFilePath));

                    // 写入脚本文件
                    File.WriteAllText(_scriptFilePath, scriptContent, Encoding.ASCII);
                    _logger.LogInformation("已创建脚本文件: {Path}", _scriptFilePath);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "创建脚本文件失败: {Path}", _scriptFilePath);
                }
            }
        }

        /// <summary>
        /// 创建用于生成平面图的脚本文件
        /// </summary>
        /// <param name="sessionId">会话ID</param>
        /// <returns>脚本文件路径</returns>
        private string CreateFloorPlanScript(string sessionId)
        {
            string floorPlanScriptPath = Path.Combine(_tempDirectory, sessionId, "floor_plan_script.scr");

            try
            {
                // 创建平面图生成脚本内容
                string scriptContent = "NETLOAD \"RESCADServerPlugin.dll\"\n" +
                                      "RES_GENERATE_FLOOR_PLANS\n" +
                                      "QUIT\n" +
                                      "N";

                // 创建目录（如果不存在）
                Directory.CreateDirectory(Path.GetDirectoryName(floorPlanScriptPath));

                // 写入脚本文件
                File.WriteAllText(floorPlanScriptPath, scriptContent, Encoding.ASCII);
                _logger.LogInformation("已创建平面图生成脚本文件: {Path}", floorPlanScriptPath);

                return floorPlanScriptPath;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "创建平面图生成脚本文件失败: {Path}", floorPlanScriptPath);
                throw;
            }
        }
        
        /// <summary>
        /// 处理 DWG 文件，使用插件拆分图纸
        /// </summary>
        /// <param name="inputFilePath">输入 DWG 文件路径</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <param name="waitForCompletion">是否等待处理完成</param>
        /// <returns>结果文件路径和会话 ID</returns>
        public async Task<(string ResultFilePath, string SessionId)> ProcessDrawingAsync(
            string inputFilePath, 
            CancellationToken cancellationToken,
            bool waitForCompletion = true)
        {
            // 生成唯一会话 ID
            string sessionId = Guid.NewGuid().ToString("N");
            
            // 创建会话目录
            string sessionDir = Path.Combine(_tempDirectory, sessionId);
            Directory.CreateDirectory(sessionDir);
            
            _logger.LogInformation("开始处理 DWG 文件，会话 ID: {SessionId}", sessionId);
            
            try
            {
                // 获取原始文件名（用于输出文件命名）
                string originalFileName = Path.GetFileName(inputFilePath);
                string originalFileNameWithoutExt = Path.GetFileNameWithoutExtension(inputFilePath);
                string dwgFileExtension = Path.GetExtension(inputFilePath);
                
                // 使用 sessionId 创建一个新的唯一文件名
                string uniqueFileName = $"{sessionId}{dwgFileExtension}";
                string uniqueFilePath = Path.Combine(sessionDir, uniqueFileName);
                
                // 复制上传的文件到会话目录，使用唯一文件名
                File.Copy(inputFilePath, uniqueFilePath);
                _logger.LogInformation("已保存上传文件到: {Path}", uniqueFilePath);
                
                // 构建命令行参数 - 使用唯一文件路径
                string arguments = $"/i \"{uniqueFilePath}\" /s \"{_scriptFilePath}\" /l \"en-US\"";
                
                _logger.LogInformation("启动 accoreconsole.exe: {Arguments}", arguments);
                
                // 创建 Process 实例 - 使用管理员权限运行
                using var process = new Process();
                process.StartInfo.FileName = _accoreConsolePath;
                process.StartInfo.Arguments = arguments;
                process.StartInfo.UseShellExecute = true; // 需要设置为true才能使用管理员权限
                process.StartInfo.CreateNoWindow = true;
                process.StartInfo.WindowStyle = ProcessWindowStyle.Hidden;
                process.StartInfo.Verb = "runas"; // 使用管理员权限运行
                
                // 启动进程
                process.Start();
                
                // 如果不需要等待完成，立即返回会话ID
                if (!waitForCompletion)
                {
                    // 获取进程ID，在后台任务中使用新的Process对象
                    int processId = process.Id;
                    
                    // 在后台任务中等待进程完成
                    _ = Task.Run(async () =>
                    {
                        // 创建新的Process对象来跟踪原始进程
                        using var backgroundProcess = Process.GetProcessById(processId);
                        
                        try
                        {
                            // 等待进程完成或取消
                            await backgroundProcess.WaitForExitAsync(CancellationToken.None);
                            _logger.LogInformation("后台任务：accoreconsole.exe 已完成，退出代码: {ExitCode}", backgroundProcess.ExitCode);

                            // 创建一个标记文件来表示进程已完成
                            string completionMarkerPath = Path.Combine(sessionDir, $"_COMPLETED_{backgroundProcess.ExitCode}");
                            await File.WriteAllTextAsync(completionMarkerPath, DateTime.UtcNow.ToString("o"), CancellationToken.None);
                            _logger.LogInformation("已在 {Path} 创建完成标记", completionMarkerPath);
                        }
                        catch (Exception ex)
                        {
                            _logger.LogError(ex, "后台任务：处理 DWG 文件时出错");

                            // 也为失败创建一个标记文件
                            string errorMarkerPath = Path.Combine(sessionDir, "_FAILED");
                            await File.WriteAllTextAsync(errorMarkerPath, ex.ToString(), CancellationToken.None);
                            _logger.LogInformation("已在 {Path} 创建失败标记", errorMarkerPath);
                        }
                    });

                    // 返回空结果路径和会话ID
                    return (string.Empty, sessionId);
                }
                
                // 如果需要等待完成，执行原来的逻辑
                // 等待进程完成或取消
                var processCompletionTask = process.WaitForExitAsync(cancellationToken);
                
                // 设置超时
                var timeoutTask = Task.Delay(TimeSpan.FromMinutes(5), cancellationToken);
                
                // 等待进程完成或超时
                var completedTask = await Task.WhenAny(processCompletionTask, timeoutTask);
                
                if (completedTask == timeoutTask)
                {
                    _logger.LogWarning("处理 DWG 文件超时，正在终止进程");
                    try
                    {
                        if (!process.HasExited)
                        {
                            process.Kill();
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "终止进程时出错");
                    }
                    
                    throw new TimeoutException("处理 DWG 文件超时");
                }
                
                // 进程已完成
                await processCompletionTask;
                int exitCode = process.ExitCode;
                
                _logger.LogInformation("accoreconsole.exe 已完成，退出代码: {ExitCode}", exitCode);
                
                // 查找 PrintDrawingsOutput 目录 - 这是插件生成文件的位置
                string printDrawingsOutput = Path.Combine(Path.GetDirectoryName(_accoreConsolePath), "PrintDrawingsOutput");
                if (!Directory.Exists(printDrawingsOutput))
                {
                    _logger.LogError("找不到 PrintDrawingsOutput 目录: {Path}", printDrawingsOutput);
                    throw new DirectoryNotFoundException($"找不到输出目录: {printDrawingsOutput}");
                }
                
                // 查找基于 sessionId 的输出子目录
                string[] possibleDirs = Directory.GetDirectories(printDrawingsOutput, $"{sessionId}*");
                
                // 如果找不到，使用最新创建的目录
                if (possibleDirs.Length == 0)
                {
                    possibleDirs = Directory.GetDirectories(printDrawingsOutput);
                    if (possibleDirs.Length > 0)
                    {
                        Array.Sort(possibleDirs, (x, y) => Directory.GetCreationTime(y).CompareTo(Directory.GetCreationTime(x)));
                        _logger.LogWarning("未找到与 SessionId 匹配的目录，使用最新创建的目录: {Dir}", possibleDirs[0]);
                    }
                    else
                    {
                        _logger.LogError("在 PrintDrawingsOutput 中找不到任何输出目录");
                        throw new DirectoryNotFoundException("找不到任何输出目录");
                    }
                }
                
                // 使用找到的目录
                string outputDir = possibleDirs[0];
                _logger.LogInformation("找到输出目录: {Path}", outputDir);

                return (outputDir, sessionId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理 DWG 文件时出错");
                throw;
            }
        }

        /// <summary>
        /// 生成平面图，使用插件生成楼层平面图
        /// </summary>
        /// <param name="inputFilePath">输入 DWG 文件路径</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <param name="waitForCompletion">是否等待处理完成</param>
        /// <returns>结果文件路径和会话 ID</returns>
        public async Task<(string ResultFilePath, string SessionId)> GenerateFloorPlansAsync(
            string inputFilePath,
            CancellationToken cancellationToken,
            bool waitForCompletion = true)
        {
            // 生成唯一会话 ID
            string sessionId = Guid.NewGuid().ToString("N");

            // 创建会话目录
            string sessionDir = Path.Combine(_tempDirectory, sessionId);
            Directory.CreateDirectory(sessionDir);

            _logger.LogInformation("开始生成平面图，会话 ID: {SessionId}", sessionId);

            try
            {
                // 获取原始文件名（用于输出文件命名）
                string originalFileName = Path.GetFileName(inputFilePath);
                string originalFileNameWithoutExt = Path.GetFileNameWithoutExtension(inputFilePath);
                string dwgFileExtension = Path.GetExtension(inputFilePath);

                // 使用 sessionId 创建一个新的唯一文件名
                string uniqueFileName = $"{sessionId}{dwgFileExtension}";
                string uniqueFilePath = Path.Combine(sessionDir, uniqueFileName);

                // 复制上传的文件到会话目录，使用唯一文件名
                File.Copy(inputFilePath, uniqueFilePath);
                _logger.LogInformation("已保存上传文件到: {Path}", uniqueFilePath);

                // 创建专用的平面图生成脚本
                string floorPlanScriptPath = CreateFloorPlanScript(sessionId);

                // 构建命令行参数 - 使用唯一文件路径和专用脚本
                string arguments = $"/i \"{uniqueFilePath}\" /s \"{floorPlanScriptPath}\" /l \"en-US\"";

                _logger.LogInformation("启动 accoreconsole.exe 生成平面图: {Arguments}", arguments);

                // 创建 Process 实例 - 使用管理员权限运行
                using var process = new Process();
                process.StartInfo.FileName = _accoreConsolePath;
                process.StartInfo.Arguments = arguments;
                process.StartInfo.UseShellExecute = true; // 需要设置为true才能使用管理员权限
                process.StartInfo.CreateNoWindow = true;
                process.StartInfo.WindowStyle = ProcessWindowStyle.Hidden;
                process.StartInfo.Verb = "runas"; // 使用管理员权限运行

                // 启动进程
                process.Start();

                // 如果不需要等待完成，立即返回会话ID
                if (!waitForCompletion)
                {
                    // 获取进程ID，在后台任务中使用新的Process对象
                    int processId = process.Id;

                    // 在后台任务中等待进程完成
                    _ = Task.Run(async () =>
                    {
                        // 创建新的Process对象来跟踪原始进程
                        using var backgroundProcess = Process.GetProcessById(processId);

                        try
                        {
                            // 等待进程完成或取消
                            await backgroundProcess.WaitForExitAsync(CancellationToken.None);
                            _logger.LogInformation("后台任务：平面图生成已完成，退出代码: {ExitCode}", backgroundProcess.ExitCode);

                            // 创建一个标记文件来表示进程已完成
                            string completionMarkerPath = Path.Combine(sessionDir, $"_COMPLETED_{backgroundProcess.ExitCode}");
                            await File.WriteAllTextAsync(completionMarkerPath, DateTime.UtcNow.ToString("o"), CancellationToken.None);
                            _logger.LogInformation("已在 {Path} 创建完成标记", completionMarkerPath);
                        }
                        catch (Exception ex)
                        {
                            _logger.LogError(ex, "后台任务：生成平面图时出错");

                            // 也为失败创建一个标记文件
                            string errorMarkerPath = Path.Combine(sessionDir, "_FAILED");
                            await File.WriteAllTextAsync(errorMarkerPath, ex.ToString(), CancellationToken.None);
                            _logger.LogInformation("已在 {Path} 创建失败标记", errorMarkerPath);
                        }
                    });

                    // 返回空结果路径和会话ID
                    return (string.Empty, sessionId);
                }

                // 如果需要等待完成，执行原来的逻辑
                // 等待进程完成或取消
                var processCompletionTask = process.WaitForExitAsync(cancellationToken);

                // 设置超时
                var timeoutTask = Task.Delay(TimeSpan.FromMinutes(5), cancellationToken);

                // 等待进程完成或超时
                var completedTask = await Task.WhenAny(processCompletionTask, timeoutTask);

                if (completedTask == timeoutTask)
                {
                    _logger.LogWarning("生成平面图超时，正在终止进程");
                    try
                    {
                        if (!process.HasExited)
                        {
                            process.Kill();
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "终止进程时出错");
                    }

                    throw new TimeoutException("生成平面图超时");
                }

                // 进程已完成
                await processCompletionTask;
                int exitCode = process.ExitCode;

                _logger.LogInformation("平面图生成已完成，退出代码: {ExitCode}", exitCode);

                // 查找 PrintDrawingsOutput 目录 - 这是插件生成文件的位置
                string printDrawingsOutput = Path.Combine(Path.GetDirectoryName(_accoreConsolePath), "PrintDrawingsOutput");
                if (!Directory.Exists(printDrawingsOutput))
                {
                    _logger.LogError("找不到 PrintDrawingsOutput 目录: {Path}", printDrawingsOutput);
                    throw new DirectoryNotFoundException($"找不到输出目录: {printDrawingsOutput}");
                }

                // 查找基于 sessionId 的输出子目录
                string[] possibleDirs = Directory.GetDirectories(printDrawingsOutput, $"{sessionId}*");

                // 如果找不到，使用最新创建的目录
                if (possibleDirs.Length == 0)
                {
                    possibleDirs = Directory.GetDirectories(printDrawingsOutput);
                    if (possibleDirs.Length > 0)
                    {
                        Array.Sort(possibleDirs, (x, y) => Directory.GetCreationTime(y).CompareTo(Directory.GetCreationTime(x)));
                        _logger.LogWarning("未找到与 SessionId 匹配的目录，使用最新创建的目录: {Dir}", possibleDirs[0]);
                    }
                    else
                    {
                        _logger.LogError("在 PrintDrawingsOutput 中找不到任何输出目录");
                        throw new DirectoryNotFoundException("找不到任何输出目录");
                    }
                }

                // 使用找到的目录
                string outputDir = possibleDirs[0];
                _logger.LogInformation("找到平面图输出目录: {Path}", outputDir);

                return (outputDir, sessionId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "生成平面图时出错");
                throw;
            }
        }
    }
}