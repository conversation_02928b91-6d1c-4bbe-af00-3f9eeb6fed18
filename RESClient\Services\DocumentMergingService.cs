using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using NPOI.XWPF.UserModel;
using NPOI.OpenXmlFormats.Wordprocessing;
using System.Text;

namespace RESClient.Services
{
    /// <summary>
    /// 文档合并服务 - 增强版本，支持完整格式保持和详细诊断
    /// </summary>
    public class DocumentMergingService
    {
        /// <summary>
        /// 报告模块的标准顺序
        /// </summary>
        private static readonly List<string> STANDARD_MODULE_ORDER = new List<string>
        {
            "封面",
            "目录",
            "作业声明",
            "作业、质量检查与验收",
            "项目基本信息",
            "楼栋基本信息",
            "经主管部门批准的相关证照",
            "地下室人防区域说明",
            "项目丘地及测绘房屋分布图",
            "建筑物现状影像图",
            "房产面积汇总表",
            "房产分户面积统计表",
            "房产分层测绘图"
        };

        /// <summary>
        /// 合并诊断信息
        /// </summary>
        public class MergeDiagnostics
        {
            public List<string> ProcessedModules { get; set; } = new List<string>();
            public List<string> SkippedModules { get; set; } = new List<string>();
            public List<string> FailedModules { get; set; } = new List<string>();
            public List<string> FormatIssues { get; set; } = new List<string>();
            public int TotalParagraphs { get; set; }
            public int TotalTables { get; set; }
            public int FailedParagraphs { get; set; }
            public int FailedTables { get; set; }
            public StringBuilder DetailedLog { get; set; } = new StringBuilder();

            public void LogInfo(string message)
            {
                DetailedLog.AppendLine($"[INFO] {DateTime.Now:HH:mm:ss} {message}");
            }

            public void LogWarning(string message)
            {
                DetailedLog.AppendLine($"[WARNING] {DateTime.Now:HH:mm:ss} {message}");
            }

            public void LogError(string message)
            {
                DetailedLog.AppendLine($"[ERROR] {DateTime.Now:HH:mm:ss} {message}");
            }
        }

        private MergeDiagnostics _diagnostics;

        /// <summary>
        /// 合并多个Word文档为完整报告
        /// </summary>
        /// <param name="outputDirectory">输出目录</param>
        /// <param name="finalReportName">最终报告文件名</param>
        /// <param name="progressCallback">进度回调</param>
        /// <returns>合并结果</returns>
        public async Task<bool> MergeDocumentsAsync(string outputDirectory, string finalReportName, Action<int, string> progressCallback)
        {
            try
            {
                progressCallback?.Invoke(0, "[INFO]开始合并文档...");

                // 1. 检查输出目录
                if (!Directory.Exists(outputDirectory))
                {
                    progressCallback?.Invoke(0, "[ERROR]输出目录不存在");
                    return false;
                }

                // 2. 查找所有可用的模块文档
                var availableModules = FindAvailableModules(outputDirectory);
                progressCallback?.Invoke(10, $"[INFO]找到 {availableModules.Count} 个可用模块");

                if (availableModules.Count == 0)
                {
                    progressCallback?.Invoke(0, "[ERROR]未找到任何可合并的模块文档");
                    return false;
                }

                // 3. 按标准顺序排序模块
                var orderedModules = OrderModulesByStandard(availableModules);
                progressCallback?.Invoke(20, "[INFO]模块排序完成");

                // 4. 创建合并后的文档
                string finalReportPath = Path.Combine(outputDirectory, finalReportName);
                bool result = await Task.Run(() => MergeDocuments(orderedModules, finalReportPath, progressCallback));

                if (result)
                {
                    progressCallback?.Invoke(100, $"[SUCCESS]文档合并完成: {finalReportName}");
                }
                else
                {
                    progressCallback?.Invoke(100, "[ERROR]文档合并失败");
                }

                return result;
            }
            catch (Exception ex)
            {
                progressCallback?.Invoke(100, $"[ERROR]文档合并过程中发生异常: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 合并文档（支持不同的源目录和目标目录）
        /// </summary>
        /// <param name="sourceDirectory">源模块目录</param>
        /// <param name="targetDirectory">目标报告目录</param>
        /// <param name="finalReportName">最终报告文件名</param>
        /// <param name="progressCallback">进度回调</param>
        /// <returns>合并结果</returns>
        public async Task<bool> MergeDocumentsAsync(string sourceDirectory, string targetDirectory, string finalReportName, Action<int, string> progressCallback)
        {
            try
            {
                progressCallback?.Invoke(0, "[INFO]开始文档合并过程");

                // 1. 验证源目录
                if (!Directory.Exists(sourceDirectory))
                {
                    progressCallback?.Invoke(0, "[ERROR]源模块目录不存在");
                    return false;
                }

                // 2. 确保目标目录存在
                if (!Directory.Exists(targetDirectory))
                {
                    Directory.CreateDirectory(targetDirectory);
                }

                // 3. 查找所有可用的模块文档
                var availableModules = FindAvailableModules(sourceDirectory);
                progressCallback?.Invoke(10, $"[INFO]找到 {availableModules.Count} 个可用模块");

                if (availableModules.Count == 0)
                {
                    progressCallback?.Invoke(0, "[ERROR]未找到任何可合并的模块文档");
                    return false;
                }

                // 4. 按标准顺序排序模块
                var orderedModules = OrderModulesByStandard(availableModules);
                progressCallback?.Invoke(20, "[INFO]模块排序完成");

                // 5. 创建合并后的文档
                string finalReportPath = Path.Combine(targetDirectory, finalReportName);
                bool result = await Task.Run(() => MergeDocuments(orderedModules, finalReportPath, progressCallback));

                if (result)
                {
                    progressCallback?.Invoke(100, $"[SUCCESS]文档合并完成: {finalReportName}");
                    progressCallback?.Invoke(100, $"[INFO]集成报告保存位置: {finalReportPath}");
                }
                else
                {
                    progressCallback?.Invoke(100, "[ERROR]文档合并失败");
                }

                return result;
            }
            catch (Exception ex)
            {
                progressCallback?.Invoke(100, $"[ERROR]文档合并过程中发生异常: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 查找输出目录中所有可用的模块文档
        /// </summary>
        /// <param name="outputDirectory">输出目录</param>
        /// <returns>可用模块文档路径字典</returns>
        private Dictionary<string, string> FindAvailableModules(string outputDirectory)
        {
            var availableModules = new Dictionary<string, string>();

            foreach (string moduleName in STANDARD_MODULE_ORDER)
            {
                // 首先尝试查找带编号前缀的文件
                string numberedModuleFilePath = FindNumberedModuleFile(outputDirectory, moduleName);
                if (!string.IsNullOrEmpty(numberedModuleFilePath) && File.Exists(numberedModuleFilePath))
                {
                    availableModules[moduleName] = numberedModuleFilePath;
                    continue;
                }

                // 如果没有找到带编号的文件，尝试查找原始文件名
                string moduleFilePath = Path.Combine(outputDirectory, $"{moduleName}.docx");
                if (File.Exists(moduleFilePath))
                {
                    availableModules[moduleName] = moduleFilePath;
                }
            }

            return availableModules;
        }

        /// <summary>
        /// 查找带编号前缀的模块文件
        /// </summary>
        /// <param name="outputDirectory">输出目录</param>
        /// <param name="moduleName">模块名称</param>
        /// <returns>带编号前缀的文件路径，如果不存在则返回null</returns>
        private string FindNumberedModuleFile(string outputDirectory, string moduleName)
        {
            try
            {
                // 获取模块对应的编号前缀
                string numberPrefix = GetModuleNumberPrefix(moduleName);
                if (string.IsNullOrEmpty(numberPrefix))
                {
                    return null;
                }

                // 构造带编号前缀的文件名
                string numberedFileName = $"{numberPrefix}{moduleName}.docx";
                string numberedFilePath = Path.Combine(outputDirectory, numberedFileName);

                return File.Exists(numberedFilePath) ? numberedFilePath : null;
            }
            catch
            {
                return null;
            }
        }

        /// <summary>
        /// 获取模块的编号前缀
        /// </summary>
        /// <param name="moduleName">模块名称</param>
        /// <returns>编号前缀，如果模块不存在则返回null</returns>
        private string GetModuleNumberPrefix(string moduleName)
        {
            // 模块编号映射表 - 与ModuleNumberingService保持一致
            var moduleNumberMapping = new Dictionary<string, string>
            {
                { "封面", "01_" },
                { "目录", "02_" },
                { "作业声明", "03_" },
                { "作业、质量检查与验收", "04_" },
                { "项目基本信息", "05_" },
                { "楼栋基本信息", "06_" },
                { "经主管部门批准的相关证照", "07_" },
                { "地下室人防区域说明", "08_" },
                { "项目丘地及测绘房屋分布图", "09_" },
                { "建筑物现状影像图", "10_" },
                { "房产面积汇总表", "11_" },
                { "房产分户面积统计表", "12_" },
                { "房产分层测绘图", "13_" }
            };

            return moduleNumberMapping.TryGetValue(moduleName, out string prefix) ? prefix : null;
        }

        /// <summary>
        /// 按标准顺序排序模块
        /// </summary>
        /// <param name="availableModules">可用模块字典</param>
        /// <returns>排序后的模块列表</returns>
        private List<KeyValuePair<string, string>> OrderModulesByStandard(Dictionary<string, string> availableModules)
        {
            var orderedModules = new List<KeyValuePair<string, string>>();

            foreach (string moduleName in STANDARD_MODULE_ORDER)
            {
                if (availableModules.ContainsKey(moduleName))
                {
                    orderedModules.Add(new KeyValuePair<string, string>(moduleName, availableModules[moduleName]));
                }
            }

            return orderedModules;
        }

        /// <summary>
        /// 执行文档合并
        /// </summary>
        /// <param name="orderedModules">排序后的模块列表</param>
        /// <param name="finalReportPath">最终报告路径</param>
        /// <param name="progressCallback">进度回调</param>
        /// <returns>合并结果</returns>
        private bool MergeDocuments(List<KeyValuePair<string, string>> orderedModules, string finalReportPath, Action<int, string> progressCallback)
        {
            _diagnostics = new MergeDiagnostics();
            XWPFDocument finalDocument = null;

            try
            {
                _diagnostics.LogInfo("开始文档合并过程");

                if (orderedModules == null || orderedModules.Count == 0)
                {
                    var errorMsg = "没有可合并的模块";
                    _diagnostics.LogError(errorMsg);
                    progressCallback?.Invoke(90, $"[ERROR]{errorMsg}");
                    return false;
                }

                if (string.IsNullOrEmpty(finalReportPath))
                {
                    var errorMsg = "最终报告路径为空";
                    _diagnostics.LogError(errorMsg);
                    progressCallback?.Invoke(90, $"[ERROR]{errorMsg}");
                    return false;
                }

                // 验证模块顺序
                ValidateModuleOrder(orderedModules, progressCallback);

                int processedModules = 0;
                int totalModules = orderedModules.Count;
                _diagnostics.LogInfo($"计划合并 {totalModules} 个模块");

                foreach (var module in orderedModules)
                {
                    string moduleName = module.Key;
                    string moduleFilePath = module.Value;

                    _diagnostics.LogInfo($"开始处理模块: {moduleName}");

                    if (string.IsNullOrEmpty(moduleFilePath) || !File.Exists(moduleFilePath))
                    {
                        var warningMsg = $"跳过不存在的模块文件: {moduleName} (路径: {moduleFilePath})";
                        _diagnostics.LogWarning(warningMsg);
                        _diagnostics.SkippedModules.Add(moduleName);
                        progressCallback?.Invoke(
                            30 + (int)((float)processedModules / totalModules * 60),
                            $"[WARNING]{warningMsg}");
                        processedModules++;
                        continue;
                    }

                    progressCallback?.Invoke(
                        30 + (int)((float)processedModules / totalModules * 60),
                        $"[INFO]正在合并: {moduleName}");

                    try
                    {
                        using (var moduleStream = new FileStream(moduleFilePath, FileMode.Open, FileAccess.Read))
                        {
                            var moduleDocument = new XWPFDocument(moduleStream);

                            if (moduleDocument == null)
                            {
                                var warningMsg = $"无法读取模块文档: {moduleName}";
                                _diagnostics.LogWarning(warningMsg);
                                _diagnostics.FailedModules.Add(moduleName);
                                progressCallback?.Invoke(
                                    30 + (int)((float)processedModules / totalModules * 60),
                                    $"[WARNING]{warningMsg}");
                                processedModules++;
                                continue;
                            }

                            // 分析模块内容
                            AnalyzeModuleContent(moduleDocument, moduleName);

                            if (finalDocument == null)
                            {
                                // 第一个文档作为基础文档
                                finalDocument = moduleDocument;
                                _diagnostics.LogInfo($"使用 {moduleName} 作为基础文档");
                                _diagnostics.ProcessedModules.Add(moduleName);
                            }
                            else
                            {
                                // 合并后续文档
                                try
                                {
                                    _diagnostics.LogInfo($"开始合并模块内容: {moduleName}");
                                    AppendDocumentWithDiagnostics(finalDocument, moduleDocument, moduleName);
                                    _diagnostics.ProcessedModules.Add(moduleName);
                                    _diagnostics.LogInfo($"成功合并模块: {moduleName}");
                                }
                                catch (Exception appendEx)
                                {
                                    var errorMsg = $"合并模块 {moduleName} 时出错: {appendEx.Message}";
                                    _diagnostics.LogError(errorMsg);
                                    _diagnostics.FailedModules.Add(moduleName);
                                    progressCallback?.Invoke(
                                        30 + (int)((float)processedModules / totalModules * 60),
                                        $"[WARNING]{errorMsg}");
                                }
                                finally
                                {
                                    try
                                    {
                                        moduleDocument.Close();
                                    }
                                    catch { /* 忽略关闭文档失败 */ }
                                }
                            }
                        }
                    }
                    catch (Exception moduleEx)
                    {
                        var errorMsg = $"处理模块 {moduleName} 时出错: {moduleEx.Message}";
                        _diagnostics.LogError(errorMsg);
                        _diagnostics.FailedModules.Add(moduleName);
                        progressCallback?.Invoke(
                            30 + (int)((float)processedModules / totalModules * 60),
                            $"[WARNING]{errorMsg}");
                    }

                    processedModules++;
                }

                // 输出合并统计信息
                OutputMergeStatistics(progressCallback);

                // 保存最终文档
                if (finalDocument != null)
                {
                    progressCallback?.Invoke(90, "[INFO]保存最终报告...");
                    _diagnostics.LogInfo("开始保存最终报告");

                    try
                    {
                        // 确保输出目录存在
                        string outputDir = Path.GetDirectoryName(finalReportPath);
                        if (!string.IsNullOrEmpty(outputDir) && !Directory.Exists(outputDir))
                        {
                            Directory.CreateDirectory(outputDir);
                        }

                        using (var outputStream = new FileStream(finalReportPath, FileMode.Create, FileAccess.Write))
                        {
                            finalDocument.Write(outputStream);
                        }

                        var successMsg = $"最终报告已保存: {Path.GetFileName(finalReportPath)}";
                        _diagnostics.LogInfo(successMsg);
                        progressCallback?.Invoke(100, $"[SUCCESS]{successMsg}");
                    }
                    catch (Exception saveEx)
                    {
                        var errorMsg = $"保存最终报告时出错: {saveEx.Message}";
                        _diagnostics.LogError(errorMsg);
                        progressCallback?.Invoke(90, $"[ERROR]{errorMsg}");
                        return false;
                    }
                    finally
                    {
                        try
                        {
                            finalDocument.Close();
                        }
                        catch { /* 忽略关闭文档失败 */ }
                    }
                }
                else
                {
                    var errorMsg = "没有成功创建任何文档内容";
                    _diagnostics.LogError(errorMsg);
                    progressCallback?.Invoke(90, $"[ERROR]{errorMsg}");
                    return false;
                }

                return true;
            }
            catch (Exception ex)
            {
                var errorMsg = $"合并文档时发生错误: {ex.Message}";
                _diagnostics.LogError(errorMsg);
                progressCallback?.Invoke(90, $"[ERROR]{errorMsg}");

                // 确保清理资源
                if (finalDocument != null)
                {
                    try
                    {
                        finalDocument.Close();
                    }
                    catch { /* 忽略关闭文档失败 */ }
                }

                return false;
            }
        }

        /// <summary>
        /// 验证模块顺序是否符合标准
        /// </summary>
        private void ValidateModuleOrder(List<KeyValuePair<string, string>> orderedModules, Action<int, string> progressCallback)
        {
            _diagnostics.LogInfo("验证模块顺序");

            var expectedOrder = new List<string>(STANDARD_MODULE_ORDER);
            var actualOrder = orderedModules.Select(m => m.Key).ToList();

            for (int i = 0; i < Math.Min(expectedOrder.Count, actualOrder.Count); i++)
            {
                if (expectedOrder[i] != actualOrder[i])
                {
                    var warningMsg = $"模块顺序不匹配: 期望 '{expectedOrder[i]}', 实际 '{actualOrder[i]}' (位置 {i + 1})";
                    _diagnostics.LogWarning(warningMsg);
                    progressCallback?.Invoke(5, $"[WARNING]{warningMsg}");
                }
            }

            // 检查缺失的模块
            var missingModules = expectedOrder.Except(actualOrder).ToList();
            if (missingModules.Any())
            {
                var warningMsg = $"缺失的标准模块: {string.Join(", ", missingModules)}";
                _diagnostics.LogWarning(warningMsg);
                progressCallback?.Invoke(5, $"[WARNING]{warningMsg}");
            }

            // 检查额外的模块
            var extraModules = actualOrder.Except(expectedOrder).ToList();
            if (extraModules.Any())
            {
                var infoMsg = $"额外的模块: {string.Join(", ", extraModules)}";
                _diagnostics.LogInfo(infoMsg);
                progressCallback?.Invoke(5, $"[INFO]{infoMsg}");
            }
        }

        /// <summary>
        /// 分析模块内容
        /// </summary>
        private void AnalyzeModuleContent(XWPFDocument document, string moduleName)
        {
            try
            {
                int paragraphCount = document.Paragraphs?.Count ?? 0;
                int tableCount = document.Tables?.Count ?? 0;

                _diagnostics.LogInfo($"模块 {moduleName} 内容分析: {paragraphCount} 个段落, {tableCount} 个表格");
                _diagnostics.TotalParagraphs += paragraphCount;
                _diagnostics.TotalTables += tableCount;

                // 检查是否有空内容
                if (paragraphCount == 0 && tableCount == 0)
                {
                    var warningMsg = $"模块 {moduleName} 似乎没有内容";
                    _diagnostics.LogWarning(warningMsg);
                    _diagnostics.FormatIssues.Add($"{moduleName}: 空内容");
                }
            }
            catch (Exception ex)
            {
                _diagnostics.LogError($"分析模块 {moduleName} 内容时出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 带诊断信息的文档追加
        /// </summary>
        private void AppendDocumentWithDiagnostics(XWPFDocument targetDocument, XWPFDocument sourceDocument, string moduleName)
        {
            try
            {
                if (targetDocument == null)
                    throw new ArgumentNullException(nameof(targetDocument), "目标文档不能为空");

                if (sourceDocument == null)
                    throw new ArgumentNullException(nameof(sourceDocument), "源文档不能为空");

                _diagnostics.LogInfo($"开始追加模块 {moduleName} 的内容");

                // 添加分页符
                try
                {
                    AddPageBreak(targetDocument);
                    _diagnostics.LogInfo($"为模块 {moduleName} 添加分页符");
                }
                catch (Exception ex)
                {
                    var warningMsg = $"为模块 {moduleName} 添加分页符失败: {ex.Message}";
                    _diagnostics.LogWarning(warningMsg);
                    _diagnostics.FormatIssues.Add($"{moduleName}: 分页符添加失败");
                }

                // 复制段落
                if (sourceDocument.Paragraphs != null)
                {
                    int paragraphIndex = 0;
                    foreach (var paragraph in sourceDocument.Paragraphs)
                    {
                        try
                        {
                            if (paragraph != null)
                            {
                                CopyParagraphWithDiagnostics(targetDocument, paragraph, moduleName, paragraphIndex);
                            }
                        }
                        catch (Exception ex)
                        {
                            _diagnostics.FailedParagraphs++;
                            var errorMsg = $"复制模块 {moduleName} 段落 {paragraphIndex} 失败: {ex.Message}";
                            _diagnostics.LogError(errorMsg);
                            _diagnostics.FormatIssues.Add($"{moduleName}: 段落 {paragraphIndex} 复制失败");
                        }
                        paragraphIndex++;
                    }
                }

                // 复制表格
                if (sourceDocument.Tables != null)
                {
                    int tableIndex = 0;
                    foreach (var table in sourceDocument.Tables)
                    {
                        try
                        {
                            if (table != null)
                            {
                                CopyTableWithDiagnostics(targetDocument, table, moduleName, tableIndex);
                            }
                        }
                        catch (Exception ex)
                        {
                            _diagnostics.FailedTables++;
                            var errorMsg = $"复制模块 {moduleName} 表格 {tableIndex} 失败: {ex.Message}";
                            _diagnostics.LogError(errorMsg);
                            _diagnostics.FormatIssues.Add($"{moduleName}: 表格 {tableIndex} 复制失败");
                        }
                        tableIndex++;
                    }
                }

                _diagnostics.LogInfo($"完成追加模块 {moduleName} 的内容");
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"追加模块 {moduleName} 时发生错误: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 输出合并统计信息
        /// </summary>
        private void OutputMergeStatistics(Action<int, string> progressCallback)
        {
            var stats = new StringBuilder();
            stats.AppendLine("=== 文档合并统计信息 ===");
            stats.AppendLine($"成功处理的模块: {_diagnostics.ProcessedModules.Count}");
            stats.AppendLine($"跳过的模块: {_diagnostics.SkippedModules.Count}");
            stats.AppendLine($"失败的模块: {_diagnostics.FailedModules.Count}");
            stats.AppendLine($"总段落数: {_diagnostics.TotalParagraphs}");
            stats.AppendLine($"总表格数: {_diagnostics.TotalTables}");
            stats.AppendLine($"失败段落数: {_diagnostics.FailedParagraphs}");
            stats.AppendLine($"失败表格数: {_diagnostics.FailedTables}");
            stats.AppendLine($"格式问题数: {_diagnostics.FormatIssues.Count}");

            if (_diagnostics.ProcessedModules.Any())
            {
                stats.AppendLine($"成功模块: {string.Join(", ", _diagnostics.ProcessedModules)}");
            }

            if (_diagnostics.FailedModules.Any())
            {
                stats.AppendLine($"失败模块: {string.Join(", ", _diagnostics.FailedModules)}");
            }

            if (_diagnostics.SkippedModules.Any())
            {
                stats.AppendLine($"跳过模块: {string.Join(", ", _diagnostics.SkippedModules)}");
            }

            _diagnostics.LogInfo(stats.ToString());
            progressCallback?.Invoke(85, $"[INFO]合并统计: 成功 {_diagnostics.ProcessedModules.Count}, 失败 {_diagnostics.FailedModules.Count}, 跳过 {_diagnostics.SkippedModules.Count}");
        }

        /// <summary>
        /// 将源文档的内容追加到目标文档
        /// </summary>
        /// <param name="targetDocument">目标文档</param>
        /// <param name="sourceDocument">源文档</param>
        private void AppendDocument(XWPFDocument targetDocument, XWPFDocument sourceDocument)
        {
            try
            {
                if (targetDocument == null)
                    throw new ArgumentNullException(nameof(targetDocument), "目标文档不能为空");

                if (sourceDocument == null)
                    throw new ArgumentNullException(nameof(sourceDocument), "源文档不能为空");

                // 添加分页符
                try
                {
                    AddPageBreak(targetDocument);
                }
                catch (Exception ex)
                {
                    // 分页符添加失败不应该阻止整个合并过程
                    System.Diagnostics.Debug.WriteLine($"添加分页符失败: {ex.Message}");
                }

                // 复制段落
                if (sourceDocument.Paragraphs != null)
                {
                    foreach (var paragraph in sourceDocument.Paragraphs)
                    {
                        try
                        {
                            if (paragraph != null)
                            {
                                CopyParagraph(targetDocument, paragraph);
                            }
                        }
                        catch (Exception ex)
                        {
                            // 单个段落复制失败不应该阻止整个合并过程
                            System.Diagnostics.Debug.WriteLine($"复制段落失败: {ex.Message}");
                        }
                    }
                }

                // 复制表格
                if (sourceDocument.Tables != null)
                {
                    foreach (var table in sourceDocument.Tables)
                    {
                        try
                        {
                            if (table != null)
                            {
                                CopyTable(targetDocument, table);
                            }
                        }
                        catch (Exception ex)
                        {
                            // 单个表格复制失败不应该阻止整个合并过程
                            System.Diagnostics.Debug.WriteLine($"复制表格失败: {ex.Message}");
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"追加文档时发生错误: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 添加分页符
        /// </summary>
        /// <param name="document">文档</param>
        private void AddPageBreak(XWPFDocument document)
        {
            try
            {
                if (document != null)
                {
                    var paragraph = document.CreateParagraph();
                    if (paragraph != null)
                    {
                        var run = paragraph.CreateRun();
                        if (run != null)
                        {
                            run.AddBreak(BreakType.PAGE);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                // 分页符添加失败时，记录错误但不抛出异常
                System.Diagnostics.Debug.WriteLine($"添加分页符失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 带诊断信息的段落复制
        /// </summary>
        private void CopyParagraphWithDiagnostics(XWPFDocument targetDocument, XWPFParagraph sourceParagraph, string moduleName, int paragraphIndex)
        {
            try
            {
                var newParagraph = targetDocument.CreateParagraph();

                // 复制段落级别的格式
                CopyParagraphFormatting(newParagraph, sourceParagraph, moduleName, paragraphIndex);

                // 复制运行
                if (sourceParagraph.Runs != null)
                {
                    int runIndex = 0;
                    foreach (var sourceRun in sourceParagraph.Runs)
                    {
                        if (sourceRun != null)
                        {
                            try
                            {
                                CopyRunWithFormatting(newParagraph, sourceRun, moduleName, paragraphIndex, runIndex);
                            }
                            catch (Exception ex)
                            {
                                _diagnostics.LogWarning($"复制模块 {moduleName} 段落 {paragraphIndex} 运行 {runIndex} 失败: {ex.Message}");
                                _diagnostics.FormatIssues.Add($"{moduleName}: 段落 {paragraphIndex} 运行 {runIndex} 格式丢失");
                            }
                        }
                        runIndex++;
                    }
                }
            }
            catch (Exception ex)
            {
                // 如果段落复制完全失败，创建一个简单的空段落
                try
                {
                    targetDocument.CreateParagraph();
                    _diagnostics.LogWarning($"模块 {moduleName} 段落 {paragraphIndex} 复制失败，创建空段落: {ex.Message}");
                }
                catch
                {
                    throw new InvalidOperationException($"复制模块 {moduleName} 段落 {paragraphIndex} 时发生错误: {ex.Message}", ex);
                }
            }
        }

        /// <summary>
        /// 复制段落格式
        /// </summary>
        private void CopyParagraphFormatting(XWPFParagraph targetParagraph, XWPFParagraph sourceParagraph, string moduleName, int paragraphIndex)
        {
            try
            {
                // 基本对齐
                try { targetParagraph.Alignment = sourceParagraph.Alignment; }
                catch (Exception ex) { _diagnostics.LogWarning($"复制段落对齐失败 {moduleName}[{paragraphIndex}]: {ex.Message}"); }

                // 间距
                try { targetParagraph.SpacingAfter = sourceParagraph.SpacingAfter; }
                catch (Exception ex) { _diagnostics.LogWarning($"复制段后间距失败 {moduleName}[{paragraphIndex}]: {ex.Message}"); }

                try { targetParagraph.SpacingBefore = sourceParagraph.SpacingBefore; }
                catch (Exception ex) { _diagnostics.LogWarning($"复制段前间距失败 {moduleName}[{paragraphIndex}]: {ex.Message}"); }

                // 行间距
                try { targetParagraph.SpacingBetween = sourceParagraph.SpacingBetween; }
                catch (Exception ex) { _diagnostics.LogWarning($"复制行间距失败 {moduleName}[{paragraphIndex}]: {ex.Message}"); }

                // 缩进
                try { targetParagraph.IndentationLeft = sourceParagraph.IndentationLeft; }
                catch (Exception ex) { _diagnostics.LogWarning($"复制左缩进失败 {moduleName}[{paragraphIndex}]: {ex.Message}"); }

                try { targetParagraph.IndentationRight = sourceParagraph.IndentationRight; }
                catch (Exception ex) { _diagnostics.LogWarning($"复制右缩进失败 {moduleName}[{paragraphIndex}]: {ex.Message}"); }

                try { targetParagraph.IndentationFirstLine = sourceParagraph.IndentationFirstLine; }
                catch (Exception ex) { _diagnostics.LogWarning($"复制首行缩进失败 {moduleName}[{paragraphIndex}]: {ex.Message}"); }

                // 边框
                try { targetParagraph.BorderTop = sourceParagraph.BorderTop; }
                catch (Exception ex) { _diagnostics.LogWarning($"复制上边框失败 {moduleName}[{paragraphIndex}]: {ex.Message}"); }

                try { targetParagraph.BorderBottom = sourceParagraph.BorderBottom; }
                catch (Exception ex) { _diagnostics.LogWarning($"复制下边框失败 {moduleName}[{paragraphIndex}]: {ex.Message}"); }

                try { targetParagraph.BorderLeft = sourceParagraph.BorderLeft; }
                catch (Exception ex) { _diagnostics.LogWarning($"复制左边框失败 {moduleName}[{paragraphIndex}]: {ex.Message}"); }

                try { targetParagraph.BorderRight = sourceParagraph.BorderRight; }
                catch (Exception ex) { _diagnostics.LogWarning($"复制右边框失败 {moduleName}[{paragraphIndex}]: {ex.Message}"); }
            }
            catch (Exception ex)
            {
                _diagnostics.LogError($"复制段落格式时发生错误 {moduleName}[{paragraphIndex}]: {ex.Message}");
            }
        }

        /// <summary>
        /// 复制运行及其格式
        /// </summary>
        private void CopyRunWithFormatting(XWPFParagraph targetParagraph, XWPFRun sourceRun, string moduleName, int paragraphIndex, int runIndex)
        {
            var newRun = targetParagraph.CreateRun();

            // 复制文本内容
            if (!string.IsNullOrEmpty(sourceRun.Text))
            {
                newRun.SetText(sourceRun.Text);
            }

            // 复制字体格式
            try { if (sourceRun.IsBold) newRun.IsBold = true; }
            catch (Exception ex) { _diagnostics.LogWarning($"复制粗体格式失败 {moduleName}[{paragraphIndex}][{runIndex}]: {ex.Message}"); }

            try { if (sourceRun.IsItalic) newRun.IsItalic = true; }
            catch (Exception ex) { _diagnostics.LogWarning($"复制斜体格式失败 {moduleName}[{paragraphIndex}][{runIndex}]: {ex.Message}"); }

            try { if (sourceRun.FontSize != -1 && sourceRun.FontSize > 0) newRun.FontSize = sourceRun.FontSize; }
            catch (Exception ex) { _diagnostics.LogWarning($"复制字体大小失败 {moduleName}[{paragraphIndex}][{runIndex}]: {ex.Message}"); }

            try { if (!string.IsNullOrEmpty(sourceRun.FontFamily)) newRun.FontFamily = sourceRun.FontFamily; }
            catch (Exception ex) { _diagnostics.LogWarning($"复制字体族失败 {moduleName}[{paragraphIndex}][{runIndex}]: {ex.Message}"); }

            // 复制其他格式
            try { if (sourceRun.IsStrikeThrough) newRun.IsStrikeThrough = true; }
            catch (Exception ex) { _diagnostics.LogWarning($"复制删除线失败 {moduleName}[{paragraphIndex}][{runIndex}]: {ex.Message}"); }

            try { if (sourceRun.Subscript != VerticalAlign.BASELINE) newRun.Subscript = sourceRun.Subscript; }
            catch (Exception ex) { _diagnostics.LogWarning($"复制上下标失败 {moduleName}[{paragraphIndex}][{runIndex}]: {ex.Message}"); }

            // 注意: UnderlineColor 和 UnderlinePatterns 在当前NPOI版本中可能不可用
            // 这些属性的复制将在未来版本中支持
        }

        /// <summary>
        /// 复制段落 (保持向后兼容)
        /// </summary>
        /// <param name="targetDocument">目标文档</param>
        /// <param name="sourceParagraph">源段落</param>
        private void CopyParagraph(XWPFDocument targetDocument, XWPFParagraph sourceParagraph)
        {
            try
            {
                var newParagraph = targetDocument.CreateParagraph();

                // 安全复制段落属性
                try
                {
                    newParagraph.Alignment = sourceParagraph.Alignment;
                }
                catch { /* 忽略对齐属性复制失败 */ }

                try
                {
                    newParagraph.SpacingAfter = sourceParagraph.SpacingAfter;
                }
                catch { /* 忽略段后间距复制失败 */ }

                try
                {
                    newParagraph.SpacingBefore = sourceParagraph.SpacingBefore;
                }
                catch { /* 忽略段前间距复制失败 */ }

                // 复制运行
                if (sourceParagraph.Runs != null)
                {
                    foreach (var sourceRun in sourceParagraph.Runs)
                    {
                        if (sourceRun != null)
                        {
                            var newRun = newParagraph.CreateRun();

                            // 安全设置文本
                            if (!string.IsNullOrEmpty(sourceRun.Text))
                            {
                                newRun.SetText(sourceRun.Text);
                            }

                            // 安全复制运行格式
                            try
                            {
                                if (sourceRun.IsBold)
                                    newRun.IsBold = true;
                            }
                            catch { /* 忽略粗体格式复制失败 */ }

                            try
                            {
                                if (sourceRun.IsItalic)
                                    newRun.IsItalic = true;
                            }
                            catch { /* 忽略斜体格式复制失败 */ }

                            try
                            {
                                if (sourceRun.FontSize != -1 && sourceRun.FontSize > 0)
                                    newRun.FontSize = sourceRun.FontSize;
                            }
                            catch { /* 忽略字体大小复制失败 */ }

                            try
                            {
                                if (!string.IsNullOrEmpty(sourceRun.FontFamily))
                                    newRun.FontFamily = sourceRun.FontFamily;
                            }
                            catch { /* 忽略字体族复制失败 */ }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                // 如果段落复制完全失败，创建一个简单的空段落
                try
                {
                    targetDocument.CreateParagraph();
                }
                catch
                {
                    // 如果连空段落都无法创建，重新抛出异常
                    throw new InvalidOperationException($"复制段落时发生错误: {ex.Message}", ex);
                }
            }
        }

        /// <summary>
        /// 带诊断信息的表格复制
        /// </summary>
        private void CopyTableWithDiagnostics(XWPFDocument targetDocument, XWPFTable sourceTable, string moduleName, int tableIndex)
        {
            try
            {
                if (sourceTable == null || sourceTable.Rows == null || sourceTable.Rows.Count == 0)
                {
                    _diagnostics.LogWarning($"模块 {moduleName} 表格 {tableIndex} 为空，创建空表格");
                    targetDocument.CreateTable();
                    return;
                }

                var newTable = targetDocument.CreateTable();
                _diagnostics.LogInfo($"开始复制模块 {moduleName} 表格 {tableIndex}，包含 {sourceTable.Rows.Count} 行");

                // 安全清除默认行
                try
                {
                    if (newTable.NumberOfRows > 0)
                    {
                        newTable.RemoveRow(0);
                    }
                }
                catch (Exception ex)
                {
                    _diagnostics.LogWarning($"清除表格默认行失败 {moduleName}[{tableIndex}]: {ex.Message}");
                }

                // 复制表格样式
                CopyTableStyle(newTable, sourceTable, moduleName, tableIndex);

                // 复制表格行
                int rowIndex = 0;
                foreach (var sourceRow in sourceTable.Rows)
                {
                    if (sourceRow == null)
                    {
                        rowIndex++;
                        continue;
                    }

                    try
                    {
                        CopyTableRow(newTable, sourceRow, moduleName, tableIndex, rowIndex);
                    }
                    catch (Exception ex)
                    {
                        _diagnostics.LogError($"复制表格行失败 {moduleName}[{tableIndex}][{rowIndex}]: {ex.Message}");
                        _diagnostics.FormatIssues.Add($"{moduleName}: 表格 {tableIndex} 行 {rowIndex} 复制失败");
                    }
                    rowIndex++;
                }

                _diagnostics.LogInfo($"完成复制模块 {moduleName} 表格 {tableIndex}");
            }
            catch (Exception ex)
            {
                // 如果表格复制完全失败，创建一个简单的空表格
                try
                {
                    targetDocument.CreateTable();
                    _diagnostics.LogError($"模块 {moduleName} 表格 {tableIndex} 复制失败，创建空表格: {ex.Message}");
                }
                catch
                {
                    throw new InvalidOperationException($"复制模块 {moduleName} 表格 {tableIndex} 时发生错误: {ex.Message}", ex);
                }
            }
        }

        /// <summary>
        /// 复制表格样式
        /// </summary>
        private void CopyTableStyle(XWPFTable targetTable, XWPFTable sourceTable, string moduleName, int tableIndex)
        {
            try
            {
                // 复制表格宽度
                try { targetTable.Width = sourceTable.Width; }
                catch (Exception ex) { _diagnostics.LogWarning($"复制表格宽度失败 {moduleName}[{tableIndex}]: {ex.Message}"); }

                // 注意: 某些表格样式属性在当前NPOI版本中可能不可用或只读
                // 包括: TableAlignment, CellMargin等属性
                // 这些属性的复制将在未来版本中支持

                _diagnostics.LogInfo($"表格 {tableIndex} 基本样式已复制 (宽度: {sourceTable.Width})");
            }
            catch (Exception ex)
            {
                _diagnostics.LogError($"复制表格样式时发生错误 {moduleName}[{tableIndex}]: {ex.Message}");
            }
        }

        /// <summary>
        /// 复制表格行
        /// </summary>
        private void CopyTableRow(XWPFTable targetTable, XWPFTableRow sourceRow, string moduleName, int tableIndex, int rowIndex)
        {
            var newRow = targetTable.CreateRow();
            var sourceCells = sourceRow.GetTableCells();

            if (sourceCells != null && sourceCells.Count > 0)
            {
                // 确保新行有足够的单元格
                while (newRow.GetTableCells().Count < sourceCells.Count)
                {
                    try
                    {
                        newRow.AddNewTableCell();
                    }
                    catch (Exception ex)
                    {
                        _diagnostics.LogWarning($"添加表格单元格失败 {moduleName}[{tableIndex}][{rowIndex}]: {ex.Message}");
                        break;
                    }
                }

                var newCells = newRow.GetTableCells();

                // 复制单元格内容
                for (int cellIndex = 0; cellIndex < Math.Min(sourceCells.Count, newCells.Count); cellIndex++)
                {
                    try
                    {
                        CopyTableCell(sourceCells[cellIndex], newCells[cellIndex], moduleName, tableIndex, rowIndex, cellIndex);
                    }
                    catch (Exception ex)
                    {
                        _diagnostics.LogError($"复制表格单元格失败 {moduleName}[{tableIndex}][{rowIndex}][{cellIndex}]: {ex.Message}");
                        _diagnostics.FormatIssues.Add($"{moduleName}: 表格 {tableIndex} 行 {rowIndex} 单元格 {cellIndex} 复制失败");
                    }
                }
            }
        }

        /// <summary>
        /// 复制表格单元格
        /// </summary>
        private void CopyTableCell(XWPFTableCell sourceCell, XWPFTableCell newCell, string moduleName, int tableIndex, int rowIndex, int cellIndex)
        {
            if (sourceCell == null || newCell == null) return;

            // 复制单元格样式
            try
            {
                newCell.SetColor(sourceCell.GetColor());

                var vertAlign = sourceCell.GetVerticalAlignment();
                if (vertAlign.HasValue)
                {
                    newCell.SetVerticalAlignment(vertAlign.Value);
                }
            }
            catch (Exception ex)
            {
                _diagnostics.LogWarning($"复制单元格样式失败 {moduleName}[{tableIndex}][{rowIndex}][{cellIndex}]: {ex.Message}");
            }

            // 安全清除新单元格的默认段落
            try
            {
                if (newCell.Paragraphs != null && newCell.Paragraphs.Count > 0)
                {
                    newCell.RemoveParagraph(0);
                }
            }
            catch (Exception ex)
            {
                _diagnostics.LogWarning($"清除单元格默认段落失败 {moduleName}[{tableIndex}][{rowIndex}][{cellIndex}]: {ex.Message}");
            }

            // 复制单元格段落
            if (sourceCell.Paragraphs != null)
            {
                int paragraphIndex = 0;
                foreach (var sourceParagraph in sourceCell.Paragraphs)
                {
                    if (sourceParagraph != null)
                    {
                        try
                        {
                            var newParagraph = newCell.AddParagraph();
                            CopyParagraphFormatting(newParagraph, sourceParagraph, moduleName, paragraphIndex);

                            // 复制段落内容
                            if (sourceParagraph.Runs != null)
                            {
                                int runIndex = 0;
                                foreach (var sourceRun in sourceParagraph.Runs)
                                {
                                    if (sourceRun != null)
                                    {
                                        try
                                        {
                                            CopyRunWithFormatting(newParagraph, sourceRun, moduleName, paragraphIndex, runIndex);
                                        }
                                        catch (Exception ex)
                                        {
                                            _diagnostics.LogWarning($"复制单元格运行失败 {moduleName}[{tableIndex}][{rowIndex}][{cellIndex}][{paragraphIndex}][{runIndex}]: {ex.Message}");
                                        }
                                    }
                                    runIndex++;
                                }
                            }
                        }
                        catch (Exception ex)
                        {
                            _diagnostics.LogWarning($"复制单元格段落失败 {moduleName}[{tableIndex}][{rowIndex}][{cellIndex}][{paragraphIndex}]: {ex.Message}");
                        }
                    }
                    paragraphIndex++;
                }
            }
        }

        /// <summary>
        /// 复制表格 (保持向后兼容)
        /// </summary>
        /// <param name="targetDocument">目标文档</param>
        /// <param name="sourceTable">源表格</param>
        private void CopyTable(XWPFDocument targetDocument, XWPFTable sourceTable)
        {
            try
            {
                if (sourceTable == null || sourceTable.Rows == null || sourceTable.Rows.Count == 0)
                {
                    // 如果源表格为空，创建一个简单的空表格
                    targetDocument.CreateTable();
                    return;
                }

                var newTable = targetDocument.CreateTable();

                // 安全清除默认行
                try
                {
                    if (newTable.NumberOfRows > 0)
                    {
                        newTable.RemoveRow(0);
                    }
                }
                catch { /* 忽略默认行清除失败 */ }

                // 复制表格行
                foreach (var sourceRow in sourceTable.Rows)
                {
                    if (sourceRow == null) continue;

                    try
                    {
                        var newRow = newTable.CreateRow();
                        var sourceCells = sourceRow.GetTableCells();

                        if (sourceCells != null && sourceCells.Count > 0)
                        {
                            // 确保新行有足够的单元格
                            while (newRow.GetTableCells().Count < sourceCells.Count)
                            {
                                try
                                {
                                    newRow.AddNewTableCell();
                                }
                                catch
                                {
                                    break; // 如果无法添加更多单元格，停止尝试
                                }
                            }

                            var newCells = newRow.GetTableCells();

                            // 复制单元格内容
                            for (int i = 0; i < Math.Min(sourceCells.Count, newCells.Count); i++)
                            {
                                try
                                {
                                    var sourceCell = sourceCells[i];
                                    var newCell = newCells[i];

                                    if (sourceCell != null && newCell != null)
                                    {
                                        // 安全清除新单元格的默认段落
                                        try
                                        {
                                            if (newCell.Paragraphs != null && newCell.Paragraphs.Count > 0)
                                            {
                                                newCell.RemoveParagraph(0);
                                            }
                                        }
                                        catch { /* 忽略默认段落清除失败 */ }

                                        // 复制单元格段落
                                        if (sourceCell.Paragraphs != null)
                                        {
                                            foreach (var sourceParagraph in sourceCell.Paragraphs)
                                            {
                                                if (sourceParagraph != null)
                                                {
                                                    try
                                                    {
                                                        var newParagraph = newCell.AddParagraph();

                                                        // 复制段落内容
                                                        if (sourceParagraph.Runs != null)
                                                        {
                                                            foreach (var sourceRun in sourceParagraph.Runs)
                                                            {
                                                                if (sourceRun != null)
                                                                {
                                                                    try
                                                                    {
                                                                        var newRun = newParagraph.CreateRun();

                                                                        if (!string.IsNullOrEmpty(sourceRun.Text))
                                                                        {
                                                                            newRun.SetText(sourceRun.Text);
                                                                        }

                                                                        // 安全复制运行格式
                                                                        try { if (sourceRun.IsBold) newRun.IsBold = true; } catch { }
                                                                        try { if (sourceRun.IsItalic) newRun.IsItalic = true; } catch { }
                                                                        try { if (sourceRun.FontSize != -1 && sourceRun.FontSize > 0) newRun.FontSize = sourceRun.FontSize; } catch { }
                                                                        try { if (!string.IsNullOrEmpty(sourceRun.FontFamily)) newRun.FontFamily = sourceRun.FontFamily; } catch { }
                                                                    }
                                                                    catch { /* 忽略单个运行复制失败 */ }
                                                                }
                                                            }
                                                        }
                                                    }
                                                    catch { /* 忽略段落复制失败 */ }
                                                }
                                            }
                                        }
                                    }
                                }
                                catch { /* 忽略单元格复制失败 */ }
                            }
                        }
                    }
                    catch { /* 忽略行复制失败 */ }
                }
            }
            catch (Exception ex)
            {
                // 如果表格复制完全失败，创建一个简单的空表格
                try
                {
                    targetDocument.CreateTable();
                }
                catch
                {
                    // 如果连空表格都无法创建，重新抛出异常
                    throw new InvalidOperationException($"复制表格时发生错误: {ex.Message}", ex);
                }
            }
        }
    }
}
