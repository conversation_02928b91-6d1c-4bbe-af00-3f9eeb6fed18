using Microsoft.AspNetCore.Mvc;
using RESServer.Services;
using System.Net.Mime;
using System.Text.Json;

namespace RESServer.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class GeminiAnalysisController : ControllerBase
    {
        private readonly ILogger<GeminiAnalysisController> _logger;
        private readonly GeminiService _geminiService;
        private readonly FileConversionService _fileConversionService;
        // Gemini API支持的MIME类型列表（不完整，仅包含常见类型）
        private readonly HashSet<string> _supportedMimeTypes = new(StringComparer.OrdinalIgnoreCase)
        {
            // 图像类型
            "image/jpeg", "image/jpg", "image/png", "image/gif", "image/webp", "image/heic",
            
            // 文档类型
            "application/pdf", "text/plain", "text/csv", 
            "application/vnd.openxmlformats-officedocument.wordprocessingml.document", // .docx
            "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", // .xlsx
            "application/vnd.openxmlformats-officedocument.presentationml.presentation", // .pptx
            
            // 代码和文本类型
            "application/json", "text/html", "text/markdown", "text/md",
            "application/x-javascript", "text/javascript", // JavaScript
            "application/x-python", "text/x-python", // Python
            "text/css", // CSS
            "text/xml", // XML
            "text/rtf", // RTF
            
            // 其他通用类型
            "application/octet-stream", // 通用二进制文件
            "text/plain" // 通用文本文件
        };

        public GeminiAnalysisController(
            ILogger<GeminiAnalysisController> logger,
            GeminiService geminiService,
            FileConversionService fileConversionService)
        {
            _logger = logger;
            _geminiService = geminiService;
            _fileConversionService = fileConversionService;
        }

        [HttpPost("analyze")]
        [RequestSizeLimit(100_000_000)] // ~100MB limit
        [RequestFormLimits(MultipartBodyLengthLimit = 100_000_000)]
        [Produces(MediaTypeNames.Application.Json)]
        public async Task<IActionResult> AnalyzeFiles(
            [FromForm] AnalysisRequest request)
        {
            try
            {
                if (request.Files == null || !request.Files.Any())
                {
                    _logger.LogWarning("未上传任何文件");
                    return BadRequest("未上传任何文件");
                }

                _logger.LogInformation($"接收到{request.Files.Count}个文件进行分析");
                var fileContents = new List<FileContentData>();
                var unsupportedFiles = new List<string>();
                var processedFileInfo = new List<Dictionary<string, object>>();
                long totalSize = 0;

                foreach (var file in request.Files)
                {
                    if (file.Length == 0) continue;

                    string originalMimeType = file.ContentType ?? "application/octet-stream";
                    string originalFileName = file.FileName;

                    using var memoryStream = new MemoryStream();
                    await file.CopyToAsync(memoryStream);
                    byte[] fileData = memoryStream.ToArray();

                    string finalMimeType = originalMimeType;
                    string finalFileName = originalFileName;
                    byte[] finalData = fileData;
                    bool wasConverted = false;

                    // 检查是否需要转换.xls文件
                    if (_fileConversionService.NeedsConversion(originalFileName, originalMimeType))
                    {
                        try
                        {
                            _logger.LogInformation($"检测到.xls文件，开始转换: {originalFileName}");
                            var conversionResult = await _fileConversionService.ConvertXlsToXlsxAsync(fileData, originalFileName);

                            finalData = conversionResult.Data;
                            finalMimeType = conversionResult.MimeType;
                            finalFileName = conversionResult.FileName;
                            wasConverted = true;

                            _logger.LogInformation($"文件转换成功: {originalFileName} -> {finalFileName}");
                        }
                        catch (Exception ex)
                        {
                            _logger.LogError(ex, $"文件转换失败: {originalFileName}");
                            // 转换失败时，继续使用原始文件，但会在后续处理中被标记为不支持
                        }
                    }

                    bool isSupported = _supportedMimeTypes.Contains(finalMimeType);

                    if (!isSupported)
                    {
                        _logger.LogWarning($"文件类型可能不受支持: {finalFileName}，类型: {finalMimeType}");
                        unsupportedFiles.Add($"{finalFileName} ({finalMimeType})");
                        // 继续处理，因为Gemini可能支持更多类型
                    }

                    totalSize += finalData.Length;
                    _logger.LogInformation($"处理文件: {finalFileName}，大小: {finalData.Length} 字节，类型: {finalMimeType}");

                    fileContents.Add(new FileContentData
                    {
                        FileName = finalFileName,
                        MimeType = finalMimeType,
                        Data = finalData
                    });

                    // 记录处理的文件信息，用于响应
                    var fileInfo = new Dictionary<string, object>
                    {
                        ["fileName"] = finalFileName,
                        ["mimeType"] = finalMimeType,
                        ["size"] = finalData.Length,
                        ["isSupported"] = isSupported
                    };

                    if (wasConverted)
                    {
                        fileInfo["originalFileName"] = originalFileName;
                        fileInfo["originalMimeType"] = originalMimeType;
                        fileInfo["converted"] = true;
                        fileInfo["conversionNote"] = $"已自动转换 {originalFileName} 为 {finalFileName} 以支持Gemini API";
                    }

                    processedFileInfo.Add(fileInfo);
                }

                if (!fileContents.Any())
                {
                    _logger.LogWarning("所有上传的文件均为空");
                    return BadRequest("所有上传的文件均为空");
                }

                // 检查文件总大小
                if (totalSize > 19_000_000) // 19MB作为安全阈值
                {
                    _logger.LogWarning($"文件总大小超过Gemini API限制: {totalSize / 1024 / 1024}MB > 20MB");
                    return BadRequest($"文件总大小超过API限制（20MB），当前大小: {totalSize / 1024 / 1024}MB");
                }

                string prompt = request.Prompt ?? "分析这些文件并提供详细信息";
                _logger.LogInformation($"使用提示词: {prompt}");

                // 检查是否是Excel文件的快速查询
                string result = await HandleExcelQuickAnalysis(fileContents, prompt);

                if (string.IsNullOrEmpty(result))
                {
                    // 如果不是快速查询或快速查询失败，使用Gemini API进行完整分析
                    result = await _geminiService.AnalyzeFilesAsync(fileContents, prompt);
                }

                _logger.LogInformation("分析完成，返回结果");
                
                // 构建响应
                var response = new Dictionary<string, object>
                {
                    ["analysis"] = result,
                    ["processedFiles"] = processedFileInfo,
                    ["totalFiles"] = fileContents.Count,
                    ["prompt"] = prompt
                };
                
                if (unsupportedFiles.Count > 0)
                {
                    response["warnings"] = $"以下文件类型可能不受Gemini API完全支持: {string.Join(", ", unsupportedFiles)}";
                }
                
                // 确保响应配置正确处理大型结果
                var options = new JsonSerializerOptions
                {
                    PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
                    WriteIndented = false  // 减少响应大小
                };

                // 返回完整分析结果
                return Ok(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "文件分析过程中发生错误");
                return StatusCode(500, new { message = $"内部服务器错误: {ex.Message}", details = ex.ToString() });
            }
        }

        /// <summary>
        /// 处理Excel文件的快速分析查询
        /// </summary>
        private Task<string> HandleExcelQuickAnalysis(List<FileContentData> fileContents, string prompt)
        {
            try
            {
                // 检查是否是关于Excel工作表的查询
                var lowerPrompt = prompt.ToLower();
                bool isSheetCountQuery = lowerPrompt.Contains("多少个") && (lowerPrompt.Contains("sheet") || lowerPrompt.Contains("工作表") || lowerPrompt.Contains("表格"));
                bool isSheetNameQuery = lowerPrompt.Contains("sheet") && (lowerPrompt.Contains("名称") || lowerPrompt.Contains("名字") || lowerPrompt.Contains("叫什么"));

                if (!isSheetCountQuery && !isSheetNameQuery)
                {
                    return Task.FromResult(string.Empty); // 不是快速查询
                }

                var excelFiles = fileContents.Where(f =>
                    f.MimeType.Contains("spreadsheet") ||
                    f.FileName.EndsWith(".xlsx", StringComparison.OrdinalIgnoreCase) ||
                    f.FileName.EndsWith(".xls", StringComparison.OrdinalIgnoreCase)).ToList();

                if (!excelFiles.Any())
                {
                    return Task.FromResult(string.Empty); // 没有Excel文件
                }

                var results = new List<string>();

                foreach (var excelFile in excelFiles)
                {
                    try
                    {
                        bool isXlsx = excelFile.MimeType.Contains("openxmlformats") || excelFile.FileName.EndsWith(".xlsx", StringComparison.OrdinalIgnoreCase);

                        if (isSheetCountQuery)
                        {
                            int sheetCount = _fileConversionService.GetSheetCount(excelFile.Data, isXlsx);
                            results.Add($"文件 {excelFile.FileName} 包含 {sheetCount} 个工作表");
                        }

                        if (isSheetNameQuery)
                        {
                            var sheetNames = _fileConversionService.GetSheetNames(excelFile.Data, isXlsx);
                            if (sheetNames.Any())
                            {
                                results.Add($"文件 {excelFile.FileName} 的工作表名称：{string.Join(", ", sheetNames)}");
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning(ex, $"快速分析Excel文件失败: {excelFile.FileName}");
                        results.Add($"无法分析文件 {excelFile.FileName}: {ex.Message}");
                    }
                }

                return Task.FromResult(results.Any() ? string.Join("\n", results) : string.Empty);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Excel快速分析过程中发生错误");
                return Task.FromResult(string.Empty); // 快速分析失败，回退到完整分析
            }
        }
    }

    public class AnalysisRequest
    {
        public List<IFormFile> Files { get; set; } = new List<IFormFile>();
        public string? Prompt { get; set; }
    }
} 