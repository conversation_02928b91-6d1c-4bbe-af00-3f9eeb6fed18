﻿using System;
using System.Net;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using System.Threading;
using Autodesk.AutoCAD.Runtime;
using Autodesk.AutoCAD.ApplicationServices;
using Autodesk.AutoCAD.DatabaseServices;
using Autodesk.AutoCAD.Geometry;
using Autodesk.AutoCAD.EditorInput;
using RESCADServerPlugin.Services;

[assembly: ExtensionApplication(typeof(RESCADServerPlugin.MyServerPlugin))]
[assembly: CommandClass(typeof(RESCADServerPlugin.Commands.CADCommandService))]

namespace RESCADServerPlugin
{
    public class MyServerPlugin : IExtensionApplication
    {
        // IExtensionApplication 接口实现
        public void Initialize()
        {
            try
            {
                // 插件初始化代码
                Document doc = Application.DocumentManager.MdiActiveDocument;
                if (doc != null)
                {
                    doc.Editor.WriteMessage("\n测试插件已成功加载！");
                    doc.Editor.WriteMessage($"\n输入 RESTestCommand、RESAPIStatus 或 RESDrawDemo 执行命令");
                }

                // 启动Web服务器
                //CADServiceManager.GetServerService().StartServer();
            }
            catch (System.Exception ex)
            {
                // 记录错误信息
                Document doc = Application.DocumentManager.MdiActiveDocument;
                if (doc != null)
                {
                    doc.Editor.WriteMessage($"\n启动Web服务器时出错: {ex.Message}");
                }
            }
        }

        public void Terminate()
        {
            // 停止Web服务器
            CADServiceManager.GetServerService().StopServer();

            // 其他清理代码
            Document doc = Application.DocumentManager.MdiActiveDocument;
            if (doc != null)
            {
                doc.Editor.WriteMessage("\n测试插件已卸载");
            }
        }
    }
}
