using System;
using System.Collections.Generic;
using System.Linq;

namespace RESClient.Services
{
    /// <summary>
    /// 错误类型枚举
    /// </summary>
    public enum ErrorType
    {
        /// <summary>
        /// 缺失文件错误
        /// </summary>
        MissingFile,
        
        /// <summary>
        /// 数据验证错误
        /// </summary>
        DataValidation,
        
        /// <summary>
        /// 模板处理错误
        /// </summary>
        TemplateProcessing,
        
        /// <summary>
        /// 参数配置错误
        /// </summary>
        ParameterConfiguration,
        
        /// <summary>
        /// 文档生成错误
        /// </summary>
        DocumentGeneration,
        
        /// <summary>
        /// 文档合并错误
        /// </summary>
        DocumentMerging,
        
        /// <summary>
        /// 系统异常
        /// </summary>
        SystemException,
        
        /// <summary>
        /// 其他未分类错误
        /// </summary>
        Other
    }

    /// <summary>
    /// 错误严重程度枚举
    /// </summary>
    public enum ErrorSeverity
    {
        /// <summary>
        /// 信息
        /// </summary>
        Info,
        
        /// <summary>
        /// 警告
        /// </summary>
        Warning,
        
        /// <summary>
        /// 错误
        /// </summary>
        Error,
        
        /// <summary>
        /// 严重错误
        /// </summary>
        Critical
    }

    /// <summary>
    /// 错误信息类
    /// </summary>
    public class ErrorInfo
    {
        /// <summary>
        /// 错误类型
        /// </summary>
        public ErrorType Type { get; set; }
        
        /// <summary>
        /// 错误严重程度
        /// </summary>
        public ErrorSeverity Severity { get; set; }
        
        /// <summary>
        /// 模块名称
        /// </summary>
        public string ModuleName { get; set; }
        
        /// <summary>
        /// 错误消息
        /// </summary>
        public string Message { get; set; }
        
        /// <summary>
        /// 详细描述
        /// </summary>
        public string Details { get; set; }
        
        /// <summary>
        /// 建议解决方案
        /// </summary>
        public string SuggestedSolution { get; set; }
        
        /// <summary>
        /// 发生时间
        /// </summary>
        public DateTime Timestamp { get; set; }
        
        /// <summary>
        /// 异常对象（如果有）
        /// </summary>
        public Exception Exception { get; set; }

        /// <summary>
        /// 构造函数
        /// </summary>
        public ErrorInfo()
        {
            Timestamp = DateTime.Now;
        }
    }

    /// <summary>
    /// 错误收集器服务
    /// </summary>
    public class ErrorCollector
    {
        private readonly List<ErrorInfo> _errors;
        private readonly object _lockObject;

        /// <summary>
        /// 构造函数
        /// </summary>
        public ErrorCollector()
        {
            _errors = new List<ErrorInfo>();
            _lockObject = new object();
        }

        /// <summary>
        /// 添加错误
        /// </summary>
        /// <param name="error">错误信息</param>
        public void AddError(ErrorInfo error)
        {
            if (error == null) return;

            lock (_lockObject)
            {
                _errors.Add(error);
            }
        }

        /// <summary>
        /// 添加错误（简化版本）
        /// </summary>
        /// <param name="type">错误类型</param>
        /// <param name="severity">严重程度</param>
        /// <param name="moduleName">模块名称</param>
        /// <param name="message">错误消息</param>
        /// <param name="details">详细描述</param>
        /// <param name="suggestedSolution">建议解决方案</param>
        /// <param name="exception">异常对象</param>
        public void AddError(ErrorType type, ErrorSeverity severity, string moduleName, 
            string message, string details = null, string suggestedSolution = null, Exception exception = null)
        {
            var error = new ErrorInfo
            {
                Type = type,
                Severity = severity,
                ModuleName = moduleName,
                Message = message,
                Details = details,
                SuggestedSolution = suggestedSolution,
                Exception = exception
            };

            AddError(error);
        }

        /// <summary>
        /// 获取所有错误
        /// </summary>
        /// <returns>错误列表</returns>
        public List<ErrorInfo> GetAllErrors()
        {
            lock (_lockObject)
            {
                return new List<ErrorInfo>(_errors);
            }
        }

        /// <summary>
        /// 按类型获取错误
        /// </summary>
        /// <param name="type">错误类型</param>
        /// <returns>指定类型的错误列表</returns>
        public List<ErrorInfo> GetErrorsByType(ErrorType type)
        {
            lock (_lockObject)
            {
                return _errors.Where(e => e.Type == type).ToList();
            }
        }

        /// <summary>
        /// 按严重程度获取错误
        /// </summary>
        /// <param name="severity">严重程度</param>
        /// <returns>指定严重程度的错误列表</returns>
        public List<ErrorInfo> GetErrorsBySeverity(ErrorSeverity severity)
        {
            lock (_lockObject)
            {
                return _errors.Where(e => e.Severity == severity).ToList();
            }
        }

        /// <summary>
        /// 按模块获取错误
        /// </summary>
        /// <param name="moduleName">模块名称</param>
        /// <returns>指定模块的错误列表</returns>
        public List<ErrorInfo> GetErrorsByModule(string moduleName)
        {
            lock (_lockObject)
            {
                return _errors.Where(e => e.ModuleName == moduleName).ToList();
            }
        }

        /// <summary>
        /// 获取错误统计信息
        /// </summary>
        /// <returns>错误统计字典</returns>
        public Dictionary<ErrorType, int> GetErrorStatistics()
        {
            lock (_lockObject)
            {
                return _errors.GroupBy(e => e.Type)
                             .ToDictionary(g => g.Key, g => g.Count());
            }
        }

        /// <summary>
        /// 获取严重程度统计信息
        /// </summary>
        /// <returns>严重程度统计字典</returns>
        public Dictionary<ErrorSeverity, int> GetSeverityStatistics()
        {
            lock (_lockObject)
            {
                return _errors.GroupBy(e => e.Severity)
                             .ToDictionary(g => g.Key, g => g.Count());
            }
        }

        /// <summary>
        /// 是否有错误
        /// </summary>
        /// <returns>是否有错误</returns>
        public bool HasErrors()
        {
            lock (_lockObject)
            {
                return _errors.Count > 0;
            }
        }

        /// <summary>
        /// 是否有严重错误
        /// </summary>
        /// <returns>是否有严重错误</returns>
        public bool HasCriticalErrors()
        {
            lock (_lockObject)
            {
                return _errors.Any(e => e.Severity == ErrorSeverity.Critical || e.Severity == ErrorSeverity.Error);
            }
        }

        /// <summary>
        /// 获取错误总数
        /// </summary>
        /// <returns>错误总数</returns>
        public int GetErrorCount()
        {
            lock (_lockObject)
            {
                return _errors.Count;
            }
        }

        /// <summary>
        /// 清空所有错误
        /// </summary>
        public void Clear()
        {
            lock (_lockObject)
            {
                _errors.Clear();
            }
        }

        /// <summary>
        /// 从进度消息中解析并添加错误
        /// </summary>
        /// <param name="progressMessage">进度消息</param>
        /// <param name="moduleName">模块名称</param>
        public void ParseAndAddErrorFromProgressMessage(string progressMessage, string moduleName = null)
        {
            if (string.IsNullOrEmpty(progressMessage)) return;

            // 解析不同类型的错误消息
            if (progressMessage.StartsWith("[MODULE_FAILED]"))
            {
                var parts = progressMessage.Substring(15).Split(':');
                var module = parts.Length > 0 ? parts[0] : moduleName;
                var message = parts.Length > 1 ? parts[1] : "模块生成失败";

                AddError(ErrorType.DocumentGeneration, ErrorSeverity.Error, module, message,
                    suggestedSolution: "检查模块配置和资源文件是否正确");
            }
            else if (progressMessage.StartsWith("[ERROR]"))
            {
                var message = progressMessage.Substring(7);
                var errorType = DetermineErrorType(message);
                var suggestedSolution = GetSuggestedSolution(errorType, message);

                AddError(errorType, ErrorSeverity.Error, moduleName, message,
                    suggestedSolution: suggestedSolution);
            }
            else if (progressMessage.StartsWith("[WARNING]"))
            {
                var message = progressMessage.Substring(9);
                var errorType = DetermineErrorType(message);

                AddError(errorType, ErrorSeverity.Warning, moduleName, message);
            }
        }

        /// <summary>
        /// 根据消息内容确定错误类型
        /// </summary>
        /// <param name="message">错误消息</param>
        /// <returns>错误类型</returns>
        private ErrorType DetermineErrorType(string message)
        {
            if (string.IsNullOrEmpty(message)) return ErrorType.Other;

            var lowerMessage = message.ToLower();

            if (lowerMessage.Contains("文件") && (lowerMessage.Contains("不存在") || lowerMessage.Contains("缺失") || lowerMessage.Contains("找不到")))
                return ErrorType.MissingFile;

            if (lowerMessage.Contains("模板") || lowerMessage.Contains("template"))
                return ErrorType.TemplateProcessing;

            if (lowerMessage.Contains("参数") || lowerMessage.Contains("配置") || lowerMessage.Contains("parameter"))
                return ErrorType.ParameterConfiguration;

            if (lowerMessage.Contains("验证") || lowerMessage.Contains("validation") || lowerMessage.Contains("格式"))
                return ErrorType.DataValidation;

            if (lowerMessage.Contains("合并") || lowerMessage.Contains("merge"))
                return ErrorType.DocumentMerging;

            if (lowerMessage.Contains("生成") || lowerMessage.Contains("generate"))
                return ErrorType.DocumentGeneration;

            return ErrorType.Other;
        }

        /// <summary>
        /// 根据错误类型和消息获取建议解决方案
        /// </summary>
        /// <param name="errorType">错误类型</param>
        /// <param name="message">错误消息</param>
        /// <returns>建议解决方案</returns>
        private string GetSuggestedSolution(ErrorType errorType, string message)
        {
            switch (errorType)
            {
                case ErrorType.MissingFile:
                    return "请检查资料文件夹中是否包含所需的文件，确保文件路径正确且文件未被占用";

                case ErrorType.TemplateProcessing:
                    return "请检查模板文件是否存在且格式正确，确保模板文件未被损坏";

                case ErrorType.ParameterConfiguration:
                    return "请检查模块参数配置是否正确，确保所有必需参数都已设置";

                case ErrorType.DataValidation:
                    return "请检查输入数据的格式和内容是否符合要求";

                case ErrorType.DocumentMerging:
                    return "请确保所有模块文档都已正确生成，检查输出目录权限";

                case ErrorType.DocumentGeneration:
                    return "请检查模块配置、模板文件和输入数据是否正确";

                default:
                    return "请检查系统配置和输入参数，如问题持续存在请联系技术支持";
            }
        }
    }
}
