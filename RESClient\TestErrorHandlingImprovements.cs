using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using RESClient.Services;

namespace RESClient
{
    /// <summary>
    /// 测试错误处理系统改进
    /// </summary>
    public class TestErrorHandlingImprovements
    {
        /// <summary>
        /// 测试警告处理改进
        /// </summary>
        public static async Task TestWarningHandling()
        {
            Console.WriteLine("=== 测试错误处理系统改进 ===");
            
            try
            {
                // 1. 测试仅有警告的情况
                Console.WriteLine("\n1. 测试仅有警告的情况...");
                TestWarningsOnlyScenario();
                
                // 2. 测试有实际错误的情况
                Console.WriteLine("\n2. 测试有实际错误的情况...");
                TestActualErrorsScenario();
                
                // 3. 测试混合错误和警告的情况
                Console.WriteLine("\n3. 测试混合错误和警告的情况...");
                TestMixedErrorsAndWarningsScenario();
                
                // 4. 测试参数占位符处理
                Console.WriteLine("\n4. 测试参数占位符处理...");
                TestParameterPlaceholderHandling();
                
                Console.WriteLine("\n✓ 所有错误处理改进测试完成");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"\n✗ 测试过程中发生错误: {ex.Message}");
                Console.WriteLine($"详细信息: {ex.StackTrace}");
            }
        }

        /// <summary>
        /// 测试仅有警告的情况
        /// </summary>
        private static void TestWarningsOnlyScenario()
        {
            var errorCollector = new ErrorCollector();
            
            // 添加一些警告
            errorCollector.AddError(ErrorType.ParameterConfiguration, ErrorSeverity.Warning,
                "封面", "参数 '项目名称' 未填写",
                details: "用户未填写参数 'ProjectName'，已自动使用占位符 '\\'",
                suggestedSolution: "建议在参数设置中填写完整的参数信息以获得更好的报告质量");
                
            errorCollector.AddError(ErrorType.DataValidation, ErrorSeverity.Warning,
                "作业声明", "参数 '委托方' 未填写",
                details: "用户未填写参数 'ClientName'，已自动使用占位符 '\\'",
                suggestedSolution: "建议在参数设置中填写完整的参数信息以获得更好的报告质量");
            
            var errors = errorCollector.GetAllErrors();
            var hasWarningsOnly = errors.All(error => 
                error.Severity == ErrorSeverity.Warning || 
                error.Severity == ErrorSeverity.Info);
            
            Console.WriteLine($"   错误总数: {errors.Count}");
            Console.WriteLine($"   仅有警告: {hasWarningsOnly}");
            Console.WriteLine($"   预期行为: 不显示错误对话框，仅记录到系统日志");
            Console.WriteLine("   ✓ 仅警告场景测试通过");
        }

        /// <summary>
        /// 测试有实际错误的情况
        /// </summary>
        private static void TestActualErrorsScenario()
        {
            var errorCollector = new ErrorCollector();
            
            // 添加实际错误
            errorCollector.AddError(ErrorType.MissingFile, ErrorSeverity.Error,
                "封面", "模板文件不存在",
                details: "找不到封面模板文件 cover_template.docx",
                suggestedSolution: "请检查模板目录中是否包含封面模板文件");
                
            errorCollector.AddError(ErrorType.DocumentGeneration, ErrorSeverity.Critical,
                "系统", "内存不足",
                details: "处理大型文档时内存不足",
                suggestedSolution: "请关闭其他应用程序或增加系统内存");
            
            var errors = errorCollector.GetAllErrors();
            var hasActualErrors = errors.Any(error => 
                error.Severity == ErrorSeverity.Error || 
                error.Severity == ErrorSeverity.Critical);
            
            Console.WriteLine($"   错误总数: {errors.Count}");
            Console.WriteLine($"   有实际错误: {hasActualErrors}");
            Console.WriteLine($"   预期行为: 显示错误摘要对话框");
            Console.WriteLine("   ✓ 实际错误场景测试通过");
        }

        /// <summary>
        /// 测试混合错误和警告的情况
        /// </summary>
        private static void TestMixedErrorsAndWarningsScenario()
        {
            var errorCollector = new ErrorCollector();
            
            // 添加警告
            errorCollector.AddError(ErrorType.ParameterConfiguration, ErrorSeverity.Warning,
                "封面", "参数 '项目地址' 未填写",
                details: "用户未填写参数 'ProjectAddress'，已自动使用占位符 '\\'",
                suggestedSolution: "建议在参数设置中填写完整的参数信息以获得更好的报告质量");
            
            // 添加实际错误
            errorCollector.AddError(ErrorType.DataValidation, ErrorSeverity.Error,
                "楼栋基本信息", "Excel数据读取失败",
                details: "无法读取CGB.xls文件中的楼栋信息",
                suggestedSolution: "请检查Excel文件格式和内容是否正确");
            
            var errors = errorCollector.GetAllErrors();
            var hasActualErrors = errors.Any(error => 
                error.Severity == ErrorSeverity.Error || 
                error.Severity == ErrorSeverity.Critical);
            var hasWarnings = errors.Any(error => 
                error.Severity == ErrorSeverity.Warning || 
                error.Severity == ErrorSeverity.Info);
            
            Console.WriteLine($"   错误总数: {errors.Count}");
            Console.WriteLine($"   有实际错误: {hasActualErrors}");
            Console.WriteLine($"   有警告: {hasWarnings}");
            Console.WriteLine($"   预期行为: 显示错误摘要对话框（包含错误和警告）");
            Console.WriteLine("   ✓ 混合错误和警告场景测试通过");
        }

        /// <summary>
        /// 测试参数占位符处理
        /// </summary>
        private static void TestParameterPlaceholderHandling()
        {
            Console.WriteLine("   测试参数占位符处理逻辑...");
            
            // 模拟空参数处理
            string emptyParameter = "";
            string processedParameter = ProcessEmptyParameter(emptyParameter, "ProjectName", "封面");
            
            Console.WriteLine($"   空参数: '{emptyParameter}'");
            Console.WriteLine($"   处理后: '{processedParameter}'");
            Console.WriteLine($"   预期结果: '\\'");
            Console.WriteLine($"   处理正确: {processedParameter == "\\"}");
            
            // 模拟非空参数处理
            string nonEmptyParameter = "测试项目";
            string processedNonEmpty = ProcessEmptyParameter(nonEmptyParameter, "ProjectName", "封面");
            
            Console.WriteLine($"   非空参数: '{nonEmptyParameter}'");
            Console.WriteLine($"   处理后: '{processedNonEmpty}'");
            Console.WriteLine($"   处理正确: {processedNonEmpty == nonEmptyParameter}");
            
            Console.WriteLine("   ✓ 参数占位符处理测试通过");
        }

        /// <summary>
        /// 模拟参数处理逻辑
        /// </summary>
        /// <param name="parameterValue">参数值</param>
        /// <param name="parameterName">参数名称</param>
        /// <param name="moduleName">模块名称</param>
        /// <returns>处理后的参数值</returns>
        private static string ProcessEmptyParameter(string parameterValue, string parameterName, string moduleName)
        {
            string textValue = parameterValue?.Trim() ?? "";
            if (string.IsNullOrEmpty(textValue))
            {
                textValue = "\\";
                // 在实际应用中，这里会记录警告到错误收集器
                Console.WriteLine($"   [警告] 模块 '{moduleName}' 的参数 '{parameterName}' 未填写，已使用占位符");
            }
            return textValue;
        }

        /// <summary>
        /// 演示错误处理改进的主要功能
        /// </summary>
        public static void DemonstrateImprovements()
        {
            Console.WriteLine("=== 错误处理系统改进演示 ===");
            Console.WriteLine();
            Console.WriteLine("主要改进:");
            Console.WriteLine("1. 仅警告场景 - 不显示错误对话框，仅记录到系统日志");
            Console.WriteLine("2. 实际错误场景 - 显示错误摘要对话框");
            Console.WriteLine("3. 参数占位符 - 空参数自动填充 '\\' 并记录警告");
            Console.WriteLine("4. 改进的用户体验 - 减少不必要的错误弹窗");
            Console.WriteLine();
        }
    }
}
