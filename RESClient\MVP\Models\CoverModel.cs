using System;
using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;
using NPOI.XWPF.UserModel;
using RESClient.MVP.Base;
using RESClient.Utils;

namespace RESClient.MVP.Models
{
    /// <summary>
    /// 封面模型，用于处理封面模板的填充
    /// </summary>
    public class CoverModel : BaseModel
    {
        /// <summary>
        /// 封面信息数据结构
        /// </summary>
        public class CoverData
        {
            public string SurveyNumber { get; set; } // 测绘编号
            public string ProjectNumber { get; set; } // 项目编号
            public string ProjectName { get; set; } // 项目名称
            public string ProjectAddress { get; set; } // 项目地址
            public string ConstructionUnit { get; set; } // 建设单位
            public string SurveyBuilding { get; set; } // 测绘楼栋
            public string SurveyUnitName { get; set; } // 测绘单位名称
            public string QualificationNumber { get; set; } // 测绘资格证书号/甲测资字
        }

        /// <summary>
        /// 生成测试用的封面数据
        /// </summary>
        /// <returns>封面测试数据</returns>
        public CoverData GenerateTestCoverData()
        {
            return new CoverData
            {
                SurveyNumber = "SC" + DateTime.Now.ToString("yyyyMMdd") + "001",
                ProjectNumber = "PJ" + DateTime.Now.ToString("yyyyMM") + "-123",
                ProjectName = "锦江区测试花园小区",
                ProjectAddress = "成都市锦江区测试路123号",
                ConstructionUnit = "成都市测试建设有限公司",
                SurveyBuilding = "1号楼、2号楼、3号楼",
                SurveyUnitName = "成都市房产测绘研究院",
                QualificationNumber = "甲测资字1234567号"
            };
        }

        /// <summary>
        /// 填充封面模板
        /// </summary>
        /// <param name="data">封面数据</param>
        /// <param name="outputPath">输出路径</param>
        /// <returns>是否成功</returns>
        public async Task<bool> FillCoverTemplateAsync(CoverData data, string outputPath)
        {
            try
            {
                return await Task.Run(() =>
                {
                    // 获取模板路径
                    string templatePath = Path.Combine(
                        AppDomain.CurrentDomain.BaseDirectory,
                        Program.TemplateDirectory,
                        "01_封面",
                        "封面.docx");

                    if (!File.Exists(templatePath))
                    {
                        throw new FileNotFoundException("封面模板文件不存在", templatePath);
                    }

                    // 确保输出目录存在
                    string outputDir = Path.GetDirectoryName(outputPath);
                    Directory.CreateDirectory(outputDir);

                    // 创建模板的临时副本
                    string tempFilePath = Path.Combine(
                        Path.GetTempPath(),
                        $"temp_cover_{DateTime.Now.Ticks}.docx");

                    // 复制模板到临时文件
                    File.Copy(templatePath, tempFilePath, true);

                    // 使用NPOI打开临时Word文档
                    XWPFDocument doc;
                    using (FileStream fs = new FileStream(tempFilePath, FileMode.Open, FileAccess.Read))
                    {
                        doc = new XWPFDocument(fs);
                    }

                    // 替换所有文本中的占位符
                    foreach (var paragraph in doc.Paragraphs)
                    {
                        string text = paragraph.Text;
                        
                        // 使用通用的变量替换方法
                        ReplaceVariablesInParagraph(paragraph, data);
                    }

                    // 表格中的占位符替换
                    foreach (var table in doc.Tables)
                    {
                        foreach (var row in table.Rows)
                        {
                            foreach (var cell in row.GetTableCells())
                            {
                                foreach (var paragraph in cell.Paragraphs)
                                {
                                    string text = paragraph.Text;
                                    
                                    // 使用通用的变量替换方法
                                    ReplaceVariablesInParagraph(paragraph, data);
                                }
                            }
                        }
                    }

                    // 保存填充后的文档到指定输出路径
                    using (FileStream fs = new FileStream(outputPath, FileMode.Create, FileAccess.Write))
                    {
                        doc.Write(fs);
                    }

                    // 删除临时文件
                    try
                    {
                        if (File.Exists(tempFilePath))
                        {
                            File.Delete(tempFilePath);
                        }
                    }
                    catch
                    {
                        // 临时文件删除失败不影响主流程
                    }

                    return true;
                });
            }
            catch (Exception ex)
            {
                HandleException(ex);
                return false;
            }
        }

        /// <summary>
        /// 替换段落中的文本，保留原始格式
        /// </summary>
        private void ReplaceTextInParagraph(XWPFParagraph paragraph, string searchText, string replaceText)
        {
            // 查找包含占位符的运行
            for (int i = 0; i < paragraph.Runs.Count; i++)
            {
                var run = paragraph.Runs[i];
                string runText = run.Text;
                
                if (runText != null && runText.Contains(searchText))
                {
                    // 保留格式，仅替换文本
                    run.SetText(runText.Replace(searchText, replaceText), 0);
                    return; // 替换完成
                }
                
                // 处理跨多个运行的占位符情况
                if (i < paragraph.Runs.Count - 1 && runText != null)
                {
                    // 查找是否占位符跨多个运行
                    string combinedText = runText;
                    int startRun = i;
                    int j = i + 1;
                    
                    // 尝试组合多个运行的文本查找占位符
                    while (j < paragraph.Runs.Count && !combinedText.Contains(searchText))
                    {
                        string nextRunText = paragraph.Runs[j].Text ?? "";
                        combinedText += nextRunText;
                        
                        if (combinedText.Contains(searchText))
                        {
                            // 找到了跨越多个运行的占位符
                            // 现在需要删除整个占位符并在第一个运行位置插入替换文本
                            
                            // 在第一个运行中放入替换文本
                            int placeholderStart = combinedText.IndexOf(searchText);
                            int firstRunLength = runText.Length;
                            
                            if (placeholderStart < firstRunLength)
                            {
                                // 占位符开始于第一个运行
                                string beforePlaceholder = runText.Substring(0, placeholderStart);
                                run.SetText(beforePlaceholder + replaceText, 0);
                                
                                // 计算占位符末尾位置
                                int placeholderEnd = placeholderStart + searchText.Length;
                                
                                // 如果占位符结束于当前运行之后，需要调整后面的运行
                                if (placeholderEnd > firstRunLength)
                                {
                                    int currentEnd = firstRunLength;
                                    int currentRun = i + 1;
                                    
                                    // 处理或删除跨越的运行
                                    while (currentRun <= j && currentEnd < placeholderEnd)
                                    {
                                        string currentText = paragraph.Runs[currentRun].Text ?? "";
                                        int currentLength = currentText.Length;
                                        
                                        if (currentEnd + currentLength > placeholderEnd)
                                        {
                                            // 这个运行包含占位符结束后的文本
                                            string afterPlaceholder = currentText.Substring(
                                                placeholderEnd - currentEnd);
                                            paragraph.Runs[currentRun].SetText(afterPlaceholder, 0);
                                            break;
                                        }
                                        else
                                        {
                                            // 这个运行完全在占位符内，清空它
                                            paragraph.Runs[currentRun].SetText("", 0);
                                        }
                                        
                                        currentEnd += currentLength;
                                        currentRun++;
                                    }
                                }
                                
                                return; // 替换完成
                            }
                        }
                        
                        j++;
                    }
                }
            }
        }

        /// <summary>
        /// 替换段落中的所有变量
        /// </summary>
        /// <param name="paragraph">段落</param>
        /// <param name="data">封面数据</param>
        private void ReplaceVariablesInParagraph(XWPFParagraph paragraph, CoverData data)
        {
            // 获取变量映射
            var variableMapping = GetVariableMapping(data);

            // 替换所有变量
            foreach (var variable in variableMapping)
            {
                if (paragraph.Text.Contains(variable.Key))
                {
                    ReplaceTextInParagraph(paragraph, variable.Key, variable.Value);
                }
            }
        }

        /// <summary>
        /// 获取变量映射字典
        /// </summary>
        /// <param name="data">封面数据</param>
        /// <returns>变量映射字典</returns>
        private Dictionary<string, string> GetVariableMapping(CoverData data)
        {
            return new Dictionary<string, string>
            {
                { "${测绘编号}", data.SurveyNumber ?? "" },
                { "${项目编号}", data.ProjectNumber ?? "" },
                { "${项目名称}", data.ProjectName ?? "" },
                { "${项目地址}", data.ProjectAddress ?? "" },
                { "${建设单位}", data.ConstructionUnit ?? "" },
                { "${测绘楼栋}", data.SurveyBuilding ?? "" },
                { "${测绘公司}", data.SurveyUnitName ?? "" },
                { "${测绘单位名称}", data.SurveyUnitName ?? "" }, // 兼容旧模板
                { "${测绘资格证书号}", data.QualificationNumber ?? "" },
                { "${甲测资字}", data.QualificationNumber ?? "" }, // 兼容旧模板
                { "${日期}", ChineseDateFormatter.ToChineseUppercase(DateTime.Now) }
            };
        }
    }
}