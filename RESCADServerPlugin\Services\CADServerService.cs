using System;
using System.Net;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using System.Threading;
using Autodesk.AutoCAD.ApplicationServices;
using System.Collections.Generic;
using System.IO;
using RESCADServerPlugin.Controllers;

namespace RESCADServerPlugin.Services
{
    public class CADServerService
    {
        private HttpListener _listener;
        private CancellationTokenSource _cancellationTokenSource;
        private Task _serverTask;
        private const int DefaultPort = 6060;
        private int _currentPort = DefaultPort;
        private bool _isRunning = false;
        private readonly Dictionary<string, RouteHandler> _routes;
        private readonly PrintDrawingController _printDrawingController;
        private readonly DirectPrintDrawingController _directPrintDrawingController;
        private readonly SinglePrintDrawingController _singlePrintDrawingController;
        private readonly UploadController _uploadController;
        private readonly FrameIsolationController _frameIsolationController;

        public int CurrentPort => _currentPort;
        public bool IsRunning => _isRunning;

        public CADServerService()
        {
            _printDrawingController = new PrintDrawingController();
            _directPrintDrawingController = new DirectPrintDrawingController();
            _singlePrintDrawingController = new SinglePrintDrawingController();
            _uploadController = new UploadController();
            _frameIsolationController = new FrameIsolationController();
            
            _routes = new Dictionary<string, RouteHandler>();
            RegisterRoutes();
        }

        private void RegisterRoutes()
        {
            // Register API endpoints
            _routes.Add("GET:/api/test/info", GetServiceInfo);
            _routes.Add("GET:/api/test/draw-circle", DrawCircle);
            _routes.Add("GET:/api/test/draw-line", DrawLine);
            _routes.Add("GET:/api/test/add-text", AddText);
            
            // Register print drawing API endpoints
            _routes.Add("POST:/api/print-drawings/process", ProcessPrintDrawings);
            _routes.Add("GET:/api/print-drawings/queue-status", GetPrintDrawingsQueueStatus);
            _routes.Add("POST:/api/print-drawings/process-with-progress", ProcessPrintDrawingsWithProgress);
            _routes.Add("GET:/api/print-drawings/progress", GetPrintDrawingsProgress);
            _routes.Add("GET:/api/print-drawings/download", DownloadPrintDrawings);

            // Register direct process API endpoint (no background queue)
            _routes.Add("POST:/api/print-drawings/process-direct", ProcessPrintDrawingsDirect);
            
            // Register single print drawing export API endpoints
            _routes.Add("POST:/api/single-print-drawing/export", ProcessSinglePrintDrawing);
            _routes.Add("GET:/api/single-print-drawing/check-queue", GetSinglePrintDrawingQueueStatus);
            
            // Register file upload API endpoints
            _routes.Add("POST:/api/upload/dwg", HandleUploadDwg);
            
            // Register frame isolation API endpoints
            _routes.Add("POST:/api/frame-isolation/process", ProcessFrameIsolation);
            _routes.Add("GET:/api/frame-isolation/queue-status", GetFrameIsolationQueueStatus);
        }

        // 启动简易HTTP服务器
        public void StartServer()
        {
            if (_isRunning)
                return;

            // 尝试从默认端口开始，如果被占用就尝试其他端口
            int[] portsToTry = { DefaultPort, 6061, 6062, 6063, 6064, 6065 };
            bool serverStarted = false;

            foreach (int port in portsToTry)
            {
                try
                {
                    _listener = new HttpListener();
                    _listener.Prefixes.Add($"http://localhost:{port}/");
                    _listener.Start();
                    _isRunning = true;
                    _currentPort = port;

                    _cancellationTokenSource = new CancellationTokenSource();
                    _serverTask = Task.Run(() => ListenForRequests(_cancellationTokenSource.Token));

                    Document doc = Application.DocumentManager.MdiActiveDocument;
                    if (doc != null)
                    {
                        doc.Editor.WriteMessage($"\n简易 Web API 服务已在 http://localhost:{port} 启动");
                        doc.Editor.WriteMessage($"\n访问 http://localhost:{port} 可打开Web界面");
                    }
                    
                    serverStarted = true;
                    break; // 成功启动，跳出循环
                }
                catch (System.Exception ex) when (ex is HttpListenerException || ex.Message.Contains("被占用"))
                {
                    // 端口被占用，尝试下一个端口
                    Document doc = Application.DocumentManager.MdiActiveDocument;
                    if (doc != null)
                    {
                        doc.Editor.WriteMessage($"\n端口 {port} 被占用，尝试下一个端口");
                    }
                    
                    // 确保释放之前创建的监听器
                    if (_listener != null)
                    {
                        _listener.Close();
                    }
                }
                catch (System.Exception ex)
                {
                    // 其他错误
                    Document doc = Application.DocumentManager.MdiActiveDocument;
                    if (doc != null)
                    {
                        doc.Editor.WriteMessage($"\n启动Web服务器时出错: {ex.Message}");
                    }
                    _isRunning = false;
                    throw;
                }
            }

            if (!serverStarted)
            {
                Document doc = Application.DocumentManager.MdiActiveDocument;
                if (doc != null)
                {
                    doc.Editor.WriteMessage($"\n无法启动Web服务器: 所有尝试的端口均被占用");
                }
                _isRunning = false;
            }
        }

        // 处理HTTP请求
        private async Task ListenForRequests(CancellationToken cancellationToken)
        {
            while (!cancellationToken.IsCancellationRequested && _listener.IsListening)
            {
                try
                {
                    var context = await _listener.GetContextAsync();
                    
                    // 在新线程中处理请求，避免阻塞监听线程
                    ThreadPool.QueueUserWorkItem((state) => 
                    {
                        try 
                        {
                            HandleRequest((HttpListenerContext)state);
                        }
                        catch (System.Exception ex)
                        {
                            // 记录错误但继续监听
                            Document doc = Application.DocumentManager.MdiActiveDocument;
                            if (doc != null)
                            {
                                doc.Editor.WriteMessage($"\n处理HTTP请求时出错: {ex.Message}");
                            }
                        }
                    }, context);
                }
                catch (System.Exception ex) when (!cancellationToken.IsCancellationRequested)
                {
                    // 记录错误但继续监听
                    Document doc = Application.DocumentManager.MdiActiveDocument;
                    if (doc != null)
                    {
                        doc.Editor.WriteMessage($"\n监听HTTP请求时出错: {ex.Message}");
                    }
                }
            }
        }

        // 处理请求路由和响应
        private void HandleRequest(HttpListenerContext context)
        {
            try
            {
                string path = context.Request.Url.AbsolutePath.TrimEnd('/');
                string method = context.Request.HttpMethod;
                string routeKey = $"{method}:{path}";

                // 记录请求
                LogRequest(context.Request);

                // 应用CORS头
                ApplyCorsHeaders(context.Response);

                // 检查是否是预检请求
                if (method == "OPTIONS")
                {
                    context.Response.StatusCode = 200;
                    context.Response.Close();
                    return;
                }

                // 尝试提供静态文件
                if (method == "GET" && TryServeStaticFile(context, path))
                {
                    return;
                }

                // 查找路由处理器
                if (_routes.TryGetValue(routeKey, out RouteHandler handler))
                {
                    handler(context);
                }
                else
                {
                    // 尝试匹配不区分大小写的路由
                    bool found = false;
                    foreach (var route in _routes.Keys)
                    {
                        if (string.Equals(route, routeKey, StringComparison.OrdinalIgnoreCase))
                        {
                            _routes[route](context);
                            found = true;
                            break;
                        }
                    }

                    if (!found)
                    {
                        // 如果找不到路由，返回404
                        context.Response.StatusCode = 404;
                        context.Response.ContentType = "text/plain";

                        // 添加调试信息
                        string debugInfo = $"Not Found: {path}\n\n";
                        debugInfo += "Available routes:\n";
                        foreach (var route in _routes.Keys)
                        {
                            debugInfo += $"  {route}\n";
                        }
                        debugInfo += "\nStatic file check:\n";

                        // 检查静态文件路径
                        string? pluginDir = Path.GetDirectoryName(System.Reflection.Assembly.GetExecutingAssembly().Location);
                        if (pluginDir == null) pluginDir = "";
                        string wwwrootDir = Path.Combine(pluginDir, "wwwroot");

                        // 规范化路径
                        string normalizedPath = path;
                        if (path == "/") normalizedPath = "/index.html";

                        string filePath = Path.Combine(wwwrootDir, normalizedPath.TrimStart('/').Replace('/', Path.DirectorySeparatorChar));

                        debugInfo += $"  Plugin directory: {pluginDir}\n";
                        debugInfo += $"  Wwwroot directory: {wwwrootDir}\n";
                        debugInfo += $"  Requested path: {path}\n";
                        debugInfo += $"  File path: {filePath}\n";
                        debugInfo += $"  Wwwroot exists: {Directory.Exists(wwwrootDir)}\n";
                        debugInfo += $"  File exists: {File.Exists(filePath)}\n";

                        // 检查 index.html 文件
                        string indexPath = Path.Combine(wwwrootDir, "index.html");
                        debugInfo += $"  Index.html path: {indexPath}\n";
                        debugInfo += $"  Index.html exists: {File.Exists(indexPath)}\n";

                        byte[] buffer = Encoding.UTF8.GetBytes(debugInfo);
                        context.Response.ContentLength64 = buffer.Length;
                        context.Response.OutputStream.Write(buffer, 0, buffer.Length);
                        context.Response.Close();

                        Document doc = Application.DocumentManager.MdiActiveDocument;
                        if (doc != null)
                        {
                            doc.Editor.WriteMessage($"\n未找到路由: {routeKey}");
                            doc.Editor.WriteMessage($"\n静态文件路径: {filePath}");
                            doc.Editor.WriteMessage($"\n文件存在: {File.Exists(filePath)}");
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                try
                {
                    Document doc = Application.DocumentManager.MdiActiveDocument;
                    if (doc != null)
                    {
                        doc.Editor.WriteMessage($"\n处理请求时出错: {ex.Message}");
                    }

                    context.Response.StatusCode = 500;
                    context.Response.ContentType = "text/plain";
                    byte[] buffer = Encoding.UTF8.GetBytes($"Internal Server Error: {ex.Message}");
                    context.Response.ContentLength64 = buffer.Length;
                    context.Response.OutputStream.Write(buffer, 0, buffer.Length);
                    context.Response.Close();
                }
                catch
                {
                    // 忽略关闭响应时的错误
                }
            }
        }
        
        #region Middleware
        
        private void ApplyCorsHeaders(HttpListenerResponse response)
        {
            response.AddHeader("Access-Control-Allow-Origin", "*");
            response.AddHeader("Access-Control-Allow-Methods", "GET, POST, OPTIONS");
            response.AddHeader("Access-Control-Allow-Headers", "Content-Type, Accept, X-Requested-With");
            response.AddHeader("Access-Control-Expose-Headers", "Content-Disposition");
            response.AddHeader("Access-Control-Max-Age", "86400"); // 24小时
        }
        
        private void LogRequest(HttpListenerRequest request)
        {
            Document doc = Application.DocumentManager.MdiActiveDocument;
            if (doc != null)
            {
                doc.Editor.WriteMessage($"\n收到请求: {request.HttpMethod} {request.Url?.AbsolutePath ?? "unknown"}");
            }
        }
        
        #endregion
        
        #region Response Helpers
        
        private void SendJsonResponse(HttpListenerResponse response, int statusCode, object data)
        {
            response.StatusCode = statusCode;
            response.ContentType = "application/json; charset=utf-8";
            
            string json = JsonSerializer.Serialize(data, new JsonSerializerOptions 
            { 
                WriteIndented = false,
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase
            });
            
            byte[] buffer = Encoding.UTF8.GetBytes(json);
            response.ContentLength64 = buffer.Length;
            response.OutputStream.Write(buffer, 0, buffer.Length);
            response.OutputStream.Close();
        }
        
        #endregion
        
        #region API Route Handlers
        
        private void GetServiceInfo(HttpListenerContext context)
        {
            var response = new ApiResponse 
            {
                Success = true,
                Data = new 
                {
                    Message = "AutoCAD API服务正常运行中",
                    Version = "1.0.0",
                    Timestamp = DateTime.Now
                }
            };
            
            SendJsonResponse(context.Response, 200, response);
        }
        
        private void DrawCircle(HttpListenerContext context)
        {
            var request = context.Request;
            
            try
            {
                // Parse and validate parameters
                if (!TryParseDouble(request.QueryString["x"], out double x))
                    x = 0;
                
                if (!TryParseDouble(request.QueryString["y"], out double y))
                    y = 0;
                
                if (!TryParseDouble(request.QueryString["radius"], out double radius))
                    radius = 10;
                
                // Execute CAD operation
                var drawingService = CADServiceManager.GetDrawingService();
                bool success = drawingService.DrawCircle(x, y, radius, out string message);
                
                if (success)
                {
                    SendJsonResponse(context.Response, 200, new ApiResponse 
                    { 
                        Success = true,
                        Message = message,
                        Data = new { X = x, Y = y, Radius = radius }
                    });
                }
                else
                {
                    SendJsonResponse(context.Response, 400, new ApiResponse 
                    { 
                        Success = false,
                        Message = "操作失败",
                        Error = message
                    });
                }
            }
            catch (Exception ex)
            {
                SendJsonResponse(context.Response, 400, new ApiResponse 
                { 
                    Success = false,
                    Message = "参数无效",
                    Error = ex.Message
                });
            }
        }
        
        private void DrawLine(HttpListenerContext context)
        {
            var request = context.Request;
            
            try
            {
                // Parse and validate parameters
                if (!TryParseDouble(request.QueryString["x1"], out double x1))
                    x1 = 0;
                
                if (!TryParseDouble(request.QueryString["y1"], out double y1))
                    y1 = 0;
                
                if (!TryParseDouble(request.QueryString["x2"], out double x2))
                    x2 = 10;
                
                if (!TryParseDouble(request.QueryString["y2"], out double y2))
                    y2 = 10;
                
                // Execute CAD operation
                var drawingService = CADServiceManager.GetDrawingService();
                bool success = drawingService.DrawLine(x1, y1, x2, y2, out string message);
                
                if (success)
                {
                    SendJsonResponse(context.Response, 200, new ApiResponse 
                    { 
                        Success = true,
                        Message = message,
                        Data = new { StartPoint = new { X = x1, Y = y1 }, EndPoint = new { X = x2, Y = y2 } }
                    });
                }
                else
                {
                    SendJsonResponse(context.Response, 400, new ApiResponse 
                    { 
                        Success = false,
                        Message = "操作失败",
                        Error = message
                    });
                }
            }
            catch (Exception ex)
            {
                SendJsonResponse(context.Response, 400, new ApiResponse 
                { 
                    Success = false,
                    Message = "参数无效",
                    Error = ex.Message
                });
            }
        }
        
        private void AddText(HttpListenerContext context)
        {
            var request = context.Request;
            
            try
            {
                // Parse and validate parameters
                if (!TryParseDouble(request.QueryString["x"], out double x))
                    x = 0;
                
                if (!TryParseDouble(request.QueryString["y"], out double y))
                    y = 0;
                
                if (!TryParseDouble(request.QueryString["height"], out double height))
                    height = 2.5;
                
                string text = request.QueryString["text"] ?? "测试文字";
                
                // Execute CAD operation
                var drawingService = CADServiceManager.GetDrawingService();
                bool success = drawingService.AddText(x, y, text, height, out string message);
                
                if (success)
                {
                    SendJsonResponse(context.Response, 200, new ApiResponse 
                    { 
                        Success = true,
                        Message = message,
                        Data = new { X = x, Y = y, Text = text, Height = height }
                    });
                }
                else
                {
                    SendJsonResponse(context.Response, 400, new ApiResponse 
                    { 
                        Success = false,
                        Message = "操作失败",
                        Error = message
                    });
                }
            }
            catch (Exception ex)
            {
                SendJsonResponse(context.Response, 400, new ApiResponse 
                { 
                    Success = false,
                    Message = "参数无效",
                    Error = ex.Message
                });
            }
        }
        
        private async void ProcessPrintDrawings(HttpListenerContext context)
        {
            try
            {
                await _printDrawingController.HandleProcessDwgRequest(context);
            }
            catch (Exception ex)
            {
                // 在发生异常时返回错误响应
                Document doc = Application.DocumentManager.MdiActiveDocument;
                if (doc != null)
                {
                    doc.Editor.WriteMessage($"\n处理打印图请求时出错: {ex.Message}");
                }
                
                SendJsonResponse(context.Response, 500, new ApiResponse 
                { 
                    Success = false, 
                    Message = "服务器内部错误", 
                    Error = ex.Message 
                });
            }
        }
        
        private void GetPrintDrawingsQueueStatus(HttpListenerContext context)
        {
            try
            {
                _printDrawingController.HandleGetQueueStatus(context);
            }
            catch (Exception ex)
            {
                // 在发生异常时返回错误响应
                Document doc = Application.DocumentManager.MdiActiveDocument;
                if (doc != null)
                {
                    doc.Editor.WriteMessage($"\n获取打印图队列状态时出错: {ex.Message}");
                }
                
                SendJsonResponse(context.Response, 500, new ApiResponse 
                { 
                    Success = false, 
                    Message = "服务器内部错误", 
                    Error = ex.Message 
                });
            }
        }

        private void ProcessPrintDrawingsWithProgress(HttpListenerContext context)
        {
            try
            {
                _printDrawingController.HandleProcessWithProgress(context);
            }
            catch (Exception ex)
            {
                // 在发生异常时返回错误响应
                Document doc = Application.DocumentManager.MdiActiveDocument;
                if (doc != null)
                {
                    doc.Editor.WriteMessage($"\n处理带进度报告的打印图请求时出错: {ex.Message}");
                }

                SendJsonResponse(context.Response, 500, new ApiResponse
                {
                    Success = false,
                    Message = "服务器内部错误",
                    Error = ex.Message
                });
            }
        }

        private void GetPrintDrawingsProgress(HttpListenerContext context)
        {
            try
            {
                _printDrawingController.HandleGetProgress(context);
            }
            catch (Exception ex)
            {
                // 在发生异常时返回错误响应
                Document doc = Application.DocumentManager.MdiActiveDocument;
                if (doc != null)
                {
                    doc.Editor.WriteMessage($"\n获取打印图进度时出错: {ex.Message}");
                }

                SendJsonResponse(context.Response, 500, new ApiResponse
                {
                    Success = false,
                    Message = "服务器内部错误",
                    Error = ex.Message
                });
            }
        }

        private void DownloadPrintDrawings(HttpListenerContext context)
        {
            try
            {
                _printDrawingController.HandleDownload(context);
            }
            catch (Exception ex)
            {
                // 在发生异常时返回错误响应
                Document doc = Application.DocumentManager.MdiActiveDocument;
                if (doc != null)
                {
                    doc.Editor.WriteMessage($"\n下载打印图文件时出错: {ex.Message}");
                }

                SendJsonResponse(context.Response, 500, new ApiResponse
                {
                    Success = false,
                    Message = "服务器内部错误",
                    Error = ex.Message
                });
            }
        }

        // 新增的处理直接打印图请求的路由处理方法
        private async void ProcessPrintDrawingsDirect(HttpListenerContext context)
        {
            try
            {
                await _directPrintDrawingController.HandleDirectProcessRequest(context);
            }
            catch (Exception ex)
            {
                Document doc = Application.DocumentManager.MdiActiveDocument;
                if (doc != null)
                {
                    doc.Editor.WriteMessage($"\n处理直接打印图请求出错: {ex.Message}");
                }
                
                SendJsonResponse(context.Response, 500, new ApiResponse 
                { 
                    Success = false, 
                    Message = "处理打印图失败", 
                    Error = ex.Message 
                });
            }
        }
        
        private async void ProcessSinglePrintDrawing(HttpListenerContext context)
        {
            try
            {
                await _singlePrintDrawingController.ExportSinglePrintDrawing(context);
            }
            catch (Exception ex)
            {
                Document doc = Application.DocumentManager.MdiActiveDocument;
                if (doc != null)
                {
                    doc.Editor.WriteMessage($"\n处理单个打印图导出请求出错: {ex.Message}");
                }
                
                SendJsonResponse(context.Response, 500, new ApiResponse 
                { 
                    Success = false, 
                    Message = "处理单个打印图导出失败", 
                    Error = ex.Message 
                });
            }
        }
        
        private void GetSinglePrintDrawingQueueStatus(HttpListenerContext context)
        {
            try
            {
                _singlePrintDrawingController.CheckQueue(context);
            }
            catch (Exception ex)
            {
                Document doc = Application.DocumentManager.MdiActiveDocument;
                if (doc != null)
                {
                    doc.Editor.WriteMessage($"\n获取单个打印图队列状态出错: {ex.Message}");
                }
                
                SendJsonResponse(context.Response, 500, new ApiResponse 
                { 
                    Success = false, 
                    Message = "获取单个打印图队列状态失败", 
                    Error = ex.Message 
                });
            }
        }
        
        private async void HandleUploadDwg(HttpListenerContext context)
        {
            await _uploadController.HandleUploadDwg(context);
        }
        
        private async void ProcessFrameIsolation(HttpListenerContext context)
        {
            try
            {
                await _frameIsolationController.HandleProcessDwgRequest(context);
            }
            catch (Exception ex)
            {
                Document doc = Application.DocumentManager.MdiActiveDocument;
                if (doc != null)
                {
                    doc.Editor.WriteMessage($"\n处理图框隔离请求出错: {ex.Message}");
                }
                
                SendJsonResponse(context.Response, 500, new ApiResponse 
                { 
                    Success = false, 
                    Message = "图框隔离处理失败", 
                    Error = ex.Message 
                });
            }
        }
        
        private void GetFrameIsolationQueueStatus(HttpListenerContext context)
        {
            try
            {
                _frameIsolationController.HandleGetQueueStatus(context);
            }
            catch (Exception ex)
            {
                Document doc = Application.DocumentManager.MdiActiveDocument;
                if (doc != null)
                {
                    doc.Editor.WriteMessage($"\n获取图框隔离队列状态出错: {ex.Message}");
                }
                
                SendJsonResponse(context.Response, 500, new ApiResponse 
                { 
                    Success = false, 
                    Message = "获取图框隔离队列状态失败", 
                    Error = ex.Message 
                });
            }
        }
        
        #endregion
        
        #region Utility Methods
        
        private bool TryParseDouble(string? value, out double result)
        {
            if (string.IsNullOrWhiteSpace(value))
            {
                result = 0;
                return false;
            }

            return double.TryParse(value, out result);
        }
        
        #endregion

        // 停止服务器
        public void StopServer()
        {
            if (_isRunning)
            {
                _cancellationTokenSource?.Cancel();
                _listener?.Stop();
                
                // 等待服务器任务完成
                try
                {
                    _serverTask?.Wait(1000);
                }
                catch { }
                
                _isRunning = false;
                
                Document doc = Application.DocumentManager.MdiActiveDocument;
                if (doc != null)
                {
                    doc.Editor.WriteMessage("\nWeb API 服务已停止");
                }
            }
        }

        // 获取服务状态信息
        public (bool isRunning, int port) GetServerStatus()
        {
            return (_isRunning, _currentPort);
        }
        
        #region Types
        
        // Define delegate for route handlers
        private delegate void RouteHandler(HttpListenerContext context);
        
        // Standard API response structure
        private class ApiResponse
        {
            public bool Success { get; set; }
            public string Message { get; set; }
            public object Data { get; set; }
            public string Error { get; set; }
        }
        
        #endregion

        // 尝试提供静态文件
        private bool TryServeStaticFile(HttpListenerContext context, string path)
        {
            // 规范化路径
            if (path == "/")
                path = "/index.html";
            
            // 获取插件目录
            string? pluginDir = Path.GetDirectoryName(System.Reflection.Assembly.GetExecutingAssembly().Location);
            if (pluginDir == null) return false;
            string wwwrootDir = Path.Combine(pluginDir, "wwwroot");
            
            // 确保wwwroot目录存在
            if (!Directory.Exists(wwwrootDir))
            {
                try
                {
                    Directory.CreateDirectory(wwwrootDir);
                }
                catch
                {
                    return false;
                }
            }
            
            // 构建物理文件路径
            string filePath = Path.Combine(wwwrootDir, path.TrimStart('/').Replace('/', Path.DirectorySeparatorChar));
            
            // 检查文件是否存在
            if (!File.Exists(filePath))
                return false;
            
            try
            {
                // 获取文件内容类型
                string contentType = GetContentType(Path.GetExtension(filePath));
                
                // 读取文件内容
                byte[] fileBytes = File.ReadAllBytes(filePath);
                
                // 设置响应头
                context.Response.ContentType = contentType;
                context.Response.ContentLength64 = fileBytes.Length;
                context.Response.StatusCode = 200;
                
                // 发送文件内容
                context.Response.OutputStream.Write(fileBytes, 0, fileBytes.Length);
                context.Response.OutputStream.Close();
                
                return true;
            }
            catch (Exception ex)
            {
                // 记录错误但不阻止其他处理逻辑
                Document doc = Application.DocumentManager.MdiActiveDocument;
                if (doc != null)
                {
                    doc.Editor.WriteMessage($"\n提供静态文件时出错: {ex.Message}");
                }
                
                return false;
            }
        }
        
        // 获取内容类型
        private string GetContentType(string extension)
        {
            switch (extension.ToLower())
            {
                case ".html":
                case ".htm":
                    return "text/html; charset=utf-8";
                case ".css":
                    return "text/css; charset=utf-8";
                case ".js":
                    return "application/javascript; charset=utf-8";
                case ".json":
                    return "application/json; charset=utf-8";
                case ".jpg":
                case ".jpeg":
                    return "image/jpeg";
                case ".png":
                    return "image/png";
                case ".gif":
                    return "image/gif";
                case ".svg":
                    return "image/svg+xml";
                case ".ico":
                    return "image/x-icon";
                default:
                    return "application/octet-stream";
            }
        }
    }
} 