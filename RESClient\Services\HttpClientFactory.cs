using System;
using System.Net.Http;
using System.Threading.Tasks;
using RESClient.Models;

namespace RESClient.Services
{
    /// <summary>
    /// HTTP 客户端工厂服务
    /// 自动处理认证和配置
    /// </summary>
    public class HttpClientFactory
    {
        private readonly RESServerConfigService _configService;
        private readonly IAuthService _authService;

        public HttpClientFactory(RESServerConfigService configService, IAuthService authService)
        {
            _configService = configService;
            _authService = authService;
        }

        /// <summary>
        /// 创建配置好的 HttpClient
        /// </summary>
        /// <param name="requireAuth">是否需要认证</param>
        /// <returns>配置好的 HttpClient</returns>
        public async Task<HttpClient> CreateClientAsync(bool requireAuth = true)
        {
            var client = new HttpClient();
            client.Timeout = TimeSpan.FromMinutes(_configService.RequestTimeoutMinutes);

            if (requireAuth)
            {
                // 检查是否需要认证
                var authStatus = await _authService.GetServerStatusAsync();
                
                if (authStatus.IsAuthenticationEnabled)
                {
                    if (!_authService.IsLoggedIn)
                    {
                        throw new UnauthorizedAccessException("需要登录才能访问此功能");
                    }

                    // 验证 Token 是否仍然有效
                    var isValid = await _authService.ValidateTokenAsync();
                    if (!isValid)
                    {
                        throw new UnauthorizedAccessException("登录已过期，请重新登录");
                    }

                    // 添加认证头
                    client.DefaultRequestHeaders.Authorization = 
                        new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", _authService.CurrentToken);
                }
            }

            return client;
        }

        /// <summary>
        /// 创建用于检查服务器状态的 HttpClient（不需要认证）
        /// </summary>
        /// <returns>配置好的 HttpClient</returns>
        public HttpClient CreateStatusClient()
        {
            var client = new HttpClient();
            client.Timeout = TimeSpan.FromSeconds(10); // 状态检查使用较短的超时时间
            return client;
        }

        /// <summary>
        /// 创建用于长时间操作的 HttpClient
        /// </summary>
        /// <param name="requireAuth">是否需要认证</param>
        /// <returns>配置好的 HttpClient</returns>
        public async Task<HttpClient> CreateLongRunningClientAsync(bool requireAuth = true)
        {
            var client = await CreateClientAsync(requireAuth);
            client.Timeout = TimeSpan.FromMinutes(_configService.ConnectionTimeoutMinutes);
            return client;
        }

        /// <summary>
        /// 处理 HTTP 响应中的认证错误
        /// </summary>
        /// <param name="response">HTTP 响应</param>
        /// <returns>是否为认证错误</returns>
        public bool IsAuthenticationError(HttpResponseMessage response)
        {
            return response.StatusCode == System.Net.HttpStatusCode.Unauthorized ||
                   response.StatusCode == System.Net.HttpStatusCode.Forbidden;
        }

        /// <summary>
        /// 处理认证错误
        /// </summary>
        /// <param name="response">HTTP 响应</param>
        /// <returns>错误消息</returns>
        public async Task<string> HandleAuthenticationErrorAsync(HttpResponseMessage response)
        {
            if (response.StatusCode == System.Net.HttpStatusCode.Unauthorized)
            {
                // 清除本地认证状态
                _authService.Logout();
                return "认证已过期，请重新登录";
            }
            else if (response.StatusCode == System.Net.HttpStatusCode.Forbidden)
            {
                return "权限不足，无法访问此功能";
            }

            var content = await response.Content.ReadAsStringAsync();
            return $"请求失败：{response.StatusCode} - {content}";
        }
    }

    /// <summary>
    /// HTTP 客户端工厂服务的静态实例
    /// </summary>
    public static class HttpClientFactoryInstance
    {
        private static HttpClientFactory _instance;

        /// <summary>
        /// 初始化工厂实例
        /// </summary>
        /// <param name="configService">配置服务</param>
        /// <param name="authService">认证服务</param>
        public static void Initialize(RESServerConfigService configService, IAuthService authService)
        {
            _instance = new HttpClientFactory(configService, authService);
        }

        /// <summary>
        /// 获取工厂实例
        /// </summary>
        public static HttpClientFactory Instance
        {
            get
            {
                if (_instance == null)
                {
                    throw new InvalidOperationException("HttpClientFactory 尚未初始化。请先调用 Initialize 方法。");
                }
                return _instance;
            }
        }
    }
}
