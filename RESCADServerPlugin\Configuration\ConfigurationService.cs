using Autodesk.AutoCAD.ApplicationServices;
using System;
using System.IO;
using System.Reflection;
using System.Xml.Serialization;

namespace RESCADServerPlugin.Configuration
{
    public static class ConfigurationService
    {
        private static readonly string ConfigFileName = "RESCADServerPlugin.config.xml";
        private static PluginSettings _settings;

        public static PluginSettings GetSettings()
        {
            if (_settings == null)
            {
                LoadSettings();
            }
            return _settings;
        }

        private static void LoadSettings()
        {
            try
            {
                string assemblyLocation = Assembly.GetExecutingAssembly().Location;
                string configFilePath = Path.Combine(Path.GetDirectoryName(assemblyLocation), ConfigFileName);

                if (File.Exists(configFilePath))
                {
                    XmlSerializer serializer = new XmlSerializer(typeof(PluginSettings));
                    using (FileStream fs = new FileStream(configFilePath, FileMode.Open, FileAccess.Read))
                    {
                        _settings = (PluginSettings)serializer.Deserialize(fs);
                        Application.DocumentManager.MdiActiveDocument?.Editor.WriteMessage($"\nConfiguration loaded from: {configFilePath}");
                    }
                }
                else
                {
                    Application.DocumentManager.MdiActiveDocument?.Editor.WriteMessage($"\nConfiguration file not found: {configFilePath}. Using default settings.");
                    _settings = new PluginSettings(); // Use default (null or empty) settings
                }
            }
            catch (System.Exception ex)
            {
                Application.DocumentManager.MdiActiveDocument?.Editor.WriteMessage($"\nError loading configuration: {ex.Message}. Using default settings.");
                _settings = new PluginSettings(); // Fallback to default settings on error
            }
        }
    }
} 