using System;
using Autodesk.AutoCAD.ApplicationServices;
using Autodesk.AutoCAD.DatabaseServices;
using Autodesk.AutoCAD.EditorInput;

namespace RESCADServerPlugin.Commands.Implementations
{
    /// <summary>
    /// 命令基类，提供通用功能
    /// </summary>
    public abstract class CommandBase
    {
        /// <summary>
        /// 获取当前活动文档
        /// </summary>
        protected Document GetActiveDocument()
        {
            return Application.DocumentManager.MdiActiveDocument;
        }
        
        /// <summary>
        /// 获取当前编辑器
        /// </summary>
        protected Editor GetEditor()
        {
            Document doc = GetActiveDocument();
            return doc?.Editor;
        }
        
        /// <summary>
        /// 获取当前数据库
        /// </summary>
        protected Database GetDatabase()
        {
            Document doc = GetActiveDocument();
            return doc?.Database;
        }
        
        /// <summary>
        /// 向命令行写入消息
        /// </summary>
        protected void WriteMessage(string message)
        {
            Editor ed = GetEditor();
            if (ed != null)
            {
                ed.WriteMessage(message);
            }
        }
        
        /// <summary>
        /// 向命令行写入格式化消息
        /// </summary>
        protected void WriteMessage(string format, params object[] args)
        {
            Editor ed = GetEditor();
            if (ed != null)
            {
                ed.WriteMessage(format, args);
            }
        }
    }
} 