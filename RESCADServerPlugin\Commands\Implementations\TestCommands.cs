using System;
using Autodesk.AutoCAD.ApplicationServices;
using Autodesk.AutoCAD.DatabaseServices;
using Autodesk.AutoCAD.Geometry;
using Autodesk.AutoCAD.EditorInput;

namespace RESCADServerPlugin.Commands.Implementations
{
    /// <summary>
    /// 测试命令实现类
    /// </summary>
    public class TestCommands : CommandBase
    {
        /// <summary>
        /// 执行测试命令 - 在原点创建一个圆
        /// </summary>
        public void ExecuteTestCommand()
        {
            // 获取当前文档和编辑器
            Document doc = GetActiveDocument();
            if (doc == null) return;

            Editor ed = GetEditor();
            Database db = GetDatabase();

            WriteMessage("\n执行测试命令");

            // 在事务中创建一个圆
            using (Transaction trans = db.TransactionManager.StartTransaction())
            {
                try
                {
                    // 打开当前空间（模型空间或图纸空间）
                    BlockTable? bt = trans.GetObject(db.BlockTableId, OpenMode.ForRead) as BlockTable;
                    BlockTableRecord? btr = trans.GetObject(bt[BlockTableRecord.ModelSpace], OpenMode.ForWrite) as BlockTableRecord;

                    // 创建一个圆
                    Point3d center = new Point3d(0, 0, 0);
                    double radius = 5.0;
                    Circle circle = new Circle(center, Vector3d.ZAxis, radius);

                    // 添加到数据库
                    btr.AppendEntity(circle);
                    trans.AddNewlyCreatedDBObject(circle, true);

                    // 提交事务
                    trans.Commit();

                    // 显示成功消息
                    WriteMessage("\n成功创建一个半径为 {0} 的圆", radius);
                }
                catch (System.Exception ex)
                {
                    WriteMessage("\n错误：{0}", ex.Message);
                    trans.Abort();
                }
            }
        }
    }
} 