# 封面参数系统使用说明

## 📋 功能概述

封面参数系统为 RESClient 项目提供了完整的封面模板变量管理功能，支持用户自定义项目信息并自动替换模板中的占位符。

## 🚀 快速开始

### 1. 设置封面参数

1. 启动 RESClient 应用程序
2. 在主界面的模块列表中找到"封面"模块
3. 点击封面模块对应的"设置"按钮
4. 在弹出的参数设置窗口中填写项目信息：
   - **测绘编号**: 测绘项目的唯一编号
   - **项目编号**: 项目的唯一编号  
   - **项目名称**: 项目的完整名称
   - **项目地址**: 项目所在地址
   - **建设单位**: 项目建设单位名称
   - **测绘楼栋**: 需要测绘的楼栋信息
   - **测绘公司**: 执行测绘的公司名称
   - **测绘资格证书号**: 测绘公司的资格证书号
   - **日期**: 报告生成日期（默认为当前日期）

5. 点击"确定"保存参数

### 2. 生成封面

1. 选择数据目录
2. 勾选"封面"模块
3. 点击"生成报告"按钮
4. 系统将自动使用保存的参数生成封面文档

## 📝 支持的模板变量

系统支持以下模板占位符，会在生成时自动替换：

| 占位符 | 说明 | 示例值 |
|--------|------|--------|
| `${测绘编号}` | 测绘项目编号 | SC20241225001 |
| `${项目编号}` | 项目编号 | PJ202412-123 |
| `${项目名称}` | 项目名称 | 锦江区测试花园小区 |
| `${项目地址}` | 项目地址 | 成都市锦江区测试路123号 |
| `${建设单位}` | 建设单位 | 成都市测试建设有限公司 |
| `${测绘楼栋}` | 测绘楼栋 | 1号楼、2号楼、3号楼 |
| `${测绘公司}` | 测绘公司 | 成都市房产测绘研究院 |
| `${测绘资格证书号}` | 资格证书号 | 甲测资字1234567号 |
| `${日期}` | 报告日期 | 2024年12月25日 |

### 兼容性变量

为了兼容旧版模板，系统还支持以下别名：
- `${测绘单位名称}` = `${测绘公司}`
- `${甲测资字}` = `${测绘资格证书号}`

## 💾 数据存储

### 配置文件位置
参数数据保存在：`%LocalAppData%\RESClient\cover_parameters.json`

### 配置文件格式
```json
{
  "SurveyNumber": "SC20241225001",
  "ProjectNumber": "PJ202412-123",
  "ProjectName": "锦江区测试花园小区",
  "ProjectAddress": "成都市锦江区测试路123号",
  "ConstructionUnit": "成都市测试建设有限公司",
  "SurveyBuilding": "1号楼、2号楼、3号楼",
  "SurveyCompany": "成都市房产测绘研究院",
  "QualificationNumber": "甲测资字1234567号",
  "ReportDate": "2024-12-25T00:00:00"
}
```

## 🔧 高级功能

### 参数验证
系统会自动验证参数的完整性：
- 所有必填字段不能为空
- 验证失败时会显示具体的错误信息
- 只有验证通过的参数才能用于生成报告

### 参数重置
在参数设置窗口中点击"重置"按钮可以恢复默认值。

### 数据备份
可以手动备份配置文件 `cover_parameters.json` 以防数据丢失。

## 🛠️ 测试功能

### 运行测试
可以通过命令行运行测试来验证功能：
```bash
RESClient.exe test
```

### 测试内容
- 参数模型创建和初始化
- 参数保存和加载
- 参数验证
- 变量映射
- 文件操作

## ❗ 注意事项

1. **权限要求**: 需要对 `%LocalAppData%` 目录的写入权限
2. **编码格式**: 配置文件使用 UTF-8 编码，确保中文字符正确显示
3. **模板兼容**: 支持现有模板格式，向后兼容
4. **数据安全**: 建议定期备份配置文件

## 🔄 扩展开发

### 添加新参数
1. 在 `CoverParametersModel.CoverParameters` 类中添加新属性
2. 为新属性添加 `DisplayName` 和 `Description` 特性
3. 在 `GetVariableMapping` 方法中添加对应的占位符映射
4. 在 `Validate` 方法中添加验证逻辑（如需要）

### 扩展到其他模块
参数录入窗体 `ParameterInputForm` 是通用设计，可以轻松扩展到其他模块：
1. 创建对应模块的参数模型
2. 使用 `ParameterInputForm` 创建设置窗体
3. 在模块生成器中集成参数替换逻辑

## 📞 技术支持

如果在使用过程中遇到问题，请检查：
1. 配置文件是否存在且格式正确
2. 应用程序是否有足够的文件系统权限
3. 模板文件是否包含正确的占位符
4. 参数是否通过验证

更多技术细节请参考源代码注释和开发文档。
