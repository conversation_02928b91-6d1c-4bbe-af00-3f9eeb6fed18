using System;

namespace RESClient.MVP.Interfaces
{
    /// <summary>
    /// 视图接口基类，所有视图接口都应继承此接口
    /// </summary>
    public interface IView
    {
        /// <summary>
        /// 显示消息
        /// </summary>
        /// <param name="message">消息内容</param>
        /// <param name="title">消息标题</param>
        void ShowMessage(string message, string title);
        
        /// <summary>
        /// 显示错误消息
        /// </summary>
        /// <param name="message">错误消息内容</param>
        /// <param name="title">错误消息标题</param>
        void ShowError(string message, string title);
    }
} 