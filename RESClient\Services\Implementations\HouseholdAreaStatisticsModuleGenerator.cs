using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using NPOI.SS.UserModel;
using NPOI.XWPF.UserModel;
using System.Text.RegularExpressions;
using RESClient.Services;

namespace RESClient.Services.Implementations
{
    /// <summary>
    /// 房产分户面积统计表模块生成器
    /// </summary>
    public class HouseholdAreaStatisticsModuleGenerator : IReportModuleGenerator
    {
        private ArchiveExtractionService _archiveExtractionService;

        /// <summary>
        /// 模块名称
        /// </summary>
        public string ModuleName => "房产分户面积统计表";

        /// <summary>
        /// 生成报告模块
        /// </summary>
        /// <param name="parameters">生成参数</param>
        /// <param name="progressCallback">进度回调</param>
        /// <returns>生成结果</returns>
        public async Task<bool> GenerateAsync(Dictionary<string, object> parameters, Action<int, string> progressCallback)
        {
            _archiveExtractionService = new ArchiveExtractionService();

            try
            {
                // 记录开始进度
                progressCallback?.Invoke(0, $"[INFO]开始生成{ModuleName}...");

                // 1. 获取选择的数据路径
                if (!parameters.TryGetValue("DataFolder", out object dataDirectoryObj) || dataDirectoryObj == null)
                {
                    progressCallback?.Invoke(0, "[ERROR]未找到数据目录");
                    return false;
                }

                string dataDirectory = dataDirectoryObj.ToString();
                if (!Directory.Exists(dataDirectory))
                {
                    progressCallback?.Invoke(0, $"[ERROR]数据目录不存在: {dataDirectory}");
                    return false;
                }

                progressCallback?.Invoke(5, "[INFO]查找CGB.xls文件...");

                // 2. 查找CGB.xls文件（从7z压缩包中提取）
                string excelFile = FindCGBFile(dataDirectory, progressCallback);
                if (string.IsNullOrEmpty(excelFile))
                {
                    progressCallback?.Invoke(25, "[ERROR]未找到CGB.xls文件");
                    return false;
                }

                progressCallback?.Invoke(30, $"[INFO]找到CGB文件: {Path.GetFileName(excelFile)}");

                // 3. 读取Excel数据
                progressCallback?.Invoke(30, "[INFO]读取Excel数据...");
                bool hasFullCountFourthColumn = false;
                bool hasHalfCountFourthColumn = false;
                var buildingData = ReadExcelData(excelFile, progressCallback, out hasFullCountFourthColumn, out hasHalfCountFourthColumn);
                if (buildingData == null || buildingData.Count == 0)
                {
                    progressCallback?.Invoke(30, "[ERROR]未能读取到有效数据");
                    return false;
                }

                progressCallback?.Invoke(50, $"[SUCCESS]成功读取{buildingData.Count}栋楼的房产分户面积统计数据");
                
                // 提示用户第4列未被复制的信息
                if (hasFullCountFourthColumn || hasHalfCountFourthColumn)
                {
                    string message = "[WARNING]注意：";
                    if (hasFullCountFourthColumn)
                        message += "Excel中的全算第4列数据将被忽略，Word表格模板中没有对应列。";
                    if (hasHalfCountFourthColumn)
                        message += "Excel中的半算第4列数据将被忽略，Word表格模板中没有对应列。";
                    progressCallback?.Invoke(51, message);
                }

                // 4. 获取模板路径
                string templateDirectory = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "报告模板");
                string templatePath = Path.Combine(templateDirectory, "12_房产分户面积统计表", "房产分户面积统计表.docx");
                if (!File.Exists(templatePath))
                {
                    progressCallback?.Invoke(50, $"[ERROR]模板文件不存在: {templatePath}");
                    return false;
                }

                // 5. 获取输出目录
                if (!parameters.TryGetValue("OutputDir", out object outputDirectoryObj) || outputDirectoryObj == null)
                {
                    progressCallback?.Invoke(50, "[ERROR]未找到输出目录");
                    return false;
                }

                string outputDirectory = outputDirectoryObj.ToString();
                if (!Directory.Exists(outputDirectory))
                {
                    Directory.CreateDirectory(outputDirectory);
                }

                // 6. 生成Word文档
                progressCallback?.Invoke(60, "[INFO]生成Word文档...");
                string outputPath = Path.Combine(outputDirectory, $"{ModuleName}.docx");
                bool result = GenerateWordDocument(templatePath, outputPath, buildingData, progressCallback);

                progressCallback?.Invoke(100, result ? $"[SUCCESS]{ModuleName}生成完成" : $"[ERROR]{ModuleName}生成失败");
                return result;
            }
            catch (Exception ex)
            {
                progressCallback?.Invoke(100, $"[ERROR]{ModuleName}生成失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 查找CGB.xls文件（支持从7z压缩包中提取）
        /// </summary>
        /// <param name="directoryPath">目录路径</param>
        /// <param name="progressCallback">进度回调</param>
        /// <returns>文件路径</returns>
        private string FindCGBFile(string directoryPath, Action<int, string> progressCallback = null)
        {
            // 第一步：尝试查找包含4个指定工作表的Excel文件（最高优先级）
            progressCallback?.Invoke(5, "[INFO]查找包含标准工作表的Excel文件...");
            string standardExcelFile = FindStandardExcelFile(directoryPath, progressCallback);
            if (!string.IsNullOrEmpty(standardExcelFile))
            {
                progressCallback?.Invoke(25, "[INFO]找到包含标准工作表的Excel文件");
                return standardExcelFile;
            }

            // 第二步：尝试直接查找CGB.xls文件（向后兼容）
            progressCallback?.Invoke(10, "[INFO]查找CGB.xls文件...");
            var files = Directory.GetFiles(directoryPath, "*.xls*", SearchOption.AllDirectories)
                .Where(file => Path.GetFileName(file).ToLower().Contains("cgb"))
                .ToArray();

            if (files.Any())
            {
                progressCallback?.Invoke(25, "[INFO]找到直接的CGB.xls文件");
                return files[0];
            }

            // 第三步：如果没有找到直接的CGB文件，尝试从7z压缩包中提取
            progressCallback?.Invoke(15, "[INFO]未找到直接的CGB文件，尝试从成果包.7z中提取...");
            return _archiveExtractionService?.ExtractAndFindCGBFile(directoryPath, progressCallback);
        }

        /// <summary>
        /// 查找包含标准工作表的Excel文件
        /// </summary>
        /// <param name="directoryPath">目录路径</param>
        /// <param name="progressCallback">进度回调</param>
        /// <returns>符合条件的Excel文件路径，如果未找到则返回null</returns>
        private string FindStandardExcelFile(string directoryPath, Action<int, string> progressCallback = null)
        {
            try
            {
                // 查找所有Excel文件
                var excelFiles = Directory.GetFiles(directoryPath, "*.xls*", SearchOption.AllDirectories);

                progressCallback?.Invoke(7, $"[INFO]找到 {excelFiles.Length} 个Excel文件，开始验证工作表...");

                foreach (string filePath in excelFiles)
                {
                    try
                    {
                        if (ValidateExcelFileStructure(filePath))
                        {
                            progressCallback?.Invoke(20, $"[INFO]验证通过: {Path.GetFileName(filePath)}");
                            return filePath;
                        }
                    }
                    catch (Exception ex)
                    {
                        progressCallback?.Invoke(8, $"[WARNING]验证文件失败 {Path.GetFileName(filePath)}: {ex.Message}");
                        continue;
                    }
                }

                progressCallback?.Invoke(10, "[INFO]未找到包含标准工作表的Excel文件");
                return null;
            }
            catch (Exception ex)
            {
                progressCallback?.Invoke(10, $"[WARNING]查找标准Excel文件时出错: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 验证Excel文件是否包含4个指定的工作表
        /// </summary>
        /// <param name="filePath">Excel文件路径</param>
        /// <returns>是否符合条件</returns>
        private bool ValidateExcelFileStructure(string filePath)
        {
            // 定义必需的工作表名称
            var requiredSheets = new HashSet<string>
            {
                "房屋建筑面积总表",
                "共有建筑面积的分摊",
                "房产分户面积统计表",
                "楼盘表信息"
            };

            try
            {
                using (var stream = new FileStream(filePath, FileMode.Open, FileAccess.Read, FileShare.ReadWrite))
                {
                    IWorkbook workbook = WorkbookFactory.Create(stream);

                    // 检查工作表数量是否为4
                    if (workbook.NumberOfSheets != 4)
                    {
                        return false;
                    }

                    // 检查工作表名称是否完全匹配
                    var actualSheets = new HashSet<string>();
                    for (int i = 0; i < workbook.NumberOfSheets; i++)
                    {
                        actualSheets.Add(workbook.GetSheetName(i));
                    }

                    // 必须完全匹配所有4个工作表名称
                    return requiredSheets.SetEquals(actualSheets);
                }
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 读取Excel数据
        /// </summary>
        /// <param name="filePath">Excel文件路径</param>
        /// <param name="progressCallback">进度回调</param>
        /// <param name="hasFullCountFourthColumn">输出参数：是否存在全算第四列</param>
        /// <param name="hasHalfCountFourthColumn">输出参数：是否存在半算第四列</param>
        /// <returns>按栋号分组的数据行</returns>
        private Dictionary<string, List<Dictionary<string, string>>> ReadExcelData(string filePath, Action<int, string> progressCallback, out bool hasFullCountFourthColumn, out bool hasHalfCountFourthColumn)
        {
            var buildingData = new Dictionary<string, List<Dictionary<string, string>>>();
            hasFullCountFourthColumn = false;
            hasHalfCountFourthColumn = false;
            
            try
            {
                if (string.IsNullOrWhiteSpace(filePath) || !File.Exists(filePath))
                {
                    progressCallback?.Invoke(10, $"[ERROR]Excel文件不存在：{filePath}");
                    return buildingData;
                }

                using (var stream = new FileStream(filePath, FileMode.Open, FileAccess.Read, FileShare.ReadWrite))
                {
                    progressCallback?.Invoke(10, "[INFO]正在打开Excel文件...");
                    IWorkbook workbook = WorkbookFactory.Create(stream);
                    
                    ISheet worksheet = FindSheet(workbook, "房产分户面积统计表");
                    
                    if (worksheet == null)
                    {
                        progressCallback?.Invoke(20, "[ERROR]未找到房产分户面积统计表工作表");
                        return buildingData;
                    }

                    // 查找表头行
                    int headerRowIndex = -1;
                    for (int i = 0; i <= worksheet.LastRowNum && i < 20; i++)
                    {
                        var row = worksheet.GetRow(i);
                        if (row == null) continue;
                        
                        if (IsHeaderRow(row))
                        {
                            headerRowIndex = i;
                            break;
                        }
                    }
                    
                    if (headerRowIndex == -1)
                    {
                        progressCallback?.Invoke(30, "[WARNING]未找到表头行，将使用默认映射");
                        headerRowIndex = 0; // Fallback
                    }

                    Dictionary<int, string> columnMapping;
                    
                    // 根据功能区列的位置决定映射
                    IRow headerRow = worksheet.GetRow(headerRowIndex);
                    string functionAreaCellText = headerRow?.GetCell(18)?.StringCellValue ?? "";

                    if (functionAreaCellText.Contains("功能区"))
                    {
                        progressCallback?.Invoke(25, "[INFO]检测到19列格式（全算/半算各3列），应用新映射。");
                        columnMapping = new Dictionary<int, string>
                        {
                            { 0, "房屋唯一号" },
                            { 1, "栋号" },
                            { 2, "单元" },
                            { 3, "楼层" },
                            { 4, "房号" },
                            { 5, "建筑面积" },
                            { 6, "套内面积" },
                            { 7, "分摊面积" },
                            { 8, "套内使用、墙体面积" },
                            { 9, "全算_1" },
                            { 10, "全算_2" },
                            { 11, "全算_3" },
                            { 12, "半算_1" },
                            { 13, "半算_2" },
                            { 14, "半算_3" },
                            { 15, "套数" },
                            { 16, "分摊系数" },
                            { 17, "设计用途及说明" },
                            { 18, "功能区" }
                        };
                        hasFullCountFourthColumn = false;
                        hasHalfCountFourthColumn = false;
                    }
                    else
                    {
                        progressCallback?.Invoke(25, "[INFO]使用默认21列格式（全算/半算各4列）映射。");
                        columnMapping = new Dictionary<int, string>
                        {
                            { 0, "房屋唯一号" },
                            { 1, "栋号" },
                            { 2, "单元" },
                            { 3, "楼层" },
                            { 4, "房号" },
                            { 5, "建筑面积" },
                            { 6, "套内面积" },
                            { 7, "分摊面积" },
                            { 8, "套内使用、墙体面积" },
                            { 9, "全算_1" },
                            { 10, "全算_2" },
                            { 11, "全算_3" },
                            { 12, "全算_4" },
                            { 13, "半算_1" },
                            { 14, "半算_2" },
                            { 15, "半算_3" },
                            { 16, "半算_4" },
                            { 17, "套数" },
                            { 18, "分摊系数" },
                            { 19, "设计用途及说明" },
                            { 20, "功能区" }
                        };
                        hasFullCountFourthColumn = true;
                        hasHalfCountFourthColumn = true;
                    }
                    
                    // 确定数据开始行（通常是表头+3）
                    int dataStartRow = headerRowIndex + 3;
                    
                    // 读取所有数据行
                    string currentBuilding = ""; // 当前栋号
                    string lastBuildingNumber = ""; // 上一行的栋号，用于合计行
                    
                    for (int rowIndex = dataStartRow; rowIndex <= worksheet.LastRowNum; rowIndex++)
                    {
                        IRow row = worksheet.GetRow(rowIndex);
                        if (row == null) continue;
                        
                        // 跳过空行
                        if (IsEmptyRow(row)) continue;
                        
                        // 跳过表头行
                        if (IsHeaderRow(row))
                        {
                            //progressCallback?.Invoke(40, $"跳过表头行：{rowIndex + 1}");
                            continue;
                        }
                        
                        // 使用固定映射获取数据
                        var rowData = new Dictionary<string, string>(StringComparer.OrdinalIgnoreCase);
                        bool hasData = false;
                        
                        // 从每列读取数据
                        foreach (var mapping in columnMapping)
                        {
                            int colIndex = mapping.Key;
                            string fieldName = mapping.Value;
                            
                            string cellValue = GetCellValueAsString(row.GetCell(colIndex));
                            rowData[fieldName] = cellValue;
                            
                            if (!string.IsNullOrWhiteSpace(cellValue))
                                hasData = true;
                        }
                        
                        if (!hasData) continue; // 跳过完全空的行
                        
                        // 获取房号和栋号
                        string uniqueID = rowData["房屋唯一号"];
                        string buildingNumber = rowData["栋号"];
                        
                        // 如果唯一ID和栋号都为空，则跳过该行
                        if (string.IsNullOrWhiteSpace(uniqueID) && string.IsNullOrWhiteSpace(buildingNumber))
                        {
                            continue;
                        }
                        
                        // 检查是否是合计行. 移除空格并检查"房屋唯一号"或"栋号"列
                        bool isSummaryRow = (uniqueID?.Replace(" ", "").Replace("　", "") == "合计") ||
                                           (buildingNumber?.Replace(" ", "").Replace("　", "") == "合计");
                        
                        // 新增：如果一行不是合计行，但又缺少关键数据（如房号或建筑面积），则视其为无效的、类似表头的行，并跳过
                        if (!isSummaryRow && string.IsNullOrWhiteSpace(rowData["房号"]) && string.IsNullOrWhiteSpace(rowData["建筑面积"]))
                        {
                            progressCallback?.Invoke(40, $"[DEBUG] 跳过第 {rowIndex + 1} 行，因为它不是合计行且缺少房号/建筑面积等关键数据。");
                            continue;
                        }
                        
                        if (isSummaryRow)
                        {
                            rowData["IsSummaryRow"] = "true"; // Add a flag
                            progressCallback?.Invoke(40, $"[DEBUG]在第 {rowIndex + 1} 行找到合计行。");
                        }
                        
                        // 处理栋号
                        // 1. 如果是表头文本，跳过
                        if (IsHeaderText(buildingNumber) || buildingNumber.Contains("栋号"))
                        {
                            //progressCallback?.Invoke(45, $"跳过含表头文本的行：{rowIndex + 1}");
                            continue;
                        }
                        
                        // 2. 验证栋号有效性
                        if (!string.IsNullOrWhiteSpace(buildingNumber))
                        {
                            if (!IsValidBuildingNumber(buildingNumber))
                            {
                                // 如果栋号文本无效（例如 "合计"），但它是一个合计行，我们不应该跳过它
                                if (!isSummaryRow)
                                {
                                    // progressCallback?.Invoke(45, $"无效栋号：{buildingNumber}，行：{rowIndex + 1}");
                                    continue;
                                }
                            }
                            
                            // 如果栋号有效，更新当前栋号和上一行的栋号
                            if (buildingNumber != currentBuilding)
                            {
                                // 关键：如果当前行是合计行，则不更新 currentBuilding 上下文
                                if (!isSummaryRow)
                                {
                                    lastBuildingNumber = currentBuilding; // 保存上一个栋号
                                    currentBuilding = buildingNumber;
                                    // progressCallback?.Invoke(50, $"处理栋号：{buildingNumber}");
                                }
                            }
                        }
                        // 3. 如果栋号为空或当前就是合计行，使用上一行的栋号进行归属
                        if (string.IsNullOrWhiteSpace(buildingNumber) || isSummaryRow)
                        {
                            if (!string.IsNullOrWhiteSpace(lastBuildingNumber))
                            {
                                buildingNumber = lastBuildingNumber;
                                rowData["栋号"] = buildingNumber;
                                progressCallback?.Invoke(50, $"[DEBUG]合计行使用上一行栋号：{buildingNumber}");
                            }
                            else if (!string.IsNullOrWhiteSpace(currentBuilding))
                            {
                                buildingNumber = currentBuilding;
                                rowData["栋号"] = buildingNumber;
                                progressCallback?.Invoke(50, $"[DEBUG]合计行使用当前栋号：{buildingNumber}");
                            }
                            else
                            {
                                progressCallback?.Invoke(50, $"[WARNING]跳过无栋号合计行：{rowIndex + 1}");
                                continue;
                            }
                        }
                        // 4. 如果栋号仍为空，跳过此行
                        else if (string.IsNullOrWhiteSpace(buildingNumber))
                        {
                            // progressCallback?.Invoke(50, $"跳过无栋号行：{rowIndex + 1}");
                            continue;
                        }
                        
                        // 非合计行，更新lastBuildingNumber
                        if (!isSummaryRow)
                        {
                            lastBuildingNumber = buildingNumber;
                        }
                        
                        // 添加到数据集合
                        if (!buildingData.ContainsKey(buildingNumber))
                        {
                            buildingData[buildingNumber] = new List<Dictionary<string, string>>();
                        }
                        
                        buildingData[buildingNumber].Add(rowData);

                        if (isSummaryRow)
                        {
                            progressCallback?.Invoke(55, $"[DEBUG]已保存合计行数据到栋号 '{buildingNumber}' 的数据集合中。");
                        }
                        
                        // 每50行输出一次进度
                        // if (rowIndex % 50 == 0)
                        // {
                        //     progressCallback?.Invoke(55, $"已处理 {buildingData.Values.Sum(list => list.Count)} 行数据");
                        // }
                    }
                    
                    progressCallback?.Invoke(60, $"[SUCCESS]数据读取完成，共{buildingData.Values.Sum(list => list.Count)}行，{buildingData.Count}栋");
                }
            }
            catch (Exception ex)
            {
                progressCallback?.Invoke(10, $"[ERROR]读取Excel出错: {ex.Message}");
            }
            
            return buildingData;
        }

        /// <summary>
        /// 查找工作表
        /// </summary>
        /// <param name="workbook">Excel工作簿</param>
        /// <param name="sheetName">工作表名</param>
        /// <returns>工作表</returns>
        private ISheet FindSheet(IWorkbook workbook, string sheetName)
        {
            // 首先尝试通过名称直接查找工作表
            ISheet sheet = workbook.GetSheet(sheetName);
            if (sheet != null) return sheet;

            // 其次尝试通过包含名称查找
            for (int i = 0; i < workbook.NumberOfSheets; i++)
            {
                if (workbook.GetSheetName(i).Contains(sheetName))
                {
                    return workbook.GetSheetAt(i);
                }
            }
            
            // 如果通过名称未找到，检查每个工作表的内容
            for (int i = 0; i < workbook.NumberOfSheets; i++)
            {
                ISheet currentSheet = workbook.GetSheetAt(i);
                for (int rowIndex = 0; rowIndex <= Math.Min(currentSheet.LastRowNum, 5); rowIndex++)
                {
                    IRow row = currentSheet.GetRow(rowIndex);
                    if (row == null) continue;
                    for (int colIndex = 0; colIndex < Math.Min((int)row.LastCellNum, (int)10); colIndex++)
                    {
                        NPOI.SS.UserModel.ICell cell = row.GetCell(colIndex);
                        if (cell != null)
                        {
                            string cellText = GetCellValueAsString(cell);
                            if (cellText.Contains("房产分户面积统计表") || cellText.Contains("房产分户") || cellText.Contains("面积统计"))
                            {
                                return currentSheet;
                            }
                        }
                    }
                }
            }
            
            // 如果没有找到匹配的工作表，返回第一个工作表（如果存在）
            return workbook.NumberOfSheets > 0 ? workbook.GetSheetAt(0) : null;
        }

        /// <summary>
        /// 获取单元格的字符串值
        /// </summary>
        /// <param name="cell">单元格</param>
        /// <returns>字符串值</returns>
        private string GetCellValueAsString(NPOI.SS.UserModel.ICell cell)
        {
            if (cell == null)
                return string.Empty;

            switch (cell.CellType)
            {
                case CellType.String:
                    return cell.StringCellValue;
                case CellType.Numeric:
                    if (DateUtil.IsCellDateFormatted(cell))
                    {
                        DateTime? dateTimeValue = cell.DateCellValue;
                        if (dateTimeValue.HasValue)
                        {
                            DateTime dateValue = dateTimeValue.Value;
                            return dateValue.Year + "-" + dateValue.Month.ToString("00") + "-" + dateValue.Day.ToString("00");
                        }
                        else
                        {
                            return string.Empty;
                        }
                    }
                    return cell.NumericCellValue.ToString();
                case CellType.Boolean:
                    return cell.BooleanCellValue.ToString();
                case CellType.Formula:
                    try
                    {
                        // 尝试获取公式计算后的数值结果
                        return cell.NumericCellValue.ToString();
                    }
                    catch
                    {
                        try
                        {
                            // 如果数值获取失败，尝试获取字符串结果
                            return cell.StringCellValue;
                        }
                        catch
                        {
                            return string.Empty;
                        }
                    }
                default:
                    return string.Empty;
            }
        }

        /// <summary>
        /// 生成Word文档
        /// </summary>
        /// <param name="templatePath">模板路径</param>
        /// <param name="outputPath">输出路径</param>
        /// <param name="buildingData">楼栋数据</param>
        /// <param name="progressCallback">进度回调</param>
        /// <returns>是否成功</returns>
        private bool GenerateWordDocument(string templatePath, string outputPath, Dictionary<string, List<Dictionary<string, string>>> buildingData, Action<int, string> progressCallback)
        {
            string tempPath = Path.Combine(Path.GetDirectoryName(outputPath), $"temp_{Guid.NewGuid()}.docx");
            
            try
            {
                // -- Begin: Load basement template table --
                XWPFTable basementTemplateTable = null;
                string basementTemplatePath = Path.Combine(Path.GetDirectoryName(templatePath), "房产分户面积统计表-地下室.docx");

                if (File.Exists(basementTemplatePath))
                {
                    try
                    {
                        using (var fs = new FileStream(basementTemplatePath, FileMode.Open, FileAccess.Read, FileShare.ReadWrite))
                        using (var basementDoc = new XWPFDocument(fs))
                        {
                            if (basementDoc.Tables.Count > 0)
                            {
                                basementTemplateTable = basementDoc.Tables[0];
                                progressCallback?.Invoke(65, "[INFO]成功加载\"地下室\"报告模块的表格模板。");
                            }
                            else
                            {
                                progressCallback?.Invoke(65, "[WARNING]\"地下室\"报告模块的模板文件不包含表格。");
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        progressCallback?.Invoke(65, $"[WARNING]加载\"地下室\"模板失败: {ex.Message}。将使用默认模板。");
                    }
                }
                else
                {
                    progressCallback?.Invoke(65, "[INFO]未找到\"地下室\"报告模块的表格模板，所有栋号将使用默认模板。");
                }
                // -- End: Load basement template table --

                // 复制模板文件到临时文件
                File.Copy(templatePath, tempPath, true);

                using (var fileStream = new FileStream(tempPath, FileMode.Open, FileAccess.ReadWrite))
                {
                    using (var document = new XWPFDocument(fileStream))
                    {
                        progressCallback?.Invoke(70, $"[INFO]处理 {buildingData.Count} 个栋数据");

                        // 查找模板中的表格和段落
                        if (document.Tables.Count == 0)
                        {
                            progressCallback?.Invoke(70, "[ERROR]错误：模板中没有找到表格");
                            return false;
                        }

                        // 使用第一个表格作为模板
                        var templateTable = document.Tables[0];
                        var templateParagraph = FindBuildingParagraph(document);

                        // 记录处理的栋数
                        int buildingCount = 0;

                        // 为每栋数据创建表格
                        foreach (var buildingPair in buildingData)
                        {
                            string buildingNumber = buildingPair.Key;
                            var rows = buildingPair.Value;

                            // -- Begin: Select table template based on building number --
                            var sourceTableForCopy = templateTable; // Default
                            if (buildingNumber.Contains("地下室") && basementTemplateTable != null)
                            {
                                sourceTableForCopy = basementTemplateTable;
                                progressCallback?.Invoke(75, $"[INFO]栋号 '{buildingNumber}' 将使用\"地下室\"模板表格。");
                            }
                            // -- End: Select table template based on building number --

                            // 第一栋特殊处理
                            if (buildingCount == 0)
                            {
                                // 替换模板中的${栋号}占位符
                                if (templateParagraph != null)
                                {
                                    ReplacePlaceholderInParagraph(templateParagraph, "${栋号}", buildingNumber);
                                }
                                
                                // 保存模板表格的引用后，从文档中删除它
                                int tablePos = document.GetPosOfTable(templateTable);
                                document.RemoveBodyElement(tablePos);
                                
                                // 检查并删除可能存在的空段落
                                if (tablePos < document.BodyElements.Count && 
                                    document.BodyElements[tablePos] is XWPFParagraph emptyPara &&
                                    string.IsNullOrWhiteSpace(emptyPara.Text))
                                {
                                    document.RemoveBodyElement(tablePos);
                                }
                            }
                            else
                            {
                                // 添加分隔段落
                                document.CreateParagraph().CreateRun().AddBreak(BreakType.PAGE);

                                // 添加栋号段落
                                var newBuildingParagraph = document.CreateParagraph();
                                if (templateParagraph != null)
                                {
                                    // 复制段落样式
                                    CopyParagraphStyle(templateParagraph, newBuildingParagraph);
                                    // 设置栋号内容
                                    XWPFRun run = newBuildingParagraph.CreateRun();
                                    run.SetText($"栋号：{buildingNumber}");
                                    run.IsBold = true;
                                    run.FontFamily = "宋体"; // 设置字体为宋体
                                    run.FontSize = 12;
                                }
                                else
                                {
                                    // 如果找不到模板段落，创建默认样式
                                    XWPFRun run = newBuildingParagraph.CreateRun();
                                    run.SetText($"栋号：{buildingNumber}");
                                    run.IsBold = true;
                                    run.FontFamily = "宋体"; // 设置字体为宋体
                                    run.FontSize = 12;
                                    newBuildingParagraph.Alignment = ParagraphAlignment.CENTER;
                                }
                            }

                            // 创建新表格并复制模板表格的所有属性 - 所有栋都使用相同的逻辑
                            var newTable = document.CreateTable();
                            CopyEntireTable(sourceTableForCopy, newTable);

                            // 填充数据
                            FillTable(newTable, rows, progressCallback);

                            buildingCount++;
                        }
                        
                        // 保存到最终输出文件
                        using (var outStream = new FileStream(outputPath, FileMode.Create, FileAccess.Write))
                        {
                            document.Write(outStream);
                        }
                    }
                }
                
                progressCallback?.Invoke(95, $"[SUCCESS]成功生成 {buildingData.Count} 个栋数据");
                return true;
            }
            catch (Exception ex)
            {
                progressCallback?.Invoke(80, $"[ERROR]生成Word文档失败: {ex.Message}");
                return false;
            }
            finally
            {
                // 确保删除临时文件
                try { if (File.Exists(tempPath)) File.Delete(tempPath); } catch { }
            }
        }

        /// <summary>
        /// 查找栋号段落
        /// </summary>
        /// <param name="document">Word文档</param>
        /// <returns>栋号段落</returns>
        private XWPFParagraph FindBuildingParagraph(XWPFDocument document)
        {
            foreach (var para in document.Paragraphs)
            {
                if (para.Text.Contains("栋号") || para.Text.Contains("${栋号}"))
                {
                    return para;
                }
            }
            return null;
        }
        
        /// <summary>
        /// 替换段落中的占位符
        /// </summary>
        /// <param name="paragraph">段落</param>
        /// <param name="placeholder">占位符</param>
        /// <param name="value">替换值</param>
        private void ReplacePlaceholderInParagraph(XWPFParagraph paragraph, string placeholder, string value)
        {
            if (paragraph.Text.Contains(placeholder))
            {
                string replacedText = paragraph.Text.Replace(placeholder, value);
                
                // 保存第一个Run的格式
                XWPFRun templateRun = paragraph.Runs.FirstOrDefault();

                // 清空段落中的所有Run
                while (paragraph.Runs.Count > 0)
                {
                    paragraph.RemoveRun(0);
                }

                // 创建新的Run并应用格式
                XWPFRun newRun = paragraph.CreateRun();
                newRun.SetText(replacedText);
                
                // 设置字体为宋体
                newRun.FontFamily = "宋体";
                
                // 保留其他格式
                if (templateRun != null)
                {
                    newRun.IsBold = templateRun.IsBold;
                    if (templateRun.FontSize > 0)
                    {
                        newRun.FontSize = templateRun.FontSize;
                    }
                }
            }
        }
        
        /// <summary>
        /// 复制段落样式
        /// </summary>
        /// <param name="source">源段落</param>
        /// <param name="target">目标段落</param>
        private void CopyParagraphStyle(XWPFParagraph source, XWPFParagraph target)
        {
            target.Alignment = source.Alignment;
            target.SpacingBefore = source.SpacingBefore;
            target.SpacingAfter = source.SpacingAfter;
            target.SpacingBetween = source.SpacingBetween;
            
            if (source.IsWordWrapped)
                target.IsWordWrapped = source.IsWordWrapped;
        }
        
        /// <summary>
        /// 完整复制表格，包括所有表头和结构
        /// </summary>
        /// <param name="source">源表格</param>
        /// <param name="target">目标表格</param>
        private void CopyEntireTable(XWPFTable source, XWPFTable target)
        {
            // 复制表格属性
            if (source.Width > 0) target.Width = source.Width;
            
            // 复制表格级别的样式属性
            if (source.GetCTTbl().tblPr != null)
            {
                if (target.GetCTTbl().tblPr == null)
                {
                    target.GetCTTbl().AddNewTblPr();
                }
                
                // 复制表格对齐方式
                if (source.GetCTTbl().tblPr.jc != null)
                {
                    target.GetCTTbl().tblPr.jc = source.GetCTTbl().tblPr.jc;
                }
                
                // 复制表格边框
                if (source.GetCTTbl().tblPr.tblBorders != null)
                {
                    target.GetCTTbl().tblPr.tblBorders = source.GetCTTbl().tblPr.tblBorders;
                }
                
                // 复制表格宽度设置
                if (source.GetCTTbl().tblPr.tblW != null)
                {
                    target.GetCTTbl().tblPr.tblW = source.GetCTTbl().tblPr.tblW;
                }
            }
            
            // 删除目标表格中默认创建的行
            while (target.NumberOfRows > 0)
            {
                target.RemoveRow(0);
            }
            
            // 复制所有行，包括表头
            for (int rowIdx = 0; rowIdx < source.NumberOfRows; rowIdx++)
            {
                XWPFTableRow sourceRow = source.GetRow(rowIdx);
                XWPFTableRow targetRow = target.CreateRow();
                
                // 复制行高和其他行级属性
                targetRow.Height = sourceRow.Height;
                
                // 复制行属性
                if (sourceRow.GetCTRow().trPr != null)
                {
                    targetRow.GetCTRow().trPr = sourceRow.GetCTRow().trPr;
                }
                
                // 复制所有单元格
                for (int cellIdx = 0; cellIdx < sourceRow.GetTableCells().Count; cellIdx++)
                {
                    XWPFTableCell sourceCell = sourceRow.GetCell(cellIdx);
                    XWPFTableCell targetCell;
                    
                    // 创建或获取单元格
                    if (cellIdx < targetRow.GetTableCells().Count)
                        targetCell = targetRow.GetCell(cellIdx);
                    else
                        targetCell = targetRow.CreateCell();
                    
                    // 复制单元格属性
                    CopyCellProperties(sourceCell, targetCell);
                    
                    // 复制单元格内容和格式
                    CopyCellContent(sourceCell, targetCell);
                }
            }
            
            // 复制表格网格列宽
            if (source.GetCTTbl().tblGrid != null)
            {
                target.GetCTTbl().tblGrid = source.GetCTTbl().tblGrid;
            }
        }
        
        /// <summary>
        /// 复制单元格属性
        /// </summary>
        /// <param name="source">源单元格</param>
        /// <param name="target">目标单元格</param>
        private void CopyCellProperties(XWPFTableCell source, XWPFTableCell target)
        {
            if (source.GetCTTc().tcPr != null)
            {
                // 确保目标单元格有tcPr
                if (target.GetCTTc().tcPr == null)
                {
                    target.GetCTTc().AddNewTcPr();
                }
                
                // 复制完整的单元格属性
                target.GetCTTc().tcPr = source.GetCTTc().tcPr;
            }
            
            // 设置背景色
            target.SetColor(source.GetColor());
            
            // 设置垂直对齐
            if (source.GetVerticalAlignment() != null)
            {
                target.SetVerticalAlignment(source.GetVerticalAlignment().Value);
            }
        }
        
        /// <summary>
        /// 复制单元格内容
        /// </summary>
        /// <param name="source">源单元格</param>
        /// <param name="target">目标单元格</param>
        private void CopyCellContent(XWPFTableCell source, XWPFTableCell target)
        {
            // 清空目标单元格内容
            while (target.Paragraphs.Count > 0)
            {
                target.RemoveParagraph(0);
            }
            
            // 复制所有段落
            foreach (var sourceParagraph in source.Paragraphs)
            {
                XWPFParagraph targetParagraph = target.AddParagraph();
                
                // 复制段落属性
                if (sourceParagraph.GetCTP().pPr != null)
                {
                    targetParagraph.GetCTP().pPr = sourceParagraph.GetCTP().pPr;
                }
                
                // 复制所有文本运行
                foreach (var sourceRun in sourceParagraph.Runs)
                {
                    XWPFRun targetRun = targetParagraph.CreateRun();
                    
                    // 复制文本内容
                    targetRun.SetText(sourceRun.Text ?? string.Empty);
                    
                    // 复制运行属性
                    if (sourceRun.GetCTR().rPr != null)
                    {
                        targetRun.GetCTR().rPr = sourceRun.GetCTR().rPr;
                    }
                    
                    // 复制基本格式
                    targetRun.IsBold = sourceRun.IsBold;
                    targetRun.IsItalic = sourceRun.IsItalic;
                    targetRun.FontFamily = sourceRun.FontFamily;
                    if (sourceRun.FontSize > 0)
                    {
                        targetRun.FontSize = sourceRun.FontSize;
                    }
                    targetRun.SetColor(sourceRun.GetColor());
                    targetRun.Underline = sourceRun.Underline;
                }
            }
        }
        
        /// <summary>
        /// 填充表格数据
        /// </summary>
        /// <param name="table">表格</param>
        /// <param name="rows">数据行</param>
        /// <param name="progressCallback">进度回调</param>
        private void FillTable(XWPFTable table, List<Dictionary<string, string>> rows, Action<int, string> progressCallback)
        {
            try
            {
                if (table == null || rows == null || rows.Count == 0)
                {
                    return;
                }
                
                // 验证表格有足够的行
                if (table.NumberOfRows < 4)
                {
                    progressCallback?.Invoke(80, "[ERROR]表格模板需要至少4行（3行表头和1行数据模板）。");
                    return;
                }

                // 首先检查数据中是否包含半算_2列
                bool hasHalfCount2 = false;
                foreach (var row in rows)
                {
                    if (row.ContainsKey("半算_2"))
                    {
                        hasHalfCount2 = true;
                        // progressCallback?.Invoke(80, $"数据中存在半算_2列，值为: '{row["半算_2"]}'");
                        break;
                    }
                }
                
                if (!hasHalfCount2)
                {
                    progressCallback?.Invoke(80, "[WARNING]警告：数据中不包含半算_2列，请检查Excel数据提取是否正确");
                }

                //progressCallback?.Invoke(81, "分析Word表格表头结构...");
                
                // 第四行为数据模板行
                XWPFTableRow templateRow = table.GetRow(3);
                if (templateRow == null)
                {
                    progressCallback?.Invoke(81, "[ERROR]未找到数据模板行（第4行），无法继续。");
                    return;
                }
                
                // 使用用户提供的固定列映射，从1开始计数需要调整为从0开始的索引
                Dictionary<string, int> fixedColumnMapping = new Dictionary<string, int>
                {
                    { "房屋唯一号", 0 },  // Excel列1，不输出到Word
                    { "栋号", 0 },        // Excel列2，Word列1
                    { "单元", 1 },        // Excel列3，Word列2
                    { "楼层", 2 },        // Excel列4，Word列3
                    { "房号", 3 },        // Excel列5，Word列4
                    { "建筑面积", 4 },    // Excel列6，Word列5
                    { "套内面积", 5 },    // Excel列7，Word列6
                    { "分摊面积", 6 },    // Excel列8，Word列7
                    { "公摊面积", 6 },    // Excel列8，Word列7
                    { "套内使用、墙体面积", 7 }, // Excel列9，Word列8
                    { "套数", 14 },       // Excel列18，Word列15
                    { "分摊系数", 15 },   // Excel列19，Word列16
                    { "设计用途及说明", 16 }, // Excel列20，Word列17
                    { "功能区", -1 }      // Excel列21，Word中未指定
                };
                
                // 定义全算和半算列的固定索引，从1开始计数需要调整为从0开始的索引
                Dictionary<string, int> fixedFullCountColumns = new Dictionary<string, int>
                {
                    { "全算_1", 8 },  // Excel列10，Word列9
                    { "全算_2", 9 },  // Excel列11，Word列10
                    { "全算_3", 10 }  // Excel列12，Word列11
                    // Excel列13(全算_4)在Word中未指定
                };
                
                Dictionary<string, int> fixedHalfCountColumns = new Dictionary<string, int>
                {
                    { "半算_1", 11 }, // Excel列14，Word列12
                    { "半算_2", 12 }, // Excel列15，Word列13
                    { "半算_3", 13 }  // Excel列16，Word列14
                    // Excel列17(半算_4)在Word中未指定
                };

                // 输出映射信息
                // progressCallback?.Invoke(82, $"半算_1映射到列索引：{fixedHalfCountColumns["半算_1"]}");
                // progressCallback?.Invoke(82, $"半算_2映射到列索引：{fixedHalfCountColumns["半算_2"]}");
                // progressCallback?.Invoke(82, $"半算_3映射到列索引：{fixedHalfCountColumns["半算_3"]}");
                
                // progressCallback?.Invoke(82, "使用固定列映射：常规字段17列，全算3列，半算3列");
                
                // 从第一行中提取表格列信息以便作为备用
                Dictionary<int, string[]> columnHeaders = new Dictionary<int, string[]>();
                Dictionary<string, int> dynamicFieldToColumnIndex = new Dictionary<string, int>(StringComparer.OrdinalIgnoreCase);
                Dictionary<string, int> dynamicFullCountColumns = new Dictionary<string, int>();
                Dictionary<string, int> dynamicHalfCountColumns = new Dictionary<string, int>();
                
                // 仅用于调试 - 分析前三行表头
                for (int headerRowIndex = 0; headerRowIndex < 3; headerRowIndex++)
                {
                    XWPFTableRow headerRow = table.GetRow(headerRowIndex);
                    if (headerRow == null) continue;
                    
                    for (int i = 0; i < headerRow.GetTableCells().Count; i++)
                    {
                        var cell = headerRow.GetCell(i);
                        if (cell == null || string.IsNullOrWhiteSpace(cell.GetText()))
                            continue;
                            
                        string headerText = cell.GetText().Trim();
                        
                        if (!columnHeaders.ContainsKey(i))
                            columnHeaders[i] = new string[3];
                            
                        columnHeaders[i][headerRowIndex] = headerText;
                        
                        // progressCallback?.Invoke(82, $"表头行{headerRowIndex+1}，列{i+1}：{headerText}");
                    }
                }
                
                // 验证表格的列数足够
                int maxNeededColumn = Math.Max(
                    Math.Max(
                        fixedColumnMapping.Values.Max(),
                        fixedFullCountColumns.Values.Max()),
                    fixedHalfCountColumns.Values.Max()) + 1;
                    
                int templateRowCellCount = templateRow.GetTableCells().Count;
                //progressCallback?.Invoke(83, $"表格有{templateRowCellCount}列，需要{maxNeededColumn}列");
                
                if (templateRowCellCount < maxNeededColumn)
                {
                    progressCallback?.Invoke(83, $"[WARNING]警告：表格列数不足，需要{maxNeededColumn}列，但只有{templateRowCellCount}列");
                }

                // 分离合计行和数据行
                var summaryData = rows.FirstOrDefault(r => r.ContainsKey("IsSummaryRow"));
                var dataRows = rows.Where(r => !r.ContainsKey("IsSummaryRow")).ToList();

                if (summaryData != null)
                {
                    // progressCallback?.Invoke(89, "[DEBUG]在当前栋数据中找到合计行数据。");
                }

                // 如果没有数据行，直接返回
                if (dataRows.Count == 0)
                {
                    progressCallback?.Invoke(90, "[WARNING]没有有效数据行可填充");
                    return;
                }

                //progressCallback?.Invoke(81, "分析Word表格表头结构...");
                
                // 第四行已经在上面获取为数据模板行 (templateRow)
                if (templateRow == null)
                {
                    progressCallback?.Invoke(81, "[ERROR]未找到数据模板行（第4行），无法继续。");
                    return;
                }
                
                // 使用用户提供的固定列映射，从1开始计数需要调整为从0开始的索引
                
                // 输出映射信息
                // progressCallback?.Invoke(82, $"半算_1映射到列索引：{fixedHalfCountColumns["半算_1"]}");
                // progressCallback?.Invoke(82, $"半算_2映射到列索引：{fixedHalfCountColumns["半算_2"]}");
                // progressCallback?.Invoke(82, $"半算_3映射到列索引：{fixedHalfCountColumns["半算_3"]}");
                
                // progressCallback?.Invoke(82, "使用固定列映射：常规字段17列，全算3列，半算3列");

                // 先清空模板行中的文本但保留格式
                ClearRowContent(templateRow);
                
                // 在填充数据前，保证表格只包含表头和模板行，删除多余的行
                while (table.NumberOfRows > 4)
                {
                    table.RemoveRow(4);
                }

                // 填充所有数据行
                for (int i = 0; i < dataRows.Count; i++)
                {
                    var rowData = dataRows[i];
                    XWPFTableRow tableRow;

                    if (i == 0)
                    {
                        // 第一行数据直接使用模板行
                        tableRow = templateRow;
                        // progressCallback?.Invoke(86, "填充第一行数据到模板行");
                    }
                    else
                    {
                        // 创建新行并应用模板行的格式
                        tableRow = table.CreateRow();
                        CopyRowFormat(templateRow, tableRow);
                    }
                    
                    if (tableRow == null) continue;

                    // 单独记录日志调试
                    if (i == 0)
                    {
                        foreach (var field in rowData.Where(r => !r.Key.StartsWith("全算_") && !r.Key.StartsWith("半算_")))
                        {
                            string fieldName = field.Key.Trim();
                            string value = field.Value?.Trim() ?? "";
                            
                            if (fixedColumnMapping.TryGetValue(fieldName, out int columnIndex))
                            {
                                // progressCallback?.Invoke(87, $"字段 '{fieldName}' (值: '{value}') 映射到固定列索引 {columnIndex}");
                            }
                            else
                            {
                                // progressCallback?.Invoke(87, $"未映射字段: '{fieldName}' (值: '{value}')");
                            }
                        }
                    }

                    // 填充常规单元格数据
                    FillRowData(tableRow, rowData, fixedColumnMapping, i == 0);
                    
                    // 特别处理全算和半算列
                    ProcessSpecialColumns(rowData, tableRow, "全算_", fixedFullCountColumns, i == 0, progressCallback);
                    ProcessSpecialColumns(rowData, tableRow, "半算_", fixedHalfCountColumns, i == 0, progressCallback);
                }

                // 添加合计行
                if (summaryData != null)
                {
                    // progressCallback?.Invoke(90, "[INFO]添加合计行到Word表格...");
                    XWPFTableRow summaryRow = table.CreateRow();
                    CopyRowFormat(templateRow, summaryRow);
                    
                    // 特别处理关键列的合计值
                    FillSummaryRowData(summaryRow, summaryData, fixedColumnMapping, progressCallback);
                    
                    // 特别处理合计行的全算和半算列
                    ProcessSpecialColumns(summaryData, summaryRow, "全算_", fixedFullCountColumns, false, progressCallback);
                    ProcessSpecialColumns(summaryData, summaryRow, "半算_", fixedHalfCountColumns, false, progressCallback);

                    // 在填充完所有数据后，强制将第一列设置为"合计"，以覆盖可能写入的栋号
                    var firstCell = summaryRow.GetCell(0);
                    if (firstCell != null)
                    {
                        SetCellText(firstCell, "合计");
                    }
                }
                
                //progressCallback?.Invoke(90, $"表格数据填充完成，共{dataRows.Count}行");
            }
            catch (Exception ex)
            {
                progressCallback?.Invoke(90, $"[ERROR]填充表格失败: {ex.Message}");
            }
        }
        
        /// <summary>
        /// 填充合计行数据，特别关注建筑面积、套内面积、公摊面积和套数列
        /// </summary>
        /// <param name="summaryRow">合计行</param>
        /// <param name="summaryData">合计数据</param>
        /// <param name="fieldToColumnIndex">字段到列索引的映射</param>
        /// <param name="progressCallback">进度回调</param>
        private void FillSummaryRowData(XWPFTableRow summaryRow, Dictionary<string, string> summaryData, 
            Dictionary<string, int> fieldToColumnIndex, Action<int, string> progressCallback)
        {
            // progressCallback?.Invoke(90, "[DEBUG]正在填充合计行...");
            // 关键字段及其可能的别名
            var keyFields = new Dictionary<string, string[]> {
                { "建筑面积", new[] { "建筑面积", "建筑面积合计", "总建筑面积" } },
                { "套内面积", new[] { "套内面积", "套内面积合计", "总套内面积" } },
                { "分摊面积", new[] { "分摊面积", "公摊面积", "分摊面积合计", "公摊面积合计" } },
                { "套数", new[] { "套数", "套数合计", "总套数" } }
            };

            foreach (var keyField in keyFields)
            {
                string normalizedName = keyField.Key;
                string[] possibleNames = keyField.Value;
                string value = null;

                // 按优先级从summaryData中查找值
                foreach (var name in possibleNames)
                {
                    if (summaryData.TryGetValue(name, out string fieldValue) && !string.IsNullOrWhiteSpace(fieldValue))
                    {
                        value = fieldValue.Trim();
                        // progressCallback?.Invoke(90, $"合计行: 找到{name}值: {value}");
                        break;
                    }
                }

                // 如果在映射中找到对应的列索引
                if (!string.IsNullOrEmpty(value) && fieldToColumnIndex.TryGetValue(normalizedName, out int columnIndex))
                {
                    EnsureCellExists(summaryRow, columnIndex);
                    SetCellText(summaryRow.GetCell(columnIndex), value);
                    // progressCallback?.Invoke(90, $"[DEBUG]合计行填充: {normalizedName} = '{value}' -> 列 {columnIndex}");
                }
                else if (normalizedName == "分摊面积" && !string.IsNullOrEmpty(value))
                {
                    // 特别处理公摊面积/分摊面积的映射
                    if (fieldToColumnIndex.TryGetValue("分摊面积", out int allocatedAreaColumnIndex))
                    {
                        EnsureCellExists(summaryRow, allocatedAreaColumnIndex);
                        SetCellText(summaryRow.GetCell(allocatedAreaColumnIndex), value);
                        // progressCallback?.Invoke(90, $"[DEBUG]合计行填充: 分摊面积 = '{value}' -> 列 {allocatedAreaColumnIndex}");
                    }
                    
                    if (fieldToColumnIndex.TryGetValue("公摊面积", out int publicAreaColumnIndex) && 
                        publicAreaColumnIndex != allocatedAreaColumnIndex)
                    {
                        EnsureCellExists(summaryRow, publicAreaColumnIndex);
                        SetCellText(summaryRow.GetCell(publicAreaColumnIndex), value);
                        // progressCallback?.Invoke(90, $"[DEBUG]合计行填充: 公摊面积 = '{value}' -> 列 {publicAreaColumnIndex}");
                    }
                }
            }
            
            // 额外处理其他列
            foreach (var field in summaryData)
            {
                string fieldName = field.Key.Trim();
                string value = field.Value?.Trim() ?? string.Empty;
                
                // 跳过已处理的关键字段和全算/半算列
                bool isAlreadyProcessed = false;
                foreach (var keyField in keyFields)
                {
                    if (keyField.Value.Contains(fieldName))
                    {
                        isAlreadyProcessed = true;
                        break;
                    }
                }
                
                if (isAlreadyProcessed || fieldName.StartsWith("全算_") || fieldName.StartsWith("半算_"))
                    continue;
                
                // 处理其他可能的字段
                if (fieldToColumnIndex.TryGetValue(fieldName, out int columnIndex))
                {
                    EnsureCellExists(summaryRow, columnIndex);
                    SetCellText(summaryRow.GetCell(columnIndex), value);
                }
            }
        }
        
        /// <summary>
        /// 清除行内容但保留格式
        /// </summary>
        /// <param name="row">表格行</param>
        private void ClearRowContent(XWPFTableRow row)
        {
            if (row == null) return;
            
            foreach (var cell in row.GetTableCells())
            {
                // 保留单元格但清空文本
                if (cell.Paragraphs.Count > 0)
                {
                    foreach (var paragraph in cell.Paragraphs)
                    {
                        while (paragraph.Runs.Count > 0)
                        {
                            paragraph.RemoveRun(0);
                        }
                        paragraph.CreateRun().SetText(" ");
                    }
                }
            }
        }

        /// <summary>
        /// 填充行数据
        /// </summary>
        /// <param name="tableRow">表格行</param>
        /// <param name="rowData">行数据</param>
        /// <param name="fieldToColumnIndex">字段到列索引的映射</param>
        /// <param name="isFirstRow">是否为第一行</param>
        private void FillRowData(XWPFTableRow tableRow, Dictionary<string, string> rowData, Dictionary<string, int> fieldToColumnIndex, bool isFirstRow)
        {
            // 处理公摊面积和分摊面积的相互映射
            string shareAreaValue = "";
            
            // 先检查是否有分摊面积或公摊面积的值
            if (rowData.TryGetValue("分摊面积", out string allocatedArea))
            {
                shareAreaValue = FormatAreaValue(allocatedArea); // 格式化为两位小数
            }
            else if (rowData.TryGetValue("公摊面积", out string publicArea))
            {
                shareAreaValue = FormatAreaValue(publicArea); // 格式化为两位小数
            }
            
            // 将找到的值同时应用于两个字段
            if (!string.IsNullOrEmpty(shareAreaValue))
            {
                if (fieldToColumnIndex.TryGetValue("分摊面积", out int allocatedAreaColumnIndex))
                {
                    EnsureCellExists(tableRow, allocatedAreaColumnIndex);
                    SetCellText(tableRow.GetCell(allocatedAreaColumnIndex), shareAreaValue);
                }
                
                if (fieldToColumnIndex.TryGetValue("公摊面积", out int publicAreaColumnIndex) && 
                    publicAreaColumnIndex != allocatedAreaColumnIndex)
                {
                    EnsureCellExists(tableRow, publicAreaColumnIndex);
                    SetCellText(tableRow.GetCell(publicAreaColumnIndex), shareAreaValue);
                }
            }
            
            // 特别处理"套内使用、墙体面积"字段
            string innerWallAreaValue = "";
            if (rowData.TryGetValue("套内使用、墙体面积", out string innerWallArea))
            {
                innerWallAreaValue = FormatAreaValue(innerWallArea); // 格式化为两位小数
            }
            else 
            {
                string innerUseArea = null;
                string wallArea = null;
                
                // 分别获取两个可能的值
                rowData.TryGetValue("套内使用", out innerUseArea);
                rowData.TryGetValue("墙体面积", out wallArea);
                
                // 使用找到的任何一个值
                if (!string.IsNullOrEmpty(innerUseArea))
                    innerWallAreaValue = innerUseArea;
                else if (!string.IsNullOrEmpty(wallArea))
                    innerWallAreaValue = wallArea;
            }
            
            // 如果找到了值，设置到相应单元格
            if (!string.IsNullOrEmpty(innerWallAreaValue) && 
                fieldToColumnIndex.TryGetValue("套内使用、墙体面积", out int innerWallColumnIndex))
            {
                EnsureCellExists(tableRow, innerWallColumnIndex);
                SetCellText(tableRow.GetCell(innerWallColumnIndex), innerWallAreaValue);
            }
            
            // 处理常规字段
            foreach (var field in rowData)
            {
                string fieldName = field.Key.Trim();
                string value = field.Value?.Trim() ?? string.Empty;

                // 对特定字段应用格式化
                if (fieldName == "建筑面积" || fieldName == "套内面积")
                {
                    value = FormatAreaValue(value);
                }

                // 跳过已处理的字段
                if (fieldName.StartsWith("全算_") || fieldName.StartsWith("半算_") ||
                    fieldName.Equals("分摊面积", StringComparison.OrdinalIgnoreCase) ||
                    fieldName.Equals("公摊面积", StringComparison.OrdinalIgnoreCase) ||
                    fieldName.Equals("套内使用、墙体面积", StringComparison.OrdinalIgnoreCase) ||
                    fieldName.Equals("套内使用", StringComparison.OrdinalIgnoreCase) ||
                    fieldName.Equals("墙体面积", StringComparison.OrdinalIgnoreCase))
                    continue;

                if (fieldToColumnIndex.TryGetValue(fieldName, out int columnIndex))
                {
                    EnsureCellExists(tableRow, columnIndex);
                    var cell = tableRow.GetCell(columnIndex);
                    if (cell != null)
                    {
                        SetCellText(cell, value);
                    }
                }
            }
        }
        
        /// <summary>
        /// 设置单元格文本，保留格式
        /// </summary>
        /// <param name="cell">单元格</param>
        /// <param name="text">文本内容</param>
        private void SetCellText(XWPFTableCell cell, string text)
        {
            try
            {
                if (cell == null)
                {
                    System.Diagnostics.Debug.WriteLine("[ERROR] SetCellText called with a null cell.");
                    return;
                }
                
                // 确保单元格有段落
                var paragraph = cell.Paragraphs.Count > 0 ? 
                    cell.Paragraphs[0] : 
                    cell.AddParagraph();
                
                if (paragraph == null)
                {
                    System.Diagnostics.Debug.WriteLine("[ERROR] Failed to get or create paragraph in a cell.");
                    return;
                }
                
                // 清空现有文本
                while (paragraph.Runs.Count > 0)
                {
                    paragraph.RemoveRun(0);
                }
                
                // 确保文本不是null
                string safeText = text ?? string.Empty;
                
                // 添加新文本，确保空文本显示为空格
                var run = paragraph.CreateRun();
                run.SetText(string.IsNullOrEmpty(safeText) ? " " : safeText);
                run.FontFamily = "仿宋";
                // 如果栋号列单元格包含"地下室"，设置字体大小为10，否则为12
                run.FontSize = safeText.Contains("地下室") ? 10 : 12;

                // 设置文本在单元格中的对齐方式（居中）
                paragraph.Alignment = ParagraphAlignment.CENTER;

                // 确保文本确实被写入
                if (cell.GetText() != safeText && !string.IsNullOrEmpty(safeText))
                {
                    // 尝试用更直接的方法设置文本
                    paragraph.RemoveRun(0);
                    var newRun = paragraph.CreateRun();
                    newRun.SetText(safeText);
                    newRun.FontFamily = "仿宋";
                    // 如果栋号列单元格包含"地下室"，设置字体大小为10，否则为12
                    newRun.FontSize = safeText.Contains("地下室") ? 10 : 12;
                }
            }
            catch (Exception ex)
            {
                // 记录错误但不抛出异常，避免中断处理
                System.Diagnostics.Debug.WriteLine($"设置单元格文本失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 处理特殊列（全算/半算）
        /// </summary>
        /// <param name="rowData">行数据</param>
        /// <param name="tableRow">表格行</param>
        /// <param name="prefix">前缀</param>
        /// <param name="columnMap">列映射</param>
        /// <param name="isFirstRow">是否为第一行</param>
        /// <param name="progressCallback">进度回调</param>
        private void ProcessSpecialColumns(Dictionary<string, string> rowData, XWPFTableRow tableRow, string prefix, Dictionary<string, int> columnMap, bool isFirstRow, Action<int, string> progressCallback = null)
        {
            // 获取所有列，确保按列值(Value)排序，这样第一列在前，第二列在后
            var availableColumns = columnMap.OrderBy(c => c.Value).ToList();
            
            // if (isFirstRow)
            // {
            //     progressCallback?.Invoke(87, $"处理{prefix}列: Word模板有{availableColumns.Count}列");
            //     foreach (var col in availableColumns)
            //     {
            //         progressCallback?.Invoke(87, $"Word {col.Key} 在列索引 {col.Value}");
            //     }
            // }
            
            // 如果是半算列，特殊检查半算_2是否存在
            if (prefix == "半算_")
            {
                var halfCountKeys = rowData.Keys.Where(k => k.StartsWith("半算_")).ToList();
                // progressCallback?.Invoke(87, $"Excel中找到的半算列: {string.Join(", ", halfCountKeys)}");
                
                // 检查是否有半算_2
                if (halfCountKeys.Contains("半算_2"))
                {
                    // progressCallback?.Invoke(87, $"找到半算_2键，值为: '{rowData["半算_2"]}'");
                }
                else
                {
                    // progressCallback?.Invoke(87, "警告：数据中缺少半算_2键");
                }
            }
            
            // 没有列可映射则返回
            if (availableColumns.Count == 0)
                return;
                
            // 处理每一个可能的列索引 (1到3，对应全算_1到全算_3或半算_1到半算_3)
            int maxColumns = Math.Min(3, availableColumns.Count);
            for (int i = 1; i <= maxColumns; i++)
            {
                // 构建键名，例如 "全算_1", "全算_2" 等
                string key = $"{prefix}{i}";
                
                // 尝试从数据中获取值
                string value = "";
                bool keyExists = rowData.TryGetValue(key, out string fieldValue);
                if (keyExists)
                {
                    value = fieldValue?.Trim() ?? string.Empty;
                    // 格式化全算/半算列的值为两位小数
                    value = FormatAreaValue(value);
                    // if (prefix == "半算_" || isFirstRow)
                    // {
                    //     progressCallback?.Invoke(87, $"找到Excel {key} = {value}");
                    // }
                }
                else if (prefix == "半算_" || isFirstRow)
                {
                    // progressCallback?.Invoke(87, $"Excel中未找到 {key} 键");
                }
                
                // 获取对应的Word列索引 - 第i-1个可用列 (0-based index)
                int columnIndex = availableColumns[i - 1].Value;
                
                // 设置单元格值
                EnsureCellExists(tableRow, columnIndex);
                var cell = tableRow.GetCell(columnIndex);
                if (cell != null)
                {
                    // 特别处理：如果是半算_2，强制输出调试信息
                    bool isHalfCount2 = (prefix == "半算_" && i == 2);
                    
                    // if (isHalfCount2)
                    // {
                    //     progressCallback?.Invoke(87, $"准备写入 半算_2={value} 到列索引 {columnIndex}");
                    // }
                    
                    SetCellText(cell, value);
                    
                    // if (isFirstRow || prefix == "半算_")
                    // {
                    //     progressCallback?.Invoke(87, $"设置 {key} 到列索引 {columnIndex}, 值为: '{value}'");
                    // }
                }
            }

            // 调试：检查行中每个单元格的内容
            if (prefix == "半算_")
            {
                for (int i = 0; i < tableRow.GetTableCells().Count; i++)
                {
                    var cell = tableRow.GetCell(i);
                    // if (cell != null && (i == 11 || i == 12 || i == 13)) // 只检查半算相关的列
                    // {
                    //     string text = cell.GetText();
                    //     progressCallback?.Invoke(87, $"单元格检查 - 列索引 {i}, 内容: '{text}'");
                    // }
                }
            }
        }

        /// <summary>
        /// 确保表格行中存在指定索引的单元格
        /// </summary>
        /// <param name="row">表格行</param>
        /// <param name="columnIndex">列索引</param>
        private void EnsureCellExists(XWPFTableRow row, int columnIndex)
        {
            while (columnIndex >= row.GetTableCells().Count)
            {
                row.CreateCell();
            }
        }

        /// <summary>
        /// 复制行格式
        /// </summary>
        /// <param name="source">源行</param>
        /// <param name="target">目标行</param>
        private void CopyRowFormat(XWPFTableRow source, XWPFTableRow target)
        {
            if (source == null || target == null) return;
            
            // 复制行高
            target.Height = source.Height;
            
            // 确保目标行有足够的单元格
            while (target.GetTableCells().Count < source.GetTableCells().Count)
            {
                target.CreateCell();
            }
            
            // 复制单元格格式
            for (int i = 0; i < Math.Min(source.GetTableCells().Count, target.GetTableCells().Count); i++)
            {
                XWPFTableCell sourceCell = source.GetCell(i);
                XWPFTableCell targetCell = target.GetCell(i);
                
                if (sourceCell == null || targetCell == null) continue;
                
                // 复制颜色
                targetCell.SetColor(sourceCell.GetColor());
                
                // 复制垂直对齐
                if (sourceCell.GetVerticalAlignment() != null)
                {
                    targetCell.SetVerticalAlignment(sourceCell.GetVerticalAlignment().Value);
                }
                
                // 清空目标单元格内容但保留格式
                while (targetCell.Paragraphs.Count > 0)
                {
                    targetCell.RemoveParagraph(0);
                }
                
                // 创建新段落并复制格式
                var paragraph = targetCell.AddParagraph();
                if (sourceCell.Paragraphs.Count > 0)
                {
                    var sourcePara = sourceCell.Paragraphs[0];
                    paragraph.Alignment = sourcePara.Alignment;
                    paragraph.SpacingBefore = sourcePara.SpacingBefore;
                    paragraph.SpacingAfter = sourcePara.SpacingAfter;
                    
                    if (sourcePara.IsWordWrapped)
                        paragraph.IsWordWrapped = sourcePara.IsWordWrapped;
                }
                
                // 尝试复制宽度 - 使用CTTc直接设置
                if (sourceCell.GetCTTc() != null && targetCell.GetCTTc() != null && 
                    sourceCell.GetCTTc().tcPr != null && sourceCell.GetCTTc().tcPr.tcW != null)
                {
                    if (targetCell.GetCTTc().tcPr == null)
                    {
                        targetCell.GetCTTc().AddNewTcPr();
                    }
                    
                    if (sourceCell.GetCTTc().tcPr.tcW.w != null)
                    {
                        if (targetCell.GetCTTc().tcPr.tcW == null)
                        {
                            targetCell.GetCTTc().tcPr.AddNewTcW();
                        }
                        
                        targetCell.GetCTTc().tcPr.tcW.w = sourceCell.GetCTTc().tcPr.tcW.w;
                        if (sourceCell.GetCTTc().tcPr.tcW.type != null)
                        {
                            targetCell.GetCTTc().tcPr.tcW.type = sourceCell.GetCTTc().tcPr.tcW.type;
                        }
                    }
                }
            }
        }

        /// <summary>
        /// 辅助方法：分析表头结构
        /// </summary>
        private HeaderInfo ParseHeaderStructure(ISheet worksheet, int headerRowIndex, Action<int, string> progressCallback)
        {
            try
            {
                var headerInfo = new HeaderInfo();
                
                // 获取表头行
                IRow headerRow = worksheet.GetRow(headerRowIndex);
                if (headerRow == null) return null;
                
                // 获取最大列数
                int maxColumnIndex = 0;
                for (int i = headerRowIndex; i < Math.Min(worksheet.LastRowNum + 1, headerRowIndex + 4); i++)
                {
                    IRow row = worksheet.GetRow(i);
                    if (row != null)
                    {
                        maxColumnIndex = Math.Max(maxColumnIndex, row.LastCellNum);
                    }
                }
                
                // 分析第一行表头
                for (int colIndex = headerRow.FirstCellNum; colIndex < maxColumnIndex; colIndex++)
                {
                    var cell = headerRow.GetCell(colIndex);
                    if (cell == null) continue;
                    
                    string headerText = GetCellValueAsString(cell);
                    if (string.IsNullOrWhiteSpace(headerText)) continue;
                    
                    // 检查是否是特殊字段
                    if (headerText.Contains("房屋唯一号"))
                    {
                        // 房屋唯一号作为表格标识，但不作为输出
                        headerInfo.UniqueIDColumnIndex = colIndex;
                        // progressCallback?.Invoke(41, $"找到房屋唯一号列：{colIndex + 1}，作为表格标识但不输出");
                    }
                    else if (headerText.Contains("房号"))
                    {
                        // 如果UniqueIDColumnIndex还未设置，则使用房号列
                        if (headerInfo.UniqueIDColumnIndex < 0)
                        {
                            headerInfo.UniqueIDColumnIndex = colIndex;
                        }
                        // 房号列需要输出
                        headerInfo.RegularColumns["房号"] = colIndex;
                        // progressCallback?.Invoke(41, $"找到房号列：{colIndex + 1}");
                    }
                    else if (headerText.Contains("栋号"))
                    {
                        headerInfo.BuildingColumnIndex = colIndex;
                        headerInfo.RegularColumns["栋号"] = colIndex;
                        // progressCallback?.Invoke(41, $"找到栋号列：{colIndex + 1}");
                    }
                    else if (headerText.Contains("单元"))
                    {
                        headerInfo.RegularColumns["单元"] = colIndex;
                    }
                    else if (headerText.Contains("楼层"))
                    {
                        headerInfo.RegularColumns["楼层"] = colIndex;
                    }
                    else if (headerText.Contains("建筑面积"))
                    {
                        headerInfo.RegularColumns["建筑面积"] = colIndex;
                    }
                    else if (headerText.Contains("套内面积") && !headerText.Contains("构成"))
                    {
                        headerInfo.RegularColumns["套内面积"] = colIndex;
                    }
                    else if (headerText.Contains("分摊面积") || headerText.Contains("公摊面积"))
                    {
                        headerInfo.RegularColumns["分摊面积"] = colIndex;
                    }
                    else if (headerText.Contains("套内使用") || headerText.Contains("墙体面积"))
                    {
                        headerInfo.RegularColumns["套内使用、墙体面积"] = colIndex;
                        // progressCallback?.Invoke(41, $"找到套内使用、墙体面积列：{colIndex + 1}");
                    }                    
                    else if (headerText.Contains("套数"))
                    {
                        headerInfo.RegularColumns["套数"] = colIndex;
                    }
                    else if (headerText.Contains("分摊系数"))
                    {
                        headerInfo.RegularColumns["分摊系数"] = colIndex;
                    }
                    else if (headerText.Contains("设计用途") || headerText.Contains("说明"))
                    {
                        headerInfo.RegularColumns["设计用途及说明"] = colIndex;
                    }
                    else if (headerText.Contains("套内面积构成"))
                    {
                        headerInfo.HasNestedStructure = true;
                    }
                }
                
                // 分析第二行表头（如果存在嵌套结构）
                IRow secondRow = worksheet.GetRow(headerRowIndex + 1);
                if (secondRow != null && headerInfo.HasNestedStructure)
                {
                    for (int colIndex = secondRow.FirstCellNum; colIndex < maxColumnIndex; colIndex++)
                    {
                        var cell = secondRow.GetCell(colIndex);
                        if (cell == null) continue;
                        
                        string headerText = GetCellValueAsString(cell);
                        if (string.IsNullOrWhiteSpace(headerText)) continue;
                        
                        if (headerText.Contains("阳台面积") || headerText.Contains("全算") || headerText.Contains("半算"))
                        {
                            headerInfo.HasBalconyColumns = true;
                        }
                    }
                }
                
                // 分析第三行表头
                IRow thirdRow = worksheet.GetRow(headerRowIndex + 2);
                if (thirdRow != null)
                {
                    for (int colIndex = thirdRow.FirstCellNum; colIndex < maxColumnIndex; colIndex++)
                    {
                        var cell = thirdRow.GetCell(colIndex);
                        if (cell == null) continue;
                        
                        string headerText = GetCellValueAsString(cell);
                        if (string.IsNullOrWhiteSpace(headerText)) continue;
                        
                        if (headerText.Contains("全算"))
                        {
                            headerInfo.FullCountColumns.Add(colIndex);
                            // progressCallback?.Invoke(42, $"找到全算列：{colIndex + 1}");
                        }
                        else if (headerText.Contains("半算"))
                        {
                            headerInfo.HalfCountColumns.Add(colIndex);
                            // progressCallback?.Invoke(42, $"找到半算列：{colIndex + 1}, 文本：{headerText}");
                        }
                        else if (headerText.Contains("套内使用") || headerText.Contains("墙体面积"))
                        {
                            // 检查第三行是否还有套内使用或墙体面积列
                            if (!headerInfo.RegularColumns.ContainsKey("套内使用、墙体面积"))
                            {
                                headerInfo.RegularColumns["套内使用、墙体面积"] = colIndex;
                                // progressCallback?.Invoke(42, $"在第三行找到套内使用、墙体面积列：{colIndex + 1}");
                            }
                        }
                    }
                }
                
                // 输出检测到的半算列数量
                // progressCallback?.Invoke(42, $"共找到{headerInfo.HalfCountColumns.Count}个半算列");
                
                return headerInfo;
            }
            catch (Exception ex)
            {
                progressCallback?.Invoke(35, $"[ERROR]解析表头结构出错: {ex.Message}");
                return null;
            }
        }

        // 表头信息类
        private class HeaderInfo
        {
            public int UniqueIDColumnIndex { get; set; } = -1;
            public int BuildingColumnIndex { get; set; } = -1;
            public bool HasNestedStructure { get; set; } = false;
            public bool HasBalconyColumns { get; set; } = false;
            public Dictionary<string, int> RegularColumns { get; } = new Dictionary<string, int>();
            public List<int> FullCountColumns { get; } = new List<int>();
            public List<int> HalfCountColumns { get; } = new List<int>();
        }

        // 辅助方法：判断是否为空行
        private bool IsEmptyRow(IRow row)
        {
            if (row == null) return true;
            
            for (int i = row.FirstCellNum; i < Math.Min((int)row.LastCellNum, (int)10); i++)
            {
                var cell = row.GetCell(i);
                if (cell != null && !string.IsNullOrWhiteSpace(GetCellValueAsString(cell)))
                {
                    return false;
                }
            }
            
            return true;
        }

        // 辅助方法：判断是否为表头行
        private bool IsHeaderRow(IRow row)
        {
            if (row == null) return false;
            
            int headerTextCount = 0;
            for (int i = row.FirstCellNum; i < Math.Min((int)row.LastCellNum, (int)10); i++)
            {
                var cell = row.GetCell(i);
                if (cell == null) continue;
                
                string text = GetCellValueAsString(cell);
                if (IsHeaderText(text))
                {
                    headerTextCount++;
                }
            }
            
            // 如果有多个单元格包含表头文本，则认为是表头行
            return headerTextCount >= 2;
        }

        // 辅助方法：判断是否为有效栋号
        private bool IsValidBuildingNumber(string buildingNumber)
        {
            if (string.IsNullOrWhiteSpace(buildingNumber)) return false;
            
            // 栋号通常是数字或数字+字母
            return int.TryParse(buildingNumber, out _) || 
                   Regex.IsMatch(buildingNumber, @"^\d+[A-Za-z]?$") || // 匹配如"1A"等格式
                   (buildingNumber.Length <= 3 && !IsHeaderText(buildingNumber)); // 短文本且不是表头
        }

        /// <summary>
        /// 辅助方法：从标签中提取栋号
        /// </summary>
        private string ExtractBuildingNumber(string label)
        {
            if (string.IsNullOrWhiteSpace(label)) return "";
            
            // 从"1栋"或"栋号：1"等格式中提取数字
            Match match = Regex.Match(label, @"^([0-9]+[A-Za-z]?)|\D+([0-9]+[A-Za-z]?)");
            if (match.Success)
            {
                return match.Groups[1].Value.Length > 0 ? match.Groups[1].Value : match.Groups[2].Value;
            }
            
            return "";
        }

        // 辅助方法：判断是否为表头文本
        private bool IsHeaderText(string text)
        {
            if (string.IsNullOrWhiteSpace(text)) return false;
            
            return text.Contains("栋号") || 
                   text.Contains("单元") || 
                   text.Contains("楼层") || 
                   text.Contains("房号") || 
                   text.Contains("面积") || 
                   text.Contains("全算") || 
                   text.Contains("半算") || 
                   text.Contains("功能区") || 
                   text.Contains("项目") || 
                   text.Contains("序号");
        }

        /// <summary>
        /// 是否可用
        /// </summary>
        /// <param name="parameters">检查参数</param>
        /// <returns>是否可用</returns>
        public bool IsAvailable(Dictionary<string, object> parameters)
        {
            if (parameters == null)
                return false;

            // 检查必要参数
            if (!parameters.ContainsKey("DataFolder") || !parameters.ContainsKey("OutputDir"))
                return false;

            string dataDirectory = parameters["DataFolder"]?.ToString();
            
            // 检查数据目录
            if (string.IsNullOrEmpty(dataDirectory) || !Directory.Exists(dataDirectory))
                return false;
            
            // 检查模板文件
            string templatePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "报告模板", "12_房产分户面积统计表", "房产分户面积统计表.docx");
            if (!File.Exists(templatePath))
                return false;

            // 检查是否存在可用的Excel文件（标准Excel文件、CGB.xls文件或成果包.7z文件）
            try
            {
                // 首先检查标准Excel文件（包含4个指定工作表）
                var excelFiles = Directory.GetFiles(dataDirectory, "*.xls*", SearchOption.AllDirectories);
                foreach (string filePath in excelFiles)
                {
                    try
                    {
                        if (ValidateExcelFileStructure(filePath))
                        {
                            return true; // 找到标准Excel文件
                        }
                    }
                    catch
                    {
                        // 忽略验证失败的文件，继续检查其他文件
                        continue;
                    }
                }

                // 检查直接的CGB文件
                var cgbFiles = Directory.GetFiles(dataDirectory, "*.xls*", SearchOption.AllDirectories)
                    .Where(file => Path.GetFileName(file).ToLower().Contains("cgb"))
                    .ToArray();

                if (cgbFiles.Any())
                    return true;

                // 检查是否存在成果包.7z文件
                var archiveFiles = Directory.GetFiles(dataDirectory, "*.7z", SearchOption.AllDirectories)
                    .Where(file => Path.GetFileName(file).Contains("成果包"))
                    .ToArray();

                return archiveFiles.Any();
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 格式化面积值为两位小数
        /// </summary>
        /// <param name="value">面积值字符串</param>
        /// <returns>格式化后的字符串</returns>
        private string FormatAreaValue(string value)
        {
            // 如果为空或不是数字，直接返回
            if (string.IsNullOrWhiteSpace(value)) return value;
            
            // 尝试解析为数值
            if (double.TryParse(value, out double numericValue))
            {
                // 格式化为两位小数
                return numericValue.ToString("F2");
            }
            
            return value; // 无法解析为数字时返回原值
        }
    }
} 