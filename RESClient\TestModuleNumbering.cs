using System;
using System.Collections.Generic;
using System.IO;
using RESClient.Services;

namespace RESClient
{
    /// <summary>
    /// 测试模块编号功能
    /// </summary>
    public class TestModuleNumbering
    {
        public static void Main(string[] args)
        {
            Console.WriteLine("=== 模块编号功能测试 ===");
            
            try
            {
                // 创建测试目录
                string testDirectory = Path.Combine(Path.GetTempPath(), "ModuleNumberingTest");
                if (Directory.Exists(testDirectory))
                {
                    Directory.Delete(testDirectory, true);
                }
                Directory.CreateDirectory(testDirectory);
                
                Console.WriteLine($"测试目录: {testDirectory}");
                
                // 创建一些测试文件
                var testModules = new List<string>
                {
                    "封面.docx",
                    "目录.docx", 
                    "作业声明.docx",
                    "项目基本信息.docx",
                    "楼栋基本信息.docx"
                };
                
                Console.WriteLine("\n创建测试文件:");
                foreach (var module in testModules)
                {
                    string filePath = Path.Combine(testDirectory, module);
                    File.WriteAllText(filePath, "测试内容");
                    Console.WriteLine($"  创建: {module}");
                }
                
                // 测试编号服务
                var numberingService = new ModuleNumberingService();
                
                Console.WriteLine("\n开始添加编号前缀:");
                bool result = numberingService.AddNumberingPrefixes(testDirectory, (percent, message) =>
                {
                    Console.WriteLine($"  [{percent}%] {message}");
                });
                
                Console.WriteLine($"\n编号结果: {(result ? "成功" : "失败")}");
                
                // 检查结果
                Console.WriteLine("\n编号后的文件:");
                var files = Directory.GetFiles(testDirectory, "*.docx");
                Array.Sort(files);
                foreach (var file in files)
                {
                    Console.WriteLine($"  {Path.GetFileName(file)}");
                }
                
                // 验证编号功能
                Console.WriteLine("\n验证编号功能:");
                var supportedModules = numberingService.GetSupportedModules();
                Console.WriteLine($"支持的模块数量: {supportedModules.Count}");

                foreach (var moduleName in new[] { "封面", "目录", "作业声明" })
                {
                    string prefix = numberingService.GetModuleNumberPrefix(moduleName);
                    bool isSupported = numberingService.IsModuleSupported(moduleName);
                    Console.WriteLine($"  {moduleName}: 前缀={prefix}, 支持={isSupported}");
                }
                
                // 清理测试目录
                Directory.Delete(testDirectory, true);
                Console.WriteLine("\n测试完成，已清理测试目录");
                
            }
            catch (Exception ex)
            {
                Console.WriteLine($"测试过程中发生错误: {ex.Message}");
                Console.WriteLine($"详细信息: {ex}");
            }
            
            Console.WriteLine("\n按任意键退出...");
            Console.ReadKey();
        }
    }
}
