using System;
using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;
using RESClient.MVP.Models;

namespace RESClient.Services.Implementations
{
    /// <summary>
    /// 作业声明模块生成器
    /// </summary>
    public class WorkStatementModuleGenerator : IReportModuleGenerator
    {
        private readonly WorkStatementParametersModel _parametersModel;
        private readonly WorkStatementModel _workStatementModel;

        public WorkStatementModuleGenerator()
        {
            _parametersModel = new WorkStatementParametersModel();
            _workStatementModel = new WorkStatementModel();
        }

        /// <summary>
        /// 模块名称
        /// </summary>
        public string ModuleName => "作业声明";

        /// <summary>
        /// 生成报告模块
        /// </summary>
        /// <param name="parameters">生成参数</param>
        /// <param name="progressCallback">进度回调</param>
        /// <returns>生成结果</returns>
        public async Task<bool> GenerateAsync(Dictionary<string, object> parameters, Action<int, string> progressCallback)
        {
            try
            {
                progressCallback?.Invoke(0, "[INFO]开始生成作业声明模块...");

                // 获取输出目录 - 支持两种参数键名
                object outputDirObj = null;
                if (!parameters.TryGetValue("OutputDirectory", out outputDirObj) ||
                    string.IsNullOrEmpty(outputDirObj?.ToString()))
                {
                    // 尝试使用 OutputDir 参数键
                    if (!parameters.TryGetValue("OutputDir", out outputDirObj) ||
                        string.IsNullOrEmpty(outputDirObj?.ToString()))
                    {
                        progressCallback?.Invoke(0, "[ERROR]未指定输出目录 (需要 OutputDirectory 或 OutputDir 参数)");
                        return false;
                    }
                }

                string outputDirectory = outputDirObj.ToString();
                progressCallback?.Invoke(10, "[INFO]输出目录: " + outputDirectory);

                // 确保输出目录存在
                if (!Directory.Exists(outputDirectory))
                {
                    Directory.CreateDirectory(outputDirectory);
                    progressCallback?.Invoke(20, "[INFO]创建输出目录");
                }

                // 获取作业声明参数
                var workStatementParameters = _parametersModel.GetCurrentParameters();
                progressCallback?.Invoke(30, "[INFO]加载作业声明参数");

                // 验证参数
                var (isValid, errors) = workStatementParameters.Validate();
                if (!isValid)
                {
                    string errorMessage = "作业声明参数验证失败: " + string.Join(", ", errors);
                    progressCallback?.Invoke(30, "[ERROR]" + errorMessage);
                    return false;
                }

                progressCallback?.Invoke(50, "[INFO]参数验证通过");

                // 转换为WorkStatementModel所需的数据格式
                var workStatementData = ConvertToWorkStatementData(workStatementParameters);
                progressCallback?.Invoke(60, "[INFO]转换参数格式");

                // 生成输出文件路径
                string outputPath = Path.Combine(outputDirectory, $"{ModuleName}.docx");
                progressCallback?.Invoke(70, "[INFO]准备生成文档: " + Path.GetFileName(outputPath));

                // 调用WorkStatementModel生成作业声明
                bool result = await _workStatementModel.FillWorkStatementTemplateAsync(workStatementData, outputPath);

                if (result)
                {
                    progressCallback?.Invoke(100, "[SUCCESS]作业声明生成完成");
                    return true;
                }
                else
                {
                    progressCallback?.Invoke(100, "[ERROR]作业声明生成失败");
                    return false;
                }
            }
            catch (Exception ex)
            {
                progressCallback?.Invoke(100, $"[ERROR]作业声明生成过程中发生异常: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 转换参数格式
        /// </summary>
        /// <param name="parameters">作业声明参数</param>
        /// <returns>WorkStatementModel所需的数据格式</returns>
        private WorkStatementModel.WorkStatementData ConvertToWorkStatementData(WorkStatementParametersModel.WorkStatementParameters parameters)
        {
            return new WorkStatementModel.WorkStatementData
            {
                ClientName = parameters.ClientName,
                ProjectAddress = parameters.ProjectAddress,
                CertificateLevel = parameters.CertificateLevel,
                CertificateObtainDate = parameters.CertificateObtainDate.ToString("yyyy年M月d日"),
                CertificateNumber = parameters.CertificateNumber,
                CertificateExpiryDate = parameters.CertificateExpiryDate.ToString("yyyy年M月d日"),
                ProfessionalScope = parameters.ProfessionalScope,
                LegalRepresentative = parameters.LegalRepresentative,
                TechnicalManager = parameters.TechnicalManager,
                CompanyAddress = parameters.CompanyAddress,
                Phone = parameters.Phone,
                Fax = parameters.Fax,
                SurveyInstitution = parameters.SurveyInstitution,
                ReportDate = Utils.ChineseDateFormatter.ToChineseUppercase(parameters.ReportDate)
            };
        }

        /// <summary>
        /// 是否可用
        /// </summary>
        /// <param name="parameters">检查参数</param>
        /// <returns>是否可用</returns>
        public bool IsAvailable(Dictionary<string, object> parameters)
        {
            try
            {
                // 检查模板文件是否存在
                string templatePath = Path.Combine(
                    AppDomain.CurrentDomain.BaseDirectory,
                    Program.TemplateDirectory,
                    "03_作业声明",
                    "作业声明.docx");

                return File.Exists(templatePath);
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 获取参数模型（用于外部访问）
        /// </summary>
        /// <returns>参数模型</returns>
        public WorkStatementParametersModel GetParametersModel()
        {
            return _parametersModel;
        }
    }
} 