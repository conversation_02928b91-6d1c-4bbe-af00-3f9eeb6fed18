using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.IO;
using Newtonsoft.Json;
using RESClient.Utils;
using RESClient.MVP.Base;

namespace RESClient.MVP.Models
{
    /// <summary>
    /// 经主管部门批准的相关证照参数数据模型
    /// </summary>
    public class CertificatesParametersModel : BaseModel
    {
        /// <summary>
        /// 经主管部门批准的相关证照参数数据结构
        /// </summary>
        public class CertificatesParameters
        {
            [DisplayName("规划许可证")]
            [Description("规划许可证编号或相关信息")]
            public string PlanningPermit { get; set; } = "/";

            [DisplayName("规划总平图")]
            [Description("规划总平图编号或相关信息")]
            public string PlanningLayout { get; set; } = "/";

            [DisplayName("施工许可证")]
            [Description("施工许可证编号或相关信息")]
            public string ConstructionPermit { get; set; } = "/";

            [DisplayName("审查报告出具方")]
            [Description("出具施工图审查报告的机构名称")]
            public string ReviewReportIssuer { get; set; } = "/";

            [DisplayName("项目编号")]
            [Description("施工图审查报告的项目编号")]
            public string ProjectNumber { get; set; } = "/";

            [DisplayName("派出所地址证明")]
            [Description("派出所出具的地址证明相关信息")]
            public string PoliceStationAddressProof { get; set; } = "/";

            [DisplayName("建筑物名称备案通知")]
            [Description("建筑物名称备案通知相关信息")]
            public string BuildingNameRegistrationNotice { get; set; } = "/";

            /// <summary>
            /// 获取所有变量的映射字典
            /// </summary>
            /// <returns>变量名到值的映射</returns>
            public Dictionary<string, string> GetVariableMapping()
            {
                return new Dictionary<string, string>
                {
                    { "${规划许可证}", PlanningPermit },
                    { "${规划总平图}", PlanningLayout },
                    { "${施工许可证}", ConstructionPermit },
                    { "${审查报告出具方}", ReviewReportIssuer },
                    { "${项目编号}", ProjectNumber },
                    { "${派出所地址证明}", PoliceStationAddressProof },
                    { "${建筑物名称备案通知}", BuildingNameRegistrationNotice }
                };
            }

            /// <summary>
            /// 验证参数完整性
            /// </summary>
            /// <returns>验证结果和错误信息</returns>
            public (bool IsValid, List<string> Errors) Validate()
            {
                var errors = new List<string>();

                if (string.IsNullOrWhiteSpace(PlanningPermit))
                    errors.Add("规划许可证不能为空");

                if (string.IsNullOrWhiteSpace(PlanningLayout))
                    errors.Add("规划总平图不能为空");

                if (string.IsNullOrWhiteSpace(ConstructionPermit))
                    errors.Add("施工许可证不能为空");

                if (string.IsNullOrWhiteSpace(ReviewReportIssuer))
                    errors.Add("审查报告出具方不能为空");

                if (string.IsNullOrWhiteSpace(ProjectNumber))
                    errors.Add("项目编号不能为空");

                if (string.IsNullOrWhiteSpace(PoliceStationAddressProof))
                    errors.Add("派出所地址证明不能为空");

                if (string.IsNullOrWhiteSpace(BuildingNameRegistrationNotice))
                    errors.Add("建筑物名称备案通知不能为空");

                return (errors.Count == 0, errors);
            }
        }

        private readonly string _configFilePath;
        private CertificatesParameters _currentParameters;

        /// <summary>
        /// 构造函数
        /// </summary>
        public CertificatesParametersModel()
        {
            // 设置配置文件路径
            string appDataPath = Environment.GetFolderPath(Environment.SpecialFolder.LocalApplicationData);
            string appFolder = Path.Combine(appDataPath, "RESClient");
            
            if (!Directory.Exists(appFolder))
            {
                Directory.CreateDirectory(appFolder);
            }
            
            _configFilePath = Path.Combine(appFolder, "certificates_parameters.json");
            
            // 加载参数
            _currentParameters = LoadParameters();
        }

        /// <summary>
        /// 获取当前参数
        /// </summary>
        /// <returns>当前参数</returns>
        public CertificatesParameters GetCurrentParameters()
        {
            return _currentParameters;
        }

        /// <summary>
        /// 保存参数
        /// </summary>
        /// <param name="parameters">要保存的参数</param>
        /// <returns>保存是否成功</returns>
        public bool SaveParameters(CertificatesParameters parameters)
        {
            try
            {
                _currentParameters = parameters;
                string json = JsonConvert.SerializeObject(parameters, Formatting.Indented);
                File.WriteAllText(_configFilePath, json, System.Text.Encoding.UTF8);
                return true;
            }
            catch (Exception ex)
            {
                HandleException(ex);
                return false;
            }
        }

        /// <summary>
        /// 加载参数
        /// </summary>
        /// <returns>加载的参数，如果失败则返回默认参数</returns>
        private CertificatesParameters LoadParameters()
        {
            try
            {
                if (!File.Exists(_configFilePath))
                {
                    return CreateDefaultParameters();
                }

                string json = File.ReadAllText(_configFilePath, System.Text.Encoding.UTF8);
                var parameters = JsonConvert.DeserializeObject<CertificatesParameters>(json);
                
                return parameters ?? CreateDefaultParameters();
            }
            catch (Exception ex)
            {
                HandleException(ex);
                return CreateDefaultParameters();
            }
        }

        /// <summary>
        /// 创建默认参数
        /// </summary>
        /// <returns>默认参数</returns>
        private CertificatesParameters CreateDefaultParameters()
        {
            return new CertificatesParameters
            {
                PlanningPermit = "/",
                PlanningLayout = "/",
                ConstructionPermit = "/",
                ReviewReportIssuer = "/",
                ProjectNumber = "/",
                PoliceStationAddressProof = "/",
                BuildingNameRegistrationNotice = "/"
            };
        }
    }
}
