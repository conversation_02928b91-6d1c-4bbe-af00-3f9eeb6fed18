using Autodesk.AutoCAD.ApplicationServices;
using Autodesk.AutoCAD.DatabaseServices;
using Autodesk.AutoCAD.EditorInput;
using Autodesk.AutoCAD.Geometry;
using RESCADServerPlugin.Commands.Implementations;
using RESCADServerPlugin.Configuration;
using System;
using System.Collections.Generic;
using System.IO;
using System.IO.Compression;
using System.Net;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;

namespace RESCADServerPlugin.Controllers
{
    public class DirectPrintDrawingController
    {
        private readonly PrintDrawingCommands _printDrawingCommands;
        
        public DirectPrintDrawingController()
        {
            _printDrawingCommands = new PrintDrawingCommands();
        }
        
        /// <summary>
        /// 处理DWG文件的直接打印图提取请求（无需后台队列）
        /// </summary>
        public async Task HandleDirectProcessRequest(HttpListenerContext context)
        {
            try
            {
                // 检查是否为POST请求以及Content-Type是否为multipart/form-data
                if (!context.Request.HttpMethod.Equals("POST", StringComparison.OrdinalIgnoreCase) || 
                    !context.Request.ContentType.StartsWith("multipart/form-data", StringComparison.OrdinalIgnoreCase))
                {
                    SendErrorResponse(context.Response, 400, "无效请求。必须是POST请求并且Content-Type为multipart/form-data。");
                    return;
                }
                
                // 处理上传的文件
                string tempFilePath = await SaveUploadedFile(context.Request);
                if (string.IsNullOrEmpty(tempFilePath))
                {
                    SendErrorResponse(context.Response, 400, "未上传文件或文件无效。");
                    return;
                }
                
                try
                {
                    // 创建唯一的会话目录
                    string sessionId = Guid.NewGuid().ToString("N");
                    string tempDir = Path.GetTempPath();
                    string sessionDir = Path.Combine(tempDir, "RESCADServerPlugin", sessionId);
                    Directory.CreateDirectory(sessionDir);
                    
                    // 将文件加载到AutoCAD并直接导出打印图
                    string result = await ProcessDwgFileDirectly(tempFilePath, sessionDir);
                    
                    // 将结果打包为ZIP文件
                    string zipFilePath = Path.Combine(sessionDir, "print_drawings.zip");
                    ZipDirectory(result, zipFilePath);
                    
                    // 发送ZIP文件响应
                    SendFileResponse(context.Response, zipFilePath, "application/zip", "print_drawings.zip");
                }
                finally
                {
                    // 清理临时上传的文件
                    try
                    {
                        if (File.Exists(tempFilePath))
                        {
                            File.Delete(tempFilePath);
                        }
                    }
                    catch { /* 忽略清理错误 */ }
                }
            }
            catch (Exception ex)
            {
                SendErrorResponse(context.Response, 500, $"处理图纸时出错: {ex.Message}");
            }
        }
        
        /// <summary>
        /// 直接处理DWG文件，不使用后台任务队列
        /// </summary>
        private async Task<string> ProcessDwgFileDirectly(string filePath, string outputDir)
        {
            Document doc = Application.DocumentManager.MdiActiveDocument;
            Editor ed = doc?.Editor;
            
            // 创建输出目录
            string printDrawingsOutputDir = Path.Combine(outputDir, "PrintDrawings");
            Directory.CreateDirectory(printDrawingsOutputDir);
            
            // 存储原始输出目录路径和更新临时输出目录路径
            PluginSettings settings = ConfigurationService.GetSettings() ?? new PluginSettings();
            string originalOutputPath = settings.PrintDrawingOutputPath;
            settings.PrintDrawingOutputPath = printDrawingsOutputDir;
            
            // 应用临时设置
            // 注: 我们不保存设置，而是临时修改内存中的实例
            // ConfigurationService没有SaveSettings方法，所以我们不调用它
            
            try
            {
                // 打开文件
                DocumentLock docLock = null;
                bool needsCloseDocument = false;
                Document dwgDoc = doc;  // 默认使用当前活动文档
                
                try
                {
                    // 使用DocumentCollection打开DWG文件而不是直接覆盖当前数据库
                    DocumentCollection docCol = Application.DocumentManager;
                    
                    // 将打开的DWG设置为活动文档
                    dwgDoc = docCol.Open(filePath, false); // 以只读方式打开
                    needsCloseDocument = true;
                    
                    // 确保dwgDoc现在是活动文档
                    docCol.MdiActiveDocument = dwgDoc;
                    
                    // 尝试锁定文档以执行操作
                    docLock = dwgDoc.LockDocument();
                    dwgDoc.Editor?.WriteMessage($"\n正在锁定文档以处理打印图...");
                    dwgDoc.Editor?.WriteMessage($"\n已加载文件: {filePath}");
                    
                    // 调用PrintDrawingCommands执行提取打印图功能
                    await Task.Run(() => {
                        _printDrawingCommands.ExecuteSavePrintDrawings();
                    });
                    
                    dwgDoc.Editor?.WriteMessage($"\n打印图已保存到: {printDrawingsOutputDir}");
                    
                    return printDrawingsOutputDir;
                }
                finally
                {
                    // 释放文档锁
                    docLock?.Dispose();
                    
                    // 如果我们打开了新文档，我们应该关闭它
                    if (needsCloseDocument && dwgDoc != null && dwgDoc != doc)
                    {
                        try
                        {
                            dwgDoc.CloseAndDiscard();
                            // 重新激活原始文档
                            if (doc != null)
                            {
                                Application.DocumentManager.MdiActiveDocument = doc;
                            }
                        }
                        catch (Exception ex)
                        {
                            ed?.WriteMessage($"\n关闭文档时出错: {ex.Message}");
                        }
                    }
                }
            }
            finally
            {
                // 还原原始设置
                settings.PrintDrawingOutputPath = originalOutputPath;
                // 不需要保存设置，因为我们只是临时更改了内存中的实例
            }
        }
        
        /// <summary>
        /// 将目录压缩为ZIP文件
        /// </summary>
        private void ZipDirectory(string sourceDir, string zipFilePath)
        {
            if (File.Exists(zipFilePath))
            {
                File.Delete(zipFilePath);
            }
            
            ZipFile.CreateFromDirectory(sourceDir, zipFilePath);
        }
        
        /// <summary>
        /// 将上传的文件保存到临时位置
        /// </summary>
        private async Task<string> SaveUploadedFile(HttpListenerRequest request)
        {
            string tempDir = Path.Combine(Path.GetTempPath(), "RESCADServerPlugin");
            if (!Directory.Exists(tempDir))
            {
                Directory.CreateDirectory(tempDir);
            }
            
            string boundary = GetBoundary(request.ContentType);
            if (string.IsNullOrEmpty(boundary))
            {
                return null;
            }
            
            // Convert boundary to byte array once
            byte[] boundaryBytes = Encoding.ASCII.GetBytes($"--{boundary}\r\n");
            byte[] endBoundaryBytes = Encoding.ASCII.GetBytes($"--{boundary}--\r\n");
            
            using (var ms = new MemoryStream())
            {
                // Copy the request stream to a memory stream
                await request.InputStream.CopyToAsync(ms);
                ms.Position = 0;
                
                // Read the memory stream
                byte[] data = ms.ToArray();
                
                // Find the file content start
                int fileStart = FindBytePattern(data, boundaryBytes);
                if (fileStart < 0)
                {
                    return null;
                }
                
                // Move past boundary
                fileStart += boundaryBytes.Length;
                
                // Find the file data start (after headers)
                int headerEnd = FindBytePattern(data, new byte[] { 13, 10, 13, 10 }, fileStart); // \r\n\r\n
                if (headerEnd < 0)
                {
                    return null;
                }
                
                // Extract headers
                string headers = Encoding.UTF8.GetString(data, fileStart, headerEnd - fileStart);
                
                // Parse Content-Disposition header to get filename
                string fileName = ParseFileName(headers);
                if (string.IsNullOrEmpty(fileName))
                {
                    fileName = $"upload_{Guid.NewGuid()}.dwg";
                }
                else if (!fileName.EndsWith(".dwg", StringComparison.OrdinalIgnoreCase))
                {
                    fileName += ".dwg";
                }
                
                // Move past headers to file content
                int fileContentStart = headerEnd + 4; // +4 for \r\n\r\n
                
                // Find the end boundary
                int fileEnd = FindBytePattern(data, endBoundaryBytes, fileContentStart);
                if (fileEnd < 0)
                {
                    // Try normal boundary
                    fileEnd = FindBytePattern(data, boundaryBytes, fileContentStart);
                    if (fileEnd < 0)
                    {
                        return null;
                    }
                }
                
                // Extract file content
                int fileLength = fileEnd - fileContentStart - 2; // -2 for \r\n before boundary
                
                // Save to temp file
                string tempFilePath = Path.Combine(tempDir, fileName);
                using (FileStream fs = new FileStream(tempFilePath, FileMode.Create))
                {
                    fs.Write(data, fileContentStart, fileLength);
                }
                
                return tempFilePath;
            }
        }
        
        /// <summary>
        /// 从Content-Type标头获取multipart边界
        /// </summary>
        private string GetBoundary(string contentType)
        {
            if (string.IsNullOrEmpty(contentType))
            {
                return null;
            }
            
            int index = contentType.IndexOf("boundary=");
            if (index < 0)
            {
                return null;
            }
            
            return contentType.Substring(index + 9);
        }
        
        /// <summary>
        /// 解析Content-Disposition标头中的文件名
        /// </summary>
        private string ParseFileName(string headers)
        {
            const string fileNameTag = "filename=\"";
            int fileNameIndex = headers.IndexOf(fileNameTag);
            if (fileNameIndex < 0)
            {
                return null;
            }
            
            int startIndex = fileNameIndex + fileNameTag.Length;
            int endIndex = headers.IndexOf("\"", startIndex);
            if (endIndex < 0)
            {
                return null;
            }
            
            return headers.Substring(startIndex, endIndex - startIndex);
        }
        
        /// <summary>
        /// 在源数组中查找模式数组
        /// </summary>
        private int FindBytePattern(byte[] source, byte[] pattern, int startIndex = 0)
        {
            if (source == null || pattern == null || pattern.Length > source.Length)
                return -1;
                
            for (int i = startIndex; i <= source.Length - pattern.Length; i++)
            {
                bool found = true;
                for (int j = 0; j < pattern.Length; j++)
                {
                    if (source[i + j] != pattern[j])
                    {
                        found = false;
                        break;
                    }
                }
                if (found)
                    return i;
            }
            
            return -1;
        }
        
        /// <summary>
        /// 发送错误响应
        /// </summary>
        private void SendErrorResponse(HttpListenerResponse response, int statusCode, string message)
        {
            response.StatusCode = statusCode;
            response.ContentType = "application/json; charset=utf-8";
            
            string json = JsonSerializer.Serialize(new { success = false, message = message }, 
                new JsonSerializerOptions 
                { 
                    WriteIndented = false,
                    PropertyNamingPolicy = JsonNamingPolicy.CamelCase
                });
            
            byte[] buffer = Encoding.UTF8.GetBytes(json);
            response.ContentLength64 = buffer.Length;
            response.OutputStream.Write(buffer, 0, buffer.Length);
            response.OutputStream.Close();
        }
        
        /// <summary>
        /// 发送文件响应
        /// </summary>
        private void SendFileResponse(HttpListenerResponse response, string filePath, string contentType, string fileName)
        {
            if (!File.Exists(filePath))
            {
                SendErrorResponse(response, 404, "文件不存在");
                return;
            }
            
            try
            {
                response.StatusCode = 200;
                response.ContentType = contentType;
                response.AddHeader("Content-Disposition", $"attachment; filename=\"{fileName}\"");
                
                byte[] buffer = File.ReadAllBytes(filePath);
                response.ContentLength64 = buffer.Length;
                response.OutputStream.Write(buffer, 0, buffer.Length);
                response.OutputStream.Close();
            }
            catch (Exception ex)
            {
                SendErrorResponse(response, 500, $"发送文件时出错: {ex.Message}");
            }
            finally
            {
                try
                {
                    // 清理临时文件
                    File.Delete(filePath);
                }
                catch { /* 忽略清理错误 */ }
            }
        }
    }
} 