# RESClient 资源文件规则与标准

## 📋 目录

1. [概述](#概述)
2. [目录结构规范](#目录结构规范)
3. [模板文件规则](#模板文件规则)
4. [Excel文件规范](#excel文件规范)
5. [DWG文件要求](#dwg文件要求)
6. [文件命名规范](#文件命名规范)
7. [版本控制规则](#版本控制规则)
8. [质量检查标准](#质量检查标准)
9. [错误处理规则](#错误处理规则)
10. [维护更新流程](#维护更新流程)

---

## 概述

### 资源文件系统
RESClient依赖完整的资源文件系统来生成高质量的房产测绘报告。资源文件包括：

- **模板文件**: Word文档模板（.docx）
- **Excel文件**: 数据模板和源数据文件（.xlsx/.xls）
- **DWG文件**: AutoCAD图纸文件
- **配置文件**: 系统配置和参数文件（.json）
- **工具文件**: 第三方工具和库文件

### 设计原则
1. **标准化**: 统一的文件格式和命名规范
2. **模块化**: 按功能模块组织资源文件
3. **可维护性**: 便于更新和版本管理
4. **兼容性**: 确保向前和向后兼容
5. **可扩展性**: 支持新模块和功能扩展

### 质量要求
- **完整性**: 所有必需的资源文件必须存在
- **正确性**: 文件内容和格式必须正确
- **一致性**: 相关文件间保持一致性
- **稳定性**: 文件结构和接口保持稳定

---

## 目录结构规范

### 标准目录结构
```
RESClient/
├── 报告模板/                    # 报告模板根目录
│   ├── 01_封面/                 # 封面模块模板
│   │   └── 封面.docx
│   ├── 02_目录/                 # 目录模块模板
│   │   └── 目录.docx
│   ├── 03_作业声明/             # 作业声明模块模板
│   │   └── 作业声明.docx
│   ├── 04_作业、质量检查与验收/  # 质量检查模块模板
│   │   └── 作业、质量检查与验收.docx
│   ├── 05_项目基本信息/         # 项目信息模块模板
│   │   └── 项目基本信息.docx
│   ├── 06_楼栋基本信息/         # 楼栋信息模块模板
│   │   ├── 楼栋基本信息.docx
│   │   └── 楼栋基本信息-地下室.docx
│   ├── 07_经主管部门批准的相关证照/
│   │   └── 经主管部门批准的相关证照.docx
│   ├── 08_地下室人防区域说明/
│   │   └── 地下室人防区域说明.docx
│   ├── 09_项目住地及测绘房屋分布图/
│   │   └── 项目住地及测绘房屋分布图.docx
│   ├── 10_建筑物现状影像图/
│   │   └── 建筑物现状影像图.docx
│   ├── 11_房产面积汇总表/
│   │   ├── 房产面积汇总表.docx
│   │   └── 房产面积汇总表模板.xlsx
│   ├── 12_房产分户面积统计表/
│   │   ├── 房产分户面积统计表.docx
│   │   ├── 房产分户面积统计表.xlsx
│   │   └── 房产分户面积统计表-地下室.docx
│   └── 13_房产分层测绘图/
│       └── 房产分层测绘图.docx
├── tool/                        # 工具目录
│   └── 7z/                      # 7z压缩工具
│       ├── 7z.dll
│       └── 7z.exe
├── 报告生成/                    # 输出目录
│   ├── 默认用户/                # 用户目录
│   │   ├── 单独模块/            # 单独模块输出
│   │   ├── 完整报告/            # 完整报告输出
│   │   └── 报告分块/            # 临时文件目录
│   └── [其他用户]/
└── 配置文件/                    # 配置目录
    ├── 参数配置/
    └── 日志文件/
```

### 目录命名规则
1. **模块目录**: 使用`序号_模块名称`格式
2. **序号格式**: 两位数字，如`01`、`02`等
3. **中文命名**: 使用标准中文名称，避免特殊字符
4. **层级限制**: 目录层级不超过4级
5. **路径长度**: 完整路径长度不超过260字符

### 权限要求
- **读取权限**: 所有模板目录需要读取权限
- **写入权限**: 输出目录需要写入权限
- **执行权限**: 工具目录需要执行权限
- **网络权限**: 需要网络访问权限（连接服务器）

---

## 模板文件规则

### Word模板规范

#### 文件格式要求
- **文件格式**: 必须使用.docx格式
- **Word版本**: 兼容Microsoft Word 2016及以上版本
- **文档结构**: 使用标准的Word文档结构
- **字体要求**: 使用系统标准字体（如宋体、黑体）

#### 变量占位符规范
**标准格式**: `${变量名}`

**命名规则**:
- 使用有意义的中文变量名
- 避免使用特殊字符和空格
- 保持变量名的一致性
- 区分大小写

**示例变量**:
```
${项目名称}          # 项目的完整名称
${项目地址}          # 项目的详细地址
${建设单位}          # 建设单位名称
${测绘公司}          # 测绘公司名称
${测绘编号}          # 测绘项目编号
${报告日期}          # 报告生成日期
${楼栋信息}          # 楼栋基本信息
${面积统计}          # 面积统计数据
```

#### 格式要求
- **页面设置**: A4纸张，标准页边距
- **字体大小**: 正文12号字，标题适当加大
- **行间距**: 1.5倍行距或固定值
- **段落格式**: 统一的段落间距和缩进
- **表格格式**: 统一的表格样式和边框

#### 样式规范
- **标题样式**: 使用Word内置标题样式
- **正文样式**: 统一的正文格式
- **表格样式**: 预定义的表格样式
- **图片样式**: 统一的图片排版格式

### 模板变量映射

#### 封面模块变量
```
模板变量              → 参数字段
${测绘编号}          → SurveyNumber
${项目编号}          → ProjectNumber
${项目名称}          → ProjectName
${项目地址}          → ProjectAddress
${建设单位}          → ConstructionUnit
${测绘楼栋}          → SurveyBuilding
${测绘公司}          → SurveyCompany
${测绘资格证书号}    → QualificationNumber
${报告日期}          → ReportDate
```

#### 兼容性变量
为保持向后兼容，支持以下变量映射：
```
${测绘单位名称}      → ${测绘公司}
${甲测资字}          → ${测绘资格证书号}
```

### 特殊模板处理

#### 地下室模板
- **文件命名**: 在标准模板名后添加`-地下室`后缀
- **触发条件**: 检测到负数楼层或地下室标识
- **自动切换**: 系统自动选择合适的模板
- **数据兼容**: 保持与标准模板相同的数据结构

#### 条件模板
- **条件判断**: 基于数据内容选择不同模板
- **动态切换**: 运行时动态选择模板
- **参数传递**: 保持参数传递的一致性

---

## Excel文件规范

### 文件格式要求

#### 支持格式
- **.xlsx**: 首选格式，现代Excel格式
- **.xls**: 传统格式，自动转换为.xlsx
- **压缩包**: 支持从.7z压缩包中提取

#### 格式转换规则
1. **自动转换**: .xls文件自动转换为.xlsx
2. **格式保持**: 转换过程保持数据完整性
3. **样式保持**: 保持原有的单元格格式
4. **公式保持**: 保持Excel公式的正确性

### 数据结构规范

#### 楼栋信息Excel结构
**必需字段**:
- **楼栋号**: 建筑物编号（支持数字和文字混合）
- **楼栋名称**: 建筑物名称
- **地上层数**: 地面以上层数
- **地下层数**: 地面以下层数（可选）
- **建筑面积**: 总建筑面积
- **主要用途**: 建筑物主要用途

**可选字段**:
- **建筑结构**: 建筑结构类型
- **建成年份**: 建筑完成年份
- **备注信息**: 其他说明信息

#### 面积统计Excel结构
**数据来源**: CGB.xls文件（通常在成果包.7z中）

**关键字段**:
- **楼栋号**: 用于数据聚合的关键字段
- **户号**: 房屋户号
- **房间号**: 具体房间编号
- **建筑面积**: 房间建筑面积
- **套内面积**: 套内使用面积
- **用途**: 房间用途分类

### 数据处理规则

#### 楼栋号处理
- **智能排序**: 支持数字和文字混合的楼栋号排序
- **格式统一**: 自动统一楼栋号格式
- **重复检查**: 检查和处理重复的楼栋号

#### 楼层处理
- **地下室标识**: 负数表示地下室楼层
- **楼层范围**: 支持楼层范围的表示和处理
- **用途聚合**: 按楼层聚合用途信息

#### 面积计算
- **精度控制**: 面积数据保持2位小数精度
- **单位统一**: 统一使用平方米作为面积单位
- **合理性检查**: 检查面积数据的合理性

### 模板Excel规范

#### 模板结构
- **表头设计**: 清晰的表头结构和字段说明
- **数据区域**: 明确的数据填充区域
- **格式设置**: 预设的单元格格式
- **公式设置**: 必要的计算公式

#### 数据验证
- **数据类型**: 设置正确的数据类型验证
- **取值范围**: 设置合理的数值范围
- **必填检查**: 标识必填字段
- **格式检查**: 验证数据格式正确性

---

## DWG文件要求

### 文件格式规范

#### AutoCAD版本支持
- **主要版本**: AutoCAD 2025
- **兼容版本**: AutoCAD 2020-2025
- **文件格式**: .dwg格式
- **版本转换**: 支持版本间的自动转换

#### 文件命名要求
- **标识关键字**: 文件名必须包含"打印图"关键字
- **命名格式**: `项目名称_打印图_编号.dwg`
- **字符限制**: 避免使用特殊字符和过长文件名
- **编码格式**: 使用UTF-8编码支持中文

### 图纸内容要求

#### 图层规范
- **标准图层**: 使用标准的CAD图层命名
- **图层颜色**: 统一的图层颜色设置
- **线型设置**: 标准的线型和线宽设置
- **文字样式**: 统一的文字样式和大小

#### 图纸比例
- **标准比例**: 使用标准的制图比例
- **比例标注**: 明确标注图纸比例
- **尺寸标注**: 完整的尺寸标注信息
- **图例说明**: 必要的图例和说明

### 处理要求

#### 权限要求
- **管理员权限**: DWG处理需要管理员权限
- **文件权限**: 确保DWG文件可读
- **目录权限**: 输出目录需要写入权限

#### 批量处理规范
- **文件数量**: 建议每批不超过50个文件
- **文件大小**: 单个文件不超过100MB
- **处理时间**: 预留充足的处理时间
- **错误处理**: 完善的错误处理和重试机制

### 输出规范

#### 输出格式
- **图片格式**: 支持PNG、JPG等格式
- **分辨率**: 标准的图片分辨率设置
- **文件命名**: 规范的输出文件命名
- **目录结构**: 清晰的输出目录结构

#### 质量要求
- **图像清晰**: 确保输出图像清晰可读
- **比例正确**: 保持正确的图纸比例
- **内容完整**: 确保图纸内容完整
- **格式统一**: 统一的输出格式和样式

---

## 文件命名规范

### 通用命名规则

#### 基本原则
1. **描述性**: 文件名应清楚描述文件内容
2. **唯一性**: 同一目录下文件名必须唯一
3. **简洁性**: 文件名应简洁明了，避免过长
4. **标准化**: 使用统一的命名格式和规范

#### 字符规范
- **允许字符**: 中文、英文、数字、下划线、连字符
- **禁用字符**: `\ / : * ? " < > |` 等特殊字符
- **大小写**: 保持一致的大小写使用
- **编码格式**: 使用UTF-8编码支持中文

### 模板文件命名

#### Word模板命名
**格式**: `模块名称.docx`
**示例**:
- `封面.docx`
- `目录.docx`
- `作业声明.docx`
- `楼栋基本信息.docx`
- `楼栋基本信息-地下室.docx`

#### Excel模板命名
**格式**: `模块名称[_用途].xlsx`
**示例**:
- `房产面积汇总表模板.xlsx`
- `房产分户面积统计表.xlsx`
- `楼栋基本信息模板.xlsx`

### 输出文件命名

#### 单独模块文件
**格式**: `序号_模块名称.docx`
**示例**:
- `01_封面.docx`
- `02_目录.docx`
- `06_楼栋基本信息.docx`

#### 集成报告文件
**格式**: `完整报告_YYYYMMDD_HHMMSS.docx`
**示例**:
- `完整报告_20241225_143022.docx`
- `完整报告_20241225_150845.docx`

### 数据文件命名

#### Excel数据文件
**格式**: `数据类型_项目标识_日期.xlsx`
**示例**:
- `楼栋信息_锦江花园_20241225.xlsx`
- `面积统计_测试项目_20241225.xlsx`

#### DWG文件命名
**格式**: `项目名称_打印图_编号.dwg`
**示例**:
- `锦江花园_打印图_001.dwg`
- `测试项目_打印图_1-1.dwg`

### 版本控制命名

#### 版本号格式
**格式**: `文件名_v主版本.次版本[.修订版本].扩展名`
**示例**:
- `封面_v1.0.docx`
- `楼栋基本信息_v1.2.docx`
- `面积汇总表模板_v2.1.xlsx`

#### 备份文件命名
**格式**: `原文件名_backup_YYYYMMDD.扩展名`
**示例**:
- `封面_backup_20241225.docx`
- `配置文件_backup_20241225.json`

---

## 版本控制规则

### 版本管理策略

#### 版本号规范
**格式**: `主版本.次版本.修订版本`
- **主版本**: 重大功能变更或不兼容更新
- **次版本**: 新功能添加或重要改进
- **修订版本**: 错误修复或小幅改进

**示例**:
- `1.0.0`: 初始版本
- `1.1.0`: 添加新模块或功能
- `1.1.1`: 修复错误或小幅改进
- `2.0.0`: 重大架构变更

#### 兼容性规则
1. **向前兼容**: 新版本必须支持旧版本的资源文件
2. **向后兼容**: 在可能的情况下保持向后兼容
3. **迁移支持**: 提供版本间的迁移工具和指南
4. **废弃通知**: 提前通知即将废弃的功能

### 文件版本管理

#### 模板文件版本控制
- **版本标识**: 在文件属性中记录版本信息
- **变更日志**: 维护详细的变更记录
- **兼容性测试**: 确保新版本模板的兼容性
- **回滚机制**: 支持版本回滚功能

#### 配置文件版本控制
- **结构版本**: 配置文件结构的版本标识
- **自动升级**: 自动升级旧版本配置文件
- **备份保护**: 升级前自动备份原配置
- **错误恢复**: 升级失败时的恢复机制

### 发布管理

#### 发布流程
1. **开发测试**: 在开发环境充分测试
2. **兼容性验证**: 验证与现有版本的兼容性
3. **文档更新**: 更新相关文档和说明
4. **发布准备**: 准备发布包和安装说明
5. **正式发布**: 发布新版本并通知用户

#### 发布包内容
- **程序文件**: 更新的程序文件
- **模板文件**: 新增或更新的模板文件
- **配置文件**: 默认配置文件
- **文档资料**: 更新说明和用户手册
- **工具文件**: 必要的工具和依赖文件

---

## 质量检查标准

### 文件完整性检查

#### 必需文件检查清单
**模板文件检查**:
- [ ] 01_封面/封面.docx
- [ ] 02_目录/目录.docx
- [ ] 03_作业声明/作业声明.docx
- [ ] 04_作业、质量检查与验收/作业、质量检查与验收.docx
- [ ] 05_项目基本信息/项目基本信息.docx
- [ ] 06_楼栋基本信息/楼栋基本信息.docx
- [ ] 06_楼栋基本信息/楼栋基本信息-地下室.docx
- [ ] 07_经主管部门批准的相关证照/经主管部门批准的相关证照.docx
- [ ] 08_地下室人防区域说明/地下室人防区域说明.docx
- [ ] 09_项目住地及测绘房屋分布图/项目住地及测绘房屋分布图.docx
- [ ] 10_建筑物现状影像图/建筑物现状影像图.docx
- [ ] 11_房产面积汇总表/房产面积汇总表.docx
- [ ] 11_房产面积汇总表/房产面积汇总表模板.xlsx
- [ ] 12_房产分户面积统计表/房产分户面积统计表.docx
- [ ] 12_房产分户面积统计表/房产分户面积统计表.xlsx
- [ ] 12_房产分户面积统计表/房产分户面积统计表-地下室.docx
- [ ] 13_房产分层测绘图/房产分层测绘图.docx

**工具文件检查**:
- [ ] tool/7z/7z.dll
- [ ] tool/7z/7z.exe

#### 文件属性检查
- **文件大小**: 检查文件大小是否合理
- **修改时间**: 验证文件的最后修改时间
- **文件权限**: 确保文件具有正确的访问权限
- **文件完整性**: 验证文件未损坏

### 内容质量检查

#### Word模板内容检查
**格式检查**:
- 页面设置是否正确（A4、页边距）
- 字体和字号是否符合规范
- 段落格式是否统一
- 表格样式是否一致

**变量检查**:
- 变量占位符格式是否正确（`${变量名}`）
- 变量名称是否规范和一致
- 必需变量是否完整
- 变量映射是否正确

**内容检查**:
- 文档结构是否完整
- 标题层次是否正确
- 内容是否符合业务要求
- 语言表达是否准确

#### Excel模板内容检查
**结构检查**:
- 表头设计是否清晰
- 数据区域是否明确
- 字段定义是否完整
- 表格格式是否统一

**数据检查**:
- 数据类型设置是否正确
- 数据验证规则是否合理
- 公式设置是否正确
- 示例数据是否准确

### 功能测试标准

#### 模板生成测试
1. **参数填充测试**: 验证所有参数能正确填充
2. **格式保持测试**: 确保生成后格式不变
3. **特殊字符测试**: 测试特殊字符的处理
4. **边界条件测试**: 测试极限情况下的表现

#### 数据处理测试
1. **Excel读取测试**: 验证Excel文件能正确读取
2. **数据转换测试**: 测试数据格式转换功能
3. **聚合计算测试**: 验证数据聚合和计算正确性
4. **错误处理测试**: 测试异常数据的处理

#### 集成测试
1. **端到端测试**: 完整的报告生成流程测试
2. **兼容性测试**: 不同版本间的兼容性测试
3. **性能测试**: 大数据量下的性能测试
4. **稳定性测试**: 长时间运行的稳定性测试

---

## 错误处理规则

### 错误分类体系

#### 文件相关错误
**缺失文件错误**:
- 错误代码: `FILE_NOT_FOUND`
- 严重程度: 严重错误
- 处理方式: 停止处理，提示用户
- 解决建议: 检查文件路径，确保文件存在

**文件权限错误**:
- 错误代码: `ACCESS_DENIED`
- 严重程度: 严重错误
- 处理方式: 停止处理，提示权限问题
- 解决建议: 检查文件权限，以管理员身份运行

**文件损坏错误**:
- 错误代码: `FILE_CORRUPTED`
- 严重程度: 严重错误
- 处理方式: 跳过损坏文件，记录错误
- 解决建议: 使用备份文件或重新获取文件

#### 数据相关错误
**数据验证错误**:
- 错误代码: `DATA_VALIDATION_FAILED`
- 严重程度: 警告
- 处理方式: 使用默认值，记录警告
- 解决建议: 检查数据格式和内容

**数据类型错误**:
- 错误代码: `DATA_TYPE_MISMATCH`
- 严重程度: 错误
- 处理方式: 尝试类型转换，失败则跳过
- 解决建议: 确保数据类型正确

**数据范围错误**:
- 错误代码: `DATA_OUT_OF_RANGE`
- 严重程度: 警告
- 处理方式: 使用边界值，记录警告
- 解决建议: 检查数据是否在合理范围内

#### 模板相关错误
**模板格式错误**:
- 错误代码: `TEMPLATE_FORMAT_ERROR`
- 严重程度: 严重错误
- 处理方式: 停止处理该模板
- 解决建议: 检查模板文件格式和结构

**变量映射错误**:
- 错误代码: `VARIABLE_MAPPING_ERROR`
- 严重程度: 错误
- 处理方式: 跳过未映射变量
- 解决建议: 检查变量名称和映射关系

### 错误处理策略

#### 错误恢复机制
1. **自动重试**: 对于临时性错误，自动重试处理
2. **降级处理**: 在部分功能失败时，提供降级服务
3. **跳过处理**: 跳过有问题的数据项，继续处理其他项
4. **回滚机制**: 在严重错误时，回滚到安全状态

#### 错误报告机制
1. **实时反馈**: 在处理过程中实时显示错误信息
2. **错误汇总**: 处理完成后提供错误汇总报告
3. **详细日志**: 记录详细的错误日志供技术分析
4. **用户指导**: 提供用户友好的错误解决指导

### 错误预防措施

#### 输入验证
- **文件存在性检查**: 处理前检查所有必需文件
- **格式验证**: 验证文件格式和结构正确性
- **权限检查**: 确保有足够的文件访问权限
- **依赖检查**: 检查所有依赖组件是否可用

#### 数据验证
- **类型检查**: 验证数据类型正确性
- **范围检查**: 检查数据是否在合理范围内
- **完整性检查**: 确保必需数据字段完整
- **一致性检查**: 验证相关数据的一致性

#### 环境检查
- **系统要求**: 检查系统是否满足运行要求
- **资源可用性**: 检查内存、磁盘空间等资源
- **网络连接**: 验证网络连接状态
- **服务状态**: 检查依赖服务的运行状态

---

## 维护更新流程

### 日常维护

#### 文件监控
- **完整性监控**: 定期检查文件完整性
- **版本监控**: 监控文件版本变化
- **使用情况监控**: 统计文件使用频率
- **性能监控**: 监控文件访问性能

#### 清理维护
- **临时文件清理**: 定期清理临时文件
- **日志文件管理**: 管理和归档日志文件
- **备份文件管理**: 管理和清理过期备份
- **缓存清理**: 清理系统缓存文件

### 更新流程

#### 更新准备
1. **需求分析**: 分析更新需求和影响范围
2. **兼容性评估**: 评估与现有版本的兼容性
3. **测试计划**: 制定详细的测试计划
4. **备份策略**: 制定备份和恢复策略

#### 更新实施
1. **备份现有文件**: 备份所有相关文件
2. **停止相关服务**: 停止可能受影响的服务
3. **执行更新**: 按计划执行文件更新
4. **验证更新**: 验证更新是否成功
5. **恢复服务**: 重新启动相关服务

#### 更新验证
1. **功能测试**: 测试所有相关功能
2. **性能测试**: 验证性能是否符合要求
3. **兼容性测试**: 测试与其他组件的兼容性
4. **用户验收**: 获得用户的验收确认

### 应急处理

#### 紧急修复流程
1. **问题识别**: 快速识别和定位问题
2. **影响评估**: 评估问题的影响范围
3. **临时方案**: 实施临时解决方案
4. **正式修复**: 开发和部署正式修复
5. **验证确认**: 验证修复效果

#### 回滚机制
1. **回滚条件**: 明确回滚的触发条件
2. **回滚步骤**: 详细的回滚操作步骤
3. **数据保护**: 确保回滚过程中数据安全
4. **验证确认**: 验证回滚是否成功

### 文档维护

#### 文档更新
- **及时更新**: 随着系统变更及时更新文档
- **版本同步**: 确保文档版本与系统版本同步
- **内容准确**: 确保文档内容准确无误
- **格式统一**: 保持文档格式的统一性

#### 培训材料
- **用户手册**: 维护最新的用户操作手册
- **技术文档**: 更新技术实现文档
- **培训课程**: 开发和更新培训课程
- **常见问题**: 维护常见问题解答库

---

## 附录

### 检查清单模板

#### 新模板文件检查清单
- [ ] 文件格式正确（.docx）
- [ ] 文件命名符合规范
- [ ] 页面设置正确（A4、页边距）
- [ ] 字体和格式统一
- [ ] 变量占位符格式正确
- [ ] 变量名称规范
- [ ] 内容结构完整
- [ ] 语言表达准确
- [ ] 兼容性测试通过
- [ ] 功能测试通过

#### Excel文件检查清单
- [ ] 文件格式正确（.xlsx）
- [ ] 表头设计清晰
- [ ] 数据区域明确
- [ ] 字段定义完整
- [ ] 数据类型正确
- [ ] 验证规则合理
- [ ] 公式设置正确
- [ ] 格式统一
- [ ] 示例数据准确
- [ ] 兼容性测试通过

### 常用工具和命令

#### 文件检查工具
```bash
# 检查文件完整性
RESClient.exe verify-templates

# 检查文件权限
RESClient.exe check-permissions

# 验证配置文件
RESClient.exe validate-config
```

#### 测试命令
```bash
# 运行完整测试
RESClient.exe test

# 测试特定模块
RESClient.exe test-module [模块名称]

# 测试错误处理
RESClient.exe test-error
```

### 联系信息

#### 技术支持
- **文档问题**: 查阅在线文档或联系技术支持
- **文件问题**: 使用系统检查工具或联系管理员
- **更新问题**: 联系系统管理员或技术团队

#### 反馈渠道
- **问题报告**: 使用系统内置错误报告功能
- **改进建议**: 通过用户反馈渠道提交建议
- **技术交流**: 参与技术交流社区

---

*本规则文档版本: v1.0*
*最后更新: 2024年12月*
*适用版本: RESClient v1.0及以上*
